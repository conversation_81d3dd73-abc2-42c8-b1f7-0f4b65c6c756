import{b as Wc,c as qc,d as Zc,e as Yc,f as Qc}from"./chunk-B7SFH74S.js";import{g as yh,h as Dh}from"./chunk-UOV5QIVR.js";import{a as Sh,d as Mh}from"./chunk-GIIU5PV3.js";import{a as Kc}from"./chunk-M2X7KQLB.js";import{a as Jt,d as bh,e as _h,f as Th,h as Gc}from"./chunk-XTVTS2NW.js";import{a as je,b as Eh,c as Ch,d as wh,e as Bi,f as Ih}from"./chunk-C5RQ2IC2.js";import{b as Kt}from"./chunk-42C7ZIID.js";import{a as g,b as N,d as zc,g as ce}from"./chunk-2R6CW7ES.js";function tr(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var nt=tr(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function SE(e,n){let t=typeof n=="object";return new Promise((r,o)=>{let i=!1,s;e.subscribe({next:a=>{s=a,i=!0},error:o,complete:()=>{i?r(s):t?r(n.defaultValue):o(new nt)}})})}function T(e){return typeof e=="function"}var Ui=tr(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function vo(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var re=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(T(r))try{r()}catch(i){n=i instanceof Ui?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Ah(i)}catch(s){n=n??[],s instanceof Ui?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Ui(n)}}add(n){var t;if(n&&n!==this)if(this.closed)Ah(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&vo(t,n)}remove(n){let{_finalizers:t}=this;t&&vo(t,n),n instanceof e&&n._removeParent(this)}};re.EMPTY=(()=>{let e=new re;return e.closed=!0,e})();var Jc=re.EMPTY;function Hi(e){return e instanceof re||e&&"closed"in e&&T(e.remove)&&T(e.add)&&T(e.unsubscribe)}function Ah(e){T(e)?e():e.unsubscribe()}var rt={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var nr={setTimeout(e,n,...t){let{delegate:r}=nr;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=nr;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function $i(e){nr.setTimeout(()=>{let{onUnhandledError:n}=rt;if(n)n(e);else throw e})}function yo(){}var Nh=Xc("C",void 0,void 0);function Rh(e){return Xc("E",void 0,e)}function xh(e){return Xc("N",e,void 0)}function Xc(e,n,t){return{kind:e,value:n,error:t}}var wn=null;function rr(e){if(rt.useDeprecatedSynchronousErrorHandling){let n=!wn;if(n&&(wn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=wn;if(wn=null,t)throw r}}else e()}function Oh(e){rt.useDeprecatedSynchronousErrorHandling&&wn&&(wn.errorThrown=!0,wn.error=e)}var In=class extends re{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,Hi(n)&&n.add(this)):this.destination=NE}static create(n,t,r){return new or(n,t,r)}next(n){this.isStopped?tu(xh(n),this):this._next(n)}error(n){this.isStopped?tu(Rh(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?tu(Nh,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},ME=Function.prototype.bind;function eu(e,n){return ME.call(e,n)}var nu=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){zi(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){zi(r)}else zi(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){zi(t)}}},or=class extends In{constructor(n,t,r){super();let o;if(T(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&rt.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&eu(n.next,i),error:n.error&&eu(n.error,i),complete:n.complete&&eu(n.complete,i)}):o=n}this.destination=new nu(o)}};function zi(e){rt.useDeprecatedSynchronousErrorHandling?Oh(e):$i(e)}function AE(e){throw e}function tu(e,n){let{onStoppedNotification:t}=rt;t&&nr.setTimeout(()=>t(e,n))}var NE={closed:!0,next:yo,error:AE,complete:yo};var ir=typeof Symbol=="function"&&Symbol.observable||"@@observable";function Ie(e){return e}function ru(...e){return ou(e)}function ou(e){return e.length===0?Ie:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var O=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=xE(t)?t:new or(t,r,o);return rr(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=kh(r),new r((o,i)=>{let s=new or({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[ir](){return this}pipe(...t){return ou(t)(this)}toPromise(t){return t=kh(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function kh(e){var n;return(n=e??rt.Promise)!==null&&n!==void 0?n:Promise}function RE(e){return e&&T(e.next)&&T(e.error)&&T(e.complete)}function xE(e){return e&&e instanceof In||RE(e)&&Hi(e)}function iu(e){return T(e?.lift)}function L(e){return n=>{if(iu(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function P(e,n,t,r,o){return new su(e,n,t,r,o)}var su=class extends In{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function sr(){return L((e,n)=>{let t=null;e._refCount++;let r=P(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var ar=class extends O{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,iu(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new re;let t=this.getSubject();n.add(this.source.subscribe(P(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=re.EMPTY)}return n}refCount(){return sr()(this)}};var Ph=tr(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var $=(()=>{class e extends O{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new Gi(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new Ph}next(t){rr(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){rr(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){rr(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Jc:(this.currentObservers=null,i.push(t),new re(()=>{this.currentObservers=null,vo(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new O;return t.source=this,t}}return e.create=(n,t)=>new Gi(n,t),e})(),Gi=class extends ${constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:Jc}};var oe=class extends ${constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var xe=new O(e=>e.complete());function Fh(e){return e&&T(e.schedule)}function Lh(e){return e[e.length-1]}function Wi(e){return T(Lh(e))?e.pop():void 0}function Xt(e){return Fh(Lh(e))?e.pop():void 0}function Do(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function Vh(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,n||[])).next())})}function jh(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function bn(e){return this instanceof bn?(this.v=e,this):new bn(e)}function Bh(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(m){return Promise.resolve(m).then(f,d)}}function a(f,m){r[f]&&(o[f]=function(I){return new Promise(function(D,C){i.push([f,I,D,C])>1||c(f,I)})},m&&(o[f]=m(o[f])))}function c(f,m){try{u(r[f](m))}catch(I){h(i[0][3],I)}}function u(f){f.value instanceof bn?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,m){f(m),i.shift(),i.length&&c(i[0][0],i[0][1])}}function Uh(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof jh=="function"?jh(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var cr=e=>e&&typeof e.length=="number"&&typeof e!="function";function qi(e){return T(e?.then)}function Zi(e){return T(e[ir])}function Yi(e){return Symbol.asyncIterator&&T(e?.[Symbol.asyncIterator])}function Qi(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function OE(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Ki=OE();function Ji(e){return T(e?.[Ki])}function Xi(e){return Bh(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield bn(t.read());if(o)return yield bn(void 0);yield yield bn(r)}}finally{t.releaseLock()}})}function es(e){return T(e?.getReader)}function J(e){if(e instanceof O)return e;if(e!=null){if(Zi(e))return kE(e);if(cr(e))return PE(e);if(qi(e))return FE(e);if(Yi(e))return Hh(e);if(Ji(e))return LE(e);if(es(e))return jE(e)}throw Qi(e)}function kE(e){return new O(n=>{let t=e[ir]();if(T(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function PE(e){return new O(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function FE(e){return new O(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,$i)})}function LE(e){return new O(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function Hh(e){return new O(n=>{VE(e,n).catch(t=>n.error(t))})}function jE(e){return Hh(Xi(e))}function VE(e,n){var t,r,o,i;return Vh(this,void 0,void 0,function*(){try{for(t=Uh(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function Oe(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function ts(e,n=0){return L((t,r)=>{t.subscribe(P(r,o=>Oe(r,e,()=>r.next(o),n),()=>Oe(r,e,()=>r.complete(),n),o=>Oe(r,e,()=>r.error(o),n)))})}function ns(e,n=0){return L((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function $h(e,n){return J(e).pipe(ns(n),ts(n))}function zh(e,n){return J(e).pipe(ns(n),ts(n))}function Gh(e,n){return new O(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function Wh(e,n){return new O(t=>{let r;return Oe(t,n,()=>{r=e[Ki](),Oe(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>T(r?.return)&&r.return()})}function rs(e,n){if(!e)throw new Error("Iterable cannot be null");return new O(t=>{Oe(t,n,()=>{let r=e[Symbol.asyncIterator]();Oe(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function qh(e,n){return rs(Xi(e),n)}function Zh(e,n){if(e!=null){if(Zi(e))return $h(e,n);if(cr(e))return Gh(e,n);if(qi(e))return zh(e,n);if(Yi(e))return rs(e,n);if(Ji(e))return Wh(e,n);if(es(e))return qh(e,n)}throw Qi(e)}function Z(e,n){return n?Zh(e,n):J(e)}function _(...e){let n=Xt(e);return Z(e,n)}function ur(e,n){let t=T(e)?e:()=>e,r=o=>o.error(t());return new O(n?o=>n.schedule(r,0,o):r)}function au(e){return!!e&&(e instanceof O||T(e.lift)&&T(e.subscribe))}function R(e,n){return L((t,r)=>{let o=0;t.subscribe(P(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:BE}=Array;function UE(e,n){return BE(n)?e(...n):e(n)}function lr(e){return R(n=>UE(e,n))}var{isArray:HE}=Array,{getPrototypeOf:$E,prototype:zE,keys:GE}=Object;function os(e){if(e.length===1){let n=e[0];if(HE(n))return{args:n,keys:null};if(WE(n)){let t=GE(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function WE(e){return e&&typeof e=="object"&&$E(e)===zE}function is(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function dr(...e){let n=Xt(e),t=Wi(e),{args:r,keys:o}=os(e);if(r.length===0)return Z([],n);let i=new O(qE(r,n,o?s=>is(o,s):Ie));return t?i.pipe(lr(t)):i}function qE(e,n,t=Ie){return r=>{Yh(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)Yh(n,()=>{let u=Z(e[c],n),l=!1;u.subscribe(P(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Yh(e,n,t){e?Oe(t,e,n):n()}function Qh(e,n,t,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&n.complete()},f=I=>u<r?m(I):c.push(I),m=I=>{i&&n.next(I),u++;let D=!1;J(t(I,l++)).subscribe(P(n,C=>{o?.(C),i?f(C):n.next(C)},()=>{D=!0},void 0,()=>{if(D)try{for(u--;c.length&&u<r;){let C=c.shift();s?Oe(n,s,()=>m(C)):m(C)}h()}catch(C){n.error(C)}}))};return e.subscribe(P(n,f,()=>{d=!0,h()})),()=>{a?.()}}function K(e,n,t=1/0){return T(n)?K((r,o)=>R((i,s)=>n(r,i,o,s))(J(e(r,o))),t):(typeof n=="number"&&(t=n),L((r,o)=>Qh(r,o,e,t)))}function fr(e=1/0){return K(Ie,e)}function Kh(){return fr(1)}function hr(...e){return Kh()(Z(e,Xt(e)))}function Eo(e){return new O(n=>{J(e()).subscribe(n)})}function cu(...e){let n=Wi(e),{args:t,keys:r}=os(e),o=new O(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;J(t[l]).subscribe(P(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?is(r,a):a),i.complete())}))}});return n?o.pipe(lr(n)):o}var ZE=["addListener","removeListener"],YE=["addEventListener","removeEventListener"],QE=["on","off"];function Co(e,n,t,r){if(T(t)&&(r=t,t=void 0),r)return Co(e,n,t).pipe(lr(r));let[o,i]=XE(e)?YE.map(s=>a=>e[s](n,a,t)):KE(e)?ZE.map(Jh(e,n)):JE(e)?QE.map(Jh(e,n)):[];if(!o&&cr(e))return K(s=>Co(s,n,t))(J(e));if(!o)throw new TypeError("Invalid event target");return new O(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function Jh(e,n){return t=>r=>e[t](n,r)}function KE(e){return T(e.addListener)&&T(e.removeListener)}function JE(e){return T(e.on)&&T(e.off)}function XE(e){return T(e.addEventListener)&&T(e.removeEventListener)}function se(e,n){return L((t,r)=>{let o=0;t.subscribe(P(r,i=>e.call(n,i,o++)&&r.next(i)))})}function mt(e){return L((n,t)=>{let r=null,o=!1,i;r=n.subscribe(P(t,void 0,void 0,s=>{i=J(e(s,mt(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function Xh(e,n,t,r,o){return(i,s)=>{let a=t,c=n,u=0;i.subscribe(P(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function vt(e,n){return T(n)?K(e,n,1):K(e,1)}function en(e){return L((n,t)=>{let r=!1;n.subscribe(P(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function Rt(e){return e<=0?()=>xe:L((n,t)=>{let r=0;n.subscribe(P(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function uu(e,n=Ie){return e=e??eC,L((t,r)=>{let o,i=!0;t.subscribe(P(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function eC(e,n){return e===n}function ss(e=tC){return L((n,t)=>{let r=!1;n.subscribe(P(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function tC(){return new nt}function tn(e){return L((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function xt(e,n){let t=arguments.length>=2;return r=>r.pipe(e?se((o,i)=>e(o,i,r)):Ie,Rt(1),t?en(n):ss(()=>new nt))}function pr(e){return e<=0?()=>xe:L((n,t)=>{let r=[];n.subscribe(P(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function lu(e,n){let t=arguments.length>=2;return r=>r.pipe(e?se((o,i)=>e(o,i,r)):Ie,pr(1),t?en(n):ss(()=>new nt))}function du(e,n){return L(Xh(e,n,arguments.length>=2,!0))}function fu(...e){let n=Xt(e);return L((t,r)=>{(n?hr(e,t,n):hr(e,t)).subscribe(r)})}function ue(e,n){return L((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(P(r,c=>{o?.unsubscribe();let u=0,l=i++;J(e(c,l)).subscribe(o=P(r,d=>r.next(n?n(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function as(e){return L((n,t)=>{J(e).subscribe(P(t,()=>t.complete(),yo)),!t.closed&&n.subscribe(t)})}function le(e,n,t){let r=T(e)||n||t?{next:e,error:n,complete:t}:e;return r?L((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(P(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):Ie}var hu;function cs(){return hu}function yt(e){let n=hu;return hu=e,n}var ep=Symbol("NotFound");function gr(e){return e===ep||e?.name==="\u0275NotFound"}function hs(e,n){return Object.is(e,n)}var ae=null,us=!1,pu=1,nC=null,be=Symbol("SIGNAL");function x(e){let n=ae;return ae=e,n}function ps(){return ae}var _n={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function mr(e){if(us)throw new Error("");if(ae===null)return;ae.consumerOnSignalRead(e);let n=ae.nextProducerIndex++;if(ys(ae),n<ae.producerNode.length&&ae.producerNode[n]!==e&&Io(ae)){let t=ae.producerNode[n];vs(t,ae.producerIndexOfThis[n])}ae.producerNode[n]!==e&&(ae.producerNode[n]=e,ae.producerIndexOfThis[n]=Io(ae)?np(e,ae,n):0),ae.producerLastReadVersion[n]=e.version}function tp(){pu++}function gs(e){if(!(Io(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===pu)){if(!e.producerMustRecompute(e)&&!bo(e)){fs(e);return}e.producerRecomputeValue(e),fs(e)}}function gu(e){if(e.liveConsumerNode===void 0)return;let n=us;us=!0;try{for(let t of e.liveConsumerNode)t.dirty||rC(t)}finally{us=n}}function mu(){return ae?.consumerAllowSignalWrites!==!1}function rC(e){e.dirty=!0,gu(e),e.consumerMarkedDirty?.(e)}function fs(e){e.dirty=!1,e.lastCleanEpoch=pu}function Tn(e){return e&&(e.nextProducerIndex=0),x(e)}function vr(e,n){if(x(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Io(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)vs(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function bo(e){ys(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(gs(t),r!==t.version))return!0}return!1}function ms(e){if(ys(e),Io(e))for(let n=0;n<e.producerNode.length;n++)vs(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function np(e,n,t){if(rp(e),e.liveConsumerNode.length===0&&op(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=np(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function vs(e,n){if(rp(e),e.liveConsumerNode.length===1&&op(e))for(let r=0;r<e.producerNode.length;r++)vs(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];ys(o),o.producerIndexOfThis[r]=n}}function Io(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function ys(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function rp(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function op(e){return e.producerNode!==void 0}function Ds(e){nC?.(e)}function Es(e,n){let t=Object.create(oC);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(gs(t),mr(t),t.value===wo)throw t.error;return t.value};return r[be]=t,Ds(t),r}var ls=Symbol("UNSET"),ds=Symbol("COMPUTING"),wo=Symbol("ERRORED"),oC=N(g({},_n),{value:ls,dirty:!0,error:null,equal:hs,kind:"computed",producerMustRecompute(e){return e.value===ls||e.value===ds},producerRecomputeValue(e){if(e.value===ds)throw new Error("");let n=e.value;e.value=ds;let t=Tn(e),r,o=!1;try{r=e.computation(),x(null),o=n!==ls&&n!==wo&&r!==wo&&e.equal(n,r)}catch(i){r=wo,e.error=i}finally{vr(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function iC(){throw new Error}var ip=iC;function sp(e){ip(e)}function vu(e){ip=e}var sC=null;function yu(e,n){let t=Object.create(Cs);t.value=e,n!==void 0&&(t.equal=n);let r=()=>ap(t);return r[be]=t,Ds(t),[r,s=>yr(t,s),s=>Du(t,s)]}function ap(e){return mr(e),e.value}function yr(e,n){mu()||sp(e),e.equal(e.value,n)||(e.value=n,aC(e))}function Du(e,n){mu()||sp(e),yr(e,n(e.value))}var Cs=N(g({},_n),{equal:hs,value:void 0,kind:"signal"});function aC(e){e.version++,tp(),gu(e),sC?.(e)}function cp(e){let n=x(null);try{return e()}finally{x(n)}}var Ts="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",y=class extends Error{code;constructor(n,t){super(Er(n,t)),this.code=n}};function cC(e){return`NG0${Math.abs(e)}`}function Er(e,n){return`${cC(e)}${n?": "+n:""}`}var Ft=globalThis;function B(e){for(let n in e)if(e[n]===B)return n;throw Error("")}function dp(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function kt(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(kt).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function xu(e,n){return e?n?`${e} ${n}`:e:n||""}var uC=B({__forward_ref__:B});function Te(e){return e.__forward_ref__=Te,e.toString=function(){return kt(this())},e}function ge(e){return Ou(e)?e():e}function Ou(e){return typeof e=="function"&&e.hasOwnProperty(uC)&&e.__forward_ref__===Te}function fp(e,n){e==null&&ku(n,e,null,"!=")}function ku(e,n,t,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${t} ${r} ${n} <=Actual]`))}function w(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Be(e){return{providers:e.providers||[],imports:e.imports||[]}}function Ao(e){return lC(e,Ss)}function Pu(e){return Ao(e)!==null}function lC(e,n){return e.hasOwnProperty(n)&&e[n]||null}function dC(e){let n=e?.[Ss]??null;return n||null}function Cu(e){return e&&e.hasOwnProperty(Is)?e[Is]:null}var Ss=B({\u0275prov:B}),Is=B({\u0275inj:B}),E=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=w({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function Fu(e){return e&&!!e.\u0275providers}var Lu=B({\u0275cmp:B}),ju=B({\u0275dir:B}),Vu=B({\u0275pipe:B}),Bu=B({\u0275mod:B}),So=B({\u0275fac:B}),Rn=B({__NG_ELEMENT_ID__:B}),up=B({__NG_ENV_ID__:B});function No(e){return typeof e=="string"?e:e==null?"":String(e)}function bs(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():No(e)}var Uu=B({ngErrorCode:B}),hp=B({ngErrorMessage:B}),To=B({ngTokenPath:B});function Hu(e,n){return pp("",-200,n)}function Ms(e,n){throw new y(-201,!1)}function fC(e,n){e[To]??=[];let t=e[To],r;typeof n=="object"&&"multi"in n&&n?.multi===!0?(fp(n.provide,"Token with multi: true should have a provide property"),r=bs(n.provide)):r=bs(n),t[0]!==r&&e[To].unshift(r)}function hC(e,n){let t=e[To],r=e[Uu],o=e[hp]||e.message;return e.message=gC(o,r,t,n),e}function pp(e,n,t){let r=new y(n,e);return r[Uu]=n,r[hp]=e,t&&(r[To]=t),r}function pC(e){return e[Uu]}function gC(e,n,t=[],r=null){let o="";t&&t.length>1&&(o=` Path: ${t.join(" -> ")}.`);let i=r?` Source: ${r}.`:"";return Er(n,`${e}${i}${o}`)}var wu;function gp(){return wu}function _e(e){let n=wu;return wu=e,n}function $u(e,n,t){let r=Ao(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&8)return null;if(n!==void 0)return n;Ms(e,"Injector")}var mC={},Sn=mC,Iu="__NG_DI_FLAG__",bu=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=Mn(t)||0;try{return this.injector.get(n,r&8?null:Sn,r)}catch(o){if(gr(o))return o;throw o}}};function vC(e,n=0){let t=cs();if(t===void 0)throw new y(-203,!1);if(t===null)return $u(e,void 0,n);{let r=yC(n),o=t.retrieve(e,r);if(gr(o)){if(r.optional)return null;throw o}return o}}function b(e,n=0){return(gp()||vC)(ge(e),n)}function p(e,n){return b(e,Mn(n))}function Mn(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function yC(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function _u(e){let n=[];for(let t=0;t<e.length;t++){let r=ge(e[t]);if(Array.isArray(r)){if(r.length===0)throw new y(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=DC(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(b(o,i))}else n.push(b(r))}return n}function zu(e,n){return e[Iu]=n,e.prototype[Iu]=n,e}function DC(e){return e[Iu]}function nn(e,n){let t=e.hasOwnProperty(So);return t?e[So]:null}function mp(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function vp(e){return e.flat(Number.POSITIVE_INFINITY)}function As(e,n){e.forEach(t=>Array.isArray(t)?As(t,n):n(t))}function Gu(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function Ro(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function yp(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function Dp(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function Ep(e,n,t){let r=Cr(e,n);return r>=0?e[r|1]=t:(r=~r,Dp(e,r,n,t)),r}function Ns(e,n){let t=Cr(e,n);if(t>=0)return e[t|1]}function Cr(e,n){return EC(e,n,1)}function EC(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var on={},Ve=[],sn=new E(""),Wu=new E("",-1),qu=new E(""),Mo=class{get(n,t=Sn){if(t===Sn){let o=pp("",-201);throw o.name="\u0275NotFound",o}return t}};function Zu(e){return e[Bu]||null}function Dt(e){return e[Lu]||null}function Yu(e){return e[ju]||null}function Cp(e){return e[Vu]||null}function wr(e){return{\u0275providers:e}}function wp(...e){return{\u0275providers:Qu(!0,e),\u0275fromNgModule:!0}}function Qu(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return As(n,s=>{let a=s;_s(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Ip(o,i),t}function Ip(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];Ku(o,i=>{n(i,r)})}}function _s(e,n,t,r){if(e=ge(e),!e)return!1;let o=null,i=Cu(e),s=!i&&Dt(e);if(!i&&!s){let c=e.ngModule;if(i=Cu(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)_s(u,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{As(i.imports,l=>{_s(l,n,t,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&Ip(u,n)}if(!a){let u=nn(o)||(()=>new o);n({provide:o,useFactory:u,deps:Ve},o),n({provide:qu,useValue:o,multi:!0},o),n({provide:sn,useValue:()=>b(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Ku(c,l=>{n(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Ku(e,n){for(let t of e)Fu(t)&&(t=t.\u0275providers),Array.isArray(t)?Ku(t,n):n(t)}var CC=B({provide:String,useValue:B});function bp(e){return e!==null&&typeof e=="object"&&CC in e}function wC(e){return!!(e&&e.useExisting)}function IC(e){return!!(e&&e.useFactory)}function An(e){return typeof e=="function"}function _p(e){return!!e.useClass}var xo=new E(""),ws={},lp={},Eu;function Ir(){return Eu===void 0&&(Eu=new Mo),Eu}var q=class{},Nn=class extends q{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,Su(n,s=>this.processProvider(s)),this.records.set(Wu,Dr(void 0,this)),o.has("environment")&&this.records.set(q,Dr(void 0,this));let i=this.records.get(xo);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(qu,Ve,{self:!0}))}retrieve(n,t){let r=Mn(t)||0;try{return this.get(n,Sn,r)}catch(o){if(gr(o))return o;throw o}}destroy(){_o(this),this._destroyed=!0;let n=x(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),x(n)}}onDestroy(n){return _o(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){_o(this);let t=yt(this),r=_e(void 0),o;try{return n()}finally{yt(t),_e(r)}}get(n,t=Sn,r){if(_o(this),n.hasOwnProperty(up))return n[up](this);let o=Mn(r),i,s=yt(this),a=_e(void 0);try{if(!(o&4)){let u=this.records.get(n);if(u===void 0){let l=MC(n)&&Ao(n);l&&this.injectableDefInScope(l)?u=Dr(Tu(n),ws):u=null,this.records.set(n,u)}if(u!=null)return this.hydrate(n,u,o)}let c=o&2?Ir():this.parent;return t=o&8&&t===Sn?null:t,c.get(n,t)}catch(c){let u=pC(c);throw u===-200||u===-201?new y(u,null):c}finally{_e(a),yt(s)}}resolveInjectorInitializers(){let n=x(null),t=yt(this),r=_e(void 0),o;try{let i=this.get(sn,Ve,{self:!0});for(let s of i)s()}finally{yt(t),_e(r),x(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(kt(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=ge(n);let t=An(n)?n:ge(n&&n.provide),r=_C(n);if(!An(n)&&n.multi===!0){let o=this.records.get(t);o||(o=Dr(void 0,ws,!0),o.factory=()=>_u(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t,r){let o=x(null);try{if(t.value===lp)throw Hu(kt(n));return t.value===ws&&(t.value=lp,t.value=t.factory(void 0,r)),typeof t.value=="object"&&t.value&&SC(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{x(o)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=ge(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function Tu(e){let n=Ao(e),t=n!==null?n.factory:nn(e);if(t!==null)return t;if(e instanceof E)throw new y(204,!1);if(e instanceof Function)return bC(e);throw new y(204,!1)}function bC(e){if(e.length>0)throw new y(204,!1);let t=dC(e);return t!==null?()=>t.factory(e):()=>new e}function _C(e){if(bp(e))return Dr(void 0,e.useValue);{let n=Ju(e);return Dr(n,ws)}}function Ju(e,n,t){let r;if(An(e)){let o=ge(e);return nn(o)||Tu(o)}else if(bp(e))r=()=>ge(e.useValue);else if(IC(e))r=()=>e.useFactory(..._u(e.deps||[]));else if(wC(e))r=(o,i)=>b(ge(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=ge(e&&(e.useClass||e.provide));if(TC(e))r=()=>new o(..._u(e.deps));else return nn(o)||Tu(o)}return r}function _o(e){if(e.destroyed)throw new y(205,!1)}function Dr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function TC(e){return!!e.deps}function SC(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function MC(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function Su(e,n){for(let t of e)Array.isArray(t)?Su(t,n):t&&Fu(t)?Su(t.\u0275providers,n):n(t)}function ve(e,n){let t;e instanceof Nn?(_o(e),t=e):t=new bu(e);let r,o=yt(t),i=_e(void 0);try{return n()}finally{yt(o),_e(i)}}function Tp(){return gp()!==void 0||cs()!=null}var it=0,S=1,M=2,de=3,We=4,Se=5,br=6,_r=7,De=8,xn=9,Et=10,X=11,Tr=12,Xu=13,On=14,ke=15,an=16,kn=17,Ct=18,Oo=19,el=20,Ot=21,Rs=22,ko=23,Ue=24,Pn=25,ee=26,Sp=1;var cn=7,Po=8,Fn=9,Me=10;function wt(e){return Array.isArray(e)&&typeof e[Sp]=="object"}function st(e){return Array.isArray(e)&&e[Sp]===!0}function tl(e){return(e.flags&4)!==0}function un(e){return e.componentOffset>-1}function Sr(e){return(e.flags&1)===1}function It(e){return!!e.template}function Mr(e){return(e[M]&512)!==0}function Ln(e){return(e[M]&256)===256}var Mp="svg",Ap="math";function qe(e){for(;Array.isArray(e);)e=e[it];return e}function nl(e,n){return qe(n[e])}function at(e,n){return qe(n[e.index])}function xs(e,n){return e.data[n]}function rl(e,n){return e[n]}function ol(e,n,t,r){t>=e.data.length&&(e.data[t]=null,e.blueprint[t]=null),n[t]=r}function Ze(e,n){let t=n[e];return wt(t)?t:t[it]}function Np(e){return(e[M]&4)===4}function Os(e){return(e[M]&128)===128}function Rp(e){return st(e[de])}function ln(e,n){return n==null?null:e[n]}function il(e){e[kn]=0}function sl(e){e[M]&1024||(e[M]|=1024,Os(e)&&Ar(e))}function xp(e,n){for(;e>0;)n=n[On],e--;return n}function Fo(e){return!!(e[M]&9216||e[Ue]?.dirty)}function ks(e){e[Et].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),Fo(e)&&Ar(e)}function Ar(e){e[Et].changeDetectionScheduler?.notify(0);let n=rn(e);for(;n!==null&&!(n[M]&8192||(n[M]|=8192,!Os(n)));)n=rn(n)}function al(e,n){if(Ln(e))throw new y(911,!1);e[Ot]===null&&(e[Ot]=[]),e[Ot].push(n)}function Op(e,n){if(e[Ot]===null)return;let t=e[Ot].indexOf(n);t!==-1&&e[Ot].splice(t,1)}function rn(e){let n=e[de];return st(n)?n[de]:n}function cl(e){return e[_r]??=[]}function ul(e){return e.cleanup??=[]}function kp(e,n,t,r){let o=cl(n);o.push(t),e.firstCreatePass&&ul(e).push(r,o.length-1)}var k={lFrame:Jp(null),bindingsEnabled:!0,skipHydrationRootTNode:null},Lo=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(Lo||{}),AC=0,Mu=!1;function Pp(){return k.lFrame.elementDepthCount}function Fp(){k.lFrame.elementDepthCount++}function Lp(){k.lFrame.elementDepthCount--}function Ps(){return k.bindingsEnabled}function ll(){return k.skipHydrationRootTNode!==null}function jp(e){return k.skipHydrationRootTNode===e}function Vp(){k.skipHydrationRootTNode=null}function j(){return k.lFrame.lView}function ye(){return k.lFrame.tView}function Bp(e){return k.lFrame.contextLView=e,e[De]}function Up(e){return k.lFrame.contextLView=null,e}function fe(){let e=dl();for(;e!==null&&e.type===64;)e=e.parent;return e}function dl(){return k.lFrame.currentTNode}function Hp(){let e=k.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function Nr(e,n){let t=k.lFrame;t.currentTNode=e,t.isParent=n}function fl(){return k.lFrame.isParent}function hl(){k.lFrame.isParent=!1}function $p(){return k.lFrame.contextLView}function pl(e){ku("Must never be called in production mode"),AC=e}function gl(){return Mu}function ml(e){let n=Mu;return Mu=e,n}function zp(){let e=k.lFrame,n=e.bindingRootIndex;return n===-1&&(n=e.bindingRootIndex=e.tView.bindingStartIndex),n}function Gp(e){return k.lFrame.bindingIndex=e}function Fs(){return k.lFrame.bindingIndex++}function Wp(e){let n=k.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function qp(){return k.lFrame.inI18n}function Zp(e,n){let t=k.lFrame;t.bindingIndex=t.bindingRootIndex=e,Ls(n)}function Yp(){return k.lFrame.currentDirectiveIndex}function Ls(e){k.lFrame.currentDirectiveIndex=e}function Qp(e){let n=k.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function vl(){return k.lFrame.currentQueryIndex}function js(e){k.lFrame.currentQueryIndex=e}function NC(e){let n=e[S];return n.type===2?n.declTNode:n.type===1?e[Se]:null}function yl(e,n,t){if(t&4){let o=n,i=e;for(;o=o.parent,o===null&&!(t&1);)if(o=NC(i),o===null||(i=i[On],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=k.lFrame=Kp();return r.currentTNode=n,r.lView=e,!0}function Vs(e){let n=Kp(),t=e[S];k.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function Kp(){let e=k.lFrame,n=e===null?null:e.child;return n===null?Jp(e):n}function Jp(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function Xp(){let e=k.lFrame;return k.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Dl=Xp;function Bs(){let e=Xp();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function eg(e){return(k.lFrame.contextLView=xp(e,k.lFrame.contextLView))[De]}function jn(){return k.lFrame.selectedIndex}function dn(e){k.lFrame.selectedIndex=e}function El(){let e=k.lFrame;return xs(e.tView,e.selectedIndex)}function tg(){return k.lFrame.currentNamespace}var ng=!0;function Us(){return ng}function jo(e){ng=e}function Au(e,n=null,t=null,r){let o=Cl(e,n,t,r);return o.resolveInjectorInitializers(),o}function Cl(e,n=null,t=null,r,o=new Set){let i=[t||Ve,wp(e)];return r=r||(typeof e=="object"?void 0:kt(e)),new Nn(i,n||Ir(),r||null,o)}var me=class e{static THROW_IF_NOT_FOUND=Sn;static NULL=new Mo;static create(n,t){if(Array.isArray(n))return Au({name:""},t,n,"");{let r=n.name??"";return Au({name:r},n.parent,n.providers,r)}}static \u0275prov=w({token:e,providedIn:"any",factory:()=>b(Wu)});static __NG_ELEMENT_ID__=-1},te=new E(""),ct=(()=>{class e{static __NG_ELEMENT_ID__=RC;static __NG_ENV_ID__=t=>t}return e})(),Nu=class extends ct{_lView;constructor(n){super(),this._lView=n}get destroyed(){return Ln(this._lView)}onDestroy(n){let t=this._lView;return al(t,n),()=>Op(t,n)}};function RC(){return new Nu(j())}var ot=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Pe=new E("",{providedIn:"root",factory:()=>{let e=p(q),n;return t=>{e.destroyed&&!n?setTimeout(()=>{throw t}):(n??=e.get(ot),n.handleError(t))}}}),rg={provide:sn,useValue:()=>void p(ot),multi:!0};function Lt(e,n){let[t,r,o]=yu(e,n?.equal),i=t,s=i[be];return i.set=r,i.update=o,i.asReadonly=og.bind(i),i}function og(){let e=this[be];if(e.readonlyFn===void 0){let n=()=>this();n[be]=e,e.readonlyFn=n}return e.readonlyFn}var Pt=class{},Hs=new E("",{providedIn:"root",factory:()=>!1});var wl=new E(""),Il=new E("");var $s=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=xC}return e})();function xC(){return new $s(j(),fe())}var bt=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new oe(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new O(t=>{t.next(!1),t.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),Vo=(()=>{class e{internalPendingTasks=p(bt);scheduler=p(Pt);errorHandler=p(Pe);add(){let t=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(t)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(t))}}run(t){let r=this.add();t().catch(this.errorHandler).finally(r)}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})();function Bo(...e){}var bl=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new Ru})}return e})(),Ru=class{dirtyEffectCount=0;queues=new Map;add(n){this.enqueue(n),this.schedule(n)}schedule(n){n.dirty&&this.dirtyEffectCount++}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),n.dirty&&this.dirtyEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||r.add(n)}flush(){for(;this.dirtyEffectCount>0;){let n=!1;for(let[t,r]of this.queues)t===null?n||=this.flushQueue(r):n||=t.run(()=>this.flushQueue(r));n||(this.dirtyEffectCount=0)}}flushQueue(n){let t=!1;for(let r of n)r.dirty&&(this.dirtyEffectCount--,t=!0,r.run());return t}};function Lr(e){return{toString:e}.toString()}var zs="__parameters__";function UC(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function Og(e,n,t){return Lr(()=>{let r=UC(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(zs)?c[zs]:Object.defineProperty(c,zs,{value:[]})[zs];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var kg=zu(Og("Optional"),8);var Pg=zu(Og("SkipSelf"),4);function HC(e){return typeof e=="function"}var Ks=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function Fg(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var Ae=(()=>{let e=()=>Lg;return e.ngInherit=!0,e})();function Lg(e){return e.type.prototype.ngOnChanges&&(e.setInput=zC),$C}function $C(){let e=Vg(this),n=e?.current;if(n){let t=e.previous;if(t===on)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function zC(e,n,t,r,o){let i=this.declaredInputs[r],s=Vg(e)||GC(e,{previous:on,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Ks(u&&u.currentValue,t,c===on),Fg(e,n,o,t)}var jg="__ngSimpleChanges__";function Vg(e){return e[jg]||null}function GC(e,n){return e[jg]=n}var ig=[];var U=function(e,n=null,t){for(let r=0;r<ig.length;r++){let o=ig[r];o(e,n,t)}};function WC(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=Lg(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function Bg(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),u&&((e.viewHooks??=[]).push(t,u),(e.viewCheckHooks??=[]).push(t,u)),l!=null&&(e.destroyHooks??=[]).push(t,l)}}function qs(e,n,t){Ug(e,n,3,t)}function Zs(e,n,t,r){(e[M]&3)===t&&Ug(e,n,t,r)}function _l(e,n){let t=e[M];(t&3)===n&&(t&=16383,t+=1,e[M]=t)}function Ug(e,n,t,r){let o=r!==void 0?e[kn]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[kn]+=65536),(a<i||i==-1)&&(qC(e,t,n,c),e[kn]=(e[kn]&**********)+c+2),c++}function sg(e,n){U(4,e,n);let t=x(null);try{n.call(e)}finally{x(t),U(5,e,n)}}function qC(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[M]>>14<e[kn]>>16&&(e[M]&3)===n&&(e[M]+=16384,sg(a,i)):sg(a,i)}var xr=-1,Un=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r,o){this.factory=n,this.name=o,this.canSeeViewProviders=t,this.injectImpl=r}};function ZC(e){return(e.flags&8)!==0}function YC(e){return(e.flags&16)!==0}function QC(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];KC(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function Hg(e){return e===3||e===4||e===6}function KC(e){return e.charCodeAt(0)===64}function Or(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?ag(e,t,o,null,n[++r]):ag(e,t,o,null,null))}}return e}function ag(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function $g(e){return e!==xr}function Js(e){return e&32767}function JC(e){return e>>16}function Xs(e,n){let t=JC(e),r=n;for(;t>0;)r=r[On],t--;return r}var Fl=!0;function ea(e){let n=Fl;return Fl=e,n}var XC=256,zg=XC-1,Gg=5,ew=0,_t={};function tw(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(Rn)&&(r=t[Rn]),r==null&&(r=t[Rn]=ew++);let o=r&zg,i=1<<o;n.data[e+(o>>Gg)]|=i}function ta(e,n){let t=Wg(e,n);if(t!==-1)return t;let r=n[S];r.firstCreatePass&&(e.injectorIndex=n.length,Tl(r.data,e),Tl(n,null),Tl(r.blueprint,null));let o=cd(e,n),i=e.injectorIndex;if($g(o)){let s=Js(o),a=Xs(o,n),c=a[S].data;for(let u=0;u<8;u++)n[i+u]=a[s+u]|c[s+u]}return n[i+8]=o,i}function Tl(e,n){e.push(0,0,0,0,0,0,0,0,n)}function Wg(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function cd(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=Kg(o),r===null)return xr;if(t++,o=o[On],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return xr}function Ll(e,n,t){tw(e,n,t)}function nw(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(Hg(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function qg(e,n,t){if(t&8||e!==void 0)return e;Ms(n,"NodeInjector")}function Zg(e,n,t,r){if(t&8&&r===void 0&&(r=null),(t&3)===0){let o=e[xn],i=_e(void 0);try{return o?o.get(n,r,t&8):$u(n,r,t&8)}finally{_e(i)}}return qg(r,n,t)}function Yg(e,n,t,r=0,o){if(e!==null){if(n[M]&2048&&!(r&2)){let s=sw(e,n,t,r,_t);if(s!==_t)return s}let i=Qg(e,n,t,r,_t);if(i!==_t)return i}return Zg(n,t,r,o)}function Qg(e,n,t,r,o){let i=ow(t);if(typeof i=="function"){if(!yl(n,e,r))return r&1?qg(o,t,r):Zg(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&8))Ms(t);else return s}finally{Dl()}}else if(typeof i=="number"){let s=null,a=Wg(e,n),c=xr,u=r&1?n[ke][Se]:null;for((a===-1||r&4)&&(c=a===-1?cd(e,n):n[a+8],c===xr||!ug(r,!1)?a=-1:(s=n[S],a=Js(c),n=Xs(c,n)));a!==-1;){let l=n[S];if(cg(i,a,l.data)){let d=rw(a,n,t,s,r,u);if(d!==_t)return d}c=n[a+8],c!==xr&&ug(r,n[S].data[a+8]===u)&&cg(i,a,n)?(s=l,a=Js(c),n=Xs(c,n)):a=-1}}return o}function rw(e,n,t,r,o,i){let s=n[S],a=s.data[e+8],c=r==null?un(a)&&Fl:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=Ys(a,s,t,c,u);return l!==null?$o(n,s,l,a,o):_t}function Ys(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let m=s[f];if(f<c&&t===m||f>=c&&m.type===t)return f}if(o){let f=s[c];if(f&&It(f)&&f.type===t)return c}return null}function $o(e,n,t,r,o){let i=e[t],s=n.data;if(i instanceof Un){let a=i;if(a.resolving){let f=bs(s[t]);throw Hu(f)}let c=ea(a.canSeeViewProviders);a.resolving=!0;let u=s[t].type||s[t],l,d=a.injectImpl?_e(a.injectImpl):null,h=yl(e,r,0);try{i=e[t]=a.factory(void 0,o,s,e,r),n.firstCreatePass&&t>=r.directiveStart&&WC(t,s[t],n)}finally{d!==null&&_e(d),ea(c),a.resolving=!1,Dl()}}return i}function ow(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(Rn)?e[Rn]:void 0;return typeof n=="number"?n>=0?n&zg:iw:n}function cg(e,n,t){let r=1<<e;return!!(t[n+(e>>Gg)]&r)}function ug(e,n){return!(e&2)&&!(e&1&&n)}var Bn=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return Yg(this._tNode,this._lView,n,Mn(r),t)}};function iw(){return new Bn(fe(),j())}function Ye(e){return Lr(()=>{let n=e.prototype.constructor,t=n[So]||jl(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[So]||jl(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function jl(e){return Ou(e)?()=>{let n=jl(ge(e));return n&&n()}:nn(e)}function sw(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[M]&2048&&!Mr(s);){let a=Qg(i,s,t,r|2,_t);if(a!==_t)return a;let c=i.parent;if(!c){let u=s[el];if(u){let l=u.get(t,_t,r);if(l!==_t)return l}c=Kg(s),s=s[On]}i=c}return o}function Kg(e){let n=e[S],t=n.type;return t===2?n.declTNode:t===1?e[Se]:null}function pn(e){return nw(fe(),e)}function aw(){return jr(fe(),j())}function jr(e,n){return new Y(at(e,n))}var Y=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=aw}return e})();function cw(e){return e instanceof Y?e.nativeElement:e}function uw(){return this._results[Symbol.iterator]()}var na=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new $}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=vp(n);(this._changesDetected=!mp(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=uw};function Jg(e){return(e.flags&128)===128}var ud=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(ud||{}),Xg=new Map,lw=0;function dw(){return lw++}function fw(e){Xg.set(e[Oo],e)}function Vl(e){Xg.delete(e[Oo])}var lg="__ngContext__";function kr(e,n){wt(n)?(e[lg]=n[Oo],fw(n)):e[lg]=n}function em(e){return nm(e[Tr])}function tm(e){return nm(e[We])}function nm(e){for(;e!==null&&!st(e);)e=e[We];return e}var Bl;function ld(e){Bl=e}function rm(){if(Bl!==void 0)return Bl;if(typeof document<"u")return document;throw new y(210,!1)}var ma=new E("",{providedIn:"root",factory:()=>hw}),hw="ng",va=new E(""),Vr=new E("",{providedIn:"platform",factory:()=>"unknown"});var ya=new E("",{providedIn:"root",factory:()=>rm().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var pw="h",gw="b";var om=!1,im=new E("",{providedIn:"root",factory:()=>om});var mw=(e,n,t,r)=>{};function vw(e,n,t,r){mw(e,n,t,r)}function Da(e){return(e.flags&32)===32}var yw=()=>null;function sm(e,n,t=!1){return yw(e,n,t)}function am(e,n){let t=e.contentQueries;if(t!==null){let r=x(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];js(i),a.contentQueries(2,n[s],s)}}}finally{x(r)}}}function Ul(e,n,t){js(0);let r=x(null);try{n(e,t)}finally{x(r)}}function dd(e,n,t){if(tl(n)){let r=x(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{x(r)}}}var jt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(jt||{});var Gs;function Dw(){if(Gs===void 0&&(Gs=null,Ft.trustedTypes))try{Gs=Ft.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Gs}function dg(e){return Dw()?.createScriptURL(e)||e}var ra=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Ts})`}};function Ea(e){return e instanceof ra?e.changingThisBreaksApplicationSecurity:e}function fd(e,n){let t=cm(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${Ts})`)}return t===n}function cm(e){return e instanceof ra&&e.getTypeName()||null}var Ew=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function um(e){return e=String(e),e.match(Ew)?e:"unsafe:"+e}var Ca=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(Ca||{});function lm(e){let n=fm();return n?n.sanitize(Ca.URL,e)||"":fd(e,"URL")?Ea(e):um(No(e))}function dm(e){let n=fm();if(n)return dg(n.sanitize(Ca.RESOURCE_URL,e)||"");if(fd(e,"ResourceURL"))return dg(Ea(e));throw new y(904,!1)}function Cw(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?dm:lm}function hd(e,n,t){return Cw(n,t)(e)}function fm(){let e=j();return e&&e[Et].sanitizer}var ww=/^>|^->|<!--|-->|--!>|<!-$/g,Iw=/(<|>)/g,bw="\u200B$1\u200B";function _w(e){return e.replace(ww,n=>n.replace(Iw,bw))}function hm(e){return e instanceof Function?e():e}function Tw(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var pm="ng-template";function Sw(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&Tw(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(pd(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function pd(e){return e.type===4&&e.value!==pm}function Mw(e,n,t){let r=e.type===4&&!t?pm:e.value;return n===r}function Aw(e,n,t){let r=4,o=e.attrs,i=o!==null?xw(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!ut(r)&&!ut(c))return!1;if(s&&ut(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!Mw(e,c,t)||c===""&&n.length===1){if(ut(r))return!1;s=!0}}else if(r&8){if(o===null||!Sw(e,o,c,t)){if(ut(r))return!1;s=!0}}else{let u=n[++a],l=Nw(c,o,pd(e),t);if(l===-1){if(ut(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(ut(r))return!1;s=!0}}}}return ut(r)||s}function ut(e){return(e&1)===0}function Nw(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return Ow(n,e)}function gm(e,n,t=!1){for(let r=0;r<n.length;r++)if(Aw(e,n[r],t))return!0;return!1}function Rw(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function xw(e){for(let n=0;n<e.length;n++){let t=e[n];if(Hg(t))return n}return e.length}function Ow(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function kw(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function fg(e,n){return e?":not("+n.trim()+")":n}function Pw(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!ut(s)&&(n+=fg(i,o),o=""),r=s,i=i||!ut(r);t++}return o!==""&&(n+=fg(i,o)),n}function Fw(e){return e.map(Pw).join(",")}function Lw(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!ut(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var Vt={};function jw(e,n){return e.createText(n)}function Vw(e,n,t){e.setValue(n,t)}function Bw(e,n){return e.createComment(_w(n))}function mm(e,n,t){return e.createElement(n,t)}function oa(e,n,t,r,o){e.insertBefore(n,t,r,o)}function vm(e,n,t){e.appendChild(n,t)}function hg(e,n,t,r,o){r!==null?oa(e,n,t,r,o):vm(e,n,t)}function Uw(e,n,t){e.removeChild(null,n,t)}function Hw(e,n,t){e.setAttribute(n,"style",t)}function $w(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function ym(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&QC(e,n,r),o!==null&&$w(e,n,o),i!==null&&Hw(e,n,i)}function gd(e,n,t,r,o,i,s,a,c,u,l){let d=ee+r,h=d+o,f=zw(d,h),m=typeof u=="function"?u():u;return f[S]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:m,incompleteFirstPass:!1,ssrId:l}}function zw(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:Vt);return t}function Gw(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=gd(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function md(e,n,t,r,o,i,s,a,c,u,l){let d=n.blueprint.slice();return d[it]=o,d[M]=r|4|128|8|64|1024,(u!==null||e&&e[M]&2048)&&(d[M]|=2048),il(d),d[de]=d[On]=e,d[De]=t,d[Et]=s||e&&e[Et],d[X]=a||e&&e[X],d[xn]=c||e&&e[xn]||null,d[Se]=i,d[Oo]=dw(),d[br]=l,d[el]=u,d[ke]=n.type==2?e[ke]:d,d}function Ww(e,n,t){let r=at(n,e),o=Gw(t),i=e[Et].rendererFactory,s=vd(e,md(e,o,null,Dm(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function Dm(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function Em(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function vd(e,n){return e[Tr]?e[Xu][We]=n:e[Tr]=n,e[Xu]=n,n}function qw(e=1){Cm(ye(),j(),jn()+e,!1)}function Cm(e,n,t,r){if(!r)if((n[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&qs(n,i,t)}else{let i=e.preOrderHooks;i!==null&&Zs(n,i,0,t)}dn(t)}var wa=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(wa||{});function Hl(e,n,t,r){let o=x(null);try{let[i,s,a]=e.inputs[t],c=null;(s&wa.SignalBased)!==0&&(c=n[i][be]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):Fg(n,c,i,r)}finally{x(o)}}var Tt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Tt||{}),Zw;function yd(e,n){return Zw(e,n)}function Rr(e,n,t,r,o){if(r!=null){let i,s=!1;st(r)?i=r:wt(r)&&(s=!0,r=r[it]);let a=qe(r);e===0&&t!==null?o==null?vm(n,t,a):oa(n,t,a,o||null,!0):e===1&&t!==null?oa(n,t,a,o||null,!0):e===2?Uw(n,a,s):e===3&&n.destroyNode(a),i!=null&&oI(n,e,i,t,o)}}function Yw(e,n){wm(e,n),n[it]=null,n[Se]=null}function Qw(e,n,t,r,o,i){r[it]=o,r[Se]=n,Ia(e,r,t,1,o,i)}function wm(e,n){n[Et].changeDetectionScheduler?.notify(9),Ia(e,n,n[X],2,null,null)}function Kw(e){let n=e[Tr];if(!n)return Sl(e[S],e);for(;n;){let t=null;if(wt(n))t=n[Tr];else{let r=n[Me];r&&(t=r)}if(!t){for(;n&&!n[We]&&n!==e;)wt(n)&&Sl(n[S],n),n=n[de];n===null&&(n=e),wt(n)&&Sl(n[S],n),t=n&&n[We]}n=t}}function Dd(e,n){let t=e[Fn],r=t.indexOf(n);t.splice(r,1)}function Im(e,n){if(Ln(n))return;let t=n[X];t.destroyNode&&Ia(e,n,t,3,null,null),Kw(n)}function Sl(e,n){if(Ln(n))return;let t=x(null);try{n[M]&=-129,n[M]|=256,n[Ue]&&ms(n[Ue]),Xw(e,n),Jw(e,n),n[S].type===1&&n[X].destroy();let r=n[an];if(r!==null&&st(n[de])){r!==n[de]&&Dd(r,n);let o=n[Ct];o!==null&&o.detachView(e)}Vl(n)}finally{x(t)}}function Jw(e,n){let t=e.cleanup,r=n[_r];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[_r]=null);let o=n[Ot];if(o!==null){n[Ot]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[ko];if(i!==null){n[ko]=null;for(let s of i)s.destroy()}}function Xw(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof Un)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];U(4,a,c);try{c.call(a)}finally{U(5,a,c)}}else{U(4,o,i);try{i.call(o)}finally{U(5,o,i)}}}}}function bm(e,n,t){return eI(e,n.parent,t)}function eI(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[it];if(un(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===jt.None||o===jt.Emulated)return null}return at(r,t)}function _m(e,n,t){return nI(e,n,t)}function tI(e,n,t){return e.type&40?at(e,t):null}var nI=tI,pg;function Ed(e,n,t,r){let o=bm(e,r,n),i=n[X],s=r.parent||n[Se],a=_m(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)hg(i,o,t[c],a,!1);else hg(i,o,t,a,!1);pg!==void 0&&pg(i,r,n,t,o)}function Uo(e,n){if(n!==null){let t=n.type;if(t&3)return at(n,e);if(t&4)return $l(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return Uo(e,r);{let o=e[n.index];return st(o)?$l(-1,o):qe(o)}}else{if(t&128)return Uo(e,n.next);if(t&32)return yd(n,e)()||qe(e[n.index]);{let r=Tm(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=rn(e[ke]);return Uo(o,r)}else return Uo(e,n.next)}}}return null}function Tm(e,n){if(n!==null){let r=e[ke][Se],o=n.projection;return r.projection[o]}return null}function $l(e,n){let t=Me+e+1;if(t<n.length){let r=n[t],o=r[S].firstChild;if(o!==null)return Uo(r,o)}return n[cn]}function Cd(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&kr(qe(a),r),t.flags|=2),!Da(t))if(c&8)Cd(e,n,t.child,r,o,i,!1),Rr(n,e,o,a,i);else if(c&32){let u=yd(t,r),l;for(;l=u();)Rr(n,e,o,l,i);Rr(n,e,o,a,i)}else c&16?Sm(e,n,r,t,o,i):Rr(n,e,o,a,i);t=s?t.projectionNext:t.next}}function Ia(e,n,t,r,o,i){Cd(t,r,e.firstChild,n,o,i,!1)}function rI(e,n,t){let r=n[X],o=bm(e,t,n),i=t.parent||n[Se],s=_m(i,t,n);Sm(r,0,n,t,o,s)}function Sm(e,n,t,r,o,i){let s=t[ke],c=s[Se].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];Rr(n,e,o,l,i)}else{let u=c,l=s[de];Jg(r)&&(u.flags|=128),Cd(e,n,u,l,o,i,!0)}}function oI(e,n,t,r,o){let i=t[cn],s=qe(t);i!==s&&Rr(n,e,r,i,o);for(let a=Me;a<t.length;a++){let c=t[a];Ia(c[S],c,e,n,r,i)}}function iI(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:Tt.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Tt.Important),e.setStyle(t,r,o,i))}}function Mm(e,n,t,r,o){let i=jn(),s=r&2;try{dn(-1),s&&n.length>ee&&Cm(e,n,ee,!1),U(s?2:0,o,t),t(r,o)}finally{dn(i),U(s?3:1,o,t)}}function ba(e,n,t){hI(e,n,t),(t.flags&64)===64&&pI(e,n,t)}function qo(e,n,t=at){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function sI(e,n,t,r){let i=r.get(im,om)||t===jt.ShadowDom,s=e.selectRootElement(n,i);return aI(s),s}function aI(e){cI(e)}var cI=()=>null;function uI(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function lI(e,n,t,r,o,i){let s=n[S];if(_d(e,s,n,t,r)){un(e)&&fI(n,e.index);return}e.type&3&&(t=uI(t)),dI(e,n,t,r,o,i)}function dI(e,n,t,r,o,i){if(e.type&3){let s=at(e,n);r=i!=null?i(r,e.value||"",t):r,o.setProperty(s,t,r)}else e.type&12}function fI(e,n){let t=Ze(n,e);t[M]&16||(t[M]|=64)}function hI(e,n,t){let r=t.directiveStart,o=t.directiveEnd;un(t)&&Ww(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||ta(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=$o(n,e,s,t);if(kr(c,n),i!==null&&yI(n,s-r,c,a,t,i),It(a)){let u=Ze(t.index,n);u[De]=$o(n,e,s,t)}}}function pI(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=Yp();try{dn(i);for(let a=r;a<o;a++){let c=e.data[a],u=n[a];Ls(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&gI(c,u)}}finally{dn(-1),Ls(s)}}function gI(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function wd(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];gm(n,i.selectors,!1)&&(r??=[],It(i)?r.unshift(i):r.push(i))}return r}function mI(e,n,t,r,o,i){let s=at(e,n);vI(n[X],s,i,e.value,t,r,o)}function vI(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?No(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function yI(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];Hl(r,t,c,u)}}function Id(e,n,t,r,o){let i=ee+t,s=n[S],a=o(s,n,e,r,t);n[i]=a,Nr(e,!0);let c=e.type===2;return c?(ym(n[X],a,e),(Pp()===0||Sr(e))&&kr(a,n),Fp()):kr(a,n),Us()&&(!c||!Da(e))&&Ed(s,n,a,e),e}function bd(e){let n=e;return fl()?hl():(n=n.parent,Nr(n,!1)),n}function DI(e,n){let t=e[xn];if(!t)return;t.get(Pe,null)?.(n)}function _d(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=n.data[u];Hl(d,t[u],l,o),a=!0}if(i)for(let c of i){let u=t[c],l=n.data[c];Hl(l,u,r,o),a=!0}return a}function EI(e,n){let t=Ze(n,e),r=t[S];CI(r,t);let o=t[it];o!==null&&t[br]===null&&(t[br]=sm(o,t[xn])),U(18),Td(r,t,t[De]),U(19,t[De])}function CI(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function Td(e,n,t){Vs(n);try{let r=e.viewQuery;r!==null&&Ul(1,r,t);let o=e.template;o!==null&&Mm(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[Ct]?.finishViewCreation(e),e.staticContentQueries&&am(e,n),e.staticViewQueries&&Ul(2,e.viewQuery,t);let i=e.components;i!==null&&wI(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[M]&=-5,Bs()}}function wI(e,n){for(let t=0;t<n.length;t++)EI(e,n[t])}function Am(e,n,t,r){let o=x(null);try{let i=n.tView,a=e[M]&4096?4096:16,c=md(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[n.index];c[an]=u;let l=e[Ct];return l!==null&&(c[Ct]=l.createEmbeddedView(i)),Td(i,c,t),c}finally{x(o)}}function zl(e,n){return!n||n.firstChild===null||Jg(e)}var gg=!1,II=new E("");function zo(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(qe(i)),st(i)&&Nm(i,r);let s=t.type;if(s&8)zo(e,n,t.child,r);else if(s&32){let a=yd(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=Tm(n,t);if(Array.isArray(a))r.push(...a);else{let c=rn(n[ke]);zo(c[S],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function Nm(e,n){for(let t=Me;t<e.length;t++){let r=e[t],o=r[S].firstChild;o!==null&&zo(r[S],r,o,n)}e[cn]!==e[it]&&n.push(e[cn])}function Rm(e){if(e[Pn]!==null){for(let n of e[Pn])n.impl.addSequence(n);e[Pn].length=0}}var xm=[];function bI(e){return e[Ue]??_I(e)}function _I(e){let n=xm.pop()??Object.create(SI);return n.lView=e,n}function TI(e){e.lView[Ue]!==e&&(e.lView=null,xm.push(e))}var SI=N(g({},_n),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{Ar(e.lView)},consumerOnSignalRead(){this.lView[Ue]=this}});function MI(e){let n=e[Ue]??Object.create(AI);return n.lView=e,n}var AI=N(g({},_n),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=rn(e.lView);for(;n&&!Om(n[S]);)n=rn(n);n&&sl(n)},consumerOnSignalRead(){this.lView[Ue]=this}});function Om(e){return e.type!==2}function km(e){if(e[ko]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[ko])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[M]&8192)}}var NI=100;function Sd(e,n=0){let r=e[Et].rendererFactory,o=!1;o||r.begin?.();try{RI(e,n)}finally{o||r.end?.()}}function RI(e,n){let t=gl();try{ml(!0),Gl(e,n);let r=0;for(;Fo(e);){if(r===NI)throw new y(103,!1);r++,Gl(e,1)}}finally{ml(t)}}function Pm(e,n){pl(n?Lo.Exhaustive:Lo.OnlyDirtyViews);try{Sd(e)}finally{pl(Lo.Off)}}function xI(e,n,t,r){if(Ln(n))return;let o=n[M],i=!1,s=!1;Vs(n);let a=!0,c=null,u=null;i||(Om(e)?(u=bI(n),c=Tn(u)):ps()===null?(a=!1,u=MI(n),c=Tn(u)):n[Ue]&&(ms(n[Ue]),n[Ue]=null));try{il(n),Gp(e.bindingStartIndex),t!==null&&Mm(e,n,t,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&qs(n,f,null)}else{let f=e.preOrderHooks;f!==null&&Zs(n,f,0,null),_l(n,0)}if(s||OI(n),km(n),Fm(n,0),e.contentQueries!==null&&am(e,n),!i)if(l){let f=e.contentCheckHooks;f!==null&&qs(n,f)}else{let f=e.contentHooks;f!==null&&Zs(n,f,1),_l(n,1)}PI(e,n);let d=e.components;d!==null&&jm(n,d,0);let h=e.viewQuery;if(h!==null&&Ul(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&qs(n,f)}else{let f=e.viewHooks;f!==null&&Zs(n,f,2),_l(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[Rs]){for(let f of n[Rs])f();n[Rs]=null}i||(Rm(n),n[M]&=-73)}catch(l){throw i||Ar(n),l}finally{u!==null&&(vr(u,c),a&&TI(u)),Bs()}}function Fm(e,n){for(let t=em(e);t!==null;t=tm(t))for(let r=Me;r<t.length;r++){let o=t[r];Lm(o,n)}}function OI(e){for(let n=em(e);n!==null;n=tm(n)){if(!(n[M]&2))continue;let t=n[Fn];for(let r=0;r<t.length;r++){let o=t[r];sl(o)}}}function kI(e,n,t){U(18);let r=Ze(n,e);Lm(r,t),U(19,r[De])}function Lm(e,n){Os(e)&&Gl(e,n)}function Gl(e,n){let r=e[S],o=e[M],i=e[Ue],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&bo(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)xI(r,e,r.template,e[De]);else if(o&8192){let a=x(null);try{km(e),Fm(e,1);let c=r.components;c!==null&&jm(e,c,1),Rm(e)}finally{x(a)}}}function jm(e,n,t){for(let r=0;r<n.length;r++)kI(e,n[r],t)}function PI(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)dn(~o);else{let i=o,s=t[++r],a=t[++r];Zp(s,i);let c=n[i];U(24,c),a(2,c),U(25,c)}}}finally{dn(-1)}}function Md(e,n){let t=gl()?64:1088;for(e[Et].changeDetectionScheduler?.notify(n);e;){e[M]|=t;let r=rn(e);if(Mr(e)&&!r)return e;e=r}return null}function Vm(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function Bm(e,n,t,r=!0){let o=n[S];if(FI(o,n,e,t),r){let s=$l(t,e),a=n[X],c=a.parentNode(e[cn]);c!==null&&Qw(o,e[Se],a,n,c,s)}let i=n[br];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function Wl(e,n){if(e.length<=Me)return;let t=Me+n,r=e[t];if(r){let o=r[an];o!==null&&o!==e&&Dd(o,r),n>0&&(e[t-1][We]=r[We]);let i=Ro(e,Me+n);Yw(r[S],r);let s=i[Ct];s!==null&&s.detachView(i[S]),r[de]=null,r[We]=null,r[M]&=-129}return r}function FI(e,n,t,r){let o=Me+r,i=t.length;r>0&&(t[o-1][We]=n),r<i-Me?(n[We]=t[o],Gu(t,Me+r,n)):(t.push(n),n[We]=null),n[de]=t;let s=n[an];s!==null&&t!==s&&Um(s,n);let a=n[Ct];a!==null&&a.insertView(e),ks(n),n[M]|=128}function Um(e,n){let t=e[Fn],r=n[de];if(wt(r))e[M]|=2;else{let o=r[de][ke];n[ke]!==o&&(e[M]|=2)}t===null?e[Fn]=[n]:t.push(n)}var fn=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let n=this._lView,t=n[S];return zo(t,n,t.firstChild,[])}constructor(n,t){this._lView=n,this._cdRefInjectingView=t}get context(){return this._lView[De]}set context(n){this._lView[De]=n}get destroyed(){return Ln(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[de];if(st(n)){let t=n[Po],r=t?t.indexOf(this):-1;r>-1&&(Wl(n,r),Ro(t,r))}this._attachedToViewContainer=!1}Im(this._lView[S],this._lView)}onDestroy(n){al(this._lView,n)}markForCheck(){Md(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){ks(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,Sd(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[xn].get(II,gg)}catch{this.exhaustive=gg}}attachToViewContainerRef(){if(this._appRef)throw new y(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=Mr(this._lView),t=this._lView[an];t!==null&&!n&&Dd(t,this._lView),wm(this._lView[S],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new y(902,!1);this._appRef=n;let t=Mr(this._lView),r=this._lView[an];r!==null&&!t&&Um(r,this._lView),ks(this._lView)}};var lt=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=LI;constructor(t,r,o){this._declarationLView=t,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,r){return this.createEmbeddedViewImpl(t,r)}createEmbeddedViewImpl(t,r,o){let i=Am(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:r,dehydratedView:o});return new fn(i)}}return e})();function LI(){return Ad(fe(),j())}function Ad(e,n){return e.type&4?new lt(n,e,jr(e,n)):null}function Br(e,n,t,r,o){let i=e.data[n];if(i===null)i=jI(e,n,t,r,o),qp()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=Hp();i.injectorIndex=s===null?-1:s.injectorIndex}return Nr(i,!0),i}function jI(e,n,t,r,o){let i=dl(),s=fl(),a=s?i:i&&i.parent,c=e.data[n]=BI(e,a,t,n,r,o);return VI(e,c,i,s),c}function VI(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function BI(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return ll()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var kL=new RegExp(`^(\\d+)*(${gw}|${pw})*(.*)`);var UI=()=>null;function ql(e,n){return UI(e,n)}var Hm=class{},_a=class{},Zl=class{resolveComponentFactory(n){throw new y(917,!1)}},Zo=class{static NULL=new Zl},Hn=class{},Bt=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>HI()}return e})();function HI(){let e=j(),n=fe(),t=Ze(n.index,e);return(wt(t)?t:e)[X]}var $m=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>null})}return e})();var Qs={},Yl=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){let o=this.injector.get(n,Qs,r);return o!==Qs||t===Qs?o:this.parentInjector.get(n,t,r)}};function ia(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=xu(o,a);else if(i==2){let c=a,u=n[++s];r=xu(r,c+": "+u+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function v(e,n=0){let t=j();if(t===null)return b(e,n);let r=fe();return Yg(r,t,ge(e),n)}function zm(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}GI(e,n,t,a,i,c,u)}i!==null&&r!==null&&$I(t,r,i)}function $I(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new y(-301,!1);r.push(n[o],i)}}function zI(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function GI(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&It(f)&&(c=!0,zI(e,t,h)),Ll(ta(t,n),e,f.type)}KI(t,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=Em(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(t.mergedAttrs=Or(t.mergedAttrs,f.hostAttrs),qI(e,t,n,d,f),QI(d,f,o),s!==null&&s.has(f)){let[I,D]=s.get(f);t.directiveToIndex.set(f.type,[d,I+t.directiveStart,D+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let m=f.type.prototype;!u&&(m.ngOnChanges||m.ngOnInit||m.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),u=!0),!l&&(m.ngOnChanges||m.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),l=!0),d++}WI(e,t,i)}function WI(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))mg(0,n,o,r),mg(1,n,o,r),yg(n,r,!1);else{let i=t.get(o);vg(0,n,i,r),vg(1,n,i,r),yg(n,r,!0)}}}function mg(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),Gm(n,i)}}function vg(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),Gm(n,s)}}function Gm(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function yg(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||pd(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===n){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function qI(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=nn(o.type,!0)),s=new Un(i,It(o),v,null);e.blueprint[r]=s,t[r]=s,ZI(e,n,r,Em(e,t,o.hostVars,Vt),o)}function ZI(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;YI(s)!=a&&s.push(a),s.push(t,r,i)}}function YI(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function QI(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;It(n)&&(t[""]=e)}}function KI(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function Nd(e,n,t,r,o,i,s,a){let c=n[S],u=c.consts,l=ln(u,s),d=Br(c,e,t,r,l);return i&&zm(c,n,d,ln(u,a),o),d.mergedAttrs=Or(d.mergedAttrs,d.attrs),d.attrs!==null&&ia(d,d.attrs,!1),d.mergedAttrs!==null&&ia(d,d.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,d),d}function Rd(e,n){Bg(e,n),tl(n)&&e.queries.elementEnd(n)}function JI(e,n,t,r,o,i){let s=n.consts,a=ln(s,o),c=Br(n,e,t,r,a);if(c.mergedAttrs=Or(c.mergedAttrs,c.attrs),i!=null){let u=ln(s,i);c.localNames=[];for(let l=0;l<u.length;l+=2)c.localNames.push(u[l],-1)}return c.attrs!==null&&ia(c,c.attrs,!1),c.mergedAttrs!==null&&ia(c,c.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,c),c}function xd(e){return qm(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function Wm(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function qm(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function XI(e,n,t){return e[n]=t}function Pr(e,n,t){if(t===Vt)return!1;let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function eb(e,n,t,r){let o=Pr(e,n,t);return Pr(e,n+1,r)||o}function Ml(e,n,t){return function r(o){let i=un(e)?Ze(e.index,n):n;Md(i,5);let s=n[De],a=Dg(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=Dg(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Dg(e,n,t,r){let o=x(null);try{return U(6,n,t),t(r)!==!1}catch(i){return DI(e,i),!1}finally{U(7,n,t),x(o)}}function tb(e,n,t,r,o,i,s,a){let c=Sr(e),u=!1,l=null;if(!r&&c&&(l=nb(n,t,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=at(e,t),h=r?r(d):d;vw(t,h,i,a);let f=o.listen(h,i,a),m=r?I=>r(qe(I[e.index])):e.index;Zm(m,n,t,i,a,f,!1)}return u}function nb(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[_r],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function Zm(e,n,t,r,o,i,s){let a=n.firstCreatePass?ul(n):null,c=cl(t),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function Eg(e,n,t,r,o,i){let s=n[t],a=n[S],u=a.data[t].outputs[r],d=s[u].subscribe(i);Zm(e.index,a,n,o,i,d,!0)}var Ql=Symbol("BINDING");var sa=class extends Zo{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=Dt(n);return new hn(t,this.ngModule)}};function rb(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&wa.SignalBased)!==0};return o&&(i.transform=o),i})}function ob(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function ib(e,n,t){let r=n instanceof q?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Yl(t,r):t}function sb(e){let n=e.get(Hn,null);if(n===null)throw new y(407,!1);let t=e.get($m,null),r=e.get(Pt,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r,ngReflect:!1}}function ab(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return mm(n,t,t==="svg"?Mp:t==="math"?Ap:null)}var hn=class extends _a{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=rb(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=ob(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=Fw(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o,i,s){U(22);let a=x(null);try{let c=this.componentDef,u=cb(r,c,s,i),l=ib(c,o||this.ngModule,n),d=sb(l),h=d.rendererFactory.createRenderer(null,c),f=r?sI(h,r,c.encapsulation,l):ab(c,h),m=s?.some(Cg)||i?.some(C=>typeof C!="function"&&C.bindings.some(Cg)),I=md(null,u,null,512|Dm(c),null,null,d,h,l,null,sm(f,l,!0));I[ee]=f,Vs(I);let D=null;try{let C=Nd(ee,I,2,"#host",()=>u.directiveRegistry,!0,0);f&&(ym(h,f,C),kr(f,I)),ba(u,I,C),dd(u,C,I),Rd(u,C),t!==void 0&&lb(C,this.ngContentSelectors,t),D=Ze(C.index,I),I[De]=D[De],Td(u,I,null)}catch(C){throw D!==null&&Vl(D),Vl(I),C}finally{U(23),Bs()}return new aa(this.componentType,I,!!m)}finally{x(a)}}};function cb(e,n,t,r){let o=e?["ng-version","20.1.3"]:Lw(n.selectors[0]),i=null,s=null,a=0;if(t)for(let l of t)a+=l[Ql].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[Ql].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[n];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=Yu(d);c.push(h)}return gd(0,null,ub(i,s),1,a,c,null,null,null,[o],null)}function ub(e,n){return!e&&!n?null:t=>{if(t&1&&e)for(let r of e)r.create();if(t&2&&n)for(let r of n)r.update()}}function Cg(e){let n=e[Ql].kind;return n==="input"||n==="twoWay"}var aa=class extends Hm{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t,r){super(),this._rootLView=t,this._hasInputBindings=r,this._tNode=xs(t[S],ee),this.location=jr(this._tNode,t),this.instance=Ze(this._tNode.index,t)[De],this.hostView=this.changeDetectorRef=new fn(t,void 0),this.componentType=n}setInput(n,t){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=_d(r,o[S],o,n,t);this.previousInputValues.set(n,t);let s=Ze(r.index,o);Md(s,1)}get injector(){return new Bn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function lb(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var Qe=(()=>{class e{static __NG_ELEMENT_ID__=db}return e})();function db(){let e=fe();return Qm(e,j())}var fb=Qe,Ym=class extends fb{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return jr(this._hostTNode,this._hostLView)}get injector(){return new Bn(this._hostTNode,this._hostLView)}get parentInjector(){let n=cd(this._hostTNode,this._hostLView);if($g(n)){let t=Xs(n,this._hostLView),r=Js(n),o=t[S].data[r+8];return new Bn(o,t)}else return new Bn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=wg(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-Me}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=ql(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,zl(this._hostTNode,s)),a}createComponent(n,t,r,o,i,s,a){let c=n&&!HC(n),u;if(c)u=t;else{let D=t||{};u=D.index,r=D.injector,o=D.projectableNodes,i=D.environmentInjector||D.ngModuleRef,s=D.directives,a=D.bindings}let l=c?n:new hn(Dt(n)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let C=(c?d:this.parentInjector).get(q,null);C&&(i=C)}let h=Dt(l.componentType??{}),f=ql(this._lContainer,h?.id??null),m=f?.firstChild??null,I=l.create(d,o,m,i,s,a);return this.insertImpl(I.hostView,u,zl(this._hostTNode,f)),I}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(Rp(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[de],u=new Ym(c,c[Se],c[de]);u.detach(u.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return Bm(s,o,i,r),n.attachToViewContainerRef(),Gu(Al(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=wg(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=Wl(this._lContainer,t);r&&(Ro(Al(this._lContainer),t),Im(r[S],r))}detach(n){let t=this._adjustIndex(n,-1),r=Wl(this._lContainer,t);return r&&Ro(Al(this._lContainer),t)!=null?new fn(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function wg(e){return e[Po]}function Al(e){return e[Po]||(e[Po]=[])}function Qm(e,n){let t,r=n[e.index];return st(r)?t=r:(t=Vm(r,n,null,e),n[e.index]=t,vd(n,t)),pb(t,n,e,r),new Ym(t,e,n)}function hb(e,n){let t=e[X],r=t.createComment(""),o=at(n,e),i=t.parentNode(o);return oa(t,i,r,t.nextSibling(o),!1),r}var pb=vb,gb=()=>!1;function mb(e,n,t){return gb(e,n,t)}function vb(e,n,t,r){if(e[cn])return;let o;t.type&8?o=qe(r):o=hb(n,t),e[cn]=o}var Kl=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},Jl=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)Od(n,t).matches!==null&&this.queries[t].setDirty()}},ca=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=_b(n):this.predicate=n}},Xl=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},ed=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,yb(t,i)),this.matchTNodeWithReadOption(n,t,Ys(t,n,i,!1,!1))}else r===lt?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,Ys(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===Y||o===Qe||o===lt&&t.type&4)this.addMatch(t.index,-2);else{let i=Ys(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function yb(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function Db(e,n){return e.type&11?jr(e,n):e.type&4?Ad(e,n):null}function Eb(e,n,t,r){return t===-1?Db(n,e):t===-2?Cb(e,n,r):$o(e,e[S],t,n)}function Cb(e,n,t){if(t===Y)return jr(n,e);if(t===lt)return Ad(n,e);if(t===Qe)return Qm(n,e)}function Km(e,n,t,r){let o=n[Ct].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(Eb(n,l,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function td(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=Km(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=n[-c];for(let d=Me;d<l.length;d++){let h=l[d];h[an]===h[de]&&td(h[S],h,u,r)}if(l[Fn]!==null){let d=l[Fn];for(let h=0;h<d.length;h++){let f=d[h];td(f[S],f,u,r)}}}}}return r}function wb(e,n){return e[Ct].queries[n].queryList}function Jm(e,n,t){let r=new na((t&4)===4);return kp(e,n,r,r.destroy),(n[Ct]??=new Jl).queries.push(new Kl(r))-1}function Ib(e,n,t){let r=ye();return r.firstCreatePass&&(Xm(r,new ca(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),Jm(r,j(),n)}function bb(e,n,t,r){let o=ye();if(o.firstCreatePass){let i=fe();Xm(o,new ca(n,t,r),i.index),Tb(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return Jm(o,j(),t)}function _b(e){return e.split(",").map(n=>n.trim())}function Xm(e,n,t){e.queries===null&&(e.queries=new Xl),e.queries.track(new ed(n,t))}function Tb(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function Od(e,n){return e.queries.getByIndex(n)}function Sb(e,n){let t=e[S],r=Od(t,n);return r.crossesNgTemplate?td(t,e,n,[]):Km(t,e,r,n)}var Ig=new Set;function Ta(e){Ig.has(e)||(Ig.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var $n=class{},Sa=class{};var ua=class extends $n{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new sa(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=Zu(n);this._bootstrapComponents=hm(i.bootstrap),this._r3Injector=Cl(n,t,[{provide:$n,useValue:this},{provide:Zo,useValue:this.componentFactoryResolver},...r],kt(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},la=class extends Sa{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new ua(this.moduleType,n,[])}};var Go=class extends $n{injector;componentFactoryResolver=new sa(this);instance=null;constructor(n){super();let t=new Nn([...n.providers,{provide:$n,useValue:this},{provide:Zo,useValue:this.componentFactoryResolver}],n.parent||Ir(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function Ur(e,n,t=null){return new Go({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var Mb=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=Qu(!1,t.type),o=r.length>0?Ur([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=w({token:e,providedIn:"environment",factory:()=>new e(b(q))})}return e})();function kd(e){return Lr(()=>{let n=ev(e),t=N(g({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===ud.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(Mb).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||jt.Emulated,styles:e.styles||Ve,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&Ta("NgStandalone"),tv(t);let r=e.dependencies;return t.directiveDefs=bg(r,Ab),t.pipeDefs=bg(r,Cp),t.id=xb(t),t})}function Ab(e){return Dt(e)||Yu(e)}function Ke(e){return Lr(()=>({type:e.type,bootstrap:e.bootstrap||Ve,declarations:e.declarations||Ve,imports:e.imports||Ve,exports:e.exports||Ve,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Nb(e,n){if(e==null)return on;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=wa.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function Rb(e){if(e==null)return on;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function F(e){return Lr(()=>{let n=ev(e);return tv(n),n})}function Pd(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone??!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function ev(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||on,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Ve,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:Nb(e.inputs,n),outputs:Rb(e.outputs),debugInfo:null}}function tv(e){e.features?.forEach(n=>n(e))}function bg(e,n){return e?()=>{let t=typeof e=="function"?e():e,r=[];for(let o of t){let i=n(o);i!==null&&r.push(i)}return r}:null}function xb(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function Ob(e){return Object.getPrototypeOf(e.prototype).constructor}function Ee(e){let n=Ob(e.type),t=!0,r=[e];for(;n;){let o;if(It(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new y(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=Nl(e.inputs),s.declaredInputs=Nl(e.declaredInputs),s.outputs=Nl(e.outputs);let a=o.hostBindings;a&&jb(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&Fb(e,c),u&&Lb(e,u),kb(e,o),dp(e.outputs,o.outputs),It(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Ee&&(t=!1)}}n=Object.getPrototypeOf(n)}Pb(r)}function kb(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function Pb(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=Or(o.hostAttrs,t=Or(t,o.hostAttrs))}}function Nl(e){return e===on?{}:e===Ve?[]:e}function Fb(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function Lb(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function jb(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function nv(e,n,t,r,o,i,s,a){if(t.firstCreatePass){e.mergedAttrs=Or(e.mergedAttrs,e.attrs);let l=e.tView=gd(2,e,o,i,s,t.directiveRegistry,t.pipeRegistry,null,t.schemas,t.consts,null);t.queries!==null&&(t.queries.template(t,e),l.queries=t.queries.embeddedTView(e))}a&&(e.flags|=a),Nr(e,!1);let c=Ub(t,n,e,r);Us()&&Ed(t,n,c,e),kr(c,n);let u=Vm(c,n,c,e);n[r+ee]=u,vd(n,u),mb(u,e,n)}function Vb(e,n,t,r,o,i,s,a,c,u,l){let d=t+ee,h;return n.firstCreatePass?(h=Br(n,d,4,s||null,a||null),Ps()&&zm(n,e,h,ln(n.consts,u),wd),Bg(n,h)):h=n.data[d],nv(h,e,n,t,r,o,i,c),Sr(h)&&ba(n,e,h),u!=null&&qo(e,h,l),h}function Bb(e,n,t,r,o,i,s,a,c,u,l){let d=t+ee,h;if(n.firstCreatePass){if(h=Br(n,d,4,s||null,a||null),u!=null){let f=ln(n.consts,u);h.localNames=[];for(let m=0;m<f.length;m+=2)h.localNames.push(f[m],-1)}}else h=n.data[d];return nv(h,e,n,t,r,o,i,c),u!=null&&qo(e,h,l),h}function rv(e,n,t,r,o,i,s,a){let c=j(),u=ye(),l=ln(u.consts,i);return Vb(c,u,e,n,t,r,o,l,void 0,s,a),rv}var Ub=Hb;function Hb(e,n,t,r){return jo(!0),n[X].createComment("")}var Ma=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(Ma||{}),Gn=new E(""),ov=!1,nd=class extends ${__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,Tp()&&(this.destroyRef=p(ct,{optional:!0})??void 0,this.pendingTasks=p(bt,{optional:!0})??void 0)}emit(n){let t=x(null);try{super.next(n)}finally{x(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof re&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},z=nd;function iv(e){let n,t;function r(){e=Bo;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function _g(e){return queueMicrotask(()=>e()),()=>{e=Bo}}var Fd="isAngularZone",da=Fd+"_ID",$b=0,H=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new z(!1);onMicrotaskEmpty=new z(!1);onStable=new z(!1);onError=new z(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=ov}=n;if(typeof Zone>"u")throw new y(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,Wb(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(Fd)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new y(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new y(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,zb,Bo,Bo);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},zb={};function Ld(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Gb(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){iv(()=>{e.callbackScheduled=!1,rd(e),e.isCheckStableRunning=!0,Ld(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),rd(e)}function Wb(e){let n=()=>{Gb(e)},t=$b++;e._inner=e._inner.fork({name:"angular",properties:{[Fd]:!0,[da]:t,[da+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(qb(c))return r.invokeTask(i,s,a,c);try{return Tg(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),Sg(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return Tg(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!Zb(c)&&n(),Sg(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,rd(e),Ld(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function rd(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function Tg(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Sg(e){e._nesting--,Ld(e)}var fa=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new z;onMicrotaskEmpty=new z;onStable=new z;onError=new z;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function qb(e){return sv(e,"__ignore_ng_zone__")}function Zb(e){return sv(e,"__scheduler_tick__")}function sv(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var jd=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),av=[0,1,2,3],cv=(()=>{class e{ngZone=p(H);scheduler=p(Pt);errorHandler=p(ot,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(Gn,{optional:!0})}execute(){let t=this.sequences.size>0;t&&U(16),this.executing=!0;for(let r of av)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&U(17)}register(t){let{view:r}=t;r!==void 0?((r[Pn]??=[]).push(t),Ar(r),r[M]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(Ma.AFTER_NEXT_RENDER,t):t()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),ha=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[Pn];n&&(this.view[Pn]=n.filter(t=>t!==this))}};function Aa(e,n){let t=n?.injector??p(me);return Ta("NgAfterNextRender"),Qb(e,t,n,!0)}function Yb(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function Qb(e,n,t,r){let o=n.get(jd);o.impl??=n.get(cv);let i=n.get(Gn,null,{optional:!0}),s=t?.manualCleanup!==!0?n.get(ct):null,a=n.get($s,null,{optional:!0}),c=new ha(o.impl,Yb(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var Vd=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var Bd=new E("");function gn(e){return!!e&&typeof e.then=="function"}function Ud(e){return!!e&&typeof e.subscribe=="function"}var uv=new E("");var Hd=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=p(uv,{optional:!0})??[];injector=p(me);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=ve(this.injector,o);if(gn(i))t.push(i);else if(Ud(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Na=new E("");function lv(){vu(()=>{let e="";throw new y(600,e)})}function dv(e){return e.isBoundToModule}var Kb=10;var dt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Pe);afterRenderManager=p(jd);zonelessEnabled=p(Hs);rootEffectScheduler=p(bl);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new $;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(bt);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(R(t=>!t))}constructor(){p(Gn,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=p(q);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=me.NULL){return this._injector.get(H).run(()=>{U(10);let s=t instanceof _a;if(!this._injector.get(Hd).done){let m="";throw new y(405,m)}let c;s?c=t:c=this._injector.get(Zo).resolveComponentFactory(t),this.componentTypes.push(c.componentType);let u=dv(c)?void 0:this._injector.get($n),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(Bd,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),Ho(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),U(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){U(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(Ma.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new y(101,!1);let t=x(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,x(t),this.afterTick.next(),U(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Hn,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<Kb;)U(14),this.synchronizeOnce(),U(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let t=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!Fo(o))continue;let i=r&&!this.zonelessEnabled?0:1;Sd(o,i),t=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}t||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>Fo(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;Ho(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(t),this._injector.get(Na,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>Ho(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new y(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Ho(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function mn(e,n,t,r){let o=j(),i=Fs();if(Pr(o,i,n)){let s=ye(),a=El();mI(a,o,e,n,t,r)}return mn}function fv(e,n,t){let r=j(),o=Fs();if(Pr(r,o,n)){let i=ye(),s=El();lI(s,r,e,n,r[X],t)}return fv}function Mg(e,n,t,r,o){_d(n,e,t,o?"class":"style",r)}function $d(e,n,t,r){let o=j(),i=o[S],s=e+ee,a=i.firstCreatePass?Nd(s,o,2,n,wd,Ps(),t,r):i.data[s];if(Id(a,o,e,n,Jb),Sr(a)){let c=o[S];ba(c,o,a),dd(c,a,o)}return r!=null&&qo(o,a),$d}function zd(){let e=ye(),n=fe(),t=bd(n);return e.firstCreatePass&&Rd(e,t),jp(t)&&Vp(),Lp(),t.classesWithoutHost!=null&&ZC(t)&&Mg(e,t,j(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&YC(t)&&Mg(e,t,j(),t.stylesWithoutHost,!1),zd}function Ra(e,n,t,r){return $d(e,n,t,r),zd(),Ra}var Jb=(e,n,t,r,o)=>(jo(!0),mm(n[X],r,tg()));function Gd(e,n,t){let r=j(),o=r[S],i=e+ee,s=o.firstCreatePass?Nd(i,r,8,"ng-container",wd,Ps(),n,t):o.data[i];if(Id(s,r,e,"ng-container",gv),Sr(s)){let a=r[S];ba(a,r,s),dd(a,s,r)}return t!=null&&qo(r,s),Gd}function xa(){let e=ye(),n=fe(),t=bd(n);return e.firstCreatePass&&Rd(e,t),xa}function hv(e,n,t){return Gd(e,n,t),xa(),hv}function pv(e,n,t){let r=j(),o=r[S],i=e+ee,s=o.firstCreatePass?JI(i,o,8,"ng-container",n,t):o.data[i];return Id(s,r,e,"ng-container",gv),t!=null&&qo(r,s),pv}function Xb(){let e=fe(),n=bd(e);return xa}var gv=(e,n,t,r,o)=>(jo(!0),Bw(n[X],""));function e_(){return j()}var Vn=void 0;function t_(e){let n=Math.floor(Math.abs(e)),t=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&t===0?1:5}var n_=["en",[["a","p"],["AM","PM"],Vn],[["AM","PM"],Vn,Vn],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],Vn,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],Vn,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",Vn,"{1} 'at' {0}",Vn],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",t_],Rl={};function He(e){let n=r_(e),t=Ag(n);if(t)return t;let r=n.split("-")[0];if(t=Ag(r),t)return t;if(r==="en")return n_;throw new y(701,!1)}function Ag(e){return e in Rl||(Rl[e]=Ft.ng&&Ft.ng.common&&Ft.ng.common.locales&&Ft.ng.common.locales[e]),Rl[e]}var ne=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(ne||{});function r_(e){return e.toLowerCase().replace(/_/g,"-")}var Yo="en-US";var o_=Yo;function mv(e){typeof e=="string"&&(o_=e.toLowerCase().replace(/_/g,"-"))}function Ce(e,n,t){let r=j(),o=ye(),i=fe();return i_(o,r,r[X],i,e,n,t),Ce}function i_(e,n,t,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=Ml(r,n,i),tb(r,e,n,s,t,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=Ml(r,n,i),Eg(r,n,h,f,o,c)}if(u&&u.length)for(let d of u)c??=Ml(r,n,i),Eg(r,n,d,o,o,c)}}function s_(e=1){return eg(e)}function a_(e,n){let t=null,r=Rw(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?gm(e,i,!0):kw(r,i))return o}return t}function c_(e){let n=j()[ke][Se];if(!n.projection){let t=e?e.length:1,r=n.projection=yp(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?a_(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function u_(e,n=0,t,r,o,i){let s=j(),a=ye(),c=r?e+1:null;c!==null&&Bb(s,a,c,r,o,i,null,t);let u=Br(a,ee+e,16,null,t||null);u.projection===null&&(u.projection=n),hl();let d=!s[br]||ll();s[ke][Se].projection[u.projection]===null&&c!==null?l_(s,a,c):d&&!Da(u)&&rI(a,s,u)}function l_(e,n,t){let r=ee+t,o=n.data[r],i=e[r],s=ql(i,o.tView.ssrId),a=Am(e,o,void 0,{dehydratedView:s});Bm(i,a,0,zl(o,s))}function Qo(e,n,t,r){bb(e,n,t,r)}function Wd(e,n,t){Ib(e,n,t)}function Hr(e){let n=j(),t=ye(),r=vl();js(r+1);let o=Od(t,r);if(e.dirty&&Np(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Sb(n,r);e.reset(i,cw),e.notifyOnChanges()}return!0}return!1}function $r(){return wb(j(),vl())}function d_(e){let n=$p();return rl(n,ee+e)}function Ws(e,n){return e<<17|n<<2}function zn(e){return e>>17&32767}function f_(e){return(e&2)==2}function h_(e,n){return e&131071|n<<17}function od(e){return e|2}function Fr(e){return(e&131068)>>2}function xl(e,n){return e&-131069|n<<2}function p_(e){return(e&1)===1}function id(e){return e|1}function g_(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=zn(s),c=Fr(s);e[r]=t;let u=!1,l;if(Array.isArray(t)){let d=t;l=d[1],(l===null||Cr(d,l)>0)&&(u=!0)}else l=t;if(o)if(c!==0){let h=zn(e[a+1]);e[r+1]=Ws(h,a),h!==0&&(e[h+1]=xl(e[h+1],r)),e[a+1]=h_(e[a+1],r)}else e[r+1]=Ws(a,0),a!==0&&(e[a+1]=xl(e[a+1],r)),a=r;else e[r+1]=Ws(c,0),a===0?a=r:e[c+1]=xl(e[c+1],r),c=r;u&&(e[r+1]=od(e[r+1])),Ng(e,l,r,!0),Ng(e,l,r,!1),m_(n,l,e,r,i),s=Ws(a,c),i?n.classBindings=s:n.styleBindings=s}function m_(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&Cr(i,n)>=0&&(t[r+1]=id(t[r+1]))}function Ng(e,n,t,r){let o=e[t+1],i=n===null,s=r?zn(o):Fr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];v_(c,n)&&(a=!0,e[s+1]=r?id(u):od(u)),s=r?zn(u):Fr(u)}a&&(e[t+1]=r?od(o):id(o))}function v_(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?Cr(e,n)>=0:!1}function Ko(e,n){return y_(e,n,null,!0),Ko}function y_(e,n,t,r){let o=j(),i=ye(),s=Wp(2);if(i.firstUpdatePass&&E_(i,e,s,r),n!==Vt&&Pr(o,s,n)){let a=i.data[jn()];__(i,a,o,o[X],e,o[s+1]=T_(n,t),r,s)}}function D_(e,n){return n>=e.expandoStartIndex}function E_(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[jn()],s=D_(e,t);S_(i,r)&&n===null&&!s&&(n=!1),n=C_(o,i,n,r),g_(o,i,n,t,s,r)}}function C_(e,n,t,r){let o=Qp(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=Ol(null,e,n,t,r),t=Wo(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=Ol(o,e,n,t,r),i===null){let c=w_(e,n,r);c!==void 0&&Array.isArray(c)&&(c=Ol(null,e,n,c[1],r),c=Wo(c,n.attrs,r),I_(e,n,r,c))}else i=b_(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function w_(e,n,t){let r=t?n.classBindings:n.styleBindings;if(Fr(r)!==0)return e[zn(r)]}function I_(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[zn(o)]=r}function b_(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Wo(r,s,t)}return Wo(r,n.attrs,t)}function Ol(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=Wo(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function Wo(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Ep(e,s,t?!0:n[++i]))}return e===void 0?null:e}function __(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,u=c[a+1],l=p_(u)?Rg(c,n,t,o,Fr(u),s):void 0;if(!pa(l)){pa(i)||f_(u)&&(i=Rg(c,null,t,o,a,s));let d=nl(jn(),t);iI(r,s,d,o,i)}}function Rg(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=t[o+1];h===Vt&&(h=d?Ve:void 0);let f=d?Ns(h,r):l===r?h:void 0;if(u&&!pa(f)&&(f=Ns(c,r)),pa(f)&&(a=f,s))return a;let m=e[o+1];o=s?zn(m):Fr(m)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=Ns(c,r))}return a}function pa(e){return e!==void 0}function T_(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=kt(Ea(e)))),e}function S_(e,n){return(e.flags&(n?8:16))!==0}function M_(e,n=""){let t=j(),r=ye(),o=e+ee,i=r.firstCreatePass?Br(r,o,1,n,null):r.data[o],s=A_(r,t,i,n,e);t[o]=s,Us()&&Ed(r,t,s,i),Nr(i,!1)}var A_=(e,n,t,r,o)=>(jo(!0),jw(n[X],r));function N_(e,n,t,r=""){return Pr(e,Fs(),t)?n+No(t)+r:Vt}function vv(e){return qd("",e),vv}function qd(e,n,t){let r=j(),o=N_(r,e,n,t);return o!==Vt&&R_(r,jn(),o),qd}function R_(e,n,t){let r=nl(n,e);Vw(e[X],r,t)}function x_(e,n,t){let r=ye();if(r.firstCreatePass){let o=It(e);sd(t,r.data,r.blueprint,o,!0),sd(n,r.data,r.blueprint,o,!1)}}function sd(e,n,t,r,o){if(e=ge(e),Array.isArray(e))for(let i=0;i<e.length;i++)sd(e[i],n,t,r,o);else{let i=ye(),s=j(),a=fe(),c=An(e)?e:ge(e.provide),u=Ju(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(An(e)||!e.multi){let f=new Un(u,o,v,null),m=Pl(c,n,o?l:l+h,d);m===-1?(Ll(ta(a,s),i,c),kl(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[m]=f,s[m]=f)}else{let f=Pl(c,n,l+h,d),m=Pl(c,n,l,l+h),I=f>=0&&t[f],D=m>=0&&t[m];if(o&&!D||!o&&!I){Ll(ta(a,s),i,c);let C=P_(o?k_:O_,t.length,o,r,u,e);!o&&D&&(t[m].providerFactory=C),kl(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(C),s.push(C)}else{let C=yv(t[o?m:f],u,!o&&r);kl(i,e,f>-1?f:m,C)}!o&&r&&D&&t[m].componentProviders++}}}function kl(e,n,t,r){let o=An(n),i=_p(n);if(o||i){let c=(i?ge(n.useClass):n).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let l=u.indexOf(t);l===-1?u.push(t,[r,c]):u[l+1].push(r,c)}else u.push(t,c)}}}function yv(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function Pl(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function O_(e,n,t,r,o){return ad(this.multi,[])}function k_(e,n,t,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=$o(r,r[S],this.providerFactory.index,o);s=c.slice(0,a),ad(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],ad(i,s);return s}function ad(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function P_(e,n,t,r,o,i){let s=new Un(e,t,v,null);return s.multi=[],s.index=n,s.componentProviders=0,yv(s,o,r&&!t),s}function Je(e,n=[]){return t=>{t.providersResolver=(r,o)=>x_(r,o?o(e):e,n)}}function F_(e,n){let t=e[n];return t===Vt?void 0:t}function L_(e,n,t,r,o,i,s){let a=n+t;return eb(e,a,o,i)?XI(e,a+2,s?r.call(s,o,i):r(o,i)):F_(e,a+2)}function j_(e,n){let t=ye(),r,o=e+ee;t.firstCreatePass?(r=V_(n,t.pipeRegistry),t.data[o]=r,r.onDestroy&&(t.destroyHooks??=[]).push(o,r.onDestroy)):r=t.data[o];let i=r.factory||(r.factory=nn(r.type,!0)),s,a=_e(v);try{let c=ea(!1),u=i();return ea(c),ol(t,j(),o,u),u}finally{_e(a)}}function V_(e,n){if(n)for(let t=n.length-1;t>=0;t--){let r=n[t];if(e===r.name)return r}}function B_(e,n,t,r){let o=e+ee,i=j(),s=rl(i,o);return U_(i,o)?L_(i,zp(),n,s.transform,t,r,s):s.transform(t,r)}function U_(e,n){return e[S].data[n].pure}var ga=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},Zd=(()=>{class e{compileModuleSync(t){return new la(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=Zu(t),i=hm(o.declarations).reduce((s,a)=>{let c=Dt(a);return c&&s.push(new hn(c)),s},[]);return new ga(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var H_=(()=>{class e{zone=p(H);changeDetectionScheduler=p(Pt);applicationRef=p(dt);applicationErrorHandler=p(Pe);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(t){this.applicationErrorHandler(t)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Dv({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new H(N(g({},Ev()),{scheduleInRootZone:t})),[{provide:H,useFactory:e},{provide:sn,multi:!0,useFactory:()=>{let r=p(H_,{optional:!0});return()=>r.initialize()}},{provide:sn,multi:!0,useFactory:()=>{let r=p($_);return()=>{r.initialize()}}},n===!0?{provide:wl,useValue:!0}:[],{provide:Il,useValue:t??ov},{provide:Pe,useFactory:()=>{let r=p(H),o=p(q),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(ot),i.handleError(s))})}}}]}function Ev(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var $_=(()=>{class e{subscription=new re;initialized=!1;zone=p(H);pendingTasks=p(bt);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{H.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{H.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Cv=(()=>{class e{applicationErrorHandler=p(Pe);appRef=p(dt);taskService=p(bt);ngZone=p(H);zonelessEnabled=p(Hs);tracing=p(Gn,{optional:!0});disableScheduling=p(wl,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new re;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(da):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(Il,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof fa||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?_g:iv;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(da+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(t),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,_g(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function z_(){return typeof $localize<"u"&&$localize.locale||Yo}var Jo=new E("",{providedIn:"root",factory:()=>p(Jo,{optional:!0,skipSelf:!0})||z_()});function Xe(e){return cp(e)}function zr(e,n){return Es(e,n?.equal)}var wv=class{[be];constructor(n){this[be]=n}destroy(){this[be].destroy()}};var Tv=Symbol("InputSignalNode#UNSET"),rT=N(g({},Cs),{transformFn:void 0,applyValueToInputSignal(e,n){yr(e,n)}});function Sv(e,n){let t=Object.create(rT);t.value=e,t.transformFn=n?.transform;function r(){if(mr(t),t.value===Tv){let o=null;throw new y(-950,o)}return t.value}return r[be]=t,r}var ka=class{attributeName;constructor(n){this.attributeName=n}__NG_ELEMENT_ID__=()=>pn(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},oT=new E("");oT.__NG_ELEMENT_ID__=e=>{let n=fe();if(n===null)throw new y(204,!1);if(n.type&2)return n.value;if(e&8)return null;throw new y(204,!1)};function Iv(e,n){return Sv(e,n)}function iT(e){return Sv(Tv,e)}var Mv=(Iv.required=iT,Iv);var Yd=new E(""),sT=new E("");function Xo(e){return!e.moduleRef}function aT(e){let n=Xo(e)?e.r3Injector:e.moduleRef.injector,t=n.get(H);return t.run(()=>{Xo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Pe),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:r})}),Xo(e)){let i=()=>n.destroy(),s=e.platformInjector.get(Yd);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Yd);s.add(i),e.moduleRef.onDestroy(()=>{Ho(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return uT(r,t,()=>{let i=n.get(bt),s=i.add(),a=n.get(Hd);return a.runInitializers(),a.donePromise.then(()=>{let c=n.get(Jo,Yo);if(mv(c||Yo),!n.get(sT,!0))return Xo(e)?n.get(dt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Xo(e)){let l=n.get(dt);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return cT?.(e.moduleRef,e.allPlatformModules),e.moduleRef}).finally(()=>void i.remove(s))})})}var cT;function uT(e,n,t){try{let r=t();return gn(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e(r)),r}}var Oa=null;function lT(e=[],n){return me.create({name:n,providers:[{provide:xo,useValue:"platform"},{provide:Yd,useValue:new Set([()=>Oa=null])},...e]})}function dT(e=[]){if(Oa)return Oa;let n=lT(e);return Oa=n,lv(),fT(n),n}function fT(e){let n=e.get(va,null);ve(e,()=>{n?.forEach(t=>t())})}var ft=(()=>{class e{static __NG_ELEMENT_ID__=hT}return e})();function hT(e){return pT(fe(),j(),(e&16)===16)}function pT(e,n,t){if(un(e)&&!t){let r=Ze(e.index,n);return new fn(r,r)}else if(e.type&175){let r=n[ke];return new fn(r,n)}return null}var Qd=class{constructor(){}supports(n){return xd(n)}create(n){return new Kd(n)}},gT=(e,n)=>n,Kd=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||gT}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<bv(r,o,i)?t:r,a=bv(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,m=f+h;l<=m&&m<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!xd(n))throw new y(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,Wm(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new Jd(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new Pa),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Pa),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},Jd=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},Xd=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},Pa=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new Xd,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function bv(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function _v(){return new ef([new Qd])}var ef=(()=>{class e{factories;static \u0275prov=w({token:e,providedIn:"root",factory:_v});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||_v()),deps:[[e,new Pg,new kg]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new y(901,!1)}}return e})();function Av(e){U(8);try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,o=dT(r),i=[Dv({}),{provide:Pt,useExisting:Cv},rg,...t||[]],s=new Go({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return aT({r3Injector:s.injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}finally{U(9)}}function Gr(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Nv(e,n){let t=Dt(e),r=n.elementInjector||Ir();return new hn(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector,n.directives,n.bindings)}function tf(e){let n=Dt(e);if(!n)return null;let t=new hn(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var Ov=null;function et(){return Ov}function nf(e){Ov??=e}var ei=class{},rf=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(kv),providedIn:"platform"})}return e})();var kv=(()=>{class e extends rf{_location;_history;_doc=p(te);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return et().getBaseHref(this._doc)}onPopState(t){let r=et().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=et().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function Pv(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Rv(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function vn(e){return e&&e[0]!=="?"?`?${e}`:e}var Ut=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(Lv),providedIn:"root"})}return e})(),Fv=new E(""),Lv=(()=>{class e extends Ut{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(te).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return Pv(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+vn(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+vn(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+vn(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(b(rf),b(Fv,8))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ht=(()=>{class e{_subject=new $;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=yT(Rv(xv(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+vn(r))}normalize(t){return e.stripTrailingSlash(vT(this._basePath,xv(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+vn(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+vn(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=vn;static joinWithSlash=Pv;static stripTrailingSlash=Rv;static \u0275fac=function(r){return new(r||e)(b(Ut))};static \u0275prov=w({token:e,factory:()=>mT(),providedIn:"root"})}return e})();function mT(){return new Ht(b(Ut))}function vT(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function xv(e){return e.replace(/\/index.html$/,"")}function yT(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var we=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(we||{}),G=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(G||{}),Fe=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(Fe||{}),zt={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function Uv(e){return He(e)[ne.LocaleId]}function Hv(e,n,t){let r=He(e),o=[r[ne.DayPeriodsFormat],r[ne.DayPeriodsStandalone]],i=tt(o,n);return tt(i,t)}function $v(e,n,t){let r=He(e),o=[r[ne.DaysFormat],r[ne.DaysStandalone]],i=tt(o,n);return tt(i,t)}function zv(e,n,t){let r=He(e),o=[r[ne.MonthsFormat],r[ne.MonthsStandalone]],i=tt(o,n);return tt(i,t)}function Gv(e,n){let r=He(e)[ne.Eras];return tt(r,n)}function ti(e,n){let t=He(e);return tt(t[ne.DateFormat],n)}function ni(e,n){let t=He(e);return tt(t[ne.TimeFormat],n)}function ri(e,n){let r=He(e)[ne.DateTimeFormat];return tt(r,n)}function oi(e,n){let t=He(e),r=t[ne.NumberSymbols][n];if(typeof r>"u"){if(n===zt.CurrencyDecimal)return t[ne.NumberSymbols][zt.Decimal];if(n===zt.CurrencyGroup)return t[ne.NumberSymbols][zt.Group]}return r}function Wv(e){if(!e[ne.ExtraData])throw new y(2303,!1)}function qv(e){let n=He(e);return Wv(n),(n[ne.ExtraData][2]||[]).map(r=>typeof r=="string"?of(r):[of(r[0]),of(r[1])])}function Zv(e,n,t){let r=He(e);Wv(r);let o=[r[ne.ExtraData][0],r[ne.ExtraData][1]],i=tt(o,n)||[];return tt(i,t)||[]}function tt(e,n){for(let t=n;t>-1;t--)if(typeof e[t]<"u")return e[t];throw new y(2304,!1)}function of(e){let[n,t]=e.split(":");return{hours:+n,minutes:+t}}var DT=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Fa={},ET=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/;function Yv(e,n,t,r){let o=AT(e);n=$t(t,n)||n;let s=[],a;for(;n;)if(a=ET.exec(n),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;n=l}else{s.push(n);break}let c=o.getTimezoneOffset();r&&(c=Kv(r,c),o=MT(o,r));let u="";return s.forEach(l=>{let d=TT(l);u+=d?d(o,t,c):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),u}function Ua(e,n,t){let r=new Date(0);return r.setFullYear(e,n,t),r.setHours(0,0,0),r}function $t(e,n){let t=Uv(e);if(Fa[t]??={},Fa[t][n])return Fa[t][n];let r="";switch(n){case"shortDate":r=ti(e,Fe.Short);break;case"mediumDate":r=ti(e,Fe.Medium);break;case"longDate":r=ti(e,Fe.Long);break;case"fullDate":r=ti(e,Fe.Full);break;case"shortTime":r=ni(e,Fe.Short);break;case"mediumTime":r=ni(e,Fe.Medium);break;case"longTime":r=ni(e,Fe.Long);break;case"fullTime":r=ni(e,Fe.Full);break;case"short":let o=$t(e,"shortTime"),i=$t(e,"shortDate");r=La(ri(e,Fe.Short),[o,i]);break;case"medium":let s=$t(e,"mediumTime"),a=$t(e,"mediumDate");r=La(ri(e,Fe.Medium),[s,a]);break;case"long":let c=$t(e,"longTime"),u=$t(e,"longDate");r=La(ri(e,Fe.Long),[c,u]);break;case"full":let l=$t(e,"fullTime"),d=$t(e,"fullDate");r=La(ri(e,Fe.Full),[l,d]);break}return r&&(Fa[t][n]=r),r}function La(e,n){return n&&(e=e.replace(/\{([^}]+)}/g,function(t,r){return n!=null&&r in n?n[r]:t})),e}function ht(e,n,t="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=t));let s=String(e);for(;s.length<n;)s="0"+s;return r&&(s=s.slice(s.length-n)),i+s}function CT(e,n){return ht(e,3).substring(0,n)}function ie(e,n,t=0,r=!1,o=!1){return function(i,s){let a=wT(e,i);if((t>0||a>-t)&&(a+=t),e===3)a===0&&t===-12&&(a=12);else if(e===6)return CT(a,n);let c=oi(s,zt.MinusSign);return ht(a,n,c,r,o)}}function wT(e,n){switch(e){case 0:return n.getFullYear();case 1:return n.getMonth();case 2:return n.getDate();case 3:return n.getHours();case 4:return n.getMinutes();case 5:return n.getSeconds();case 6:return n.getMilliseconds();case 7:return n.getDay();default:throw new y(2301,!1)}}function W(e,n,t=we.Format,r=!1){return function(o,i){return IT(o,i,e,n,t,r)}}function IT(e,n,t,r,o,i){switch(t){case 2:return zv(n,o,r)[e.getMonth()];case 1:return $v(n,o,r)[e.getDay()];case 0:let s=e.getHours(),a=e.getMinutes();if(i){let u=qv(n),l=Zv(n,o,r),d=u.findIndex(h=>{if(Array.isArray(h)){let[f,m]=h,I=s>=f.hours&&a>=f.minutes,D=s<m.hours||s===m.hours&&a<m.minutes;if(f.hours<m.hours){if(I&&D)return!0}else if(I||D)return!0}else if(h.hours===s&&h.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return Hv(n,o,r)[s<12?0:1];case 3:return Gv(n,r)[e.getFullYear()<=0?0:1];default:let c=t;throw new y(2302,!1)}}function ja(e){return function(n,t,r){let o=-1*r,i=oi(t,zt.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case 0:return(o>=0?"+":"")+ht(s,2,i)+ht(Math.abs(o%60),2,i);case 1:return"GMT"+(o>=0?"+":"")+ht(s,1,i);case 2:return"GMT"+(o>=0?"+":"")+ht(s,2,i)+":"+ht(Math.abs(o%60),2,i);case 3:return r===0?"Z":(o>=0?"+":"")+ht(s,2,i)+":"+ht(Math.abs(o%60),2,i);default:throw new y(2302,!1)}}}var bT=0,Ba=4;function _T(e){let n=Ua(e,bT,1).getDay();return Ua(e,0,1+(n<=Ba?Ba:Ba+7)-n)}function Qv(e){let n=e.getDay(),t=n===0?-3:Ba-n;return Ua(e.getFullYear(),e.getMonth(),e.getDate()+t)}function sf(e,n=!1){return function(t,r){let o;if(n){let i=new Date(t.getFullYear(),t.getMonth(),1).getDay()-1,s=t.getDate();o=1+Math.floor((s+i)/7)}else{let i=Qv(t),s=_T(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return ht(o,e,oi(r,zt.MinusSign))}}function Va(e,n=!1){return function(t,r){let i=Qv(t).getFullYear();return ht(i,e,oi(r,zt.MinusSign),n)}}var af={};function TT(e){if(af[e])return af[e];let n;switch(e){case"G":case"GG":case"GGG":n=W(3,G.Abbreviated);break;case"GGGG":n=W(3,G.Wide);break;case"GGGGG":n=W(3,G.Narrow);break;case"y":n=ie(0,1,0,!1,!0);break;case"yy":n=ie(0,2,0,!0,!0);break;case"yyy":n=ie(0,3,0,!1,!0);break;case"yyyy":n=ie(0,4,0,!1,!0);break;case"Y":n=Va(1);break;case"YY":n=Va(2,!0);break;case"YYY":n=Va(3);break;case"YYYY":n=Va(4);break;case"M":case"L":n=ie(1,1,1);break;case"MM":case"LL":n=ie(1,2,1);break;case"MMM":n=W(2,G.Abbreviated);break;case"MMMM":n=W(2,G.Wide);break;case"MMMMM":n=W(2,G.Narrow);break;case"LLL":n=W(2,G.Abbreviated,we.Standalone);break;case"LLLL":n=W(2,G.Wide,we.Standalone);break;case"LLLLL":n=W(2,G.Narrow,we.Standalone);break;case"w":n=sf(1);break;case"ww":n=sf(2);break;case"W":n=sf(1,!0);break;case"d":n=ie(2,1);break;case"dd":n=ie(2,2);break;case"c":case"cc":n=ie(7,1);break;case"ccc":n=W(1,G.Abbreviated,we.Standalone);break;case"cccc":n=W(1,G.Wide,we.Standalone);break;case"ccccc":n=W(1,G.Narrow,we.Standalone);break;case"cccccc":n=W(1,G.Short,we.Standalone);break;case"E":case"EE":case"EEE":n=W(1,G.Abbreviated);break;case"EEEE":n=W(1,G.Wide);break;case"EEEEE":n=W(1,G.Narrow);break;case"EEEEEE":n=W(1,G.Short);break;case"a":case"aa":case"aaa":n=W(0,G.Abbreviated);break;case"aaaa":n=W(0,G.Wide);break;case"aaaaa":n=W(0,G.Narrow);break;case"b":case"bb":case"bbb":n=W(0,G.Abbreviated,we.Standalone,!0);break;case"bbbb":n=W(0,G.Wide,we.Standalone,!0);break;case"bbbbb":n=W(0,G.Narrow,we.Standalone,!0);break;case"B":case"BB":case"BBB":n=W(0,G.Abbreviated,we.Format,!0);break;case"BBBB":n=W(0,G.Wide,we.Format,!0);break;case"BBBBB":n=W(0,G.Narrow,we.Format,!0);break;case"h":n=ie(3,1,-12);break;case"hh":n=ie(3,2,-12);break;case"H":n=ie(3,1);break;case"HH":n=ie(3,2);break;case"m":n=ie(4,1);break;case"mm":n=ie(4,2);break;case"s":n=ie(5,1);break;case"ss":n=ie(5,2);break;case"S":n=ie(6,1);break;case"SS":n=ie(6,2);break;case"SSS":n=ie(6,3);break;case"Z":case"ZZ":case"ZZZ":n=ja(0);break;case"ZZZZZ":n=ja(3);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":n=ja(1);break;case"OOOO":case"ZZZZ":case"zzzz":n=ja(2);break;default:return null}return af[e]=n,n}function Kv(e,n){e=e.replace(/:/g,"");let t=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(t)?n:t}function ST(e,n){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+n),e}function MT(e,n,t){let o=e.getTimezoneOffset(),i=Kv(n,o);return ST(e,-1*(i-o))}function AT(e){if(jv(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Ua(o,i-1,s)}let t=parseFloat(e);if(!isNaN(e-t))return new Date(t);let r;if(r=e.match(DT))return NT(r)}let n=new Date(e);if(!jv(n))throw new y(2302,!1);return n}function NT(e){let n=new Date(0),t=0,r=0,o=e[8]?n.setUTCFullYear:n.setFullYear,i=e[8]?n.setUTCHours:n.setHours;e[9]&&(t=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(n,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-t,a=Number(e[5]||0)-r,c=Number(e[6]||0),u=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(n,s,a,c,u),n}function jv(e){return e instanceof Date&&!isNaN(e.valueOf())}var Ha=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},Jv=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Ha(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),Vv(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);Vv(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v(Qe),v(lt),v(ef))};static \u0275dir=F({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function Vv(e,n){e.context.$implicit=n.item}var RT=(()=>{class e{_viewContainer;_context=new $a;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){Bv(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){Bv(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v(Qe),v(lt))};static \u0275dir=F({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),$a=class{$implicit=null;ngIf=null};function Bv(e,n){if(e&&!e.createEmbeddedView)throw new y(2020,!1)}var xT=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(v(Qe))};static \u0275dir=F({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Ae]})}return e})();function OT(e,n){return new y(2100,!1)}var kT="mediumDate",Xv=new E(""),ey=new E(""),PT=(()=>{class e{locale;defaultTimezone;defaultOptions;constructor(t,r,o){this.locale=t,this.defaultTimezone=r,this.defaultOptions=o}transform(t,r,o,i){if(t==null||t===""||t!==t)return null;try{let s=r??this.defaultOptions?.dateFormat??kT,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return Yv(t,s,i||this.locale,a)}catch(s){throw OT(e,s.message)}}static \u0275fac=function(r){return new(r||e)(v(Jo,16),v(Xv,24),v(ey,24))};static \u0275pipe=Pd({name:"date",type:e,pure:!0})}return e})();var ty=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ke({type:e});static \u0275inj=Be({})}return e})();function ii(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var Wn=class{};var ny="browser";var Ga=new E(""),ff=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new y(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(b(Ga),b(H))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),si=class{_doc;constructor(n){this._doc=n}manager},cf="ng-app-id";function oy(e){for(let n of e)n.remove()}function iy(e,n){let t=n.createElement("style");return t.textContent=e,t}function FT(e,n,t,r){let o=e.head?.querySelectorAll(`style[${cf}="${n}"],link[${cf}="${n}"]`);if(o)for(let i of o)i.removeAttribute(cf),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function lf(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var hf=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,FT(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,iy);r?.forEach(o=>this.addUsage(o,this.external,lf))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(oy(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])oy(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,iy(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,lf(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(b(te),b(ma),b(ya,8),b(Vr))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),uf={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},pf=/%COMP%/g;var ay="%COMP%",LT=`_nghost-${ay}`,jT=`_ngcontent-${ay}`,VT=!0,BT=new E("",{providedIn:"root",factory:()=>VT});function UT(e){return jT.replace(pf,e)}function HT(e){return LT.replace(pf,e)}function cy(e,n){return n.map(t=>t.replace(pf,e))}var gf=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,u=null,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new ai(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(t,r);return o instanceof za?o.applyToHost(t):o instanceof ci&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case jt.Emulated:i=new za(c,u,r,this.appId,l,s,a,d,h);break;case jt.ShadowDom:return new df(c,u,t,r,s,a,this.nonce,d,h);default:i=new ci(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(b(ff),b(hf),b(ma),b(BT),b(te),b(Vr),b(H),b(ya),b(Gn,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),ai=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(uf[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(sy(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(sy(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new y(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=uf[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=uf[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(Tt.DashCase|Tt.Important)?n.style.setProperty(t,r,o&Tt.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&Tt.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=et().getGlobalEventTarget(this.doc,n),!n))throw new y(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;n(t)===!1&&t.preventDefault()}}};function sy(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var df=class extends ai{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,c,u),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=cy(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=lf(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},ci=class extends ai{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?cy(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},za=class extends ci{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(n,t,r,i,s,a,c,u,l),this.contentAttr=UT(l),this.hostAttr=HT(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var Wa=class e extends ei{supportsDOMEvents=!0;static makeCurrent(){nf(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=$T();return t==null?null:zT(t)}resetBaseElement(){ui=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return ii(document.cookie,n)}},ui=null;function $T(){return ui=ui||document.head.querySelector("base"),ui?ui.getAttribute("href"):null}function zT(e){return new URL(e,document.baseURI).pathname}var GT=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),ly=(()=>{class e extends si{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(b(te))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),uy=["alt","control","meta","shift"],WT={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},qT={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},dy=(()=>{class e extends si{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>et().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),uy.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=WT[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),uy.forEach(s=>{if(s!==o){let a=qT[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(b(te))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function ZT(e,n){let t=g({rootComponent:e},YT(n));return Av(t)}function YT(e){return{appProviders:[...eS,...e?.providers??[]],platformProviders:XT}}function QT(){Wa.makeCurrent()}function KT(){return new ot}function JT(){return ld(document),document}var XT=[{provide:Vr,useValue:ny},{provide:va,useValue:QT,multi:!0},{provide:te,useFactory:JT}];var eS=[{provide:xo,useValue:"root"},{provide:ot,useFactory:KT},{provide:Ga,useClass:ly,multi:!0,deps:[te]},{provide:Ga,useClass:dy,multi:!0,deps:[te]},gf,hf,ff,{provide:Hn,useExisting:gf},{provide:Wn,useClass:GT},[]];var Zr=class{},li=class{},yn=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(n){n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let o=t.slice(0,r),i=t.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.addHeaderEntry(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let o=(n.op==="a"?this.headers.get(t):void 0)||[];o.push(...r),this.headers.set(t,o);break;case"d":let i=n.value;if(!i)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}addHeaderEntry(n,t){let r=n.toLowerCase();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(t):this.headers.set(r,[t])}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(i=>i.toString()),o=n.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(n,o)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var Za=class{encodeKey(n){return fy(n)}encodeValue(n){return fy(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function tS(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[n.decodeKey(o),""]:[n.decodeKey(o.slice(0,i)),n.decodeValue(o.slice(i+1))],c=t.get(s)||[];c.push(a),t.set(s,c)}),t}var nS=/%(\d[a-f0-9])/gi,rS={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function fy(e){return encodeURIComponent(e).replace(nS,(n,t)=>rS[t]??n)}function qa(e){return`${e}`}var Gt=class e{map;encoder;updates=null;cloneFrom=null;constructor(n={}){if(this.encoder=n.encoder||new Za,n.fromString){if(n.fromObject)throw new y(2805,!1);this.map=tS(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],o=Array.isArray(r)?r.map(qa):[qa(r)];this.map.set(t,o)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let o=n[r];Array.isArray(o)?o.forEach(i=>{t.push({param:r,value:i,op:"a"})}):t.push({param:r,value:o,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(qa(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],o=r.indexOf(qa(n.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Ya=class{map=new Map;set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function oS(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function hy(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function py(e){return typeof Blob<"u"&&e instanceof Blob}function gy(e){return typeof FormData<"u"&&e instanceof FormData}function iS(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var my="Content-Type",vy="Accept",Dy="X-Request-URL",Ey="text/plain",Cy="application/json",sS=`${Cy}, ${Ey}, */*`,Wr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;credentials;keepalive=!1;cache;priority;mode;redirect;responseType="json";method;params;urlWithParams;transferCache;timeout;constructor(n,t,r,o){this.url=t,this.method=n.toUpperCase();let i;if(oS(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i){if(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),i.priority&&(this.priority=i.priority),i.cache&&(this.cache=i.cache),i.credentials&&(this.credentials=i.credentials),typeof i.timeout=="number"){if(i.timeout<1||!Number.isInteger(i.timeout))throw new Error("");this.timeout=i.timeout}i.mode&&(this.mode=i.mode),i.redirect&&(this.redirect=i.redirect),this.transferCache=i.transferCache}if(this.headers??=new yn,this.context??=new Ya,!this.params)this.params=new Gt,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),c=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||hy(this.body)||py(this.body)||gy(this.body)||iS(this.body)?this.body:this.body instanceof Gt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||gy(this.body)?null:py(this.body)?this.body.type||null:hy(this.body)?null:typeof this.body=="string"?Ey:this.body instanceof Gt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Cy:null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,o=n.responseType||this.responseType,i=n.keepalive??this.keepalive,s=n.priority||this.priority,a=n.cache||this.cache,c=n.mode||this.mode,u=n.redirect||this.redirect,l=n.credentials||this.credentials,d=n.transferCache??this.transferCache,h=n.timeout??this.timeout,f=n.body!==void 0?n.body:this.body,m=n.withCredentials??this.withCredentials,I=n.reportProgress??this.reportProgress,D=n.headers||this.headers,C=n.params||this.params,pe=n.context??this.context;return n.setHeaders!==void 0&&(D=Object.keys(n.setHeaders).reduce((Re,Q)=>Re.set(Q,n.setHeaders[Q]),D)),n.setParams&&(C=Object.keys(n.setParams).reduce((Re,Q)=>Re.set(Q,n.setParams[Q]),C)),new e(t,r,f,{params:C,headers:D,context:pe,reportProgress:I,responseType:o,withCredentials:m,transferCache:d,keepalive:i,cache:a,priority:s,timeout:h,mode:c,redirect:u,credentials:l})}},qn=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(qn||{}),Yr=class{headers;status;statusText;url;ok;type;constructor(n,t=200,r="OK"){this.headers=n.headers||new yn,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},Qa=class e extends Yr{constructor(n={}){super(n)}type=qn.ResponseHeader;clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},di=class e extends Yr{body;constructor(n={}){super(n),this.body=n.body!==void 0?n.body:null}type=qn.Response;clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},qr=class extends Yr{name="HttpErrorResponse";message;error;ok=!1;constructor(n){super(n,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},aS=200,cS=204;function mf(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive,priority:e.priority,cache:e.cache,mode:e.mode,redirect:e.redirect}}var wy=(()=>{class e{handler;constructor(t){this.handler=t}request(t,r,o={}){let i;if(t instanceof Wr)i=t;else{let c;o.headers instanceof yn?c=o.headers:c=new yn(o.headers);let u;o.params&&(o.params instanceof Gt?u=o.params:u=new Gt({fromObject:o.params})),i=new Wr(t,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive,priority:o.priority,cache:o.cache,mode:o.mode,redirect:o.redirect,credentials:o.credentials})}let s=_(i).pipe(vt(c=>this.handler.handle(c)));if(t instanceof Wr||o.observe==="events")return s;let a=s.pipe(se(c=>c instanceof di));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(R(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new y(2806,!1);return c.body}));case"blob":return a.pipe(R(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new y(2807,!1);return c.body}));case"text":return a.pipe(R(c=>{if(c.body!==null&&typeof c.body!="string")throw new y(2808,!1);return c.body}));case"json":default:return a.pipe(R(c=>c.body))}case"response":return a;default:throw new y(2809,!1)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new Gt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,o={}){return this.request("PATCH",t,mf(o,r))}post(t,r,o={}){return this.request("POST",t,mf(o,r))}put(t,r,o={}){return this.request("PUT",t,mf(o,r))}static \u0275fac=function(r){return new(r||e)(b(Zr))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var uS=new E("");function Iy(e,n){return n(e)}function lS(e,n){return(t,r)=>n.intercept(t,{handle:o=>e(o,r)})}function dS(e,n,t){return(r,o)=>ve(t,()=>n(r,i=>e(i,o)))}var by=new E(""),yf=new E(""),_y=new E(""),Df=new E("",{providedIn:"root",factory:()=>!0});function fS(){let e=null;return(n,t)=>{e===null&&(e=(p(by,{optional:!0})??[]).reduceRight(lS,Iy));let r=p(Vo);if(p(Df)){let i=r.add();return e(n,t).pipe(tn(i))}else return e(n,t)}}var Ka=(()=>{class e extends Zr{backend;injector;chain=null;pendingTasks=p(Vo);contributeToStability=p(Df);constructor(t,r){super(),this.backend=t,this.injector=r}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(yf),...this.injector.get(_y,[])]));this.chain=r.reduceRight((o,i)=>dS(o,i,this.injector),Iy)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,o=>this.backend.handle(o)).pipe(tn(r))}else return this.chain(t,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(b(li),b(q))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var hS=/^\)\]\}',?\n/,pS=RegExp(`^${Dy}:`,"m");function gS(e){return"responseURL"in e&&e.responseURL?e.responseURL:pS.test(e.getAllResponseHeaders())?e.getResponseHeader(Dy):null}var vf=(()=>{class e{xhrFactory;constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new y(-2800,!1);let r=this.xhrFactory;return _(null).pipe(ue(()=>new O(i=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((D,C)=>s.setRequestHeader(D,C.join(","))),t.headers.has(vy)||s.setRequestHeader(vy,sS),!t.headers.has(my)){let D=t.detectContentTypeHeader();D!==null&&s.setRequestHeader(my,D)}if(t.timeout&&(s.timeout=t.timeout),t.responseType){let D=t.responseType.toLowerCase();s.responseType=D!=="json"?D:"text"}let a=t.serializeBody(),c=null,u=()=>{if(c!==null)return c;let D=s.statusText||"OK",C=new yn(s.getAllResponseHeaders()),pe=gS(s)||t.url;return c=new Qa({headers:C,status:s.status,statusText:D,url:pe}),c},l=()=>{let{headers:D,status:C,statusText:pe,url:Re}=u(),Q=null;C!==cS&&(Q=typeof s.response>"u"?s.responseText:s.response),C===0&&(C=Q?aS:0);let er=C>=200&&C<300;if(t.responseType==="json"&&typeof Q=="string"){let _E=Q;Q=Q.replace(hS,"");try{Q=Q!==""?JSON.parse(Q):null}catch(TE){Q=_E,er&&(er=!1,Q={error:TE,text:Q})}}er?(i.next(new di({body:Q,headers:D,status:C,statusText:pe,url:Re||void 0})),i.complete()):i.error(new qr({error:Q,headers:D,status:C,statusText:pe,url:Re||void 0}))},d=D=>{let{url:C}=u(),pe=new qr({error:D,status:s.status||0,statusText:s.statusText||"Unknown Error",url:C||void 0});i.error(pe)},h=d;t.timeout&&(h=D=>{let{url:C}=u(),pe=new qr({error:new DOMException("Request timed out","TimeoutError"),status:s.status||0,statusText:s.statusText||"Request timeout",url:C||void 0});i.error(pe)});let f=!1,m=D=>{f||(i.next(u()),f=!0);let C={type:qn.DownloadProgress,loaded:D.loaded};D.lengthComputable&&(C.total=D.total),t.responseType==="text"&&s.responseText&&(C.partialText=s.responseText),i.next(C)},I=D=>{let C={type:qn.UploadProgress,loaded:D.loaded};D.lengthComputable&&(C.total=D.total),i.next(C)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",h),s.addEventListener("abort",d),t.reportProgress&&(s.addEventListener("progress",m),a!==null&&s.upload&&s.upload.addEventListener("progress",I)),s.send(a),i.next({type:qn.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",h),t.reportProgress&&(s.removeEventListener("progress",m),a!==null&&s.upload&&s.upload.removeEventListener("progress",I)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(b(Wn))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Ty=new E(""),mS="XSRF-TOKEN",vS=new E("",{providedIn:"root",factory:()=>mS}),yS="X-XSRF-TOKEN",DS=new E("",{providedIn:"root",factory:()=>yS}),fi=class{},ES=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(t,r){this.doc=t,this.cookieName=r}getToken(){let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=ii(t,this.cookieName),this.lastCookieString=t),this.lastToken}static \u0275fac=function(r){return new(r||e)(b(te),b(vS))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function CS(e,n){let t=e.url.toLowerCase();if(!p(Ty)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=p(fi).getToken(),o=p(DS);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),n(e)}var Ef=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Ef||{});function wS(e,n){return{\u0275kind:e,\u0275providers:n}}function Sy(...e){let n=[wy,vf,Ka,{provide:Zr,useExisting:Ka},{provide:li,useFactory:()=>p(uS,{optional:!0})??p(vf)},{provide:yf,useValue:CS,multi:!0},{provide:Ty,useValue:!0},{provide:fi,useClass:ES}];for(let t of e)n.push(...t.\u0275providers);return wr(n)}var yy=new E("");function My(){return wS(Ef.LegacyInterceptors,[{provide:yy,useFactory:fS},{provide:yf,useExisting:yy,multi:!0}])}var IS=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ke({type:e});static \u0275inj=Be({providers:[Sy(My())]})}return e})();var Ay=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(b(te))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var A="primary",Si=Symbol("RouteTitle"),_f=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Qn(e){return new _f(e)}function Ly(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function _S(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!Mt(e[t],n[t]))return!1;return!0}function Mt(e,n){let t=e?Tf(e):void 0,r=n?Tf(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!jy(e[o],n[o]))return!1;return!0}function Tf(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function jy(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function Vy(e){return e.length>0?e[e.length-1]:null}function Zt(e){return au(e)?e:gn(e)?Z(Promise.resolve(e)):_(e)}var TS={exact:Uy,subset:Hy},By={exact:SS,subset:MS,ignored:()=>!0};function Ny(e,n,t){return TS[t.paths](e.root,n.root,t.matrixParams)&&By[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function SS(e,n){return Mt(e,n)}function Uy(e,n,t){if(!Zn(e.segments,n.segments)||!ec(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!Uy(e.children[r],n.children[r],t))return!1;return!0}function MS(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>jy(e[t],n[t]))}function Hy(e,n,t){return $y(e,n,n.segments,t)}function $y(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!Zn(o,t)||n.hasChildren()||!ec(o,t,r))}else if(e.segments.length===t.length){if(!Zn(e.segments,t)||!ec(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!Hy(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!Zn(e.segments,o)||!ec(e.segments,o,r)||!e.children[A]?!1:$y(e.children[A],n,i,r)}}function ec(e,n,t){return n.every((r,o)=>By[t](e[o].parameters,r.parameters))}var Nt=class{root;queryParams;fragment;_queryParamMap;constructor(n=new V([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Qn(this.queryParams),this._queryParamMap}toString(){return RS.serialize(this)}},V=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return tc(this)}},Dn=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Qn(this.parameters),this._parameterMap}toString(){return Gy(this)}};function AS(e,n){return Zn(e,n)&&e.every((t,r)=>Mt(t.parameters,n[r].parameters))}function Zn(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function NS(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===A&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==A&&(t=t.concat(n(o,r)))}),t}var Jn=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new Kn,providedIn:"root"})}return e})(),Kn=class{parse(n){let t=new Mf(n);return new Nt(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${hi(n.root,!0)}`,r=kS(n.queryParams),o=typeof n.fragment=="string"?`#${xS(n.fragment)}`:"";return`${t}${r}${o}`}},RS=new Kn;function tc(e){return e.segments.map(n=>Gy(n)).join("/")}function hi(e,n){if(!e.hasChildren())return tc(e);if(n){let t=e.children[A]?hi(e.children[A],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==A&&r.push(`${o}:${hi(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=NS(e,(r,o)=>o===A?[hi(e.children[A],!1)]:[`${o}:${hi(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[A]!=null?`${tc(e)}/${t[0]}`:`${tc(e)}/(${t.join("//")})`}}function zy(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Ja(e){return zy(e).replace(/%3B/gi,";")}function xS(e){return encodeURI(e)}function Sf(e){return zy(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function nc(e){return decodeURIComponent(e)}function Ry(e){return nc(e.replace(/\+/g,"%20"))}function Gy(e){return`${Sf(e.path)}${OS(e.parameters)}`}function OS(e){return Object.entries(e).map(([n,t])=>`;${Sf(n)}=${Sf(t)}`).join("")}function kS(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${Ja(t)}=${Ja(o)}`).join("&"):`${Ja(t)}=${Ja(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var PS=/^[^\/()?;#]+/;function Cf(e){let n=e.match(PS);return n?n[0]:""}var FS=/^[^\/()?;=#]+/;function LS(e){let n=e.match(FS);return n?n[0]:""}var jS=/^[^=?&#]+/;function VS(e){let n=e.match(jS);return n?n[0]:""}var BS=/^[^&#]+/;function US(e){let n=e.match(BS);return n?n[0]:""}var Mf=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new V([],{}):new V([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[A]=new V(n,t)),r}parseSegment(){let n=Cf(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new y(4009,!1);return this.capture(n),new Dn(nc(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=LS(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=Cf(this.remaining);o&&(r=o,this.capture(r))}n[nc(t)]=nc(r)}parseQueryParam(n){let t=VS(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=US(this.remaining);s&&(r=s,this.capture(r))}let o=Ry(t),i=Ry(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Cf(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new y(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=A);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[A]:new V([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new y(4011,!1)}};function Wy(e){return e.segments.length>0?new V([],{[A]:e}):e}function qy(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=qy(o);if(r===A&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new V(e.segments,n);return HS(t)}function HS(e){if(e.numberOfChildren===1&&e.children[A]){let n=e.children[A];return new V(e.segments.concat(n.segments),n.children)}return e}function En(e){return e instanceof Nt}function Zy(e,n,t=null,r=null){let o=Yy(e);return Qy(o,n,t,r)}function Yy(e){let n;function t(i){let s={};for(let c of i.children){let u=t(c);s[c.outlet]=u}let a=new V(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=Wy(r);return n??o}function Qy(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return wf(o,o,o,t,r);let i=$S(n);if(i.toRoot())return wf(o,o,new V([],{}),t,r);let s=zS(i,o,e),a=s.processChildren?gi(s.segmentGroup,s.index,i.commands):Jy(s.segmentGroup,s.index,i.commands);return wf(o,s.segmentGroup,a,t,r)}function rc(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function yi(e){return typeof e=="object"&&e!=null&&e.outlets}function wf(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===n?s=t:s=Ky(e,n,t);let a=Wy(qy(s));return new Nt(a,i,o)}function Ky(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=Ky(i,n,t)}),new V(e.segments,r)}var oc=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&rc(r[0]))throw new y(4003,!1);let o=r.find(yi);if(o&&o!==Vy(r))throw new y(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function $S(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new oc(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new oc(t,n,r)}var Jr=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function zS(e,n,t){if(e.isAbsolute)return new Jr(n,!0,0);if(!t)return new Jr(n,!1,NaN);if(t.parent===null)return new Jr(t,!0,0);let r=rc(e.commands[0])?0:1,o=t.segments.length-1+r;return GS(t,o,e.numberOfDoubleDots)}function GS(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new y(4005,!1);o=r.segments.length}return new Jr(r,!1,o-i)}function WS(e){return yi(e[0])?e[0].outlets:{[A]:e}}function Jy(e,n,t){if(e??=new V([],{}),e.segments.length===0&&e.hasChildren())return gi(e,n,t);let r=qS(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new V(e.segments.slice(0,r.pathIndex),{});return i.children[A]=new V(e.segments.slice(r.pathIndex),e.children),gi(i,0,o)}else return r.match&&o.length===0?new V(e.segments,{}):r.match&&!e.hasChildren()?Af(e,n,t):r.match?gi(e,0,o):Af(e,n,t)}function gi(e,n,t){if(t.length===0)return new V(e.segments,{});{let r=WS(t),o={};if(Object.keys(r).some(i=>i!==A)&&e.children[A]&&e.numberOfChildren===1&&e.children[A].segments.length===0){let i=gi(e.children[A],n,t);return new V(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=Jy(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new V(e.segments,o)}}function qS(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(yi(a))break;let c=`${a}`,u=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!Oy(c,u,s))return i;r+=2}else{if(!Oy(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Af(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(yi(i)){let c=ZS(i.outlets);return new V(r,c)}if(o===0&&rc(t[0])){let c=e.segments[n];r.push(new Dn(c.path,xy(t[0]))),o++;continue}let s=yi(i)?i.outlets[A]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&rc(a)?(r.push(new Dn(s,xy(a))),o+=2):(r.push(new Dn(s,{})),o++)}return new V(r,{})}function ZS(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=Af(new V([],{}),0,r))}),n}function xy(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function Oy(e,n,t){return e==t.path&&Mt(n,t.parameters)}var mi="imperative",he=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(he||{}),ze=class{id;url;constructor(n,t){this.id=n,this.url=t}},Wt=class extends ze{type=he.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},gt=class extends ze{urlAfterRedirects;type=he.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ne=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(Ne||{}),Di=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Di||{}),At=class extends ze{reason;code;type=he.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},qt=class extends ze{reason;code;type=he.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},eo=class extends ze{error;target;type=he.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},Ei=class extends ze{urlAfterRedirects;state;type=he.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},ic=class extends ze{urlAfterRedirects;state;type=he.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},sc=class extends ze{urlAfterRedirects;state;shouldActivate;type=he.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ac=class extends ze{urlAfterRedirects;state;type=he.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},cc=class extends ze{urlAfterRedirects;state;type=he.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},uc=class{route;type=he.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},lc=class{route;type=he.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},dc=class{snapshot;type=he.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},fc=class{snapshot;type=he.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},hc=class{snapshot;type=he.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},pc=class{snapshot;type=he.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var Ci=class{},to=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function YS(e){return!(e instanceof Ci)&&!(e instanceof to)}function QS(e,n){return e.providers&&!e._injector&&(e._injector=Ur(e.providers,n,`Route: ${e.path}`)),e._injector??n}function pt(e){return e.outlet||A}function KS(e,n){let t=e.filter(r=>pt(r)===n);return t.push(...e.filter(r=>pt(r)!==n)),t}function oo(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var gc=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return oo(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Yt(this.rootInjector)}},Yt=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new gc(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(b(q))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),mc=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=Nf(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=Nf(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=Rf(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return Rf(n,this._root).map(t=>t.value)}};function Nf(e,n){if(e===n.value)return n;for(let t of n.children){let r=Nf(e,t);if(r)return r}return null}function Rf(e,n){if(e===n.value)return[n];for(let t of n.children){let r=Rf(e,t);if(r.length)return r.unshift(n),r}return[]}var $e=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Kr(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var wi=class extends mc{snapshot;constructor(n,t){super(n),this.snapshot=t,Vf(this,n)}toString(){return this.snapshot.toString()}};function Xy(e){let n=JS(e),t=new oe([new Dn("",{})]),r=new oe({}),o=new oe({}),i=new oe({}),s=new oe(""),a=new Le(t,r,i,s,o,A,e,n.root);return a.snapshot=n.root,new wi(new $e(a,[]),n)}function JS(e){let n={},t={},r={},o="",i=new Yn([],n,r,o,t,A,e,null,{});return new Ii("",new $e(i,[]))}var Le=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(R(u=>u[Si]))??_(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(R(n=>Qn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(R(n=>Qn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function vc(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:g(g({},n.params),e.params),data:g(g({},n.data),e.data),resolve:g(g(g(g({},e.data),n.data),o?.data),e._resolvedData)}:r={params:g({},e.params),data:g({},e.data),resolve:g(g({},e.data),e._resolvedData??{})},o&&tD(o)&&(r.resolve[Si]=o.title),r}var Yn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[Si]}constructor(n,t,r,o,i,s,a,c,u){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Qn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Qn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},Ii=class extends mc{url;constructor(n,t){super(t),this.url=n,Vf(this,t)}toString(){return eD(this._root)}};function Vf(e,n){n.value._routerState=e,n.children.forEach(t=>Vf(e,t))}function eD(e){let n=e.children.length>0?` { ${e.children.map(eD).join(", ")} } `:"";return`${e.value}${n}`}function If(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,Mt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),Mt(n.params,t.params)||e.paramsSubject.next(t.params),_S(n.url,t.url)||e.urlSubject.next(t.url),Mt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function xf(e,n){let t=Mt(e.params,n.params)&&AS(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||xf(e.parent,n.parent))}function tD(e){return typeof e.title=="string"||e.title===null}var nD=new E(""),Bf=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=A;activateEvents=new z;deactivateEvents=new z;attachEvents=new z;detachEvents=new z;routerOutletData=Mv(void 0);parentContexts=p(Yt);location=p(Qe);changeDetector=p(ft);inputBinder=p(Cc,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new y(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new y(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new y(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new y(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new Of(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ae]})}return e})(),Of=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===Le?this.route:n===Yt?this.childContexts:n===nD?this.outletData:this.parent.get(n,t)}},Cc=new E("");var Uf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=kd({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&Ra(0,"router-outlet")},dependencies:[Bf],encapsulation:2})}return e})();function Hf(e){let n=e.children&&e.children.map(Hf),t=n?N(g({},e),{children:n}):g({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==A&&(t.component=Uf),t}function XS(e,n,t){let r=bi(e,n._root,t?t._root:void 0);return new wi(r,n)}function bi(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=eM(e,n,t);return new $e(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>bi(e,a)),s}}let r=tM(n.value),o=n.children.map(i=>bi(e,i));return new $e(r,o)}}function eM(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return bi(e,r,o);return bi(e,r)})}function tM(e){return new Le(new oe(e.url),new oe(e.params),new oe(e.queryParams),new oe(e.fragment),new oe(e.data),e.outlet,e.component,e)}var no=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},rD="ngNavigationCancelingError";function yc(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=En(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=oD(!1,Ne.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function oD(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[rD]=!0,t.cancellationCode=n,t}function nM(e){return iD(e)&&En(e.url)}function iD(e){return!!e&&e[rD]}var rM=(e,n,t,r)=>R(o=>(new kf(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),kf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),If(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=Kr(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Kr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Kr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=Kr(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new pc(i.value.snapshot))}),n.children.length&&this.forwardEvent(new fc(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(If(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),If(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Dc=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Xr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function oM(e,n,t){let r=e._root,o=n?n._root:null;return pi(r,o,t,[r.value])}function iM(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function io(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!Pu(e)?e:n.get(e):r}function pi(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Kr(n);return e.children.forEach(s=>{sM(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>vi(a,t.getContext(s),o)),o}function sM(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=aM(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Dc(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?pi(e,n,a?a.children:null,r,o):pi(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Xr(a.outlet.component,s))}else s&&vi(n,a,o),o.canActivateChecks.push(new Dc(r)),i.component?pi(e,null,a?a.children:null,r,o):pi(e,null,t,r,o);return o}function aM(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!Zn(e.url,n.url);case"pathParamsOrQueryParamsChange":return!Zn(e.url,n.url)||!Mt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!xf(e,n)||!Mt(e.queryParams,n.queryParams);case"paramsChange":default:return!xf(e,n)}}function vi(e,n,t){let r=Kr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?vi(s,n.children.getContext(i),t):vi(s,null,t):vi(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Xr(n.outlet.component,o)):t.canDeactivateChecks.push(new Xr(null,o)):t.canDeactivateChecks.push(new Xr(null,o))}function Mi(e){return typeof e=="function"}function cM(e){return typeof e=="boolean"}function uM(e){return e&&Mi(e.canLoad)}function lM(e){return e&&Mi(e.canActivate)}function dM(e){return e&&Mi(e.canActivateChild)}function fM(e){return e&&Mi(e.canDeactivate)}function hM(e){return e&&Mi(e.canMatch)}function sD(e){return e instanceof nt||e?.name==="EmptyError"}var Xa=Symbol("INITIAL_VALUE");function ro(){return ue(e=>dr(e.map(n=>n.pipe(Rt(1),fu(Xa)))).pipe(R(n=>{for(let t of n)if(t!==!0){if(t===Xa)return Xa;if(t===!1||pM(t))return t}return!0}),se(n=>n!==Xa),Rt(1)))}function pM(e){return En(e)||e instanceof no}function gM(e,n){return K(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?_(N(g({},t),{guardsResult:!0})):mM(s,r,o,e).pipe(K(a=>a&&cM(a)?vM(r,i,e,n):_(a)),R(a=>N(g({},t),{guardsResult:a})))})}function mM(e,n,t,r){return Z(e).pipe(K(o=>wM(o.component,o.route,t,n,r)),xt(o=>o!==!0,!0))}function vM(e,n,t,r){return Z(n).pipe(vt(o=>hr(DM(o.route.parent,r),yM(o.route,r),CM(e,o.path,t),EM(e,o.route,t))),xt(o=>o!==!0,!0))}function yM(e,n){return e!==null&&n&&n(new hc(e)),_(!0)}function DM(e,n){return e!==null&&n&&n(new dc(e)),_(!0)}function EM(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return _(!0);let o=r.map(i=>Eo(()=>{let s=oo(n)??t,a=io(i,s),c=lM(a)?a.canActivate(n,e):ve(s,()=>a(n,e));return Zt(c).pipe(xt())}));return _(o).pipe(ro())}function CM(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>iM(s)).filter(s=>s!==null).map(s=>Eo(()=>{let a=s.guards.map(c=>{let u=oo(s.node)??t,l=io(c,u),d=dM(l)?l.canActivateChild(r,e):ve(u,()=>l(r,e));return Zt(d).pipe(xt())});return _(a).pipe(ro())}));return _(i).pipe(ro())}function wM(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return _(!0);let s=i.map(a=>{let c=oo(n)??o,u=io(a,c),l=fM(u)?u.canDeactivate(e,n,t,r):ve(c,()=>u(e,n,t,r));return Zt(l).pipe(xt())});return _(s).pipe(ro())}function IM(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return _(!0);let i=o.map(s=>{let a=io(s,e),c=uM(a)?a.canLoad(n,t):ve(e,()=>a(n,t));return Zt(c)});return _(i).pipe(ro(),aD(r))}function aD(e){return ru(le(n=>{if(typeof n!="boolean")throw yc(e,n)}),R(n=>n===!0))}function bM(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return _(!0);let i=o.map(s=>{let a=io(s,e),c=hM(a)?a.canMatch(n,t):ve(e,()=>a(n,t));return Zt(c)});return _(i).pipe(ro(),aD(r))}var _i=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},Ti=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function Qr(e){return ur(new _i(e))}function _M(e){return ur(new y(4e3,!1))}function TM(e){return ur(oD(!1,Ne.GuardRejected))}var Pf=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return _(r);if(o.numberOfChildren>1||!o.children[A])return _M(`${n.redirectTo}`);o=o.children[A]}}applyRedirectCommands(n,t,r,o,i){return SM(t,o,i).pipe(R(s=>{if(s instanceof Nt)throw new Ti(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),n,r);if(s[0]==="/")throw new Ti(a);return a}))}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new Nt(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new V(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new y(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}};function SM(e,n,t){if(typeof e=="string")return _(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=n;return Zt(ve(t,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var Ff={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function MM(e,n,t,r,o){let i=cD(e,n,t);return i.matched?(r=QS(n,r),bM(r,n,t,o).pipe(R(s=>s===!0?i:g({},Ff)))):_(i)}function cD(e,n,t){if(n.path==="**")return AM(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?g({},Ff):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||Ly)(t,e,n);if(!o)return g({},Ff);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?g(g({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function AM(e){return{matched:!0,parameters:e.length>0?Vy(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function ky(e,n,t,r){return t.length>0&&xM(e,t,r)?{segmentGroup:new V(n,RM(r,new V(t,e.children))),slicedSegments:[]}:t.length===0&&OM(e,t,r)?{segmentGroup:new V(e.segments,NM(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new V(e.segments,e.children),slicedSegments:t}}function NM(e,n,t,r){let o={};for(let i of t)if(wc(e,n,i)&&!r[pt(i)]){let s=new V([],{});o[pt(i)]=s}return g(g({},r),o)}function RM(e,n){let t={};t[A]=n;for(let r of e)if(r.path===""&&pt(r)!==A){let o=new V([],{});t[pt(r)]=o}return t}function xM(e,n,t){return t.some(r=>wc(e,n,r)&&pt(r)!==A)}function OM(e,n,t){return t.some(r=>wc(e,n,r))}function wc(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function kM(e,n,t){return n.length===0&&!e.children[t]}var Lf=class{};function PM(e,n,t,r,o,i,s="emptyOnly"){return new jf(e,n,t,r,o,s,i).recognize()}var FM=31,jf=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new Pf(this.urlSerializer,this.urlTree)}noMatchError(n){return new y(4002,`'${n.segmentGroup}'`)}recognize(){let n=ky(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(R(({children:t,rootSnapshot:r})=>{let o=new $e(r,t),i=new Ii("",o),s=Zy(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new Yn([],Object.freeze({}),Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),A,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,A,t).pipe(R(r=>({children:r,rootSnapshot:t})),mt(r=>{if(r instanceof Ti)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof _i?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(R(s=>s instanceof $e?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return Z(i).pipe(vt(s=>{let a=r.children[s],c=KS(t,s);return this.processSegmentGroup(n,c,a,s,o)}),du((s,a)=>(s.push(...a),s)),en(null),lu(),K(s=>{if(s===null)return Qr(r);let a=uD(s);return LM(a),_(a)}))}processSegment(n,t,r,o,i,s,a){return Z(t).pipe(vt(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(mt(u=>{if(u instanceof _i)return _(null);throw u}))),xt(c=>!!c),mt(c=>{if(sD(c))return kM(r,o,i)?_(new Lf):Qr(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return pt(r)!==s&&(s===A||!wc(o,i,r))?Qr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):Qr(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=cD(t,o,i);if(!c)return Qr(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>FM&&(this.allowRedirects=!1));let f=new Yn(i,u,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Py(o),pt(o),o.component??o._loadedComponent??null,o,Fy(o)),m=vc(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(m.params),f.data=Object.freeze(m.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,n).pipe(ue(D=>this.applyRedirects.lineralizeSegments(o,D)),K(D=>this.processSegment(n,r,t,D.concat(h),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=MM(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(ue(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(ue(({routes:u})=>{let l=r._loadedInjector??n,{parameters:d,consumedSegments:h,remainingSegments:f}=c,m=new Yn(h,d,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Py(r),pt(r),r.component??r._loadedComponent??null,r,Fy(r)),I=vc(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(I.params),m.data=Object.freeze(I.data);let{segmentGroup:D,slicedSegments:C}=ky(t,h,f,u);if(C.length===0&&D.hasChildren())return this.processChildren(l,u,D,m).pipe(R(Re=>new $e(m,Re)));if(u.length===0&&C.length===0)return _(new $e(m,[]));let pe=pt(r)===i;return this.processSegment(l,u,D,C,pe?A:i,!0,m).pipe(R(Re=>new $e(m,Re instanceof $e?[Re]:[])))}))):Qr(t)))}getChildConfig(n,t,r){return t.children?_({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?_({routes:t._loadedRoutes,injector:t._loadedInjector}):IM(n,t,r,this.urlSerializer).pipe(K(o=>o?this.configLoader.loadChildren(n,t).pipe(le(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):TM(t))):_({routes:[],injector:n})}};function LM(e){e.sort((n,t)=>n.value.outlet===A?-1:t.value.outlet===A?1:n.value.outlet.localeCompare(t.value.outlet))}function jM(e){let n=e.value.routeConfig;return n&&n.path===""}function uD(e){let n=[],t=new Set;for(let r of e){if(!jM(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=uD(r.children);n.push(new $e(r.value,o))}return n.filter(r=>!t.has(r))}function Py(e){return e.data||{}}function Fy(e){return e.resolve||{}}function VM(e,n,t,r,o,i){return K(s=>PM(e,n,t,r,s.extractedUrl,o,i).pipe(R(({state:a,tree:c})=>N(g({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function BM(e,n){return K(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return _(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of lD(c))s.add(u);let a=0;return Z(s).pipe(vt(c=>i.has(c)?UM(c,r,e,n):(c.data=vc(c,c.parent,e).resolve,_(void 0))),le(()=>a++),pr(1),K(c=>a===s.size?_(t):xe))})}function lD(e){let n=e.children.map(t=>lD(t)).flat();return[e,...n]}function UM(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!tD(o)&&(i[Si]=o.title),Eo(()=>(e.data=vc(e,e.parent,t).resolve,HM(i,e,n,r).pipe(R(s=>(e._resolvedData=s,e.data=g(g({},e.data),s),null)))))}function HM(e,n,t,r){let o=Tf(e);if(o.length===0)return _({});let i={};return Z(o).pipe(K(s=>$M(e[s],n,t,r).pipe(xt(),le(a=>{if(a instanceof no)throw yc(new Kn,a);i[s]=a}))),pr(1),R(()=>i),mt(s=>sD(s)?xe:ur(s)))}function $M(e,n,t,r){let o=oo(n)??r,i=io(e,o),s=i.resolve?i.resolve(n,t):ve(o,()=>i(n,t));return Zt(s)}function bf(e){return ue(n=>{let t=e(n);return t?Z(t).pipe(R(()=>n)):_(n)})}var $f=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===A);return r}getResolvedTitleForRoute(t){return t.data[Si]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(dD),providedIn:"root"})}return e})(),dD=(()=>{class e extends $f{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(b(Ay))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),so=new E("",{providedIn:"root",factory:()=>({})}),Ai=new E(""),zf=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(Zd);loadComponent(t,r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return _(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let o=Zt(ve(t,()=>r.loadComponent())).pipe(R(hD),le(s=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=s}),tn(()=>{this.componentLoaders.delete(r)})),i=new ar(o,()=>new $).pipe(sr());return this.componentLoaders.set(r,i),i}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return _({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=fD(r,this.compiler,t,this.onLoadEndListener).pipe(tn(()=>{this.childrenLoaders.delete(r)})),s=new ar(i,()=>new $).pipe(sr());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function fD(e,n,t,r){return Zt(ve(t,()=>e.loadChildren())).pipe(R(hD),K(o=>o instanceof Sa||Array.isArray(o)?_(o):Z(n.compileModuleAsync(o))),R(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(Ai,[],{optional:!0,self:!0}).flat()),{routes:s.map(Hf),injector:i}}))}function zM(e){return e&&typeof e=="object"&&"default"in e}function hD(e){return zM(e)?e.default:e}var Ic=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(GM),providedIn:"root"})}return e})(),GM=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),pD=new E("");var gD=new E(""),mD=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new $;transitionAbortWithErrorSubject=new $;configLoader=p(zf);environmentInjector=p(q);destroyRef=p(ct);urlSerializer=p(Jn);rootContexts=p(Yt);location=p(Ht);inputBindingEnabled=p(Cc,{optional:!0})!==null;titleStrategy=p($f);options=p(so,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(Ic);createViewTransition=p(pD,{optional:!0});navigationErrorHandler=p(gD,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>_(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new uc(o)),r=o=>this.events.next(new lc(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(N(g({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(t){return this.transitions=new oe(null),this.transitions.pipe(se(r=>r!==null),ue(r=>{let o=!1;return _(r).pipe(ue(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",Ne.SupersededByNewNavigation),xe;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?N(g({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new qt(i.id,this.urlSerializer.serialize(i.rawUrl),c,Di.IgnoredSameUrlNavigation)),i.resolve(!1),xe}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return _(i).pipe(ue(c=>(this.events.next(new Wt(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?xe:Promise.resolve(c))),VM(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),le(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=N(g({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new Ei(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new Wt(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let m=Xy(this.rootComponentType).snapshot;return this.currentTransition=r=N(g({},i),{targetSnapshot:m,urlAfterRedirects:u,extras:N(g({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,_(r)}else{let c="";return this.events.next(new qt(i.id,this.urlSerializer.serialize(i.extractedUrl),c,Di.IgnoredByUrlHandlingStrategy)),i.resolve(!1),xe}}),le(i=>{let s=new ic(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),R(i=>(this.currentTransition=r=N(g({},i),{guards:oM(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),gM(this.environmentInjector,i=>this.events.next(i)),le(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw yc(this.urlSerializer,i.guardsResult);let s=new sc(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),se(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",Ne.GuardRejected),!1)),bf(i=>{if(i.guards.canActivateChecks.length!==0)return _(i).pipe(le(s=>{let a=new ac(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),ue(s=>{let a=!1;return _(s).pipe(BM(this.paramsInheritanceStrategy,this.environmentInjector),le({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",Ne.NoDataFromResolver)}}))}),le(s=>{let a=new cc(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),bf(i=>{let s=a=>{let c=[];if(a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent){let u=oo(a)??this.environmentInjector;c.push(this.configLoader.loadComponent(u,a.routeConfig).pipe(le(l=>{a.component=l}),R(()=>{})))}for(let u of a.children)c.push(...s(u));return c};return dr(s(i.targetSnapshot.root)).pipe(en(null),Rt(1))}),bf(()=>this.afterPreactivation()),ue(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?Z(a).pipe(R(()=>r)):_(r)}),R(i=>{let s=XS(t.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=N(g({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),le(()=>{this.events.next(new Ci)}),rM(this.rootContexts,t.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),Rt(1),as(new O(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(se(()=>!o&&!r.targetRouterState),le(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",Ne.Aborted)}))),le({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new gt(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),as(this.transitionAbortWithErrorSubject.pipe(le(i=>{throw i}))),tn(()=>{o||this.cancelNavigationTransition(r,"",Ne.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),mt(i=>{if(this.destroyed)return r.resolve(!1),xe;if(o=!0,iD(i))this.events.next(new At(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),nM(i)?this.events.next(new to(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new eo(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=ve(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof no){let{message:c,cancellationCode:u}=yc(this.urlSerializer,a);this.events.next(new At(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new to(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return xe}))}))}cancelNavigationTransition(t,r,o){let i=new At(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function WM(e){return e!==mi}var vD=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(qM),providedIn:"root"})}return e})(),Ec=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},qM=(()=>{class e extends Ec{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),yD=(()=>{class e{urlSerializer=p(Jn);options=p(so,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(Ht);urlHandlingStrategy=p(Ic);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Nt;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof Nt?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=Xy(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(ZM),providedIn:"root"})}return e})(),ZM=(()=>{class e extends yD{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof Wt?this.updateStateMemento():t instanceof qt?this.commitTransition(r):t instanceof Ei?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof Ci?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof At&&t.code!==Ne.SupersededByNewNavigation&&t.code!==Ne.Redirect?this.restoreHistory(r):t instanceof eo?this.restoreHistory(r,!0):t instanceof gt&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=g(g({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=g(g({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Gf(e,n){e.events.pipe(se(t=>t instanceof gt||t instanceof At||t instanceof eo||t instanceof qt),R(t=>t instanceof gt||t instanceof qt?0:(t instanceof At?t.code===Ne.Redirect||t.code===Ne.SupersededByNewNavigation:!1)?2:1),se(t=>t!==2),Rt(1)).subscribe(()=>{n()})}var YM={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},QM={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},Ge=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(Vd);stateManager=p(yD);options=p(so,{optional:!0})||{};pendingTasks=p(bt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(mD);urlSerializer=p(Jn);location=p(Ht);urlHandlingStrategy=p(Ic);injector=p(q);_events=new $;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(vD);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(Ai,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Cc,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new re;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof At&&r.code!==Ne.Redirect&&r.code!==Ne.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof gt)this.navigated=!0;else if(r instanceof to){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=g({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||WM(o.source)},s);this.scheduleNavigation(a,mi,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}YS(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),mi,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=g({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i).catch(c=>{this.disposed||this.injector.get(Pe)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(Hf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=g(g({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=Yy(h)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),d=this.currentUrlTree.root}return Qy(d,t,l,u??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=En(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,mi,null,r)}navigate(t,r={skipLocationChange:!1}){return KM(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=g({},YM):r===!1?o=g({},QM):o=r,En(t))return Ny(this.currentUrlTree,t,o);let i=this.parseUrl(t);return Ny(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return Gf(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function KM(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new y(4008,!1)}var Ri=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=Lt(null);get href(){return Xe(this.reactiveHref)}set href(t){this.reactiveHref.set(t)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new $;applicationErrorHandler=p(Pe);options=p(so,{optional:!0});constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.reactiveHref.set(p(new ka("href"),{optional:!0}));let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let t=this.preserveFragment,r=o=>o==="merge"||o==="preserve";t||=r(this.queryParamsHandling),t||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),t&&(this.subscription=this.router.events.subscribe(o=>{o instanceof gt&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(En(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(u=>{this.applicationErrorHandler(u)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.reactiveHref.set(t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t))??"":null)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:En(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(v(Ge),v(Le),pn("tabindex"),v(Bt),v(Y),v(Ut))};static \u0275dir=F({type:e,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,o){r&1&&Ce("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&mn("href",o.reactiveHref(),hd)("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Gr],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Gr],replaceUrl:[2,"replaceUrl","replaceUrl",Gr],routerLink:"routerLink"},features:[Ae]})}return e})();var Ni=class{},XM=(()=>{class e{preload(t,r){return r().pipe(mt(()=>_(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var DD=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i){this.router=t,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(se(t=>t instanceof gt),vt(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Ur(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return Z(o).pipe(fr())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=_(null);let i=o.pipe(K(s=>s===null?_(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(t,r);return Z([i,s]).pipe(fr())}else return i})}static \u0275fac=function(r){return new(r||e)(b(Ge),b(q),b(Ni),b(zf))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),e0=new E("");function t0(e,...n){return wr([{provide:Ai,multi:!0,useValue:e},[],{provide:Le,useFactory:n0,deps:[Ge]},{provide:Na,multi:!0,useFactory:o0},n.map(t=>t.\u0275providers)])}function n0(e){return e.routerState.root}function r0(e,n){return{\u0275kind:e,\u0275providers:n}}function o0(){let e=p(me);return n=>{let t=e.get(dt);if(n!==t.components[0])return;let r=e.get(Ge),o=e.get(i0);e.get(s0)===1&&r.initialNavigation(),e.get(ED,null,{optional:!0})?.setUpPreloading(),e.get(e0,null,{optional:!0})?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var i0=new E("",{factory:()=>new $}),s0=new E("",{providedIn:"root",factory:()=>1});var ED=new E("");function a0(e){return r0(0,[{provide:ED,useExisting:DD},{provide:Ni,useExisting:e}])}var AD=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(v(Bt),v(Y))};static \u0275dir=F({type:e})}return e})(),kc=(()=>{class e extends AD{static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,features:[Ee]})}return e})(),fo=new E("");var u0={provide:fo,useExisting:Te(()=>ND),multi:!0};function l0(){let e=et()?et().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var d0=new E(""),ND=(()=>{class e extends AD{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!l0())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(v(Bt),v(Y),v(d0,8))};static \u0275dir=F({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&Ce("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[Je([u0]),Ee]})}return e})();function f0(e){return e==null||h0(e)===0}function h0(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var ho=new E(""),Jf=new E("");function p0(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function g0(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function m0(e){return f0(e.value)?{required:!0}:null}function CD(e){return null}function RD(e){return e!=null}function xD(e){return gn(e)?Z(e):e}function OD(e){let n={};return e.forEach(t=>{n=t!=null?g(g({},n),t):n}),Object.keys(n).length===0?null:n}function kD(e,n){return n.map(t=>t(e))}function v0(e){return!e.validate}function PD(e){return e.map(n=>v0(n)?n:t=>n.validate(t))}function y0(e){if(!e)return null;let n=e.filter(RD);return n.length==0?null:function(t){return OD(kD(t,n))}}function FD(e){return e!=null?y0(PD(e)):null}function D0(e){if(!e)return null;let n=e.filter(RD);return n.length==0?null:function(t){let r=kD(t,n).map(xD);return cu(r).pipe(R(OD))}}function LD(e){return e!=null?D0(PD(e)):null}function wD(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function jD(e){return e._rawValidators}function VD(e){return e._rawAsyncValidators}function Wf(e){return e?Array.isArray(e)?e:[e]:[]}function Tc(e,n){return Array.isArray(e)?e.includes(n):e===n}function ID(e,n){let t=Wf(n);return Wf(e).forEach(o=>{Tc(t,o)||t.push(o)}),t}function bD(e,n){return Wf(n).filter(t=>!Tc(e,t))}var Sc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=FD(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=LD(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},uo=class extends Sc{name;get formDirective(){return null}get path(){return null}},Qt=class extends Sc{_parent=null;name=null;valueAccessor=null},Mc=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},E0={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},SG=N(g({},E0),{"[class.ng-submitted]":"isSubmitted"}),MG=(()=>{class e extends Mc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(Qt,2))};static \u0275dir=F({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&Ko("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[Ee]})}return e})(),AG=(()=>{class e extends Mc{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(uo,10))};static \u0275dir=F({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&Ko("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[Ee]})}return e})();var xi="VALID",bc="INVALID",ao="PENDING",Oi="DISABLED",Cn=class{},Ac=class extends Cn{value;source;constructor(n,t){super(),this.value=n,this.source=t}},ki=class extends Cn{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},Pi=class extends Cn{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},co=class extends Cn{status;source;constructor(n,t){super(),this.status=n,this.source=t}},qf=class extends Cn{source;constructor(n){super(),this.source=n}},Zf=class extends Cn{source;constructor(n){super(),this.source=n}};function Xf(e){return(Pc(e)?e.validators:e)||null}function C0(e){return Array.isArray(e)?FD(e):e||null}function eh(e,n){return(Pc(n)?n.asyncValidators:e)||null}function w0(e){return Array.isArray(e)?LD(e):e||null}function Pc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function BD(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new y(1e3,"");if(!r[t])throw new y(1001,"")}function UD(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new y(1002,"")})}var lo=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Xe(this.statusReactive)}set status(n){Xe(()=>this.statusReactive.set(n))}_status=zr(()=>this.statusReactive());statusReactive=Lt(void 0);get valid(){return this.status===xi}get invalid(){return this.status===bc}get pending(){return this.status==ao}get disabled(){return this.status===Oi}get enabled(){return this.status!==Oi}errors;get pristine(){return Xe(this.pristineReactive)}set pristine(n){Xe(()=>this.pristineReactive.set(n))}_pristine=zr(()=>this.pristineReactive());pristineReactive=Lt(!0);get dirty(){return!this.pristine}get touched(){return Xe(this.touchedReactive)}set touched(n){Xe(()=>this.touchedReactive.set(n))}_touched=zr(()=>this.touchedReactive());touchedReactive=Lt(!1);get untouched(){return!this.touched}_events=new $;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(ID(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(ID(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(bD(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(bD(n,this._rawAsyncValidators))}hasValidator(n){return Tc(this._rawValidators,n)}hasAsyncValidator(n){return Tc(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(N(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new Pi(!0,r))}markAllAsDirty(n={}){this.markAsDirty({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsDirty(n))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new Pi(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(N(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new ki(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new ki(!0,r))}markAsPending(n={}){this.status=ao;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new co(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(N(g({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=Oi,this.errors=null,this._forEachChild(o=>{o.disable(N(g({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ac(this.value,r)),this._events.next(new co(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(N(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=xi,this._forEachChild(r=>{r.enable(N(g({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(N(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===xi||this.status===ao)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Ac(this.value,t)),this._events.next(new co(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(N(g({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Oi:xi}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=ao,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1,shouldHaveEmitted:n!==!1};let r=xD(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new co(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new z,this.statusChanges=new z}_calculateStatus(){return this._allControlsDisabled()?Oi:this.errors?bc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(ao)?ao:this._anyControlsHaveStatus(bc)?bc:xi}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new ki(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new Pi(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){Pc(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=C0(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=w0(this._rawAsyncValidators)}},Nc=class extends lo{constructor(n,t,r){super(Xf(t),eh(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){UD(this,!0,n),Object.keys(n).forEach(r=>{BD(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var Yf=class extends Nc{};var Fc=new E("",{providedIn:"root",factory:()=>Lc}),Lc="always";function I0(e,n){return[...n.path,e]}function Qf(e,n,t=Lc){th(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),_0(e,n),S0(e,n),T0(e,n),b0(e,n)}function Rc(e,n,t=!0){let r=()=>{};n.valueAccessor&&(n.valueAccessor.registerOnChange(r),n.valueAccessor.registerOnTouched(r)),Oc(e,n),e&&(n._invokeOnDestroyCallbacks(),e._registerOnCollectionChange(()=>{}))}function xc(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function b0(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function th(e,n){let t=jD(e);n.validator!==null?e.setValidators(wD(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=VD(e);n.asyncValidator!==null?e.setAsyncValidators(wD(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();xc(n._rawValidators,o),xc(n._rawAsyncValidators,o)}function Oc(e,n){let t=!1;if(e!==null){if(n.validator!==null){let o=jD(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.validator);i.length!==o.length&&(t=!0,e.setValidators(i))}}if(n.asyncValidator!==null){let o=VD(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.asyncValidator);i.length!==o.length&&(t=!0,e.setAsyncValidators(i))}}}let r=()=>{};return xc(n._rawValidators,r),xc(n._rawAsyncValidators,r),t}function _0(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&HD(e,n)})}function T0(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&HD(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function HD(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function S0(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function M0(e,n){e==null,th(e,n)}function A0(e,n){return Oc(e,n)}function $D(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function N0(e){return Object.getPrototypeOf(e.constructor)===kc}function R0(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function zD(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===ND?t=i:N0(i)?r=i:o=i}),o||r||t||null}function x0(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function _D(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function TD(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var _c=class extends lo{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(Xf(t),eh(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Pc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(TD(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){_D(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){_D(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){TD(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var O0=e=>e instanceof _c;var RG=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})(),k0={provide:fo,useExisting:Te(()=>P0),multi:!0},P0=(()=>{class e extends kc{writeValue(t){let r=t??"";this.setProperty("value",r)}registerOnChange(t){this.onChange=r=>{t(r==""?null:parseFloat(r))}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(r,o){r&1&&Ce("input",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},standalone:!1,features:[Je([k0]),Ee]})}return e})();var nh=new E(""),F0={provide:Qt,useExisting:Te(()=>L0)},L0=(()=>{class e extends Qt{_ngModelWarningConfig;callSetDisabledState;viewModel;form;set isDisabled(t){}model;update=new z;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(t,r,o,i,s){super(),this._ngModelWarningConfig=i,this.callSetDisabledState=s,this._setValidators(t),this._setAsyncValidators(r),this.valueAccessor=zD(this,o)}ngOnChanges(t){if(this._isControlChanged(t)){let r=t.form.previousValue;r&&Rc(r,this,!1),Qf(this.form,this,this.callSetDisabledState),this.form.updateValueAndValidity({emitEvent:!1})}$D(t,this.viewModel)&&(this.form.setValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.form&&Rc(this.form,this,!1)}get path(){return[]}get control(){return this.form}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}_isControlChanged(t){return t.hasOwnProperty("form")}static \u0275fac=function(r){return new(r||e)(v(ho,10),v(Jf,10),v(fo,10),v(nh,8),v(Fc,8))};static \u0275dir=F({type:e,selectors:[["","formControl",""]],inputs:{form:[0,"formControl","form"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},exportAs:["ngForm"],standalone:!1,features:[Je([F0]),Ee,Ae]})}return e})(),j0={provide:uo,useExisting:Te(()=>V0)},V0=(()=>{class e extends uo{callSetDisabledState;get submitted(){return Xe(this._submittedReactive)}set submitted(t){this._submittedReactive.set(t)}_submitted=zr(()=>this._submittedReactive());_submittedReactive=Lt(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new z;constructor(t,r,o){super(),this.callSetDisabledState=o,this._setValidators(t),this._setAsyncValidators(r)}ngOnChanges(t){t.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(Oc(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(t){let r=this.form.get(t.path);return Qf(r,t,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(t),r}getControl(t){return this.form.get(t.path)}removeControl(t){Rc(t.control||null,t,!1),x0(this.directives,t)}addFormGroup(t){this._setUpFormContainer(t)}removeFormGroup(t){this._cleanUpFormContainer(t)}getFormGroup(t){return this.form.get(t.path)}addFormArray(t){this._setUpFormContainer(t)}removeFormArray(t){this._cleanUpFormContainer(t)}getFormArray(t){return this.form.get(t.path)}updateModel(t,r){this.form.get(t.path).setValue(r)}onSubmit(t){return this._submittedReactive.set(!0),R0(this.form,this.directives),this.ngSubmit.emit(t),this.form._events.next(new qf(this.control)),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0,r={}){this.form.reset(t,r),this._submittedReactive.set(!1),r?.emitEvent!==!1&&this.form._events.next(new Zf(this.form))}_updateDomValue(){this.directives.forEach(t=>{let r=t.control,o=this.form.get(t.path);r!==o&&(Rc(r||null,t),O0(o)&&(Qf(o,t,this.callSetDisabledState),t.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(t){let r=this.form.get(t.path);M0(r,t),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(t){if(this.form){let r=this.form.get(t.path);r&&A0(r,t)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){th(this.form,this),this._oldForm&&Oc(this._oldForm,this)}static \u0275fac=function(r){return new(r||e)(v(ho,10),v(Jf,10),v(Fc,8))};static \u0275dir=F({type:e,selectors:[["","formGroup",""]],hostBindings:function(r,o){r&1&&Ce("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[Je([j0]),Ee,Ae]})}return e})();var B0={provide:Qt,useExisting:Te(()=>U0)},U0=(()=>{class e extends Qt{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(t){}model;update=new z;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(t,r,o,i,s){super(),this._ngModelWarningConfig=s,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=zD(this,i)}ngOnChanges(t){this._added||this._setUpControl(),$D(t,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}get path(){return I0(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(r){return new(r||e)(v(uo,13),v(ho,10),v(Jf,10),v(fo,10),v(nh,8))};static \u0275dir=F({type:e,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[Je([B0]),Ee,Ae]})}return e})();var H0={provide:fo,useExisting:Te(()=>WD),multi:!0};function GD(e,n){return e==null?`${n}`:(n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function $0(e){return e.split(":")[0]}var WD=(()=>{class e extends kc{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;appRefInjector=p(dt).injector;appRefDestroyRef=this.appRefInjector.get(ct);destroyRef=p(ct);cdr=p(ft);_queuedWrite=!1;_writeValueAfterRender(){this._queuedWrite||this.appRefDestroyRef.destroyed||(this._queuedWrite=!0,Aa({write:()=>{this.destroyRef.destroyed||(this._queuedWrite=!1,this.writeValue(this.value))}},{injector:this.appRefInjector}))}writeValue(t){this.cdr.markForCheck(),this.value=t;let r=this._getOptionId(t),o=GD(r,t);this.setProperty("value",o)}registerOnChange(t){this.onChange=r=>{this.value=this._getOptionValue(r),t(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),t))return r;return null}_getOptionValue(t){let r=$0(t);return this._optionMap.has(r)?this._optionMap.get(r):t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,o){r&1&&Ce("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Je([H0]),Ee]})}return e})(),xG=(()=>{class e{_element;_renderer;_select;id;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(t){this._select!=null&&(this._select._optionMap.set(this.id,t),this._setElementValue(GD(this.id,t)),this._select._writeValueAfterRender())}set value(t){this._setElementValue(t),this._select&&this._select._writeValueAfterRender()}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select._writeValueAfterRender())}static \u0275fac=function(r){return new(r||e)(v(Y),v(Bt),v(WD,9))};static \u0275dir=F({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),z0={provide:fo,useExisting:Te(()=>qD),multi:!0};function SD(e,n){return e==null?`${n}`:(typeof n=="string"&&(n=`'${n}'`),n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function G0(e){return e.split(":")[0]}var qD=(()=>{class e extends kc{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){this.value=t;let r;if(Array.isArray(t)){let o=t.map(i=>this._getOptionId(i));r=(i,s)=>{i._setSelected(o.indexOf(s.toString())>-1)}}else r=(o,i)=>{o._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(t){this.onChange=r=>{let o=[],i=r.selectedOptions;if(i!==void 0){let s=i;for(let a=0;a<s.length;a++){let c=s[a],u=this._getOptionValue(c.value);o.push(u)}}else{let s=r.options;for(let a=0;a<s.length;a++){let c=s[a];if(c.selected){let u=this._getOptionValue(c.value);o.push(u)}}}this.value=o,t(o)}}_registerOption(t){let r=(this._idCounter++).toString();return this._optionMap.set(r,t),r}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,t))return r;return null}_getOptionValue(t){let r=G0(t);return this._optionMap.has(r)?this._optionMap.get(r)._value:t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,o){r&1&&Ce("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[Je([z0]),Ee]})}return e})(),OG=(()=>{class e{_element;_renderer;_select;id;_value;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(t){this._select!=null&&(this._value=t,this._setElementValue(SD(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._select?(this._value=t,this._setElementValue(SD(this.id,t)),this._select.writeValue(this._select.value)):this._setElementValue(t)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}_setSelected(t){this._renderer.setProperty(this._element.nativeElement,"selected",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(v(Y),v(Bt),v(qD,9))};static \u0275dir=F({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();function ZD(e){return typeof e=="number"?e:parseFloat(e)}var rh=(()=>{class e{_validator=CD;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):CD,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,features:[Ae]})}return e})(),W0={provide:ho,useExisting:Te(()=>q0),multi:!0},q0=(()=>{class e extends rh{max;inputName="max";normalizeInput=t=>ZD(t);createValidator=t=>g0(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&mn("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[Je([W0]),Ee]})}return e})(),Z0={provide:ho,useExisting:Te(()=>Y0),multi:!0},Y0=(()=>{class e extends rh{min;inputName="min";normalizeInput=t=>ZD(t);createValidator=t=>p0(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&mn("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[Je([Z0]),Ee]})}return e})(),Q0={provide:ho,useExisting:Te(()=>K0),multi:!0};var K0=(()=>{class e extends rh{required;inputName="required";normalizeInput=Gr;createValidator=t=>m0;enabled(t){return t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=Ye(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,o){r&2&&mn("required",o._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[Je([Q0]),Ee]})}return e})();var YD=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ke({type:e});static \u0275inj=Be({})}return e})(),Kf=class extends lo{constructor(n,t,r){super(Xf(t),eh(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(n){return this.controls[this._adjustIndex(n)]}push(n,t={}){this.controls.push(n),this._registerControl(n),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}insert(n,t,r={}){this.controls.splice(n,0,t),this._registerControl(t),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(n,t={}){let r=this._adjustIndex(n);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:t.emitEvent})}setControl(n,t,r={}){let o=this._adjustIndex(n);o<0&&(o=0),this.controls[o]&&this.controls[o]._registerOnCollectionChange(()=>{}),this.controls.splice(o,1),t&&(this.controls.splice(o,0,t),this._registerControl(t)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(n,t={}){UD(this,!1,n),n.forEach((r,o)=>{BD(this,!1,o),this.at(o).setValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(n.forEach((r,o)=>{this.at(o)&&this.at(o).patchValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n=[],t={}){this._forEachChild((r,o)=>{r.reset(n[o],{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this.controls.map(n=>n.getRawValue())}clear(n={}){this.controls.length<1||(this._forEachChild(t=>t._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:n.emitEvent}))}_adjustIndex(n){return n<0?n+this.length:n}_syncPendingControls(){let n=this.controls.reduce((t,r)=>r._syncPendingControls()?!0:t,!1);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){this.controls.forEach((t,r)=>{n(t,r)})}_updateValue(){this.value=this.controls.filter(n=>n.enabled||this.disabled).map(n=>n.value)}_anyControls(n){return this.controls.some(t=>t.enabled&&n(t))}_setUpControls(){this._forEachChild(n=>this._registerControl(n))}_allControlsDisabled(){for(let n of this.controls)if(n.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(n){n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)}_find(n){return this.at(n)??null}};function MD(e){return!!e&&(e.asyncValidators!==void 0||e.validators!==void 0||e.updateOn!==void 0)}var kG=(()=>{class e{useNonNullable=!1;get nonNullable(){let t=new e;return t.useNonNullable=!0,t}group(t,r=null){let o=this._reduceControls(t),i={};return MD(r)?i=r:r!==null&&(i.validators=r.validator,i.asyncValidators=r.asyncValidator),new Nc(o,i)}record(t,r=null){let o=this._reduceControls(t);return new Yf(o,r)}control(t,r,o){let i={};return this.useNonNullable?(MD(r)?i=r:(i.validators=r,i.asyncValidators=o),new _c(t,N(g({},i),{nonNullable:!0}))):new _c(t,r,o)}array(t,r,o){let i=t.map(s=>this._createControl(s));return new Kf(i,r,o)}_reduceControls(t){let r={};return Object.keys(t).forEach(o=>{r[o]=this._createControl(t[o])}),r}_createControl(t){if(t instanceof _c)return t;if(t instanceof lo)return t;if(Array.isArray(t)){let r=t[0],o=t.length>1?t[1]:null,i=t.length>2?t[2]:null;return this.control(r,o,i)}else return this.control(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var PG=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:Fc,useValue:t.callSetDisabledState??Lc}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ke({type:e});static \u0275inj=Be({imports:[YD]})}return e})(),FG=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:nh,useValue:t.warnOnNgModelWithFormControl??"always"},{provide:Fc,useValue:t.callSetDisabledState??Lc}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=Ke({type:e});static \u0275inj=Be({imports:[YD]})}return e})();var VG=(e,n,t,r,o)=>X0(e[1],n[1],t[1],r[1],o).map(i=>J0(e[0],n[0],t[0],r[0],i)),J0=(e,n,t,r,o)=>{let i=3*n*Math.pow(o-1,2),s=-3*t*o+3*t+r*o,a=e*Math.pow(o-1,3);return o*(i+o*s)-a},X0=(e,n,t,r,o)=>(e-=o,n-=o,t-=o,r-=o,tA(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),eA=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},tA=(e,n,t,r)=>{if(e===0)return eA(n,t,r);n/=e,t/=e,r/=e;let o=(3*t-n*n)/3,i=(2*n*n*n-9*n*t+27*r)/27;if(o===0)return[Math.pow(-i,.3333333333333333)];if(i===0)return[Math.sqrt(-o),-Math.sqrt(-o)];let s=Math.pow(i/2,2)+Math.pow(o/3,3);if(s===0)return[Math.pow(i/2,.5)-n/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),.3333333333333333)-Math.pow(i/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(o/3),3)),c=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(o/3),3))))),u=2*Math.pow(a,1/3);return[u*Math.cos(c/3)-n/3,u*Math.cos((c+2*Math.PI)/3)-n/3,u*Math.cos((c+4*Math.PI)/3)-n/3]};var jc=e=>KD(e),go=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),jc(e).includes(n)),KD=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=nA(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},nA=e=>{let n=je.get("platform");return Object.keys(QD).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):QD[t](e)})},rA=e=>Vc(e)&&!XD(e),oh=e=>!!(Xn(e,/iPad/i)||Xn(e,/Macintosh/i)&&Vc(e)),oA=e=>Xn(e,/iPhone/i),iA=e=>Xn(e,/iPhone|iPod/i)||oh(e),JD=e=>Xn(e,/android|sink/i),sA=e=>JD(e)&&!Xn(e,/mobile/i),aA=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},cA=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return oh(e)||sA(e)||r>460&&r<820&&o>780&&o<1400},Vc=e=>fA(e,"(any-pointer:coarse)"),uA=e=>!Vc(e),XD=e=>eE(e)||tE(e),eE=e=>!!(e.cordova||e.phonegap||e.PhoneGap),tE=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},lA=e=>Xn(e,/electron/i),dA=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},Xn=(e,n)=>n.test(e.navigator.userAgent),fA=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},QD={ipad:oh,iphone:oA,ios:iA,android:JD,phablet:aA,tablet:cA,cordova:eE,capacitor:tE,electron:lA,pwa:dA,mobile:Vc,mobileweb:rA,desktop:uA,hybrid:XD},po,ih=e=>e&&Dh(e)||po,hA=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Eh(t)),{persistConfig:!1}),r.config),wh(t)),e);je.reset(o),je.getBoolean("persistConfig")&&Ch(t,o),KD(t),r.config=je,r.mode=po=je.get("mode",n.documentElement.getAttribute("mode")||(go(t,"ios")?"ios":"md")),je.set("mode",po),n.documentElement.setAttribute("mode",po),n.documentElement.classList.add(po),je.getBoolean("_testing")&&je.set("animated",!1);let i=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);yh(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;i(a)&&Bi('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return po})};var pA=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],zG=e=>{let n={};return pA(e).forEach(t=>n[t]=!0),n};var ZG=(e,n,t,r,o,i)=>ce(null,null,function*(){var s;if(e)return e.attachViewToDom(n,t,o,r);if(!i&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),o&&Object.assign(a,o),n.appendChild(a),yield new Promise(c=>Jt(a,c)),a}),YG=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},QG=()=>{let e,n;return{attachViewToDom:(c,u,...l)=>ce(null,[c,u,...l],function*(o,i,s={},a=[]){var d,h;e=o;let f;if(i){let I=typeof i=="string"?(d=e.ownerDocument)===null||d===void 0?void 0:d.createElement(i):i;a.forEach(D=>I.classList.add(D)),Object.assign(I,s),e.appendChild(I),f=I,yield new Promise(D=>Jt(I,D))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let D=(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement("div");D.classList.add("ion-delegate-host"),a.forEach(C=>D.classList.add(C)),D.append(...e.children),e.appendChild(D),f=D}let m=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),m.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var Li='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',nE=(e,n)=>{let t=e.querySelector(Li);sE(t,n??e)},rE=(e,n)=>{let t=Array.from(e.querySelectorAll(Li)),r=t.length>0?t[t.length-1]:null;sE(r,n??e)},sE=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(Li)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():Gc(t)}else n.focus()},sh=0,gA=0,Bc=new WeakMap,aE=e=>({create(t){return yA(e,t)},dismiss(t,r,o){return wA(document,t,r,e,o)},getTop(){return ce(this,null,function*(){return Fi(document,e)})}});var mA=aE("ion-modal");var vA=aE("ion-popover");var i8=e=>{typeof document<"u"&&CA(document);let n=sh++;e.overlayIndex=n},s8=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++gA}`),e.id),yA=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),uE(document).appendChild(t),new Promise(r=>Jt(t,r))}):Promise.resolve(),DA=e=>e.classList.contains("overlay-hidden"),oE=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(Li)||e),t?Gc(t):n.focus()},EA=(e,n)=>{let t=Fi(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(AA))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")oE(t.lastFocus,t);else{let s=Th(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;nE(a,t),c===n.activeElement&&rE(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")oE(t.lastFocus,t);else{let s=t.lastFocus;nE(t),s===n.activeElement&&rE(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},CA=e=>{sh===0&&(sh=1,e.addEventListener("focus",n=>{EA(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=Fi(e);t?.backdropDismiss&&n.detail.register(Mh,()=>{t.dismiss(void 0,iE)})}),Sh()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=Fi(e);t?.backdropDismiss&&t.dismiss(void 0,iE)}}))},wA=(e,n,t,r,o)=>{let i=Fi(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},IA=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),Uc=(e,n)=>IA(e,n).filter(t=>!DA(t)),Fi=(e,n,t)=>{let r=Uc(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},cE=(e=!1)=>{let t=uE(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},a8=(e,n,t,r,o)=>ce(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(cE(!0),document.body.classList.add(Kc)),SA(e.el),dE(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=ih(e),c=e.enterAnimation?e.enterAnimation:je.get(n,a==="ios"?t:r);(yield lE(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&bA(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),bA=e=>ce(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(Li)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),c8=(e,n,t,r,o,i,s)=>ce(null,null,function*(){var a,c;if(!e.presented)return!1;let l=(Kt!==void 0?Uc(Kt):[]).filter(h=>h.tagName!=="ION-TOAST");l.length===1&&l[0].id===e.el.id&&(cE(!1),document.body.classList.remove(Kc)),e.presented=!1;try{dE(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let h=ih(e),f=e.leaveAnimation?e.leaveAnimation:je.get(r,h==="ios"?o:i);t!==TA&&(yield lE(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(Bc.get(e)||[]).forEach(I=>I.destroy()),Bc.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(h){Ih(`[${e.el.tagName.toLowerCase()}] - `,h)}return e.el.remove(),MA(),!0}),uE=e=>e.querySelector("ion-app")||e.body,lE=(e,n,t,r)=>ce(null,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!je.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=Bc.get(e)||[];return Bc.set(e,[...s,i]),yield i.play(),!0}),u8=(e,n)=>{let t,r=new Promise(o=>t=o);return _A(e,n,o=>{t(o.detail)}),r},_A=(e,n,t)=>{let r=o=>{_h(e,n,r),t(o)};bh(e,n,r)};var iE="backdrop",TA="gesture",l8=39;var d8=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){Bi(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let u=()=>{c.present()};return a.addEventListener("click",u),()=>{a.removeEventListener("click",u)}})(i,r)},removeClickListener:n}},dE=e=>{Kt!==void 0&&go("android")&&e.setAttribute("aria-hidden","true")},SA=e=>{var n;if(Kt===void 0)return;let t=Uc(Kt);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},MA=()=>{if(Kt===void 0)return;let e=Uc(Kt);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},AA="ion-disable-focus-trap";var NA=["tabsInner"];var RA=(()=>{class e{doc;_readyPromise;win;backButton=new $;keyboardDidShow=new $;keyboardDidHide=new $;pause=new $;resume=new $;resize=new $;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},mo(this.pause,t,"pause",r),mo(this.resume,t,"resume",r),mo(this.backButton,t,"ionBackButton",r),mo(this.resize,this.win,"resize",r),mo(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),mo(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return go(this.win,t)}platforms(){return jc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return xA(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(b(te),b(H))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),xA=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},mo=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},Vi=(()=>{class e{location;serializer;router;topOutlet;direction=fE;animated=hE;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof Wt){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return ce(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=OA(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=fE,this.animated=hE,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=g({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(b(RA),b(Ht),b(Jn),b(Ge,8))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),OA=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},fE="auto",hE=void 0,vE=(()=>{class e{get(t,r){let o=ah();return o?o.get(t,r):null}getBoolean(t,r){let o=ah();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=ah();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),kA=new E("USERCONFIG"),ah=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},Hc=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},PA=(()=>{class e{zone=p(H);applicationRef=p(dt);config=p(kA);create(t,r,o){return new uh(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),uh=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=g({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=FA(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},FA=(e,n,t,r,o,i,s,a,c,u,l,d)=>{let h=me.create({providers:jA(c),parent:t}),f=Nv(a,{environmentInjector:n,elementInjector:h}),m=f.instance,I=f.location.nativeElement;if(c)if(l&&m[l]!==void 0&&console.error(`[Ionic Error]: ${l} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${l}" property from ${a.name}.`),d===!0&&f.setInput!==void 0){let C=c,{modal:pe,popover:Re}=C,Q=zc(C,["modal","popover"]);for(let er in Q)f.setInput(er,Q[er]);pe!==void 0&&Object.assign(m,{modal:pe}),Re!==void 0&&Object.assign(m,{popover:Re})}else Object.assign(m,c);if(u)for(let pe of u)I.classList.add(pe);let D=yE(e,m,I);return s.appendChild(I),r.attachView(f.hostView),o.set(I,f),i.set(I,D),I},LA=[Wc,qc,Zc,Yc,Qc],yE=(e,n,t)=>e.run(()=>{let r=LA.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),pE=new E("NavParamsToken"),jA=e=>[{provide:pE,useValue:e},{provide:Hc,useFactory:VA,deps:[pE]}],VA=e=>new Hc(e),BA=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},UA=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},vh=(e,n,t)=>{t.forEach(r=>e[r]=Co(n,r))};function $c(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&BA(t,o),i&&UA(t,i),t}}var HA=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],$A=["present","dismiss","onDidDismiss","onWillDismiss"],z8=(()=>{let e=class lh{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),vh(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||lh)(v(ft),v(Y),v(H))};static \u0275dir=F({type:lh,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&Qo(i,lt,5),r&2){let s;Hr(s=$r())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=Do([$c({inputs:HA,methods:$A})],e),e})(),zA=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],GA=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],G8=(()=>{let e=class dh{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),vh(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||dh)(v(ft),v(Y),v(H))};static \u0275dir=F({type:dh,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&Qo(i,lt,5),r&2){let s;Hr(s=$r())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=Do([$c({inputs:zA,methods:GA})],e),e})(),WA=(e,n,t)=>t==="root"?DE(e,n):t==="forward"?qA(e,n):ZA(e,n),DE=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),qA=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),ZA=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):DE(e,n),fh=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},EE=(e,n)=>n?e.stackId!==n.stackId:!0,YA=(e,n)=>{if(!e)return;let t=CE(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},CE=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),wE=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},hh=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?CE(n):void 0}createView(n,t){let r=fh(this.router,t),o=n?.location?.nativeElement,i=yE(this.zone,n.instance,o);return{id:this.nextId++,stackId:YA(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=fh(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=EE(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),u,l=this.router;l.getCurrentNavigation?u=l.getCurrentNavigation():l.navigations?.value&&(u=l.navigations.value),u?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let d=this.views.includes(n),h=this.insertView(n,r);d||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>QA(n,h,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,N(g({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&IE(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(wE),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=WA(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,u=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),u.commit)?u.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return ce(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},QA=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{IE(e,n,t,r,o),i()})}):Promise.resolve(),IE=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(wE)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},KA=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new oe(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=A;stackWillChange=new z;stackDidChange=new z;activateEvents=new z;deactivateEvents=new z;parentContexts=p(Yt);location=p(Qe);environmentInjector=p(q);inputBinder=p(bE,{optional:!0});supportsBindingToComponentInputs=!0;config=p(vE);navCtrl=p(Vi);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,u){this.parentOutlet=u,this.nativeEl=i.nativeElement,this.name=t||A,this.tabsPrefix=r==="true"?fh(s,c):void 0,this.stackCtrl=new hh(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>Jt(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=g({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,u=new oe(null),l=this.createActivatedRouteProxy(u,t),d=new ph(l,c,this.location.injector),h=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(h,{index:this.outletContent.length,injector:d,environmentInjector:r??this.environmentInjector}),u.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,l),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:EE(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new Le;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(se(o=>!!o),ue(o=>this.currentActivatedRoute$.pipe(se(i=>i!==null&&i.component===o),ue(i=>i&&i.activatedRoute[r]),uu())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(pn("name"),pn("tabs"),v(Ht),v(Y),v(Ge),v(H),v(Le),v(e,12))};static \u0275dir=F({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),ph=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Le?this.route:n===Yt?this.childContexts:this.parent.get(n,t)}},bE=new E(""),JA=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=dr([r.queryParams,r.params,r.data]).pipe(ue(([i,s,a],c)=>(a=g(g(g({},i),s),a),c===0?_(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=tf(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),W8=()=>({provide:bE,useFactory:XA,deps:[Ge]});function XA(e){return e?.componentInputBindingEnabled?new JA:null}var eN=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],q8=(()=>{let e=class gh{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||gh)(v(KA,8),v(Vi),v(vE),v(Y),v(H),v(ft))};static \u0275dir=F({type:gh,hostBindings:function(r,o){r&1&&Ce("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=Do([$c({inputs:eN})],e),e})(),Z8=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(v(Ut),v(Vi),v(Y),v(Ge),v(Ri,8))};static \u0275dir=F({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&Ce("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Ae]})}return e})(),Y8=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(v(Ut),v(Vi),v(Y),v(Ge),v(Ri,8))};static \u0275dir=F({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&Ce("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Ae]})}return e})(),tN=["animated","animation","root","rootParams","swipeGesture"],nN=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],Q8=(()=>{let e=class mh{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),vh(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||mh)(v(Y),v(q),v(me),v(PA),v(H),v(ft))};static \u0275dir=F({type:mh,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=Do([$c({inputs:tN,methods:nN})],e),e})(),K8=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new z;ionTabsDidChange=new z;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let u=this.outlet.getRootView(o),l=u&&s===u.url&&u.savedExtras;return this.navCtrl.navigateRoot(s,N(g({},l),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,u=a?.savedExtras;return this.navCtrl.navigateRoot(c,N(g({},u),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(v(Vi))};static \u0275dir=F({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&Wd(NA,7,Y),r&2){let i;Hr(i=$r())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&Ce("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),rN=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),J8=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,ji(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),ji(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),ji(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(Qt)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>ji(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),ji(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(v(me),v(Y))};static \u0275dir=F({type:e,hostBindings:function(r,o){r&1&&Ce("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),ji=e=>{rN(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=oN(n);ch(n,r);let o=n.closest("ion-item");o&&(t?ch(o,[...r,"item-has-value"]):ch(o,r))})},oN=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&iN(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},ch=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},iN=(e,n)=>e.substring(0,n.length)===n,gE=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},mE=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};export{O as a,Do as b,Z as c,ur as d,SE as e,R as f,Co as g,mt as h,Te as i,w as j,Be as k,p as l,wr as m,q as n,Bp as o,Up as p,me as q,te as r,Ye as s,pn as t,Y as u,lm as v,qw as w,v as x,Qe as y,kd as z,Ke as A,F as B,Ee as C,rv as D,H as E,uv as F,mn as G,fv as H,$d as I,zd as J,Ra as K,Gd as L,xa as M,hv as N,pv as O,Xb as P,e_ as Q,Ce as R,s_ as S,c_ as T,u_ as U,Qo as V,Wd as W,Hr as X,$r as Y,d_ as Z,Ko as _,M_ as $,vv as aa,Je as ba,j_ as ca,B_ as da,ft as ea,Ht as fa,Jv as ga,RT as ha,xT as ia,PT as ja,ty as ka,ZT as la,wy as ma,IS as na,Le as oa,vD as pa,Ge as qa,XM as ra,t0 as sa,a0 as ta,VG as ua,go as va,ih as wa,hA as xa,zG as ya,ZG as za,YG as Aa,QG as Ba,nE as Ca,mA as Da,vA as Ea,i8 as Fa,s8 as Ga,a8 as Ha,c8 as Ia,u8 as Ja,iE as Ka,TA as La,l8 as Ma,d8 as Na,AA as Oa,fo as Pa,ND as Qa,ho as Ra,MG as Sa,AG as Ta,_c as Ua,RG as Va,P0 as Wa,L0 as Xa,V0 as Ya,U0 as Za,WD as _a,xG as $a,OG as ab,q0 as bb,Y0 as cb,K0 as db,kG as eb,PG as fb,FG as gb,Vi as hb,vE as ib,kA as jb,PA as kb,$c as lb,z8 as mb,G8 as nb,KA as ob,W8 as pb,q8 as qb,Z8 as rb,Y8 as sb,Q8 as tb,K8 as ub,rN as vb,J8 as wb,ji as xb,gE as yb,mE as zb};
