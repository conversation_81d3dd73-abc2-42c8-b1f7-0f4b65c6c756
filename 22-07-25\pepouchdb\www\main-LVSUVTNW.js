import{Aa as re,Ba as Ee,C as ft,Ca as Nt,Da as Lt,<PERSON> as Ve,Ea as $t,F as ht,Fa as xe,Ga as ke,Ha as Se,I as Ge,Ia as je,J as qe,Ja as se,K as gt,Ka as Te,La as ge,Ma as _t,Na as Ht,O as vt,Oa as ae,P as It,T as Xe,U as Ue,W as bt,X as yt,Y as Ct,b as We,ea as Dt,fa as wt,j as dt,jb as Je,kb as Ae,l as te,la as Et,lb as Wt,m as pt,n as Ie,oa as xt,ob as Yt,pa as kt,pb as Zt,q as be,qa as St,r as mt,ra as jt,sa as Tt,t as Ye,ta as At,u as Ze,ua as ne,va as q,wa as O,x as W,xa as Ot,y as ut,ya as we,yb as et,z as ue,za as ie,zb as tt}from"./chunk-UPGPZ5HH.js";import{a as ce,c as Vt,d as Me,g as Gt,h as nt,i as qt}from"./chunk-JCWJHBB7.js";import"./chunk-GVQ3LBRF.js";import"./chunk-JSXXIFUV.js";import{a as d,g as zt,h as ye,i as Ce}from"./chunk-B7SFH74S.js";import{a as Mt,b as Y,d as Ke,e as R,f as K,i as k,j as Z}from"./chunk-UOV5QIVR.js";import{a as Qe}from"./chunk-GIIU5PV3.js";import{c as De}from"./chunk-M2X7KQLB.js";import{a as ot}from"./chunk-2YSZFPCQ.js";import{a as Xt}from"./chunk-57YRIO75.js";import{b as oe,c as Rt,d as Ft,f as T,g as V,i as he,k as Bt}from"./chunk-XTVTS2NW.js";import{a as $,e as Q,f as Pt}from"./chunk-C5RQ2IC2.js";import{a as fe}from"./chunk-42C7ZIID.js";import{a as _e,b as He,g as b}from"./chunk-2R6CW7ES.js";var le=()=>{let o;return{lock:()=>b(null,null,function*(){let t=o,n;return o=new Promise(i=>n=i),t!==void 0&&(yield t),n})}};var wn=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",En=Z(class extends Y{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=k(this,"ionNavWillLoad",7),this.ionNavWillChange=k(this,"ionNavWillChange",3),this.ionNavDidChange=k(this,"ionNavDidChange",3),this.lockController=le(),this.gestureOrAnimationInProgress=!1,this.mode=O(this),this.animated=!0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}connectedCallback(){return b(this,null,function*(){let e=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(yield import("./chunk-F4GC3DXV.js")).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>e(),t=>{var n;return(n=this.ani)===null||n===void 0?void 0:n.progressStep(t)},(t,n,i)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(t)},{oneTimeCallback:!0});let r=t?-.001:.001;t?r+=ne([0,0],[.32,.72],[0,1],[1,1],n)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),r+=ne([0,0],[1,0],[.68,.28],[1,1],n)[0]),this.ani.progressEnd(t?1:0,r,i)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()})}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}commit(e,t,n){return b(this,null,function*(){let i=yield this.lockController.lock(),r=!1;try{r=yield this.transition(e,t,n)}catch(c){Pt("[ion-router-outlet] - Exception in commit:",c)}return i(),r})}setRouteId(e,t,n,i){return b(this,null,function*(){return{changed:yield this.setRoot(e,t,{duration:n==="root"?0:void 0,direction:n==="back"?"back":"forward",animationBuilder:i}),element:this.activeEl}})}getRouteId(){return b(this,null,function*(){let e=this.activeEl;return e?{id:e.tagName,element:e,params:this.activeParams}:void 0})}setRoot(e,t,n){return b(this,null,function*(){if(this.activeComponent===e&&Bt(t,this.activeParams))return!1;let i=this.activeEl,r=yield ie(this.delegate,this.el,e,["ion-page","ion-page-invisible"],t);return this.activeComponent=e,this.activeEl=r,this.activeParams=t,yield this.commit(r,i,n),yield re(this.delegate,i),!0})}transition(i,r){return b(this,arguments,function*(e,t,n={}){if(t===e)return!1;this.ionNavWillChange.emit();let{el:c,mode:s}=this,a=this.animated&&$.getBoolean("animated",!0),l=n.animationBuilder||this.animation||$.get("navAnimation");return yield zt(Object.assign(Object.assign({mode:s,animated:a,enteringEl:e,leavingEl:t,baseEl:c,deepWait:oe(c),progressCallback:n.progressAnimation?m=>{m!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,m.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),m.progressEnd(0,0,0)):this.ani=m}:void 0},n),{animationBuilder:l})),this.ionNavDidChange.emit(),!0})}render(){return R("slot",{key:"84b50f1155b0d780dff802ee13223287259fd525"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return wn}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16,"swipe-handler"],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);function xn(){if(typeof customElements>"u")return;["ion-router-outlet"].forEach(e=>{switch(e){case"ion-router-outlet":customElements.get(e)||customElements.define(e,En);break}})}var Ut=xn;var kn=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",Sn=":host{left:0;right:0;top:0;bottom:0;display:block;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);contain:strict;cursor:pointer;opacity:0.01;-ms-touch-action:none;touch-action:none;z-index:2}:host(.backdrop-hide){background:transparent}:host(.backdrop-no-tappable){cursor:auto}:host{background-color:var(--ion-backdrop-color, #000)}",jn=Z(class extends Y{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionBackdropTap=k(this,"ionBackdropTap",7),this.visible=!0,this.tappable=!0,this.stopPropagation=!0}onMouseDown(e){this.emitTap(e)}emitTap(e){this.stopPropagation&&(e.preventDefault(),e.stopPropagation()),this.tappable&&this.ionBackdropTap.emit()}render(){let e=O(this);return R(K,{key:"7abaf2c310aa399607451b14063265e8a5846938","aria-hidden":"true",class:{[e]:!0,"backdrop-hide":!this.visible,"backdrop-no-tappable":!this.tappable}})}static get style(){return{ios:kn,md:Sn}}},[33,"ion-backdrop",{visible:[4],tappable:[4],stopPropagation:[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]);function Pe(){if(typeof customElements>"u")return;["ion-backdrop"].forEach(e=>{switch(e){case"ion-backdrop":customElements.get(e)||customElements.define(e,jn);break}})}var ve=function(o){return o.Dark="DARK",o.Light="LIGHT",o.Default="DEFAULT",o}(ve||{}),at={getEngine(){let o=Xt();if(o?.isPluginAvailable("StatusBar"))return o.Plugins.StatusBar},setStyle(o){let e=this.getEngine();e&&e.setStyle(o)},getStyle:function(){return b(this,null,function*(){let o=this.getEngine();if(!o)return ve.Default;let{style:e}=yield o.getInfo();return e})}},it=(o,e)=>{if(e===1)return 0;let t=1/(1-e),n=-(e*t);return o*t+n},en=()=>{!fe||fe.innerWidth>=768||at.setStyle({style:ve.Dark})},rt=(o=ve.Default)=>{!fe||fe.innerWidth>=768||at.setStyle({style:o})},tn=(o,e)=>b(null,null,function*(){typeof o.canDismiss!="function"||!(yield o.canDismiss(void 0,ge))||(e.isRunning()?e.onFinish(()=>{o.dismiss(void 0,"handler")},{oneTimeCallback:!0}):o.dismiss(void 0,"handler"))}),st=o=>.00255275*2.71828**(-14.9619*o)-1.00255*2.71828**(-.0380968*o)+1,X={MIN_PRESENTING_SCALE:.915},Tn=(o,e,t,n)=>{let r=o.offsetHeight,c=!1,s=!1,a=null,l=null,m=.2,p=!0,f=0,g=()=>a&&ce(a)?a.scrollY:!0,A=De({el:o,gestureName:"modalSwipeToClose",gesturePriority:_t,direction:"y",threshold:10,canStart:D=>{let y=D.event.target;return y===null||!y.closest?!0:(a=Me(y),a?(ce(a)?l=T(a).querySelector(".inner-scroll"):l=a,!!!a.querySelector("ion-refresher")&&l.scrollTop===0):y.closest("ion-footer")===null)},onStart:D=>{let{deltaY:y}=D;p=g(),s=o.canDismiss!==void 0&&o.canDismiss!==!0,y>0&&a&&nt(a),e.progressStart(!0,c?1:0)},onMove:D=>{let{deltaY:y}=D;y>0&&a&&nt(a);let j=D.deltaY/r,v=j>=0&&s,F=v?m:.9999,z=v?st(j/F):j,B=he(1e-4,z,F);e.progressStep(B),B>=.5&&f<.5?rt(t):B<.5&&f>=.5&&en(),f=B},onEnd:D=>{let y=D.velocityY,j=D.deltaY/r,v=j>=0&&s,F=v?m:.9999,z=v?st(j/F):j,B=he(1e-4,z,F),L=(D.deltaY+y*1e3)/r,M=!v&&L>=.5,P=M?-.001:.001;M?(e.easing("cubic-bezier(0.32, 0.72, 0, 1)"),P+=ne([0,0],[.32,.72],[0,1],[1,1],B)[0]):(e.easing("cubic-bezier(1, 0, 0.68, 0.28)"),P+=ne([0,0],[1,0],[.68,.28],[1,1],B)[0]);let H=Kt(M?j*r:(1-B)*r,y);c=M,A.enable(!1),a&&qt(a,p),e.onFinish(()=>{M||A.enable(!0)}).progressEnd(M?1:0,P,H),v&&B>F/4?tn(o,e):M&&n()}});return A},Kt=(o,e)=>he(400,o/Math.abs(e*1.1),500),nn=o=>{let{currentBreakpoint:e,backdropBreakpoint:t,expandToScroll:n}=o,i=t===void 0||t<e,r=i?`calc(var(--backdrop-opacity) * ${e})`:"0",c=d("backdropAnimation").fromTo("opacity",0,r);i&&c.beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]);let s=d("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:"translateY(100%)"},{offset:1,opacity:1,transform:`translateY(${100-e*100}%)`}]),a=n?void 0:d("contentAnimation").keyframes([{offset:0,opacity:1,maxHeight:`${(1-e)*100}%`},{offset:1,opacity:1,maxHeight:`${e*100}%`}]);return{wrapperAnimation:s,backdropAnimation:c,contentAnimation:a}},on=o=>{let{currentBreakpoint:e,backdropBreakpoint:t}=o,n=`calc(var(--backdrop-opacity) * ${it(e,t)})`,i=[{offset:0,opacity:n},{offset:1,opacity:0}],r=[{offset:0,opacity:n},{offset:t,opacity:0},{offset:1,opacity:0}],c=d("backdropAnimation").keyframes(t!==0?r:i);return{wrapperAnimation:d("wrapperAnimation").keyframes([{offset:0,opacity:1,transform:`translateY(${100-e*100}%)`},{offset:1,opacity:1,transform:"translateY(100%)"}]),backdropAnimation:c}},An=()=>{let o=d().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=d().fromTo("transform","translateY(100vh)","translateY(0vh)");return{backdropAnimation:o,wrapperAnimation:e,contentAnimation:void 0}},Qt=(o,e)=>{let{presentingEl:t,currentBreakpoint:n,expandToScroll:i}=e,r=T(o),{wrapperAnimation:c,backdropAnimation:s,contentAnimation:a}=n!==void 0?nn(e):An();s.addElement(r.querySelector("ion-backdrop")),c.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1}),!i&&a?.addElement(o.querySelector(".ion-page"));let l=d("entering-base").addElement(o).easing("cubic-bezier(0.32,0.72,0,1)").duration(500).addAnimation([c]);if(a&&l.addAnimation(a),t){let m=window.innerWidth<768,p=t.tagName==="ION-MODAL"&&t.presentingElement!==void 0,f=T(t),g=d().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"}),u=document.body;if(m){let S=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",h=p?"-10px":S,x=X.MIN_PRESENTING_SCALE,A=`translateY(${h}) scale(${x})`;g.afterStyles({transform:A}).beforeAddWrite(()=>u.style.setProperty("background-color","black")).addElement(t).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"},{offset:1,filter:"contrast(0.85)",transform:A,borderRadius:"10px 10px 0 0"}]),l.addAnimation(g)}else if(l.addAnimation(s),!p)c.fromTo("opacity","0","1");else{let h=`translateY(-10px) scale(${p?X.MIN_PRESENTING_SCALE:1})`;g.afterStyles({transform:h}).addElement(f.querySelector(".modal-wrapper")).keyframes([{offset:0,filter:"contrast(1)",transform:"translateY(0) scale(1)"},{offset:1,filter:"contrast(0.85)",transform:h}]);let x=d().afterStyles({transform:h}).addElement(f.querySelector(".modal-shadow")).keyframes([{offset:0,opacity:"1",transform:"translateY(0) scale(1)"},{offset:1,opacity:"0",transform:h}]);l.addAnimation([g,x])}}else l.addAnimation(s);return l},Mn=()=>{let o=d().fromTo("opacity","var(--backdrop-opacity)",0),e=d().fromTo("transform","translateY(0vh)","translateY(100vh)");return{backdropAnimation:o,wrapperAnimation:e}},Jt=(o,e,t=500)=>{let{presentingEl:n,currentBreakpoint:i}=e,r=T(o),{wrapperAnimation:c,backdropAnimation:s}=i!==void 0?on(e):Mn();s.addElement(r.querySelector("ion-backdrop")),c.addElement(r.querySelectorAll(".modal-wrapper, .modal-shadow")).beforeStyles({opacity:1});let a=d("leaving-base").addElement(o).easing("cubic-bezier(0.32,0.72,0,1)").duration(t).addAnimation(c);if(n){let l=window.innerWidth<768,m=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,p=T(n),f=d().beforeClearStyles(["transform"]).afterClearStyles(["transform"]).onFinish(u=>{if(u!==1)return;n.style.setProperty("overflow",""),Array.from(g.querySelectorAll("ion-modal:not(.overlay-hidden)")).filter(h=>h.presentingElement!==void 0).length<=1&&g.style.setProperty("background-color","")}),g=document.body;if(l){let u=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",S=m?"-10px":u,h=X.MIN_PRESENTING_SCALE,x=`translateY(${S}) scale(${h})`;f.addElement(n).keyframes([{offset:0,filter:"contrast(0.85)",transform:x,borderRadius:"10px 10px 0 0"},{offset:1,filter:"contrast(1)",transform:"translateY(0px) scale(1)",borderRadius:"0px"}]),a.addAnimation(f)}else if(a.addAnimation(s),!m)c.fromTo("opacity","1","0");else{let S=`translateY(-10px) scale(${m?X.MIN_PRESENTING_SCALE:1})`;f.addElement(p.querySelector(".modal-wrapper")).afterStyles({transform:"translate3d(0, 0, 0)"}).keyframes([{offset:0,filter:"contrast(0.85)",transform:S},{offset:1,filter:"contrast(1)",transform:"translateY(0) scale(1)"}]);let h=d().addElement(p.querySelector(".modal-shadow")).afterStyles({transform:"translateY(0) scale(1)"}).keyframes([{offset:0,opacity:"0",transform:S},{offset:1,opacity:"1",transform:"translateY(0) scale(1)"}]);a.addAnimation([f,h])}}else a.addAnimation(s);return a},Pn=(o,e,t=300)=>{let{presentingEl:n}=e;if(!n)return d("portrait-to-landscape-transition");let i=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,r=T(n),c=document.body,s=d("portrait-to-landscape-transition").addElement(o).easing("cubic-bezier(0.32,0.72,0,1)").duration(t),a=d().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(i){let m=`translateY(-10px) scale(${X.MIN_PRESENTING_SCALE})`,p="translateY(0px) scale(1)";a.addElement(n).afterStyles({transform:p}).fromTo("transform",m,p).fromTo("filter","contrast(0.85)","contrast(1)");let f=d().addElement(r.querySelector(".modal-shadow")).afterStyles({transform:p,opacity:"0"}).fromTo("transform",m,p);s.addAnimation([a,f])}else{let l=T(o),m=d().addElement(l.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),p=d().addElement(l.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),f=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",g=X.MIN_PRESENTING_SCALE,u=`translateY(${f}) scale(${g})`;a.addElement(n).afterStyles({transform:"translateY(0px) scale(1)","border-radius":"0px"}).beforeAddWrite(()=>c.style.setProperty("background-color","")).fromTo("transform",u,"translateY(0px) scale(1)").fromTo("filter","contrast(0.85)","contrast(1)").fromTo("border-radius","10px 10px 0 0","0px"),s.addAnimation([a,m,p])}return s},Rn=(o,e,t=300)=>{let{presentingEl:n}=e;if(!n)return d("landscape-to-portrait-transition");let i=n.tagName==="ION-MODAL"&&n.presentingElement!==void 0,r=T(n),c=document.body,s=d("landscape-to-portrait-transition").addElement(o).easing("cubic-bezier(0.32,0.72,0,1)").duration(t),a=d().beforeStyles({transform:"translateY(0)","transform-origin":"top center",overflow:"hidden"});if(i){let m=`translateY(-10px) scale(${X.MIN_PRESENTING_SCALE})`,p="translateY(0) scale(1)";a.addElement(n).afterStyles({transform:p}).fromTo("transform",m,p);let f=d().addElement(r.querySelector(".modal-shadow")).afterStyles({transform:p,opacity:"0"}).fromTo("transform",m,p);s.addAnimation([a,f])}else{let l=T(o),m=d().addElement(l.querySelectorAll(".modal-wrapper, .modal-shadow")).fromTo("opacity","1","1"),p=d().addElement(l.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)","var(--backdrop-opacity)"),f=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",g=X.MIN_PRESENTING_SCALE,u=`translateY(${f}) scale(${g})`;a.addElement(n).afterStyles({transform:u}).beforeAddWrite(()=>c.style.setProperty("background-color","black")).keyframes([{offset:0,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"0px"},{offset:.2,transform:"translateY(0px) scale(1)",filter:"contrast(1)",borderRadius:"10px 10px 0 0"},{offset:1,transform:u,filter:"contrast(0.85)",borderRadius:"10px 10px 0 0"}]),s.addAnimation([a,m,p])}return s},Fn=()=>{let o=d().fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),e=d().keyframes([{offset:0,opacity:.01,transform:"translateY(40px)"},{offset:1,opacity:1,transform:"translateY(0px)"}]);return{backdropAnimation:o,wrapperAnimation:e,contentAnimation:void 0}},Bn=(o,e)=>{let{currentBreakpoint:t,expandToScroll:n}=e,i=T(o),{wrapperAnimation:r,backdropAnimation:c,contentAnimation:s}=t!==void 0?nn(e):Fn();c.addElement(i.querySelector("ion-backdrop")),r.addElement(i.querySelector(".modal-wrapper")),!n&&s?.addElement(o.querySelector(".ion-page"));let a=d().addElement(o).easing("cubic-bezier(0.36,0.66,0.04,1)").duration(280).addAnimation([c,r]);return s&&a.addAnimation(s),a},zn=()=>{let o=d().fromTo("opacity","var(--backdrop-opacity)",0),e=d().keyframes([{offset:0,opacity:.99,transform:"translateY(0px)"},{offset:1,opacity:0,transform:"translateY(40px)"}]);return{backdropAnimation:o,wrapperAnimation:e}},On=(o,e)=>{let{currentBreakpoint:t}=e,n=T(o),{wrapperAnimation:i,backdropAnimation:r}=t!==void 0?on(e):zn();return r.addElement(n.querySelector("ion-backdrop")),i.addElement(n.querySelector(".modal-wrapper")),d().easing("cubic-bezier(0.47,0,0.745,0.715)").duration(200).addAnimation([r,i])},Nn=(o,e,t,n,i,r,c=[],s,a,l,m)=>{let p=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1,opacity:.01}],f=[{offset:0,opacity:"var(--backdrop-opacity)"},{offset:1-i,opacity:0},{offset:1,opacity:0}],g={WRAPPER_KEYFRAMES:[{offset:0,transform:"translateY(0%)"},{offset:1,transform:"translateY(100%)"}],BACKDROP_KEYFRAMES:i!==0?f:p,CONTENT_KEYFRAMES:[{offset:0,maxHeight:"100%"},{offset:1,maxHeight:"0%"}]},u=o.querySelector("ion-content"),S=t.clientHeight,h=n,x=0,A=!1,D=null,y=null,j=null,v=null,F=.95,z=c[c.length-1],B=c[0],L=r.childAnimations.find(w=>w.id==="wrapperAnimation"),M=r.childAnimations.find(w=>w.id==="backdropAnimation"),P=r.childAnimations.find(w=>w.id==="contentAnimation"),H=()=>{o.style.setProperty("pointer-events","auto"),e.style.setProperty("pointer-events","auto"),o.classList.remove(ae)},J=()=>{o.style.setProperty("pointer-events","none"),e.style.setProperty("pointer-events","none"),o.classList.add(ae)},U=w=>{if(!y&&(y=Array.from(o.querySelectorAll("ion-footer")),!y.length))return;let I=o.querySelector(".ion-page");if(v=w,w==="stationary")y.forEach(E=>{E.classList.remove("modal-footer-moving"),E.style.removeProperty("position"),E.style.removeProperty("width"),E.style.removeProperty("height"),E.style.removeProperty("top"),E.style.removeProperty("left"),I?.style.removeProperty("padding-bottom"),I?.appendChild(E)});else{let E=0;y.forEach((C,G)=>{let _=C.getBoundingClientRect(),N=document.body.getBoundingClientRect();E+=C.clientHeight;let pe=_.top-N.top,me=_.left-N.left;if(C.style.setProperty("--pinned-width",`${C.clientWidth}px`),C.style.setProperty("--pinned-height",`${C.clientHeight}px`),C.style.setProperty("--pinned-top",`${pe}px`),C.style.setProperty("--pinned-left",`${me}px`),G===0){j=pe;let $e=o.querySelector("ion-header");$e&&(j-=$e.clientHeight)}}),y.forEach(C=>{I?.style.setProperty("padding-bottom",`${E}px`),C.classList.add("modal-footer-moving"),C.style.setProperty("position","absolute"),C.style.setProperty("width","var(--pinned-width)"),C.style.setProperty("height","var(--pinned-height)"),C.style.setProperty("top","var(--pinned-top)"),C.style.setProperty("left","var(--pinned-left)"),document.body.appendChild(C)})}};L&&M&&(L.keyframes([...g.WRAPPER_KEYFRAMES]),M.keyframes([...g.BACKDROP_KEYFRAMES]),P?.keyframes([...g.CONTENT_KEYFRAMES]),r.progressStart(!0,1-h),h>i?H():J()),u&&h!==z&&s&&(u.scrollY=!1);let ze=w=>{let I=Me(w.event.target);if(h=a(),!s&&I)return(ce(I)?T(I).querySelector(".inner-scroll"):I).scrollTop===0;if(h===1&&I){let E=ce(I)?T(I).querySelector(".inner-scroll"):I;return!!!I.querySelector("ion-refresher")&&E.scrollTop===0}return!0},Oe=w=>{if(A=o.canDismiss!==void 0&&o.canDismiss!==!0&&B===0,!s){let I=Me(w.event.target);D=I&&ce(I)?T(I).querySelector(".inner-scroll"):I}s||U("moving"),w.deltaY>0&&u&&(u.scrollY=!1),V(()=>{o.focus()}),r.progressStart(!0,1-h)},Ne=w=>{if(!s&&j!==null&&v!==null&&(w.currentY>=j&&v==="moving"?U("stationary"):w.currentY<j&&v==="stationary"&&U("moving")),!s&&w.deltaY<=0&&D)return;w.deltaY>0&&u&&(u.scrollY=!1);let I=1-h,E=c.length>1?1-c[1]:void 0,C=I+w.deltaY/S,G=E!==void 0&&C>=E&&A,_=G?F:.9999,N=G&&E!==void 0?E+st((C-E)/(_-E)):C;x=he(1e-4,N,_),r.progressStep(x)},Le=w=>{if(!s&&w.deltaY<=0&&D&&D.scrollTop>0){U("stationary");return}let I=w.velocityY,E=(w.deltaY+I*350)/S,C=h-E,G=c.reduce((_,N)=>Math.abs(N-C)<Math.abs(_-C)?N:_);de({breakpoint:G,breakpointOffset:x,canDismiss:A,animated:!0})},de=w=>{let{breakpoint:I,canDismiss:E,breakpointOffset:C,animated:G}=w,_=E&&I===0,N=_?h:I,pe=N!==0;return h=0,L&&M&&(L.keyframes([{offset:0,transform:`translateY(${C*100}%)`},{offset:1,transform:`translateY(${(1-N)*100}%)`}]),M.keyframes([{offset:0,opacity:`calc(var(--backdrop-opacity) * ${it(1-C,i)})`},{offset:1,opacity:`calc(var(--backdrop-opacity) * ${it(N,i)})`}]),P&&P.keyframes([{offset:0,maxHeight:`${(1-C)*100}%`},{offset:1,maxHeight:`${N*100}%`}]),r.progressStep(0)),ee.enable(!1),_?tn(o,r):pe||l(),u&&(N===c[c.length-1]||!s)&&(u.scrollY=!0),!s&&N===0&&U("stationary"),new Promise(me=>{r.onFinish(()=>{pe?(s||U("stationary"),L&&M?V(()=>{L.keyframes([...g.WRAPPER_KEYFRAMES]),M.keyframes([...g.BACKDROP_KEYFRAMES]),P?.keyframes([...g.CONTENT_KEYFRAMES]),r.progressStart(!0,1-N),h=N,m(h),h>i?H():J(),ee.enable(!0),me()}):(ee.enable(!0),me())):me()},{oneTimeCallback:!0}).progressEnd(1,0,G?500:0)})},ee=De({el:t,gestureName:"modalSheet",gesturePriority:40,direction:"y",threshold:10,canStart:ze,onStart:Oe,onMove:Ne,onEnd:Le});return{gesture:ee,moveSheetToBreakpoint:de}},Ln=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.4)}:host(.modal-card),:host(.modal-sheet){--border-radius:10px}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:10px}}.modal-wrapper{-webkit-transform:translate3d(0,  100%,  0);transform:translate3d(0,  100%,  0)}@media screen and (max-width: 767px){@supports (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - max(30px, var(--ion-safe-area-top)) - 10px)}}@supports not (width: max(0px, 1px)){:host(.modal-card){--height:calc(100% - 40px)}}:host(.modal-card) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}:host(.modal-card){--backdrop-opacity:0;--width:100%;-ms-flex-align:end;align-items:flex-end}:host(.modal-card) .modal-shadow{display:none}:host(.modal-card) ion-backdrop{pointer-events:none}}@media screen and (min-width: 768px){:host(.modal-card){--width:calc(100% - 120px);--height:calc(100% - (120px + var(--ion-safe-area-top) + var(--ion-safe-area-bottom)));--max-width:720px;--max-height:1000px;--backdrop-opacity:0;--box-shadow:0px 0px 30px 10px rgba(0, 0, 0, 0.1);-webkit-transition:all 0.5s ease-in-out;transition:all 0.5s ease-in-out}:host(.modal-card) .modal-wrapper{-webkit-box-shadow:none;box-shadow:none}:host(.modal-card) .modal-shadow{-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow)}}:host(.modal-sheet) .modal-wrapper{border-start-start-radius:var(--border-radius);border-start-end-radius:var(--border-radius);border-end-end-radius:0;border-end-start-radius:0}',$n=':host{--width:100%;--min-width:auto;--max-width:auto;--height:100%;--min-height:auto;--max-height:auto;--overflow:hidden;--border-radius:0;--border-width:0;--border-style:none;--border-color:transparent;--background:var(--ion-background-color, #fff);--box-shadow:none;--backdrop-opacity:0;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);contain:strict}.modal-wrapper,ion-backdrop{pointer-events:auto}:host(.overlay-hidden){display:none}.modal-wrapper,.modal-shadow{border-radius:var(--border-radius);width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:var(--overflow);z-index:10}.modal-shadow{position:absolute;background:transparent}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--width:600px;--height:500px;--ion-safe-area-top:0px;--ion-safe-area-bottom:0px;--ion-safe-area-right:0px;--ion-safe-area-left:0px}}@media only screen and (min-width: 768px) and (min-height: 768px){:host{--width:600px;--height:600px}}.modal-handle{left:0px;right:0px;top:5px;border-radius:8px;-webkit-margin-start:auto;margin-inline-start:auto;-webkit-margin-end:auto;margin-inline-end:auto;position:absolute;width:36px;height:5px;-webkit-transform:translateZ(0);transform:translateZ(0);border:0;background:var(--ion-color-step-350, var(--ion-background-color-step-350, #c0c0be));cursor:pointer;z-index:11}.modal-handle::before{-webkit-padding-start:4px;padding-inline-start:4px;-webkit-padding-end:4px;padding-inline-end:4px;padding-top:4px;padding-bottom:4px;position:absolute;width:36px;height:5px;-webkit-transform:translate(-50%, -50%);transform:translate(-50%, -50%);content:""}:host(.modal-sheet){--height:calc(100% - (var(--ion-safe-area-top) + 10px))}:host(.modal-sheet) .modal-wrapper,:host(.modal-sheet) .modal-shadow{position:absolute;bottom:0}:host(.modal-sheet.modal-no-expand-scroll) ion-footer{position:absolute;bottom:0;width:var(--width)}:host{--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}@media only screen and (min-width: 768px) and (min-height: 600px){:host{--border-radius:2px;--box-shadow:0 28px 48px rgba(0, 0, 0, 0.4)}}.modal-wrapper{-webkit-transform:translate3d(0,  40px,  0);transform:translate3d(0,  40px,  0);opacity:0.01}',rn=Z(class extends Y{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=k(this,"ionModalDidPresent",7),this.willPresent=k(this,"ionModalWillPresent",7),this.willDismiss=k(this,"ionModalWillDismiss",7),this.didDismiss=k(this,"ionModalDidDismiss",7),this.ionBreakpointDidChange=k(this,"ionBreakpointDidChange",7),this.didPresentShorthand=k(this,"didPresent",7),this.willPresentShorthand=k(this,"willPresent",7),this.willDismissShorthand=k(this,"willDismiss",7),this.didDismissShorthand=k(this,"didDismiss",7),this.ionMount=k(this,"ionMount",7),this.lockController=le(),this.triggerController=Ht(),this.coreDelegate=Ee(),this.isSheetModal=!1,this.inheritedAttributes={},this.inline=!1,this.gestureAnimationDismissing=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.expandToScroll=!0,this.backdropBreakpoint=0,this.handleBehavior="none",this.backdropDismiss=!0,this.showBackdrop=!0,this.animated=!0,this.isOpen=!1,this.keepContentsMounted=!1,this.focusTrap=!0,this.canDismiss=!0,this.onHandleClick=()=>{let{sheetTransition:e,handleBehavior:t}=this;t!=="cycle"||e!==void 0||this.moveToNextBreakpoint()},this.onBackdropTap=()=>{let{sheetTransition:e}=this;e===void 0&&this.dismiss(void 0,Te)},this.onLifecycle=e=>{let t=this.usersElement,n=_n[e.type];if(t&&n){let i=new CustomEvent(n,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.onModalFocus=e=>{let{dragHandleEl:t,el:n}=this;e.target===n&&t&&t.tabIndex!==-1&&t.focus()},this.onSlotChange=({target:e})=>{e.assignedElements().forEach(n=>{n.querySelectorAll("ion-modal").forEach(i=>{i.getAttribute("data-parent-ion-modal")===null&&i.setAttribute("data-parent-ion-modal",this.el.id)})})}}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}triggerChanged(){let{trigger:e,el:t,triggerController:n}=this;e&&n.addClickListener(t,e)}onWindowResize(){O(this)!=="ios"||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(clearTimeout(this.resizeTimeout),this.resizeTimeout=setTimeout(()=>{this.handleViewTransition()},50))}breakpointsChanged(e){e!==void 0&&(this.sortedBreakpoints=e.sort((t,n)=>t-n))}connectedCallback(){let{el:e}=this;xe(e),this.triggerChanged()}disconnectedCallback(){this.triggerController.removeClickListener(),this.cleanupViewTransitionListener(),this.cleanupParentRemovalObserver()}componentWillLoad(){var e;let{breakpoints:t,initialBreakpoint:n,el:i,htmlAttributes:r}=this,c=this.isSheetModal=t!==void 0&&n!==void 0,s=["aria-label","role"];this.inheritedAttributes=Rt(i,s),i.parentNode&&(this.cachedOriginalParent=i.parentNode),r!==void 0&&s.forEach(a=>{r[a]&&(this.inheritedAttributes=Object.assign(Object.assign({},this.inheritedAttributes),{[a]:r[a]}),delete r[a])}),c&&(this.currentBreakpoint=this.initialBreakpoint),t!==void 0&&n!==void 0&&!t.includes(n)&&Q("[ion-modal] - Your breakpoints array must include the initialBreakpoint value."),!((e=this.htmlAttributes)===null||e===void 0)&&e.id||ke(this.el)}componentDidLoad(){this.isOpen===!0&&V(()=>this.present()),this.breakpointsChanged(this.breakpoints),this.triggerChanged()}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,n=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate;return{inline:n,delegate:i}}checkCanDismiss(e,t){return b(this,null,function*(){let{canDismiss:n}=this;return typeof n=="function"?n(e,t):n})}present(){return b(this,null,function*(){let e=yield this.lockController.lock();if(this.presented){e();return}let{presentingElement:t,el:n}=this;this.currentBreakpoint=this.initialBreakpoint;let{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield ie(r,n,this.component,["ion-page"],this.componentProps,i),oe(n)?yield Ce(this.usersElement):this.keepContentsMounted||(yield ye()),Ke(()=>this.el.classList.add("show-modal"));let c=t!==void 0;c&&O(this)==="ios"&&(this.statusBarStyle=yield at.getStyle(),en()),yield Se(this,"modalEnter",Qt,Bn,{presentingEl:t,currentBreakpoint:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll}),typeof window<"u"&&(this.keyboardOpenCallback=()=>{this.gesture&&(this.gesture.enable(!1),V(()=>{this.gesture&&this.gesture.enable(!0)}))},window.addEventListener(ot,this.keyboardOpenCallback)),this.isSheetModal?this.initSheetGesture():c&&this.initSwipeToClose(),this.initViewTransitionListener(),this.initParentRemovalObserver(),e()})}initSwipeToClose(){var e;if(O(this)!=="ios")return;let{el:t}=this,n=this.leaveAnimation||$.get("modalLeave",Jt),i=this.animation=n(t,{presentingEl:this.presentingElement,expandToScroll:this.expandToScroll});if(!Vt(t)){Gt(t);return}let c=(e=this.statusBarStyle)!==null&&e!==void 0?e:ve.Default;this.gesture=Tn(t,i,c,()=>{this.gestureAnimationDismissing=!0,rt(this.statusBarStyle),this.animation.onFinish(()=>b(this,null,function*(){yield this.dismiss(void 0,ge),this.gestureAnimationDismissing=!1}))}),this.gesture.enable(!0)}initSheetGesture(){let{wrapperEl:e,initialBreakpoint:t,backdropBreakpoint:n}=this;if(!e||t===void 0)return;let i=this.enterAnimation||$.get("modalEnter",Qt),r=this.animation=i(this.el,{presentingEl:this.presentingElement,currentBreakpoint:t,backdropBreakpoint:n,expandToScroll:this.expandToScroll});r.progressStart(!0,1);let{gesture:c,moveSheetToBreakpoint:s}=Nn(this.el,this.backdropEl,e,t,n,r,this.sortedBreakpoints,this.expandToScroll,()=>{var a;return(a=this.currentBreakpoint)!==null&&a!==void 0?a:0},()=>this.sheetOnDismiss(),a=>{this.currentBreakpoint!==a&&(this.currentBreakpoint=a,this.ionBreakpointDidChange.emit({breakpoint:a}))});this.gesture=c,this.moveSheetToBreakpoint=s,this.gesture.enable(!0)}sheetOnDismiss(){this.gestureAnimationDismissing=!0,this.animation.onFinish(()=>b(this,null,function*(){this.currentBreakpoint=0,this.ionBreakpointDidChange.emit({breakpoint:this.currentBreakpoint}),yield this.dismiss(void 0,ge),this.gestureAnimationDismissing=!1}))}dismiss(e,t){return b(this,null,function*(){var n;if(this.gestureAnimationDismissing&&t!==ge)return!1;let i=yield this.lockController.lock();if(yield this.dismissNestedModals(),t!=="handler"&&!(yield this.checkCanDismiss(e,t)))return i(),!1;let{presentingElement:r}=this;r!==void 0&&O(this)==="ios"&&rt(this.statusBarStyle),typeof window<"u"&&this.keyboardOpenCallback&&(window.removeEventListener(ot,this.keyboardOpenCallback),this.keyboardOpenCallback=void 0);let s=yield je(this,e,t,"modalLeave",Jt,On,{presentingEl:r,currentBreakpoint:(n=this.currentBreakpoint)!==null&&n!==void 0?n:this.initialBreakpoint,backdropBreakpoint:this.backdropBreakpoint,expandToScroll:this.expandToScroll});if(s){let{delegate:a}=this.getDelegate();yield re(a,this.usersElement),Ke(()=>this.el.classList.remove("show-modal")),this.animation&&this.animation.destroy(),this.gesture&&this.gesture.destroy(),this.cleanupViewTransitionListener(),this.cleanupParentRemovalObserver()}return this.currentBreakpoint=void 0,this.animation=void 0,i(),s})}onDidDismiss(){return se(this.el,"ionModalDidDismiss")}onWillDismiss(){return se(this.el,"ionModalWillDismiss")}setCurrentBreakpoint(e){return b(this,null,function*(){if(!this.isSheetModal){Q("[ion-modal] - setCurrentBreakpoint is only supported on sheet modals.");return}if(!this.breakpoints.includes(e)){Q(`[ion-modal] - Attempted to set invalid breakpoint value ${e}. Please double check that the breakpoint value is part of your defined breakpoints.`);return}let{currentBreakpoint:t,moveSheetToBreakpoint:n,canDismiss:i,breakpoints:r,animated:c}=this;t!==e&&n&&(this.sheetTransition=n({breakpoint:e,breakpointOffset:1-t,canDismiss:i!==void 0&&i!==!0&&r[0]===0,animated:c}),yield this.sheetTransition,this.sheetTransition=void 0)})}getCurrentBreakpoint(){return b(this,null,function*(){return this.currentBreakpoint})}moveToNextBreakpoint(){return b(this,null,function*(){let{breakpoints:e,currentBreakpoint:t}=this;if(!e||t==null)return!1;let n=e.filter(s=>s!==0),r=(n.indexOf(t)+1)%n.length,c=n[r];return yield this.setCurrentBreakpoint(c),!0})}initViewTransitionListener(){O(this)!=="ios"||!this.presentingElement||this.enterAnimation||this.leaveAnimation||(this.currentViewIsPortrait=window.innerWidth<768)}handleViewTransition(){let e=window.innerWidth<768;if(this.currentViewIsPortrait===e)return;this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0);let{presentingElement:t}=this;if(!t)return;let n;this.currentViewIsPortrait&&!e?n=Pn(this.el,{presentingEl:t}):n=Rn(this.el,{presentingEl:t}),this.currentViewIsPortrait=e,this.viewTransitionAnimation=n,n.play().then(()=>{this.viewTransitionAnimation=void 0,this.reinitSwipeToClose()})}cleanupViewTransitionListener(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=void 0),this.viewTransitionAnimation&&(this.viewTransitionAnimation.destroy(),this.viewTransitionAnimation=void 0)}reinitSwipeToClose(){O(this)!=="ios"||!this.presentingElement||(this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.animation&&(this.animation.progressEnd(0,0,0),this.animation.destroy(),this.animation=void 0),V(()=>{this.ensureCorrectModalPosition(),this.initSwipeToClose()}))}ensureCorrectModalPosition(){let{el:e,presentingElement:t}=this,i=T(e).querySelector(".modal-wrapper");if(i&&(i.style.transform="translateY(0vh)",i.style.opacity="1"),t?.tagName==="ION-MODAL")if(window.innerWidth<768){let c=CSS.supports("width","max(0px, 1px)")?"max(30px, var(--ion-safe-area-top))":"30px",s=X.MIN_PRESENTING_SCALE;t.style.transform=`translateY(${c}) scale(${s})`}else t.style.transform="translateY(0px) scale(1)"}dismissNestedModals(){return b(this,null,function*(){let e=document.querySelectorAll(`ion-modal[data-parent-ion-modal="${this.el.id}"]`);e?.forEach(t=>b(this,null,function*(){yield t.dismiss(void 0,"parent-dismissed")}))})}initParentRemovalObserver(){typeof MutationObserver>"u"||typeof window>"u"||!this.cachedOriginalParent||this.cachedOriginalParent.nodeType===Node.DOCUMENT_NODE||this.cachedOriginalParent.nodeType===Node.DOCUMENT_FRAGMENT_NODE||(this.parentRemovalObserver=new MutationObserver(e=>{e.forEach(t=>{if(t.type==="childList"&&t.removedNodes.length>0){let n=Array.from(t.removedNodes).some(r=>{var c,s;let a=r===this.cachedOriginalParent,l=this.cachedOriginalParent?(s=(c=r).contains)===null||s===void 0?void 0:s.call(c,this.cachedOriginalParent):!1;return a||l}),i=this.cachedOriginalParent&&!this.cachedOriginalParent.isConnected;(n||i)&&(this.dismiss(void 0,"parent-removed"),this.cachedOriginalParent=void 0)}})}),this.parentRemovalObserver.observe(document.body,{childList:!0,subtree:!0}))}cleanupParentRemovalObserver(){var e;(e=this.parentRemovalObserver)===null||e===void 0||e.disconnect(),this.parentRemovalObserver=void 0}render(){let{handle:e,isSheetModal:t,presentingElement:n,htmlAttributes:i,handleBehavior:r,inheritedAttributes:c,focusTrap:s,expandToScroll:a}=this,l=e!==!1&&t,m=O(this),p=n!==void 0&&m==="ios",f=r==="cycle";return R(K,Object.assign({key:"9e9a7bd591eb17a225a00b4fa2e379e94601d17f","no-router":!0,tabIndex:f&&(t&&l)?0:-1},i,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign({[m]:!0,"modal-default":!p&&!t,"modal-card":p,"modal-sheet":t,"modal-no-expand-scroll":t&&!a,"overlay-hidden":!0,[ae]:s===!1},we(this.cssClass)),onIonBackdropTap:this.onBackdropTap,onIonModalDidPresent:this.onLifecycle,onIonModalWillPresent:this.onLifecycle,onIonModalWillDismiss:this.onLifecycle,onIonModalDidDismiss:this.onLifecycle,onFocus:this.onModalFocus}),R("ion-backdrop",{key:"e5eae2c14f830f75e308fcd7f4c10c86fac5b962",ref:u=>this.backdropEl=u,visible:this.showBackdrop,tappable:this.backdropDismiss,part:"backdrop"}),m==="ios"&&R("div",{key:"e268f9cd310c3cf4e051b5b92524ce4fb70d005e",class:"modal-shadow"}),R("div",Object.assign({key:"9c380f36c18144c153077b15744d1c3346bce63e",role:"dialog"},c,{"aria-modal":"true",class:"modal-wrapper ion-overlay-wrapper",part:"content",ref:u=>this.wrapperEl=u}),l&&R("button",{key:"2d5ee6d5959d97309c306e8ce72eb0f2c19be144",class:"modal-handle",tabIndex:f?0:-1,"aria-label":"Activate to adjust the size of the dialog overlaying the screen",onClick:f?this.onHandleClick:void 0,part:"handle",ref:u=>this.dragHandleEl=u}),R("slot",{key:"5590434c35ea04c42fc006498bc189038e15a298",onSlotchange:this.onSlotChange})))}get el(){return this}static get watchers(){return{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}}static get style(){return{ios:Ln,md:$n}}},[33,"ion-modal",{hasController:[4,"has-controller"],overlayIndex:[2,"overlay-index"],delegate:[16],keyboardClose:[4,"keyboard-close"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],breakpoints:[16],expandToScroll:[4,"expand-to-scroll"],initialBreakpoint:[2,"initial-breakpoint"],backdropBreakpoint:[2,"backdrop-breakpoint"],handle:[4],handleBehavior:[1,"handle-behavior"],component:[1],componentProps:[16,"component-props"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],showBackdrop:[4,"show-backdrop"],animated:[4],presentingElement:[16,"presenting-element"],htmlAttributes:[16,"html-attributes"],isOpen:[4,"is-open"],trigger:[1],keepContentsMounted:[4,"keep-contents-mounted"],focusTrap:[4,"focus-trap"],canDismiss:[4,"can-dismiss"],presented:[32],present:[64],dismiss:[64],onDidDismiss:[64],onWillDismiss:[64],setCurrentBreakpoint:[64],getCurrentBreakpoint:[64]},[[9,"resize","onWindowResize"]],{isOpen:["onIsOpenChange"],trigger:["triggerChanged"]}]),_n={ionModalDidPresent:"ionViewDidEnter",ionModalWillPresent:"ionViewWillEnter",ionModalWillDismiss:"ionViewWillLeave",ionModalDidDismiss:"ionViewDidLeave"};function sn(){if(typeof customElements>"u")return;["ion-modal","ion-backdrop"].forEach(e=>{switch(e){case"ion-modal":customElements.get(e)||customElements.define(e,rn);break;case"ion-backdrop":customElements.get(e)||Pe();break}})}var an=sn;var Hn=o=>{if(!o)return{arrowWidth:0,arrowHeight:0};let{width:e,height:t}=o.getBoundingClientRect();return{arrowWidth:e,arrowHeight:t}},ln=(o,e,t)=>{let n=e.getBoundingClientRect(),i=n.height,r=n.width;return o==="cover"&&t&&(r=t.getBoundingClientRect().width),{contentWidth:r,contentHeight:i}},Wn=(o,e,t,n)=>{let i=[],c=T(n).querySelector(".popover-content");switch(e){case"hover":i=[{eventName:"mouseenter",callback:s=>{document.elementFromPoint(s.clientX,s.clientY)!==o&&t.dismiss(void 0,void 0,!1)}}];break;case"context-menu":case"click":default:i=[{eventName:"click",callback:s=>{if(s.target.closest("[data-ion-popover-trigger]")===o){s.stopPropagation();return}t.dismiss(void 0,void 0,!1)}}];break}return i.forEach(({eventName:s,callback:a})=>c.addEventListener(s,a)),()=>{i.forEach(({eventName:s,callback:a})=>c.removeEventListener(s,a))}},Yn=(o,e,t)=>{let n=[];switch(e){case"hover":let i;n=[{eventName:"mouseenter",callback:r=>b(null,null,function*(){r.stopPropagation(),i&&clearTimeout(i),i=setTimeout(()=>{V(()=>{t.presentFromTrigger(r),i=void 0})},100)})},{eventName:"mouseleave",callback:r=>{i&&clearTimeout(i);let c=r.relatedTarget;c&&c.closest("ion-popover")!==t&&t.dismiss(void 0,void 0,!1)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"context-menu":n=[{eventName:"contextmenu",callback:r=>{r.preventDefault(),t.presentFromTrigger(r)}},{eventName:"click",callback:r=>r.stopPropagation()},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break;case"click":default:n=[{eventName:"click",callback:r=>t.presentFromTrigger(r)},{eventName:"ionPopoverActivateTrigger",callback:r=>t.presentFromTrigger(r,!0)}];break}return n.forEach(({eventName:i,callback:r})=>o.addEventListener(i,r)),o.setAttribute("data-ion-popover-trigger","true"),()=>{n.forEach(({eventName:i,callback:r})=>o.removeEventListener(i,r)),o.removeAttribute("data-ion-popover-trigger")}},dn=(o,e)=>!e||e.tagName!=="ION-ITEM"?-1:o.findIndex(t=>t===e),Zn=(o,e)=>{let t=dn(o,e);return o[t+1]},Vn=(o,e)=>{let t=dn(o,e);return o[t-1]},Re=o=>{let t=T(o).querySelector("button");t&&V(()=>t.focus())},Gn=o=>o.hasAttribute("data-ion-popover-trigger"),qn=o=>{let e=t=>b(null,null,function*(){var n;let i=document.activeElement,r=[],c=(n=t.target)===null||n===void 0?void 0:n.tagName;if(!(c!=="ION-POPOVER"&&c!=="ION-ITEM")){try{r=Array.from(o.querySelectorAll("ion-item:not(ion-popover ion-popover *):not([disabled])"))}catch{}switch(t.key){case"ArrowLeft":(yield o.getParentPopover())&&o.dismiss(void 0,void 0,!1);break;case"ArrowDown":t.preventDefault();let a=Zn(r,i);a!==void 0&&Re(a);break;case"ArrowUp":t.preventDefault();let l=Vn(r,i);l!==void 0&&Re(l);break;case"Home":t.preventDefault();let m=r[0];m!==void 0&&Re(m);break;case"End":t.preventDefault();let p=r[r.length-1];p!==void 0&&Re(p);break;case"ArrowRight":case" ":case"Enter":if(i&&Gn(i)){let f=new CustomEvent("ionPopoverActivateTrigger");i.dispatchEvent(f)}break}}});return o.addEventListener("keydown",e),()=>o.removeEventListener("keydown",e)},pn=(o,e,t,n,i,r,c,s,a,l,m)=>{var p;let f={top:0,left:0,width:0,height:0};switch(r){case"event":if(!m)return a;let j=m;f={top:j.clientY,left:j.clientX,width:1,height:1};break;case"trigger":default:let v=m,F=l||((p=v?.detail)===null||p===void 0?void 0:p.ionShadowTarget)||v?.target;if(!F)return a;let z=F.getBoundingClientRect();f={top:z.top,left:z.left,width:z.width,height:z.height};break}let g=Kn(c,f,e,t,n,i,o),u=Qn(s,c,f,e,t),S=g.top+u.top,h=g.left+u.left,{arrowTop:x,arrowLeft:A}=Un(c,n,i,S,h,e,t,o),{originX:D,originY:y}=Xn(c,s,o);return{top:S,left:h,referenceCoordinates:f,arrowTop:x,arrowLeft:A,originX:D,originY:y}},Xn=(o,e,t)=>{switch(o){case"top":return{originX:cn(e),originY:"bottom"};case"bottom":return{originX:cn(e),originY:"top"};case"left":return{originX:"right",originY:Fe(e)};case"right":return{originX:"left",originY:Fe(e)};case"start":return{originX:t?"left":"right",originY:Fe(e)};case"end":return{originX:t?"right":"left",originY:Fe(e)}}},cn=o=>{switch(o){case"start":return"left";case"center":return"center";case"end":return"right"}},Fe=o=>{switch(o){case"start":return"top";case"center":return"center";case"end":return"bottom"}},Un=(o,e,t,n,i,r,c,s)=>{let a={arrowTop:n+c/2-e/2,arrowLeft:i+r-e/2},l={arrowTop:n+c/2-e/2,arrowLeft:i-e*1.5};switch(o){case"top":return{arrowTop:n+c,arrowLeft:i+r/2-e/2};case"bottom":return{arrowTop:n-t,arrowLeft:i+r/2-e/2};case"left":return a;case"right":return l;case"start":return s?l:a;case"end":return s?a:l;default:return{arrowTop:0,arrowLeft:0}}},Kn=(o,e,t,n,i,r,c)=>{let s={top:e.top,left:e.left-t-i},a={top:e.top,left:e.left+e.width+i};switch(o){case"top":return{top:e.top-n-r,left:e.left};case"right":return a;case"bottom":return{top:e.top+e.height+r,left:e.left};case"left":return s;case"start":return c?a:s;case"end":return c?s:a}},Qn=(o,e,t,n,i)=>{switch(o){case"center":return eo(e,t,n,i);case"end":return Jn(e,t,n,i);case"start":default:return{top:0,left:0}}},Jn=(o,e,t,n)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(n-e.height),left:0};case"top":case"bottom":default:return{top:0,left:-(t-e.width)}}},eo=(o,e,t,n)=>{switch(o){case"start":case"end":case"left":case"right":return{top:-(n/2-e.height/2),left:0};case"top":case"bottom":default:return{top:0,left:-(t/2-e.width/2)}}},mn=(o,e,t,n,i,r,c,s,a,l,m,p,f=0,g=0,u=0)=>{let S=f,h=g,x=t,A=e,D,y=l,j=m,v=!1,F=!1,z=p?p.top+p.height:r/2-s/2,B=p?p.height:0,L=!1;return x<n+a?(x=n,v=!0,y="left"):c+n+x+a>i&&(F=!0,x=i-c-n,y="right"),z+B+s>r&&(o==="top"||o==="bottom")&&(z-s>0?(A=Math.max(12,z-s-B-(u-1)),S=A+s,j="bottom",L=!0):D=n),{top:A,left:x,bottom:D,originX:y,originY:j,checkSafeAreaLeft:v,checkSafeAreaRight:F,arrowTop:S,arrowLeft:h,addPopoverBottomClass:L}},to=(o,e=!1,t,n)=>!(!t&&!n||o!=="top"&&o!=="bottom"&&e),no=5,oo=(o,e)=>{var t;let{event:n,size:i,trigger:r,reference:c,side:s,align:a}=e,l=o.ownerDocument,m=l.dir==="rtl",p=l.defaultView.innerWidth,f=l.defaultView.innerHeight,g=T(o),u=g.querySelector(".popover-content"),S=g.querySelector(".popover-arrow"),h=r||((t=n?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||n?.target,{contentWidth:x,contentHeight:A}=ln(i,u,h),{arrowWidth:D,arrowHeight:y}=Hn(S),j={top:f/2-A/2,left:p/2-x/2,originX:m?"right":"left",originY:"top"},v=pn(m,x,A,D,y,c,s,a,j,r,n),F=i==="cover"?0:no,z=i==="cover"?0:25,{originX:B,originY:L,top:M,left:P,bottom:H,checkSafeAreaLeft:J,checkSafeAreaRight:U,arrowTop:ze,arrowLeft:Oe,addPopoverBottomClass:Ne}=mn(s,v.top,v.left,F,p,f,x,A,z,v.originX,v.originY,v.referenceCoordinates,v.arrowTop,v.arrowLeft,y),Le=d(),de=d(),ee=d();return de.addElement(g.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),ee.addElement(g.querySelector(".popover-arrow")).addElement(g.querySelector(".popover-content")).fromTo("opacity",.01,1),Le.easing("ease").duration(100).beforeAddWrite(()=>{i==="cover"&&o.style.setProperty("--width",`${x}px`),Ne&&o.classList.add("popover-bottom"),H!==void 0&&u.style.setProperty("bottom",`${H}px`);let w=" + var(--ion-safe-area-left, 0)",I=" - var(--ion-safe-area-right, 0)",E=`${P}px`;if(J&&(E=`${P}px${w}`),U&&(E=`${P}px${I}`),u.style.setProperty("top",`calc(${M}px + var(--offset-y, 0))`),u.style.setProperty("left",`calc(${E} + var(--offset-x, 0))`),u.style.setProperty("transform-origin",`${L} ${B}`),S!==null){let C=v.top!==M||v.left!==P;to(s,C,n,r)?(S.style.setProperty("top",`calc(${ze}px + var(--offset-y, 0))`),S.style.setProperty("left",`calc(${Oe}px + var(--offset-x, 0))`)):S.style.setProperty("display","none")}}).addAnimation([de,ee])},io=o=>{let e=T(o),t=e.querySelector(".popover-content"),n=e.querySelector(".popover-arrow"),i=d(),r=d(),c=d();return r.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),c.addElement(e.querySelector(".popover-arrow")).addElement(e.querySelector(".popover-content")).fromTo("opacity",.99,0),i.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin"),n&&(n.style.removeProperty("top"),n.style.removeProperty("left"),n.style.removeProperty("display"))}).duration(300).addAnimation([r,c])},ro=12,so=(o,e)=>{var t;let{event:n,size:i,trigger:r,reference:c,side:s,align:a}=e,l=o.ownerDocument,m=l.dir==="rtl",p=l.defaultView.innerWidth,f=l.defaultView.innerHeight,g=T(o),u=g.querySelector(".popover-content"),S=r||((t=n?.detail)===null||t===void 0?void 0:t.ionShadowTarget)||n?.target,{contentWidth:h,contentHeight:x}=ln(i,u,S),A={top:f/2-x/2,left:p/2-h/2,originX:m?"right":"left",originY:"top"},D=pn(m,h,x,0,0,c,s,a,A,r,n),y=i==="cover"?0:ro,{originX:j,originY:v,top:F,left:z,bottom:B}=mn(s,D.top,D.left,y,p,f,h,x,0,D.originX,D.originY,D.referenceCoordinates),L=d(),M=d(),P=d(),H=d(),J=d();return M.addElement(g.querySelector("ion-backdrop")).fromTo("opacity",.01,"var(--backdrop-opacity)").beforeStyles({"pointer-events":"none"}).afterClearStyles(["pointer-events"]),P.addElement(g.querySelector(".popover-wrapper")).duration(150).fromTo("opacity",.01,1),H.addElement(u).beforeStyles({top:`calc(${F}px + var(--offset-y, 0px))`,left:`calc(${z}px + var(--offset-x, 0px))`,"transform-origin":`${v} ${j}`}).beforeAddWrite(()=>{B!==void 0&&u.style.setProperty("bottom",`${B}px`)}).fromTo("transform","scale(0.8)","scale(1)"),J.addElement(g.querySelector(".popover-viewport")).fromTo("opacity",.01,1),L.easing("cubic-bezier(0.36,0.66,0.04,1)").duration(300).beforeAddWrite(()=>{i==="cover"&&o.style.setProperty("--width",`${h}px`),v==="bottom"&&o.classList.add("popover-bottom")}).addAnimation([M,P,H,J])},ao=o=>{let e=T(o),t=e.querySelector(".popover-content"),n=d(),i=d(),r=d();return i.addElement(e.querySelector("ion-backdrop")).fromTo("opacity","var(--backdrop-opacity)",0),r.addElement(e.querySelector(".popover-wrapper")).fromTo("opacity",.99,0),n.easing("ease").afterAddWrite(()=>{o.style.removeProperty("--width"),o.classList.remove("popover-bottom"),t.style.removeProperty("top"),t.style.removeProperty("left"),t.style.removeProperty("bottom"),t.style.removeProperty("transform-origin")}).duration(150).addAnimation([i,r])},co=':host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:200px;--max-height:90%;--box-shadow:none;--backdrop-opacity:var(--ion-backdrop-opacity, 0.08)}:host(.popover-desktop){--box-shadow:0px 4px 16px 0px rgba(0, 0, 0, 0.12)}.popover-content{border-radius:10px}:host(.popover-desktop) .popover-content{border:0.5px solid var(--ion-color-step-100, var(--ion-background-color-step-100, #e6e6e6))}.popover-arrow{display:block;position:absolute;width:20px;height:10px;overflow:hidden;z-index:11}.popover-arrow::after{top:3px;border-radius:3px;position:absolute;width:14px;height:14px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:var(--background);content:"";z-index:10}.popover-arrow::after{inset-inline-start:3px}:host(.popover-bottom) .popover-arrow{top:auto;bottom:-10px}:host(.popover-bottom) .popover-arrow::after{top:-6px}:host(.popover-side-left) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host(.popover-side-right) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host(.popover-side-top) .popover-arrow{-webkit-transform:rotate(180deg);transform:rotate(180deg)}:host(.popover-side-start) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}:host-context([dir=rtl]):host(.popover-side-start) .popover-arrow,:host-context([dir=rtl]).popover-side-start .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}@supports selector(:dir(rtl)){:host(.popover-side-start:dir(rtl)) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}}:host(.popover-side-end) .popover-arrow{-webkit-transform:rotate(-90deg);transform:rotate(-90deg)}:host-context([dir=rtl]):host(.popover-side-end) .popover-arrow,:host-context([dir=rtl]).popover-side-end .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}@supports selector(:dir(rtl)){:host(.popover-side-end:dir(rtl)) .popover-arrow{-webkit-transform:rotate(90deg);transform:rotate(90deg)}}.popover-arrow,.popover-content{opacity:0}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.popover-translucent) .popover-content,:host(.popover-translucent) .popover-arrow::after{background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}}',lo=":host{--background:var(--ion-background-color, #fff);--min-width:0;--min-height:0;--max-width:auto;--height:auto;--offset-x:0px;--offset-y:0px;left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:fixed;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;outline:none;color:var(--ion-text-color, #000);z-index:1001}:host(.popover-nested){pointer-events:none}:host(.popover-nested) .popover-wrapper{pointer-events:auto}:host(.overlay-hidden){display:none}.popover-wrapper{z-index:10}.popover-content{display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:var(--width);min-width:var(--min-width);max-width:var(--max-width);height:var(--height);min-height:var(--min-height);max-height:var(--max-height);background:var(--background);-webkit-box-shadow:var(--box-shadow);box-shadow:var(--box-shadow);overflow:auto;z-index:10}::slotted(.popover-viewport){--ion-safe-area-top:0px;--ion-safe-area-right:0px;--ion-safe-area-bottom:0px;--ion-safe-area-left:0px;display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column}:host(.popover-nested.popover-side-left){--offset-x:5px}:host(.popover-nested.popover-side-right){--offset-x:-5px}:host(.popover-nested.popover-side-start){--offset-x:5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-start),:host-context([dir=rtl]).popover-nested.popover-side-start{--offset-x:-5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-start:dir(rtl)){--offset-x:-5px}}:host(.popover-nested.popover-side-end){--offset-x:-5px}:host-context([dir=rtl]):host(.popover-nested.popover-side-end),:host-context([dir=rtl]).popover-nested.popover-side-end{--offset-x:5px}@supports selector(:dir(rtl)){:host(.popover-nested.popover-side-end:dir(rtl)){--offset-x:5px}}:host{--width:250px;--max-height:90%;--box-shadow:0 5px 5px -3px rgba(0, 0, 0, 0.2), 0 8px 10px 1px rgba(0, 0, 0, 0.14), 0 3px 14px 2px rgba(0, 0, 0, 0.12);--backdrop-opacity:var(--ion-backdrop-opacity, 0.32)}.popover-content{border-radius:4px;-webkit-transform-origin:left top;transform-origin:left top}:host-context([dir=rtl]) .popover-content{-webkit-transform-origin:right top;transform-origin:right top}[dir=rtl] .popover-content{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.popover-content:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.popover-viewport{-webkit-transition-delay:100ms;transition-delay:100ms}.popover-wrapper{opacity:0}",un=Z(class extends Y{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.didPresent=k(this,"ionPopoverDidPresent",7),this.willPresent=k(this,"ionPopoverWillPresent",7),this.willDismiss=k(this,"ionPopoverWillDismiss",7),this.didDismiss=k(this,"ionPopoverDidDismiss",7),this.didPresentShorthand=k(this,"didPresent",7),this.willPresentShorthand=k(this,"willPresent",7),this.willDismissShorthand=k(this,"willDismiss",7),this.didDismissShorthand=k(this,"didDismiss",7),this.ionMount=k(this,"ionMount",7),this.parentPopover=null,this.coreDelegate=Ee(),this.lockController=le(),this.inline=!1,this.focusDescendantOnPresent=!1,this.presented=!1,this.hasController=!1,this.keyboardClose=!0,this.backdropDismiss=!0,this.showBackdrop=!0,this.translucent=!1,this.animated=!0,this.triggerAction="click",this.size="auto",this.dismissOnSelect=!1,this.reference="trigger",this.side="bottom",this.arrow=!0,this.isOpen=!1,this.keyboardEvents=!1,this.focusTrap=!0,this.keepContentsMounted=!1,this.onBackdropTap=()=>{this.dismiss(void 0,Te)},this.onLifecycle=e=>{let t=this.usersElement,n=po[e.type];if(t&&n){let i=new CustomEvent(n,{bubbles:!1,cancelable:!1,detail:e.detail});t.dispatchEvent(i)}},this.configureTriggerInteraction=()=>{let{trigger:e,triggerAction:t,el:n,destroyTriggerInteraction:i}=this;if(i&&i(),e===void 0)return;let r=this.triggerEl=e!==void 0?document.getElementById(e):null;if(!r){Q(`[ion-popover] - A trigger element with the ID "${e}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on ion-popover.`,this.el);return}this.destroyTriggerInteraction=Yn(r,t,n)},this.configureKeyboardInteraction=()=>{let{destroyKeyboardInteraction:e,el:t}=this;e&&e(),this.destroyKeyboardInteraction=qn(t)},this.configureDismissInteraction=()=>{let{destroyDismissInteraction:e,parentPopover:t,triggerAction:n,triggerEl:i,el:r}=this;!t||!i||(e&&e(),this.destroyDismissInteraction=Wn(i,n,r,t))}}onTriggerChange(){this.configureTriggerInteraction()}onIsOpenChange(e,t){e===!0&&t===!1?this.present():e===!1&&t===!0&&this.dismiss()}connectedCallback(){let{configureTriggerInteraction:e,el:t}=this;xe(t),e()}disconnectedCallback(){let{destroyTriggerInteraction:e}=this;e&&e()}componentWillLoad(){var e,t;let{el:n}=this,i=(t=(e=this.htmlAttributes)===null||e===void 0?void 0:e.id)!==null&&t!==void 0?t:ke(n);this.parentPopover=n.closest(`ion-popover:not(#${i})`),this.alignment===void 0&&(this.alignment=O(this)==="ios"?"center":"start")}componentDidLoad(){let{parentPopover:e,isOpen:t}=this;t===!0&&V(()=>this.present()),e&&Ft(e,"ionPopoverWillDismiss",()=>{this.dismiss(void 0,void 0,!1)}),this.configureTriggerInteraction()}presentFromTrigger(e,t=!1){return b(this,null,function*(){this.focusDescendantOnPresent=t,yield this.present(e),this.focusDescendantOnPresent=!1})}getDelegate(e=!1){if(this.workingDelegate&&!e)return{delegate:this.workingDelegate,inline:this.inline};let t=this.el.parentNode,n=this.inline=t!==null&&!this.hasController,i=this.workingDelegate=n?this.delegate||this.coreDelegate:this.delegate;return{inline:n,delegate:i}}present(e){return b(this,null,function*(){let t=yield this.lockController.lock();if(this.presented){t();return}let{el:n}=this,{inline:i,delegate:r}=this.getDelegate(!0);this.ionMount.emit(),this.usersElement=yield ie(r,n,this.component,["popover-viewport"],this.componentProps,i),this.keyboardEvents||this.configureKeyboardInteraction(),this.configureDismissInteraction(),oe(n)?yield Ce(this.usersElement):this.keepContentsMounted||(yield ye()),yield Se(this,"popoverEnter",oo,so,{event:e||this.event,size:this.size,trigger:this.triggerEl,reference:this.reference,side:this.side,align:this.alignment}),this.focusDescendantOnPresent&&Nt(n),t()})}dismiss(e,t,n=!0){return b(this,null,function*(){let i=yield this.lockController.lock(),{destroyKeyboardInteraction:r,destroyDismissInteraction:c}=this;n&&this.parentPopover&&this.parentPopover.dismiss(e,t,n);let s=yield je(this,e,t,"popoverLeave",io,ao,this.event);if(s){r&&(r(),this.destroyKeyboardInteraction=void 0),c&&(c(),this.destroyDismissInteraction=void 0);let{delegate:a}=this.getDelegate();yield re(a,this.usersElement)}return i(),s})}getParentPopover(){return b(this,null,function*(){return this.parentPopover})}onDidDismiss(){return se(this.el,"ionPopoverDidDismiss")}onWillDismiss(){return se(this.el,"ionPopoverWillDismiss")}render(){let e=O(this),{onLifecycle:t,parentPopover:n,dismissOnSelect:i,side:r,arrow:c,htmlAttributes:s,focusTrap:a}=this,l=q("desktop"),m=c&&!n;return R(K,Object.assign({key:"16866c02534968c982cf4730d2936d03a5107c8b","aria-modal":"true","no-router":!0,tabindex:"-1"},s,{style:{zIndex:`${2e4+this.overlayIndex}`},class:Object.assign(Object.assign({},we(this.cssClass)),{[e]:!0,"popover-translucent":this.translucent,"overlay-hidden":!0,"popover-desktop":l,[`popover-side-${r}`]:!0,[ae]:a===!1,"popover-nested":!!n}),onIonPopoverDidPresent:t,onIonPopoverWillPresent:t,onIonPopoverWillDismiss:t,onIonPopoverDidDismiss:t,onIonBackdropTap:this.onBackdropTap}),!n&&R("ion-backdrop",{key:"0df258601a4d30df3c27aa8234a7d5e056c3ecbb",tappable:this.backdropDismiss,visible:this.showBackdrop,part:"backdrop"}),R("div",{key:"f94e80ed996b957b5cd09b826472b4f60e8fcc78",class:"popover-wrapper ion-overlay-wrapper",onClick:i?()=>this.dismiss():void 0},m&&R("div",{key:"185ce22f6386e8444a9cc7b8818dbfc16c463c99",class:"popover-arrow",part:"arrow"}),R("div",{key:"206202b299404e110de5397b229678cca18568d3",class:"popover-content",part:"content"},R("slot",{key:"ee543a0b92d6e35a837c0a0e4617c7b0fc4ad0b0"}))))}get el(){return this}static get watchers(){return{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}}static get style(){return{ios:co,md:lo}}},[33,"ion-popover",{hasController:[4,"has-controller"],delegate:[16],overlayIndex:[2,"overlay-index"],enterAnimation:[16,"enter-animation"],leaveAnimation:[16,"leave-animation"],component:[1],componentProps:[16,"component-props"],keyboardClose:[4,"keyboard-close"],cssClass:[1,"css-class"],backdropDismiss:[4,"backdrop-dismiss"],event:[8],showBackdrop:[4,"show-backdrop"],translucent:[4],animated:[4],htmlAttributes:[16,"html-attributes"],triggerAction:[1,"trigger-action"],trigger:[1],size:[1],dismissOnSelect:[4,"dismiss-on-select"],reference:[1],side:[1],alignment:[1025],arrow:[4],isOpen:[4,"is-open"],keyboardEvents:[4,"keyboard-events"],focusTrap:[4,"focus-trap"],keepContentsMounted:[4,"keep-contents-mounted"],presented:[32],presentFromTrigger:[64],present:[64],dismiss:[64],getParentPopover:[64],onDidDismiss:[64],onWillDismiss:[64]},void 0,{trigger:["onTriggerChange"],triggerAction:["onTriggerChange"],isOpen:["onIsOpenChange"]}]),po={ionPopoverDidPresent:"ionViewDidEnter",ionPopoverWillPresent:"ionViewWillEnter",ionPopoverWillDismiss:"ionViewWillLeave",ionPopoverDidDismiss:"ionViewDidLeave"};function fn(){if(typeof customElements>"u")return;["ion-popover","ion-backdrop"].forEach(e=>{switch(e){case"ion-popover":customElements.get(e)||customElements.define(e,un);break;case"ion-backdrop":customElements.get(e)||Pe();break}})}var hn=fn;var mo="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",uo=Z(class extends Y{constructor(){super(),this.__registerHost()}componentDidLoad(){Mt.isBrowser&&ho(()=>b(this,null,function*(){let e=q(window,"hybrid");if($.getBoolean("_testing")||import("./chunk-MILUEENM.js").then(i=>i.startTapClick($)),$.getBoolean("statusTap",e)&&import("./chunk-PGBOM236.js").then(i=>i.startStatusTap()),$.getBoolean("inputShims",fo())){let i=q(window,"ios")?"ios":"android";import("./chunk-UUUGU3B6.js").then(r=>r.startInputShims($,i))}let t=yield import("./chunk-OXT6KIWA.js"),n=e||Qe();$.getBoolean("hardwareBackButton",n)?t.startHardwareBackButton():(Qe()&&Q("[ion-app] - experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),t.blockHardwareBackButton()),typeof window<"u"&&import("./chunk-6AHBHCSC.js").then(i=>i.startKeyboardAssist(window)),import("./chunk-GCQ4TVF3.js").then(i=>this.focusVisible=i.startFocusVisible())}))}setFocus(e){return b(this,null,function*(){this.focusVisible&&this.focusVisible.setFocus(e)})}render(){let e=O(this);return R(K,{key:"9be440c65819e4fa67c2c3c6477ab40b3ad3eed3",class:{[e]:!0,"ion-page":!0,"force-statusbar-padding":$.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return mo}},[0,"ion-app",{setFocus:[64]}]),fo=()=>!!(q(window,"ios")&&q(window,"mobile")||q(window,"android")&&q(window,"mobileweb")),ho=o=>{"requestIdleCallback"in window?window.requestIdleCallback(o):setTimeout(o,32)};function go(){if(typeof customElements>"u")return;["ion-app"].forEach(e=>{switch(e){case"ion-app":customElements.get(e)||customElements.define(e,uo);break}})}var gn=go;var bo=["outletContent"],vn=["*"];var In=(()=>{let o=class Be extends Yt{parentOutlet;outletContent;constructor(t,n,i,r,c,s,a,l){super(t,n,i,r,c,s,a,l),this.parentOutlet=l}static \u0275fac=function(n){return new(n||Be)(Ye("name"),Ye("tabs"),W(wt),W(Ze),W(St),W(Ve),W(xt),W(Be,12))};static \u0275cmp=ue({type:Be,selectors:[["ion-router-outlet"]],viewQuery:function(n,i){if(n&1&&bt(bo,7,ut),n&2){let r;yt(r=Ct())&&(i.outletContent=r.first)}},features:[ft],ngContentSelectors:vn,decls:3,vars:0,consts:[["outletContent",""]],template:function(n,i){n&1&&(Xe(),vt(0,null,0),Ue(2),It())},encapsulation:2})};return o=We([Wt({defineCustomElementFn:Ut})],o),o})();var yo=(o,e)=>{let t=o.prototype;e.forEach(n=>{Object.defineProperty(t,n,{get(){return this.el[n]},set(i){this.z.runOutsideAngular(()=>this.el[n]=i)},configurable:!0})})},Co=(o,e)=>{let t=o.prototype;e.forEach(n=>{t[n]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[n].apply(this.el,i))}})};function Do(o){return function(t){let{defineCustomElementFn:n,inputs:i,methods:r}=o;return n!==void 0&&n(),i&&yo(t,i),r&&Co(t,r),t}}var bn=(()=>{let o=class ct{z;el;constructor(t,n,i){this.z=i,t.detach(),this.el=n.nativeElement}static \u0275fac=function(n){return new(n||ct)(W(Dt),W(Ze),W(Ve))};static \u0275cmp=ue({type:ct,selectors:[["ion-app"]],ngContentSelectors:vn,decls:1,vars:0,template:function(n,i){n&1&&(Xe(),Ue(0))},encapsulation:2,changeDetection:0})};return o=We([Do({defineCustomElementFn:gn,methods:["setFocus"]})],o),o})();var wo=(()=>{class o extends tt{angularDelegate=te(Ae);injector=te(be);environmentInjector=te(Ie);constructor(){super(Lt),an()}create(t){return super.create(He(_e({},t),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(n){return new(n||o)};static \u0275prov=dt({token:o,factory:o.\u0275fac})}return o})(),lt=class extends tt{angularDelegate=te(Ae);injector=te(be);environmentInjector=te(Ie);constructor(){super($t),hn()}create(e){return super.create(He(_e({},e),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},yn=(o={})=>pt([{provide:Je,useValue:o},{provide:ht,useFactory:Eo,multi:!0,deps:[Je,mt]},Zt(),Ae,wo,lt]),Eo=(o,e)=>()=>{e.documentElement.classList.add("ion-ce"),Ot(o)};var Cn=[{path:"home",loadComponent:()=>import("./chunk-GWMV4FQA.js").then(o=>o.HomePage)},{path:"",redirectTo:"home",pathMatch:"full"},{path:"patient-entry",loadComponent:()=>import("./chunk-MQ2PTJSU.js").then(o=>o.PatientEntryPage)}];var Dn=(()=>{let e=class e{constructor(){}};e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=ue({type:e,selectors:[["app-root"]],decls:2,vars:0,template:function(i,r){i&1&&(Ge(0,"ion-app"),gt(1,"ion-router-outlet"),qe())},dependencies:[bn,In],encapsulation:2});let o=e;return o})();Et(Dn,{providers:[{provide:kt,useClass:et},yn(),Tt(Cn,At(jt))]});
