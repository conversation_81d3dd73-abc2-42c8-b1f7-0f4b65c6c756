import{a as p,b as s,c as l}from"./chunk-GPBDFC3Z.js";import"./chunk-WFLKXDOX.js";import"./chunk-QUJFQN2Y.js";import"./chunk-4LI3CALM.js";import"./chunk-2FKZ3GBX.js";import"./chunk-642SEYX3.js";import"./chunk-JIT2VFLB.js";import"./chunk-PK2JKDOR.js";import"./chunk-B7F22SOJ.js";import"./chunk-BMJVVMK5.js";import"./chunk-753GXAUT.js";import"./chunk-XFXTD7QR.js";import"./chunk-MTHZ7MWU.js";import"./chunk-WI5MSH4N.js";import"./chunk-JCOV4R6X.js";import"./chunk-CKP3SGE2.js";import"./chunk-EHNA26RN.js";import{H as i,I as r,J as c,K as a,z as o}from"./chunk-CA3KZVYD.js";import"./chunk-GVQ3LBRF.js";import"./chunk-JSXXIFUV.js";import"./chunk-B7SFH74S.js";import"./chunk-UOV5QIVR.js";import"./chunk-GIIU5PV3.js";import"./chunk-M2X7KQLB.js";import"./chunk-XTVTS2NW.js";import"./chunk-C5RQ2IC2.js";import"./chunk-42C7ZIID.js";import"./chunk-2R6CW7ES.js";var M=(()=>{let t=class t{constructor(){}};t.\u0275fac=function(n){return new(n||t)},t.\u0275cmp=o({type:t,selectors:[["app-home"]],decls:2,vars:1,consts:[[3,"fullscreen"]],template:function(n,f){n&1&&(r(0,"ion-content",0),a(1,"app-patient-entry"),c()),n&2&&i("fullscreen",!0)},dependencies:[s,p,l],styles:["#container[_ngcontent-%COMP%]{text-align:center;position:absolute;left:0;right:0;top:50%;transform:translateY(-50%)}#container[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%]{font-size:20px;line-height:26px}#container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:16px;line-height:22px;color:#8c8c8c;margin:0}#container[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]{text-decoration:none}"]});let e=t;return e})();export{M as HomePage};
