@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* ================================
   MOBILE-FIRST PATIENT ENTRY STYLES
   ================================ */

/* Base Mobile Styles */
.container {
  width: 100%;
  height: 100vh;
  background: #F8F9FA;
  display: flex;
  flex-direction: column;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Mobile Header */
.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: #FFFFFF;
  border-bottom: 1px solid #E5E7EB;
  position: sticky;
  top: 0;
  z-index: 100;
}

.back-button {
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.back-button:hover {
  background: #F3F4F6;
}

.title {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #1F2937;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.notification-icon, .profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.notification-icon {
  background: #F3F4F6;
  color: #6B7280;
}

.profile-avatar {
  background: #E5E7EB;
  overflow: hidden;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Mobile Progress Steps */
.progress-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  background: #FFFFFF;
  border-bottom: 1px solid #E5E7EB;
  gap: 8px;
  width: 100%;
}

.step {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 80px;
}

.step .dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #E5E7EB;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.step.completed .dot {
  background: #10B981;
}

.step.active .dot {
  background: #F59E0B;
}

.step:not(:last-child)::after {
  content: "";
  position: absolute;
  left: 12px;
  right: -8px;
  top: 50%;
  height: 2px;
  background: #E5E7EB;
  z-index: 1;
  transform: translateY(-50%);
}

.step.completed:not(:last-child)::after {
  background: #10B981;
}

.step.active:not(:last-child)::after {
  background: #F59E0B;
}

/* Mobile Form Section */
.form-section {
  flex: 1;
  overflow-y: auto;
  background: #FFFFFF;
  padding: 24px 20px;

  /* Hide scrollbar */
  -ms-overflow-style: none;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.form-section h3 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #1F2937;
  margin: 0 0 24px 0;
  padding: 0;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  width: 100%;
}

/* Mobile Form Inputs */
label {
  font-family: 'Inter', sans-serif;
  font-weight: 500;
  font-size: 14px;
  color: #374151;
  margin: 0 0 8px 0;
}

input, select, textarea {
  width: 100%;
  height: 48px;
  padding: 12px 16px;
  border: 1px solid #D1D5DB;
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  color: #1F2937;
  background: #FFFFFF;
  transition: all 0.2s ease;
  box-sizing: border-box;
}

textarea {
  height: 96px;
  min-height: 96px;
  max-height: 160px;
  resize: vertical;
}

input:focus, select:focus, textarea:focus {
  outline: none;
  border-color: #3B82F6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

input::placeholder, textarea::placeholder {
  color: #9CA3AF;
  font-size: 16px;
}

select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 40px;
}

/* ---------- Form Actions ---------- */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 33px;
  border-top: 1px solid #E5E7EB;
  padding: 20px;
  gap: 16px;
}

.btn-back {
  background: none;
  border: none;
  color: #007AFF;
  font-size: 14px;
  cursor: pointer;
  min-width: 80px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-family: 'DM Sans', system-ui, -apple-system, sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  text-align: center;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.btn-back:hover {
  background: #F0F8FF;
}

.btn-back img {
  width: 14px;
  height: 14px;
}

.btn-next {
  min-width: 120px;
  height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  opacity: 1;
  padding: 12px 24px;
  border: 1px solid #007AFF;
  background-color: transparent;
  color: #007AFF;
  box-sizing: border-box;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 140%;
  letter-spacing: 0.4px;
  cursor: pointer;
  transition: all 0.3s ease;
}



// .btn-next:hover {
//   background: #0056CC;
// }

.btn-register {
  background: #007AFF;
  color: #fff;
  padding: 12px 24px;
  font-size: 14px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  min-width: 120px;
  height: 48px;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-register:hover {
  background: #0056CC;
}

/* ---------- Image Capture Section ---------- */
.image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
}

.img-2{
  width: 402.67px; /* rounded for readability */
  height: 366px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  opacity: 1;
}

.image-box {
  width: 402.67px;   /* rounded for simplicity */
  height: 302px;
  border-radius: 8px;
  opacity: 1;
  background-color: #D1D5DB;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.image-box video {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.image-box .captured-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.open-camera-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.btn-open-camera {
  width: 160px;
  height: 48px;
  border-radius: 8px;
  border: 1px solid #007AFF;
  background: transparent;
  color: #007AFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-open-camera:hover {
  background: #007AFF;
  color: white;
}

.btn-open-camera img {
  width: 20px;
  height: 20px;
}

.actions {
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: space-between;
}

.btn-retake {
  width: 140px;
  min-width: 140px;
  height: 48px;
  min-height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  box-sizing: border-box;
  border: 1px solid #007AFF;
  opacity: 1;
  background: transparent;
  color: #007AFF;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-retake:hover {
  background: #007AFF;
  color: white;
}



.btn-save {
  width: 120px;
  min-width: 120px;
  height: 48px;
  min-height: 48px;
  border-radius: 8px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  box-sizing: border-box;
  border: 1px solid #007AFF;
  opacity: 1;
  background: #007AFF;
  color: white;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-save:hover {
  background: #0056CC;
}

.capture-controls {
  display: flex;
  gap: 0;
  width: 96px;
  height: 48px;
  position: relative;
}

.btn-camera {
  width: 48px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  opacity: 1;
  cursor: pointer;
  background-color: #007AFF;
  border: 1px solid #007AFF;
  transition: all 0.3s ease;
}

.btn-camera:hover {
  background-color: #0056CC;
}

.btn-dropdown {
  width: 48px;
  height: 48px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-top-right-radius: 8px;
  border-bottom-right-radius: 8px;
  border: 1px solid #007AFF;
  opacity: 1;
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-dropdown:hover {
  background: #F0F8FF;
}

.btn-camera img,
.btn-dropdown img {
  max-width: 24px;
  max-height: 24px;
}

/* Camera Dropdown Styles */
.camera-dropdown {
  position: absolute;
  top: 52px;
  right: 0;
  width: 250px;
  background: white;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
}

.dropdown-header {
  padding: 12px 16px;
  background: #F9FAFB;
  border-bottom: 1px solid #E5E7EB;
  font-family: 'DM Sans', sans-serif;
  font-weight: 600;
  font-size: 14px;
  color: #374151;
}

.dropdown-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  font-family: 'DM Sans', sans-serif;
  font-size: 14px;
  color: #374151;
  transition: background 0.2s ease;
}

.dropdown-item:hover {
  background: #F3F4F6;
}

.dropdown-item.selected {
  background: #EBF8FF;
  color: #007AFF;
}

.check-icon {
  color: #007AFF;
  font-weight: bold;
  font-size: 16px;
}

/* Mobile Upload Documents Section */
.upload-section {
  background: #FFFFFF;
  padding: 24px 20px;
}

.upload-section h3 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #1F2937;
  margin: 0 0 24px 0;
}

/* No Documents State */
.no-documents-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  border: 2px dashed #D1D5DB;
  border-radius: 16px;
  background: #F9FAFB;
  margin-bottom: 20px;
}

.upload-icon {
  width: 64px;
  height: 64px;
  background: #E5E7EB;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  font-size: 24px;
  color: #9CA3AF;
}

.upload-text {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #1F2937;
  margin-bottom: 8px;
}

.upload-subtext {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 24px;
  line-height: 1.5;
}

.upload-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.choose-file, .capture-image {
  color: #3B82F6;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.2s;
}

.choose-file:hover, .capture-image:hover {
  color: #2563EB;
}

.upload-separator {
  font-size: 14px;
  color: #6B7280;
  margin: 0 8px;
}

/* Mobile Uploaded Documents List */
.uploaded-docs {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.uploaded-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: #FFFFFF;
  border: 1px solid #E5E7EB;
  border-radius: 12px;
  gap: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.uploaded-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.doc-preview {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background: #F3F4F6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.doc-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doc-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.doc-name {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: #1F2937;
  margin: 0;
}

.doc-meta {
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  color: #6B7280;
  margin: 0;
}

.doc-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 8px;
  background: #F3F4F6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.icon-btn.edit {
  background: #DBEAFE;
  color: #3B82F6;
}

.icon-btn.delete {
  background: #FEE2E2;
  color: #EF4444;
}

.icon-btn:hover {
  transform: scale(1.05);
}

/* Mobile Upload Button */
.upload-button {
  width: 100%;
  height: 52px;
  background: #FFFFFF;
  border: 2px solid #3B82F6;
  border-radius: 12px;
  color: #3B82F6;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 20px;
}

.upload-button:hover {
  background: #3B82F6;
  color: #FFFFFF;
}

.upload-button ion-icon {
  font-size: 20px;
}

/* Mobile Camera Section */
.camera-section {
  background: #FFFFFF;
  padding: 24px 20px;
}

.camera-section h3 {
  font-family: 'Inter', sans-serif;
  font-weight: 600;
  font-size: 18px;
  color: #1F2937;
  margin: 0 0 24px 0;
}

.camera-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.camera-preview {
  width: 100%;
  max-width: 400px;
  height: 300px;
  border-radius: 16px;
  background: #F3F4F6;
  border: 2px dashed #D1D5DB;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.camera-preview video,
.camera-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 14px;
}

.camera-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: #9CA3AF;
}

.camera-placeholder ion-icon {
  font-size: 48px;
}

.camera-placeholder-text {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 500;
  color: #6B7280;
}

.camera-actions {
  display: flex;
  gap: 16px;
  width: 100%;
  max-width: 400px;
}

.btn-camera-primary {
  flex: 1;
  height: 52px;
  background: #3B82F6;
  color: #FFFFFF;
  border: none;
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-camera-primary:hover {
  background: #2563EB;
}

.btn-camera-secondary {
  flex: 1;
  height: 52px;
  background: #FFFFFF;
  color: #3B82F6;
  border: 2px solid #3B82F6;
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-camera-secondary:hover {
  background: #3B82F6;
  color: #FFFFFF;
}

/* Mobile Form Actions (Bottom Navigation) */
.form-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: #FFFFFF;
  border-top: 1px solid #E5E7EB;
  gap: 16px;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.btn-back {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #3B82F6;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  padding: 12px 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  min-width: 100px;
  justify-content: center;
}

.btn-back:hover {
  background: #F0F8FF;
}

.btn-back ion-icon {
  font-size: 20px;
}

.btn-next, .btn-register {
  height: 52px;
  background: #3B82F6;
  color: #FFFFFF;
  border: none;
  border-radius: 12px;
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  padding: 0 24px;
  transition: all 0.3s ease;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-next:hover, .btn-register:hover {
  background: #2563EB;
}

.btn-next:disabled, .btn-register:disabled {
  background: #D1D5DB;
  color: #9CA3AF;
  cursor: not-allowed;
}

.btn-next ion-icon, .btn-register ion-icon {
  font-size: 20px;
}

/* Mobile Bottom Tab Bar */
.bottom-tabs {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 12px 0;
  background: #FFFFFF;
  border-top: 1px solid #E5E7EB;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 8px;
  min-width: 60px;
}

.tab-item:hover {
  background: #F3F4F6;
}

.tab-item.active {
  color: #3B82F6;
}

.tab-item ion-icon {
  font-size: 24px;
}

.tab-label {
  font-family: 'Inter', sans-serif;
  font-size: 12px;
  font-weight: 500;
}

/* Android-Specific Optimizations */
@media screen and (max-width: 768px) and (orientation: portrait) {
  /* Ensure full viewport usage on Android */
  .container {
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for modern browsers */
  }

  /* Optimize touch targets for Android */
  .btn-back, .btn-next, .btn-register, .upload-button,
  .btn-camera-primary, .btn-camera-secondary {
    min-height: 48px;
    touch-action: manipulation;
  }

  /* Android keyboard handling */
  .form-section {
    padding-bottom: 100px; /* Extra space for keyboard */
  }

  /* Improve scrolling on Android */
  .form-section {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  /* Android-specific input styling */
  input, select, textarea {
    appearance: none;
    -webkit-appearance: none;
    -webkit-border-radius: 12px;
    border-radius: 12px;
  }

  /* Prevent zoom on input focus for Android */
  input, select, textarea {
    font-size: 16px !important;
  }
}

/* Desktop fallback styles (hidden on mobile) */
@media (min-width: 769px) {
  .container {
    width: 90%;
    max-width: 1200px;
    height: 90vh;
    margin: 5vh auto;
    border-radius: 16px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  }
}

/* ================================
   FINAL ANDROID MOBILE STYLES
   ================================ */

/* Perfect Android Mobile Design */
@media screen and (max-width: 768px) {
  /* Base container for mobile */
  .container {
    width: 100%;
    height: 100vh;
    height: 100dvh;
    border-radius: 0;
    background: #F8F9FA;
  }

  /* Mobile header with back button and profile */
  .header-container {
    padding: 16px 20px;
    background: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .title {
    font-size: 18px;
    font-weight: 600;
    color: #1F2937;
  }

  /* Optimized progress steps for mobile */
  .progress-steps {
    padding: 20px;
    background: #FFFFFF;
    border-bottom: 1px solid #E5E7EB;
    margin-top: 0;
    width: 100%;
    justify-content: center;
  }

  .step {
    flex: 1;
    max-width: 80px;
    margin-right: 0;
  }

  .step .dot {
    width: 12px;
    height: 12px;
    margin-right: 0;
  }

  .step:not(:last-child)::after {
    width: calc(100% - 16px);
    left: 12px;
    right: -4px;
    top: 6px;
  }

  /* Mobile form sections */
  .form-section {
    padding: 24px 20px;
    background: #FFFFFF;
    margin-top: 0;
    border-radius: 0;
  }

  .form-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 24px;
    padding: 0;
  }

  /* Perfect mobile form inputs */
  input, select, textarea {
    height: 48px;
    font-size: 16px !important; /* Prevent zoom on iOS */
    border-radius: 12px;
    border: 1px solid #D1D5DB;
    padding: 12px 16px;
    background: #FFFFFF;
    appearance: none;
    -webkit-appearance: none;
  }

  input:focus, select:focus, textarea:focus {
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    outline: none;
  }

  label {
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    margin-top: 16px;
  }

  /* Mobile form actions (bottom navigation) */
  .form-actions {
    padding: 20px;
    background: #FFFFFF;
    border-top: 1px solid #E5E7EB;
    gap: 16px;
    position: sticky;
    bottom: 0;
    z-index: 100;
  }

  .btn-back {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    color: #3B82F6;
    background: #FFFFFF;
    border: none;
    border-radius: 12px;
    min-height: 48px;
    touch-action: manipulation;
  }

  .btn-next, .btn-register {
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    background: #3B82F6;
    color: #FFFFFF;
    border: none;
    min-width: 120px;
    min-height: 48px;
    touch-action: manipulation;
  }

  .btn-next:hover, .btn-register:hover {
    background: #2563EB;
  }

  /* Optimize scrolling for Android */
  .form-section {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
    padding-bottom: 100px; /* Extra space for keyboard */
  }
}

/* Extra small Android devices */
@media (max-width: 360px) {
  .progress-steps {
    padding: 6px 8px 12px 8px;
    gap: 2px;
  }

  .step .dot {
    width: 10px;
    height: 10px;
  }

  .step:not(:last-child)::after {
    height: 1px;
    top: 4px;
  }

  .label .step-title {
    font-size: 9px;
  }

  .label .step-status {
    font-size: 7px;
  }

  .form-actions {
    padding: 10px 12px;
  }

  .btn-back {
    min-width: 60px;
    font-size: 12px;
  }

  .btn-next,
  .btn-register {
    min-width: 80px;
    font-size: 12px;
  }
}
