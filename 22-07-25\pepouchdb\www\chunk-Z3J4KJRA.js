import{b as we}from"./chunk-JIT2VFLB.js";import{e as ue,f as pe,g as me}from"./chunk-PK2JKDOR.js";import{g as c3,r as l3}from"./chunk-EHNA26RN.js";import{$ as X,$a as Fn,A as kn,B as v0,C as F1,D as D1,E as q,F as yn,G as re,H as u1,I as D,J as j,K as w1,L as Y0,M as e2,N as ce,Pa as t2,Q as T1,Qa as jn,R as f1,Ra as ve,S as s1,Sa as Vn,T as N,Ta as On,U as F,Ua as ge,V as M2,Va as Hn,W as z0,Wa as Dn,X as o0,Xa as En,Y as i0,Ya as Pn,Z as le,Za as Tn,_ as w0,_a as Rn,a as mn,aa as U1,ab as qn,b as K,ba as C0,bb as Nn,c as e0,ca as zn,cb as $n,d as x2,da as Cn,db as Un,e as Q0,ea as U,eb as Gn,f as t0,fa as _n,fb as Kn,g as wn,ga as bn,gb as Wn,h as n0,ha as H0,hb as Zn,i as ie,ia as de,ib as Qn,j as X0,ja as Bn,jb as he,k as fn,ka as k2,kb as D0,l as y0,ma as Sn,mb as Xn,n as J0,na as An,nb as Jn,o as k1,oa as Ln,ob as Yn,p as y1,pb as e3,q as d0,qa as In,qb as t3,r as xn,rb as n3,s as m0,sb as o3,t as ae,tb as i3,u as R,ub as a3,v as se,vb as s3,w as r1,wb as n2,x as m,xb as r3,y as Mn,z as T,zb as y2}from"./chunk-UPGPZ5HH.js";import{a as $1,b as p0,e as oe,f as Z0,g as Q}from"./chunk-2R6CW7ES.js";var e4=oe((z3,C3)=>{"use strict";(function(e){if(typeof z3=="object")C3.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var o;try{o=window}catch{o=self}o.SparkMD5=e()}})(function(e){"use strict";var o=function(u,h){return u+h&4294967295},n=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function t(u,h,r,v,w,f){return h=o(o(h,u),o(v,f)),o(h<<w|h>>>32-w,r)}function i(u,h){var r=u[0],v=u[1],w=u[2],f=u[3];r+=(v&w|~v&f)+h[0]-680876936|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[1]-389564586|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[2]+606105819|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[3]-1044525330|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[4]-176418897|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[5]+1200080426|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[6]-1473231341|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[7]-45705983|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[8]+1770035416|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[9]-1958414417|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[10]-42063|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[11]-1990404162|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[12]+1804603682|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[13]-40341101|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[14]-1502002290|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[15]+1236535329|0,v=(v<<22|v>>>10)+w|0,r+=(v&f|w&~f)+h[1]-165796510|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[6]-1069501632|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[11]+643717713|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[0]-373897302|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[5]-701558691|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[10]+38016083|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[15]-660478335|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[4]-405537848|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[9]+568446438|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[14]-1019803690|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[3]-187363961|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[8]+1163531501|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[13]-1444681467|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[2]-51403784|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[7]+1735328473|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[12]-1926607734|0,v=(v<<20|v>>>12)+w|0,r+=(v^w^f)+h[5]-378558|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[8]-2022574463|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[11]+1839030562|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[14]-35309556|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[1]-1530992060|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[4]+1272893353|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[7]-155497632|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[10]-1094730640|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[13]+681279174|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[0]-358537222|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[3]-722521979|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[6]+76029189|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[9]-640364487|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[12]-421815835|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[15]+530742520|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[2]-995338651|0,v=(v<<23|v>>>9)+w|0,r+=(w^(v|~f))+h[0]-198630844|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[7]+1126891415|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[14]-1416354905|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[5]-57434055|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[12]+1700485571|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[3]-1894986606|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[10]-1051523|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[1]-2054922799|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[8]+1873313359|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[15]-30611744|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[6]-1560198380|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[13]+1309151649|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[4]-145523070|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[11]-1120210379|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[2]+718787259|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[9]-343485551|0,v=(v<<21|v>>>11)+w|0,u[0]=r+u[0]|0,u[1]=v+u[1]|0,u[2]=w+u[2]|0,u[3]=f+u[3]|0}function a(u){var h=[],r;for(r=0;r<64;r+=4)h[r>>2]=u.charCodeAt(r)+(u.charCodeAt(r+1)<<8)+(u.charCodeAt(r+2)<<16)+(u.charCodeAt(r+3)<<24);return h}function s(u){var h=[],r;for(r=0;r<64;r+=4)h[r>>2]=u[r]+(u[r+1]<<8)+(u[r+2]<<16)+(u[r+3]<<24);return h}function c(u){var h=u.length,r=[1732584193,-271733879,-1732584194,271733878],v,w,f,O,E,V;for(v=64;v<=h;v+=64)i(r,a(u.substring(v-64,v)));for(u=u.substring(v-64),w=u.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],v=0;v<w;v+=1)f[v>>2]|=u.charCodeAt(v)<<(v%4<<3);if(f[v>>2]|=128<<(v%4<<3),v>55)for(i(r,f),v=0;v<16;v+=1)f[v]=0;return O=h*8,O=O.toString(16).match(/(.*?)(.{0,8})$/),E=parseInt(O[2],16),V=parseInt(O[1],16)||0,f[14]=E,f[15]=V,i(r,f),r}function l(u){var h=u.length,r=[1732584193,-271733879,-1732584194,271733878],v,w,f,O,E,V;for(v=64;v<=h;v+=64)i(r,s(u.subarray(v-64,v)));for(u=v-64<h?u.subarray(v-64):new Uint8Array(0),w=u.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],v=0;v<w;v+=1)f[v>>2]|=u[v]<<(v%4<<3);if(f[v>>2]|=128<<(v%4<<3),v>55)for(i(r,f),v=0;v<16;v+=1)f[v]=0;return O=h*8,O=O.toString(16).match(/(.*?)(.{0,8})$/),E=parseInt(O[2],16),V=parseInt(O[1],16)||0,f[14]=E,f[15]=V,i(r,f),r}function x(u){var h="",r;for(r=0;r<4;r+=1)h+=n[u>>r*8+4&15]+n[u>>r*8&15];return h}function k(u){var h;for(h=0;h<u.length;h+=1)u[h]=x(u[h]);return u.join("")}k(c("hello"))!=="5d41402abc4b2a76b9719d911017c592"&&(o=function(u,h){var r=(u&65535)+(h&65535),v=(u>>16)+(h>>16)+(r>>16);return v<<16|r&65535}),typeof ArrayBuffer<"u"&&!ArrayBuffer.prototype.slice&&function(){function u(h,r){return h=h|0||0,h<0?Math.max(h+r,0):Math.min(h,r)}ArrayBuffer.prototype.slice=function(h,r){var v=this.byteLength,w=u(h,v),f=v,O,E,V,n1;return r!==e&&(f=u(r,v)),w>f?new ArrayBuffer(0):(O=f-w,E=new ArrayBuffer(O),V=new Uint8Array(E),n1=new Uint8Array(this,w,O),V.set(n1),E)}}();function d(u){return/[\u0080-\uFFFF]/.test(u)&&(u=unescape(encodeURIComponent(u))),u}function M(u,h){var r=u.length,v=new ArrayBuffer(r),w=new Uint8Array(v),f;for(f=0;f<r;f+=1)w[f]=u.charCodeAt(f);return h?w:v}function _(u){return String.fromCharCode.apply(null,new Uint8Array(u))}function B(u,h,r){var v=new Uint8Array(u.byteLength+h.byteLength);return v.set(new Uint8Array(u)),v.set(new Uint8Array(h),u.byteLength),r?v:v.buffer}function A(u){var h=[],r=u.length,v;for(v=0;v<r-1;v+=2)h.push(parseInt(u.substr(v,2),16));return String.fromCharCode.apply(String,h)}function z(){this.reset()}return z.prototype.append=function(u){return this.appendBinary(d(u)),this},z.prototype.appendBinary=function(u){this._buff+=u,this._length+=u.length;var h=this._buff.length,r;for(r=64;r<=h;r+=64)i(this._hash,a(this._buff.substring(r-64,r)));return this._buff=this._buff.substring(r-64),this},z.prototype.end=function(u){var h=this._buff,r=h.length,v,w=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],f;for(v=0;v<r;v+=1)w[v>>2]|=h.charCodeAt(v)<<(v%4<<3);return this._finish(w,r),f=k(this._hash),u&&(f=A(f)),this.reset(),f},z.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},z.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},z.prototype.setState=function(u){return this._buff=u.buff,this._length=u.length,this._hash=u.hash,this},z.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},z.prototype._finish=function(u,h){var r=h,v,w,f;if(u[r>>2]|=128<<(r%4<<3),r>55)for(i(this._hash,u),r=0;r<16;r+=1)u[r]=0;v=this._length*8,v=v.toString(16).match(/(.*?)(.{0,8})$/),w=parseInt(v[2],16),f=parseInt(v[1],16)||0,u[14]=w,u[15]=f,i(this._hash,u)},z.hash=function(u,h){return z.hashBinary(d(u),h)},z.hashBinary=function(u,h){var r=c(u),v=k(r);return h?A(v):v},z.ArrayBuffer=function(){this.reset()},z.ArrayBuffer.prototype.append=function(u){var h=B(this._buff.buffer,u,!0),r=h.length,v;for(this._length+=u.byteLength,v=64;v<=r;v+=64)i(this._hash,s(h.subarray(v-64,v)));return this._buff=v-64<r?new Uint8Array(h.buffer.slice(v-64)):new Uint8Array(0),this},z.ArrayBuffer.prototype.end=function(u){var h=this._buff,r=h.length,v=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],w,f;for(w=0;w<r;w+=1)v[w>>2]|=h[w]<<(w%4<<3);return this._finish(v,r),f=k(this._hash),u&&(f=A(f)),this.reset(),f},z.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},z.ArrayBuffer.prototype.getState=function(){var u=z.prototype.getState.call(this);return u.buff=_(u.buff),u},z.ArrayBuffer.prototype.setState=function(u){return u.buff=M(u.buff,!0),z.prototype.setState.call(this,u)},z.ArrayBuffer.prototype.destroy=z.prototype.destroy,z.ArrayBuffer.prototype._finish=z.prototype._finish,z.ArrayBuffer.hash=function(u,h){var r=l(new Uint8Array(u)),v=k(r);return h?A(v):v},z})});var S3=oe(n4=>{"use strict";n4.stringify=function(o){var n=[];n.push({obj:o});for(var t="",i,a,s,c,l,x,k,d,M,_,B;i=n.pop();)if(a=i.obj,s=i.prefix||"",c=i.val||"",t+=s,c)t+=c;else if(typeof a!="object")t+=typeof a>"u"?null:JSON.stringify(a);else if(a===null)t+="null";else if(Array.isArray(a)){for(n.push({val:"]"}),l=a.length-1;l>=0;l--)x=l===0?"":",",n.push({obj:a[l],prefix:x});n.push({val:"["})}else{k=[];for(d in a)a.hasOwnProperty(d)&&k.push(d);for(n.push({val:"}"}),l=k.length-1;l>=0;l--)M=k[l],_=a[M],B=l>0?",":"",B+=JSON.stringify(M)+":",n.push({obj:_,prefix:B});n.push({val:"{"})}return t};function E0(e,o,n){var t=n[n.length-1];e===t.element&&(n.pop(),t=n[n.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(e);else if(a===o.length-2){var s=o.pop();i[s]=e}else o.push(e)}n4.parse=function(e){for(var o=[],n=[],t=0,i,a,s,c,l,x,k,d,M;;){if(i=e[t++],i==="}"||i==="]"||typeof i>"u"){if(o.length===1)return o.pop();E0(o.pop(),o,n);continue}switch(i){case" ":case"	":case`
`:case":":case",":break;case"n":t+=3,E0(null,o,n);break;case"t":t+=3,E0(!0,o,n);break;case"f":t+=4,E0(!1,o,n);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"-":for(a="",t--;;)if(s=e[t++],/[\d\.\-e\+]/.test(s))a+=s;else{t--;break}E0(parseFloat(a),o,n);break;case'"':for(c="",l=void 0,x=0;k=e[t++],k!=='"'||l==="\\"&&x%2===1;)c+=k,l=k,l==="\\"?x++:x=0;E0(JSON.parse('"'+c+'"'),o,n);break;case"[":d={element:[],index:o.length},o.push(d.element),n.push(d);break;case"{":M={element:{},index:o.length},o.push(M.element),n.push(M);break;default:throw new Error("unexpectedly reached end of input: "+i)}}}});var i4=oe((E7,o4)=>{"use strict";var P0=typeof Reflect=="object"?Reflect:null,A3=P0&&typeof P0.apply=="function"?P0.apply:function(o,n,t){return Function.prototype.apply.call(o,n,t)},b2;P0&&typeof P0.ownKeys=="function"?b2=P0.ownKeys:Object.getOwnPropertySymbols?b2=function(o){return Object.getOwnPropertyNames(o).concat(Object.getOwnPropertySymbols(o))}:b2=function(o){return Object.getOwnPropertyNames(o)};function J8(e){console&&console.warn&&console.warn(e)}var I3=Number.isNaN||function(o){return o!==o};function z1(){z1.init.call(this)}o4.exports=z1;o4.exports.once=na;z1.EventEmitter=z1;z1.prototype._events=void 0;z1.prototype._eventsCount=0;z1.prototype._maxListeners=void 0;var L3=10;function B2(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(z1,"defaultMaxListeners",{enumerable:!0,get:function(){return L3},set:function(e){if(typeof e!="number"||e<0||I3(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");L3=e}});z1.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};z1.prototype.setMaxListeners=function(o){if(typeof o!="number"||o<0||I3(o))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+o+".");return this._maxListeners=o,this};function j3(e){return e._maxListeners===void 0?z1.defaultMaxListeners:e._maxListeners}z1.prototype.getMaxListeners=function(){return j3(this)};z1.prototype.emit=function(o){for(var n=[],t=1;t<arguments.length;t++)n.push(arguments[t]);var i=o==="error",a=this._events;if(a!==void 0)i=i&&a.error===void 0;else if(!i)return!1;if(i){var s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;var c=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw c.context=s,c}var l=a[o];if(l===void 0)return!1;if(typeof l=="function")A3(l,this,n);else for(var x=l.length,k=E3(l,x),t=0;t<x;++t)A3(k[t],this,n);return!0};function V3(e,o,n,t){var i,a,s;if(B2(n),a=e._events,a===void 0?(a=e._events=Object.create(null),e._eventsCount=0):(a.newListener!==void 0&&(e.emit("newListener",o,n.listener?n.listener:n),a=e._events),s=a[o]),s===void 0)s=a[o]=n,++e._eventsCount;else if(typeof s=="function"?s=a[o]=t?[n,s]:[s,n]:t?s.unshift(n):s.push(n),i=j3(e),i>0&&s.length>i&&!s.warned){s.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(o)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=o,c.count=s.length,J8(c)}return e}z1.prototype.addListener=function(o,n){return V3(this,o,n,!1)};z1.prototype.on=z1.prototype.addListener;z1.prototype.prependListener=function(o,n){return V3(this,o,n,!0)};function Y8(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function O3(e,o,n){var t={fired:!1,wrapFn:void 0,target:e,type:o,listener:n},i=Y8.bind(t);return i.listener=n,t.wrapFn=i,i}z1.prototype.once=function(o,n){return B2(n),this.on(o,O3(this,o,n)),this};z1.prototype.prependOnceListener=function(o,n){return B2(n),this.prependListener(o,O3(this,o,n)),this};z1.prototype.removeListener=function(o,n){var t,i,a,s,c;if(B2(n),i=this._events,i===void 0)return this;if(t=i[o],t===void 0)return this;if(t===n||t.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete i[o],i.removeListener&&this.emit("removeListener",o,t.listener||n));else if(typeof t!="function"){for(a=-1,s=t.length-1;s>=0;s--)if(t[s]===n||t[s].listener===n){c=t[s].listener,a=s;break}if(a<0)return this;a===0?t.shift():ea(t,a),t.length===1&&(i[o]=t[0]),i.removeListener!==void 0&&this.emit("removeListener",o,c||n)}return this};z1.prototype.off=z1.prototype.removeListener;z1.prototype.removeAllListeners=function(o){var n,t,i;if(t=this._events,t===void 0)return this;if(t.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):t[o]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete t[o]),this;if(arguments.length===0){var a=Object.keys(t),s;for(i=0;i<a.length;++i)s=a[i],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=t[o],typeof n=="function")this.removeListener(o,n);else if(n!==void 0)for(i=n.length-1;i>=0;i--)this.removeListener(o,n[i]);return this};function H3(e,o,n){var t=e._events;if(t===void 0)return[];var i=t[o];return i===void 0?[]:typeof i=="function"?n?[i.listener||i]:[i]:n?ta(i):E3(i,i.length)}z1.prototype.listeners=function(o){return H3(this,o,!0)};z1.prototype.rawListeners=function(o){return H3(this,o,!1)};z1.listenerCount=function(e,o){return typeof e.listenerCount=="function"?e.listenerCount(o):D3.call(e,o)};z1.prototype.listenerCount=D3;function D3(e){var o=this._events;if(o!==void 0){var n=o[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}z1.prototype.eventNames=function(){return this._eventsCount>0?b2(this._events):[]};function E3(e,o){for(var n=new Array(o),t=0;t<o;++t)n[t]=e[t];return n}function ea(e,o){for(;o+1<e.length;o++)e[o]=e[o+1];e.pop()}function ta(e){for(var o=new Array(e.length),n=0;n<o.length;++n)o[n]=e[n].listener||e[n];return o}function na(e,o){return new Promise(function(n,t){function i(s){e.removeListener(o,a),t(s)}function a(){typeof e.removeListener=="function"&&e.removeListener("error",i),n([].slice.call(arguments))}P3(e,o,a,{once:!0}),o!=="error"&&oa(e,i,{once:!0})})}function oa(e,o,n){typeof e.on=="function"&&P3(e,"error",o,n)}function P3(e,o,n,t){if(typeof e.on=="function")t.once?e.once(o,n):e.on(o,n);else if(typeof e.addEventListener=="function")e.addEventListener(o,function i(a){t.once&&e.removeEventListener(o,i),n(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var h3=(e,o)=>Q(null,null,function*(){if(!(typeof window>"u"))return yield c3(),l3(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16,"router-animation"]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16,"html-attributes"],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16,"counter-formatter"],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16,"root-params"],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16,"component-props"],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16,"counter-formatter"],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16,"component-props"],"beforeLeave":[16,"before-leave"],"beforeEnter":[16,"before-enter"]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-input-otp",[[38,"ion-input-otp",{"autocapitalize":[1],"color":[513],"disabled":[516],"fill":[1],"inputmode":[1],"length":[2],"pattern":[1],"readonly":[516],"separators":[1],"shape":[1],"size":[1],"type":[1],"value":[1032],"inputValues":[32],"hasFocus":[32],"previousInputValues":[32],"setFocus":[64]},null,{"value":["valueChanged"],"separators":["processSeparators"],"length":["processSeparators"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16,"pin-formatter"],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16,"format-options"],"readonly":[4],"isDateEnabled":[16,"is-date-enabled"],"showAdjacentDays":[4,"show-adjacent-days"],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16,"title-selected-dates-formatter"],"multiple":[4],"highlightedDates":[16,"highlighted-dates"],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16,"component-props"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16,"presenting-element"],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},[[9,"resize","onWindowResize"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"component":[1],"componentProps":[16,"component-props"],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16,"router-animation"],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32],"isInteractive":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16,"swipe-handler"],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),o)});(function(){if(typeof window<"u"&&window.Reflect!==void 0&&window.customElements!==void 0){var e=HTMLElement;window.HTMLElement=function(){return Reflect.construct(e,[],this.constructor)},HTMLElement.prototype=e.prototype,HTMLElement.prototype.constructor=HTMLElement,Object.setPrototypeOf(HTMLElement,e)}})();var G=["*"],$o=["outletContent"],Uo=["outlet"],Go=[[["","slot","top"]],"*",[["ion-tab"]]],Ko=["[slot=top]","*","ion-tab"];function Wo(e,o){if(e&1){let n=T1();D(0,"ion-router-outlet",5,1),f1("stackWillChange",function(i){k1(n);let a=s1();return y1(a.onStackWillChange(i))})("stackDidChange",function(i){k1(n);let a=s1();return y1(a.onStackDidChange(i))}),j()}}function Zo(e,o){e&1&&F(0,2,["*ngIf","tabs.length > 0"])}function Qo(e,o){if(e&1&&(D(0,"div",1),ce(1,2),j()),e&2){let n=s1();r1(),u1("ngTemplateOutlet",n.template)}}function Xo(e,o){if(e&1&&ce(0,1),e&2){let n=s1();u1("ngTemplateOutlet",n.template)}}var Jo=(()=>{class e extends n2{constructor(n,t){super(n,t)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,r3(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}static \u0275fac=function(t){return new(t||e)(m(d0),m(R))};static \u0275dir=v0({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(t,i){t&1&&f1("ionChange",function(s){return i._handleIonChange(s.target)})},standalone:!1,features:[C0([{provide:t2,useExisting:e,multi:!0}]),F1]})}return e})(),Yo=(()=>{class e extends n2{el;constructor(n,t){super(n,t),this.el=t}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){this.el.nativeElement.tagName==="ION-INPUT"||this.el.nativeElement.tagName==="ION-INPUT-OTP"?super.registerOnChange(t=>{n(t===""?null:parseFloat(t))}):super.registerOnChange(n)}static \u0275fac=function(t){return new(t||e)(m(d0),m(R))};static \u0275dir=v0({type:e,selectors:[["ion-input","type","number"],["ion-input-otp",3,"type","text"],["ion-range"]],hostBindings:function(t,i){t&1&&f1("ionInput",function(s){return i.handleInputEvent(s.target)})},standalone:!1,features:[C0([{provide:t2,useExisting:e,multi:!0}]),F1]})}return e})(),ei=(()=>{class e extends n2{constructor(n,t){super(n,t)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(t){return new(t||e)(m(d0),m(R))};static \u0275dir=v0({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(t,i){t&1&&f1("ionChange",function(s){return i._handleChangeEvent(s.target)})},standalone:!1,features:[C0([{provide:t2,useExisting:e,multi:!0}]),F1]})}return e})(),ti=(()=>{class e extends n2{constructor(n,t){super(n,t)}_handleInputEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(t){return new(t||e)(m(d0),m(R))};static \u0275dir=v0({type:e,selectors:[["ion-input",3,"type","number"],["ion-input-otp","type","text"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(t,i){t&1&&f1("ionInput",function(s){return i._handleInputEvent(s.target)})},standalone:!1,features:[C0([{provide:t2,useExisting:e,multi:!0}]),F1]})}return e})(),ni=(e,o)=>{let n=e.prototype;o.forEach(t=>{Object.defineProperty(n,t,{get(){return this.el[t]},set(i){this.z.runOutsideAngular(()=>this.el[t]=i)},configurable:!0})})},oi=(e,o)=>{let n=e.prototype;o.forEach(t=>{n[t]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[t].apply(this.el,i))}})},x1=(e,o,n)=>{n.forEach(t=>e[t]=wn(o,t))};function W(e){return function(n){let{defineCustomElementFn:t,inputs:i,methods:a}=e;return t!==void 0&&t(),i&&ni(n,i),a&&oi(n,a),n}}var ii=(()=>{let e=class fe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||fe)(m(U),m(R),m(q))};static \u0275cmp=T({type:fe,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),ai=(()=>{let e=class xe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||xe)(m(U),m(R),m(q))};static \u0275cmp=T({type:xe,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),si=(()=>{let e=class Me{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||Me)(m(U),m(R),m(q))};static \u0275cmp=T({type:Me,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),ri=(()=>{let e=class ke{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||ke)(m(U),m(R),m(q))};static \u0275cmp=T({type:ke,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),ci=(()=>{let e=class ye{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ye)(m(U),m(R),m(q))};static \u0275cmp=T({type:ye,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({methods:["setFocus"]})],e),e})(),li=(()=>{let e=class ze{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ze)(m(U),m(R),m(q))};static \u0275cmp=T({type:ze,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),di=(()=>{let e=class Ce{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionBackdropTap"])}static \u0275fac=function(t){return new(t||Ce)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ce,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["stopPropagation","tappable","visible"]})],e),e})(),vi=(()=>{let e=class _e{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||_e)(m(U),m(R),m(q))};static \u0275cmp=T({type:_e,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),gi=(()=>{let e=class be{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||be)(m(U),m(R),m(q))};static \u0275cmp=T({type:be,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),hi=(()=>{let e=class Be{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(t){return new(t||Be)(m(U),m(R),m(q))};static \u0275cmp=T({type:Be,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),ui=(()=>{let e=class Se{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Se)(m(U),m(R),m(q))};static \u0275cmp=T({type:Se,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),pi=(()=>{let e=class Ae{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ae)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ae,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["collapse"]})],e),e})(),mi=(()=>{let e=class Le{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Le)(m(U),m(R),m(q))};static \u0275cmp=T({type:Le,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),wi=(()=>{let e=class Ie{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ie)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ie,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["mode"]})],e),e})(),fi=(()=>{let e=class je{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||je)(m(U),m(R),m(q))};static \u0275cmp=T({type:je,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","translucent"]})],e),e})(),xi=(()=>{let e=class Ve{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ve)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ve,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),Mi=(()=>{let e=class Oe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Oe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Oe,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),ki=(()=>{let e=class He{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||He)(m(U),m(R),m(q))};static \u0275cmp=T({type:He,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),yi=(()=>{let e=class De{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||De)(m(U),m(R),m(q))};static \u0275cmp=T({type:De,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","mode","outline"]})],e),e})(),zi=(()=>{let e=class Ee{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ee)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ee,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),Ci=(()=>{let e=class Pe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(t){return new(t||Pe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Pe,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),_i=(()=>{let e=class Te{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Te)(m(U),m(R),m(q))};static \u0275cmp=T({type:Te,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showAdjacentDays:"showAdjacentDays",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showAdjacentDays","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),bi=(()=>{let e=class Re{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Re)(m(U),m(R),m(q))};static \u0275cmp=T({type:Re,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","datetime","disabled","mode"]})],e),e})(),Bi=(()=>{let e=class Fe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Fe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Fe,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),Si=(()=>{let e=class qe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||qe)(m(U),m(R),m(q))};static \u0275cmp=T({type:qe,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),Ai=(()=>{let e=class Ne{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ne)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ne,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activated","side"]})],e),e})(),Li=(()=>{let e=class $e{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||$e)(m(U),m(R),m(q))};static \u0275cmp=T({type:$e,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["collapse","mode","translucent"]})],e),e})(),Ii=(()=>{let e=class Ue{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ue)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ue,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["fixed"]})],e),e})(),ji=(()=>{let e=class Ge{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ge)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ge,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["collapse","mode","translucent"]})],e),e})(),Vi=(()=>{let e=class Ke{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ke)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ke,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),Oi=(()=>{let e=class We{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(t){return new(t||We)(m(U),m(R),m(q))};static \u0275cmp=T({type:We,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alt","src"]})],e),e})(),Hi=(()=>{let e=class Ze{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionInfinite"])}static \u0275fac=function(t){return new(t||Ze)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ze,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),Di=(()=>{let e=class Qe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Qe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Qe,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["loadingSpinner","loadingText"]})],e),e})(),Ei=(()=>{let e=class Xe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||Xe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Xe,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),Pi=(()=>{let e=class Je{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionInput","ionChange","ionComplete","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||Je)(m(U),m(R),m(q))};static \u0275cmp=T({type:Je,selectors:[["ion-input-otp"]],inputs:{autocapitalize:"autocapitalize",color:"color",disabled:"disabled",fill:"fill",inputmode:"inputmode",length:"length",pattern:"pattern",readonly:"readonly",separators:"separators",shape:"shape",size:"size",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autocapitalize","color","disabled","fill","inputmode","length","pattern","readonly","separators","shape","size","type","value"],methods:["setFocus"]})],e),e})(),Ti=(()=>{let e=class Ye{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ye)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ye,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),Ri=(()=>{let e=class et{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||et)(m(U),m(R),m(q))};static \u0275cmp=T({type:et,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),Fi=(()=>{let e=class tt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||tt)(m(U),m(R),m(q))};static \u0275cmp=T({type:tt,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","sticky"]})],e),e})(),qi=(()=>{let e=class nt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||nt)(m(U),m(R),m(q))};static \u0275cmp=T({type:nt,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),Ni=(()=>{let e=class ot{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ot)(m(U),m(R),m(q))};static \u0275cmp=T({type:ot,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),$i=(()=>{let e=class it{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionSwipe"])}static \u0275fac=function(t){return new(t||it)(m(U),m(R),m(q))};static \u0275cmp=T({type:it,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["side"]})],e),e})(),Ui=(()=>{let e=class at{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionDrag"])}static \u0275fac=function(t){return new(t||at)(m(U),m(R),m(q))};static \u0275cmp=T({type:at,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),Gi=(()=>{let e=class st{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||st)(m(U),m(R),m(q))};static \u0275cmp=T({type:st,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","position"]})],e),e})(),Ki=(()=>{let e=class rt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||rt)(m(U),m(R),m(q))};static \u0275cmp=T({type:rt,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),Wi=(()=>{let e=class ct{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ct)(m(U),m(R),m(q))};static \u0275cmp=T({type:ct,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","lines","mode"]})],e),e})(),Zi=(()=>{let e=class lt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||lt)(m(U),m(R),m(q))};static \u0275cmp=T({type:lt,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Qi=(()=>{let e=class dt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(t){return new(t||dt)(m(U),m(R),m(q))};static \u0275cmp=T({type:dt,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),Xi=(()=>{let e=class vt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||vt)(m(U),m(R),m(q))};static \u0275cmp=T({type:vt,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),Ji=(()=>{let e=class gt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||gt)(m(U),m(R),m(q))};static \u0275cmp=T({type:gt,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autoHide","menu"]})],e),e})(),Yi=(()=>{let e=class ht{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ht)(m(U),m(R),m(q))};static \u0275cmp=T({type:ht,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),e8=(()=>{let e=class ut{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ut)(m(U),m(R),m(q))};static \u0275cmp=T({type:ut,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),t8=(()=>{let e=class pt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||pt)(m(U),m(R),m(q))};static \u0275cmp=T({type:pt,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["mode"]})],e),e})(),n8=(()=>{let e=class mt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||mt)(m(U),m(R),m(q))};static \u0275cmp=T({type:mt,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),o8=(()=>{let e=class wt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||wt)(m(U),m(R),m(q))};static \u0275cmp=T({type:wt,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","value"]})],e),e})(),i8=(()=>{let e=class ft{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||ft)(m(U),m(R),m(q))};static \u0275cmp=T({type:ft,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),a8=(()=>{let e=class xt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||xt)(m(U),m(R),m(q))};static \u0275cmp=T({type:xt,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),s8=(()=>{let e=class Mt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Mt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Mt,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),r8=(()=>{let e=class kt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||kt)(m(U),m(R),m(q))};static \u0275cmp=T({type:kt,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),c8=(()=>{let e=class yt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(t){return new(t||yt)(m(U),m(R),m(q))};static \u0275cmp=T({type:yt,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),l8=(()=>{let e=class zt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(t){return new(t||zt)(m(U),m(R),m(q))};static \u0275cmp=T({type:zt,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),d8=(()=>{let e=class Ct{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ct)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ct,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),v8=(()=>{let e=class _t{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||_t)(m(U),m(R),m(q))};static \u0275cmp=T({type:_t,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),g8=(()=>{let e=class bt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionItemReorder"])}static \u0275fac=function(t){return new(t||bt)(m(U),m(R),m(q))};static \u0275cmp=T({type:bt,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled"],methods:["complete"]})],e),e})(),h8=(()=>{let e=class Bt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Bt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Bt,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["type"],methods:["addRipple"]})],e),e})(),u8=(()=>{let e=class St{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||St)(m(U),m(R),m(q))};static \u0275cmp=T({type:St,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),p8=(()=>{let e=class At{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||At)(m(U),m(R),m(q))};static \u0275cmp=T({type:At,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),m8=(()=>{let e=class Lt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||Lt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Lt,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),w8=(()=>{let e=class It{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||It)(m(U),m(R),m(q))};static \u0275cmp=T({type:It,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),f8=(()=>{let e=class jt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||jt)(m(U),m(R),m(q))};static \u0275cmp=T({type:jt,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),x8=(()=>{let e=class Vt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(t){return new(t||Vt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Vt,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled"]})],e),e})(),M8=(()=>{let e=class Ot{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Ot)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ot,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),k8=(()=>{let e=class Ht{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ht)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ht,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["header","multiple","options"]})],e),e})(),y8=(()=>{let e=class Dt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Dt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Dt,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","value"]})],e),e})(),z8=(()=>{let e=class Et{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Et)(m(U),m(R),m(q))};static \u0275cmp=T({type:Et,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated"]})],e),e})(),C8=(()=>{let e=class Pt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Pt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Pt,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","duration","name","paused"]})],e),e})(),_8=(()=>{let e=class Tt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(t){return new(t||Tt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Tt,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["contentId","disabled","when"]})],e),e})(),p3=(()=>{let e=class Rt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Rt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Rt,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["component","tab"],methods:["setActive"]})],e),e})(),Ft=(()=>{let e=class qt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||qt)(m(U),m(R),m(q))};static \u0275cmp=T({type:qt,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),b8=(()=>{let e=class Nt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Nt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Nt,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),B8=(()=>{let e=class $t{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||$t)(m(U),m(R),m(q))};static \u0275cmp=T({type:$t,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),S8=(()=>{let e=class Ut{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||Ut)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ut,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),A8=(()=>{let e=class Gt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Gt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Gt,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),L8=(()=>{let e=class Kt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Kt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Kt,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","size"]})],e),e})(),I8=(()=>{let e=class Wt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||Wt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Wt,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),j8=(()=>{let e=class Zt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,x1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Zt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Zt,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),V8=(()=>{let e=class Qt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Qt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Qt,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),z2=(()=>{class e extends Yn{parentOutlet;outletContent;constructor(n,t,i,a,s,c,l,x){super(n,t,i,a,s,c,l,x),this.parentOutlet=x}static \u0275fac=function(t){return new(t||e)(ae("name"),ae("tabs"),m(_n),m(R),m(In),m(q),m(Ln),m(e,12))};static \u0275cmp=T({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(t,i){if(t&1&&z0($o,7,Mn),t&2){let a;o0(a=i0())&&(i.outletContent=a.first)}},standalone:!1,features:[F1],ngContentSelectors:G,decls:3,vars:0,consts:[["outletContent",""]],template:function(t,i){t&1&&(N(),Y0(0,null,0),F(2),e2())},encapsulation:2})}return e})(),O8=(()=>{class e extends a3{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275cmp=T({type:e,selectors:[["ion-tabs"]],contentQueries:function(t,i,a){if(t&1&&(M2(a,Ft,5),M2(a,Ft,4),M2(a,p3,4)),t&2){let s;o0(s=i0())&&(i.tabBar=s.first),o0(s=i0())&&(i.tabBars=s),o0(s=i0())&&(i.tabs=s)}},viewQuery:function(t,i){if(t&1&&z0(Uo,5,z2),t&2){let a;o0(a=i0())&&(i.outlet=a.first)}},standalone:!1,features:[F1],ngContentSelectors:Ko,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(t,i){t&1&&(N(Go),F(0),D(1,"div",2,0),D1(3,Wo,2,0,"ion-router-outlet",3)(4,Zo,1,0,"ng-content",4),j(),F(5,1)),t&2&&(r1(3),u1("ngIf",i.tabs.length===0),r1(),u1("ngIf",i.tabs.length>0))},dependencies:[H0,z2],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),H8=(()=>{class e extends t3{constructor(n,t,i,a,s,c){super(n,t,i,a,s,c)}static \u0275fac=function(t){return new(t||e)(m(z2,8),m(Zn),m(Qn),m(R),m(q),m(U))};static \u0275cmp=T({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[F1],ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})}return e})(),D8=(()=>{class e extends i3{constructor(n,t,i,a,s,c){super(n,t,i,a,s,c)}static \u0275fac=function(t){return new(t||e)(m(R),m(J0),m(d0),m(D0),m(q),m(U))};static \u0275cmp=T({type:e,selectors:[["ion-nav"]],standalone:!1,features:[F1],ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})}return e})(),E8=(()=>{class e extends n3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=v0({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[F1]})}return e})(),P8=(()=>{class e extends o3{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=v0({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[F1]})}return e})(),T8=(()=>{class e extends Jn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275cmp=T({type:e,selectors:[["ion-modal"]],standalone:!1,features:[F1],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(t,i){t&1&&D1(0,Qo,2,1,"div",0),t&2&&u1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[H0,de],encapsulation:2,changeDetection:0})}return e})(),R8=(()=>{class e extends Xn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275cmp=T({type:e,selectors:[["ion-popover"]],standalone:!1,features:[F1],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(t,i){t&1&&D1(0,Xo,1,1,"ng-container",0),t&2&&u1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[H0,de],encapsulation:2,changeDetection:0})}return e})(),F8={provide:ve,useExisting:ie(()=>m3),multi:!0},m3=(()=>{class e extends Nn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=v0({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(t,i){t&2&&re("max",i._enabled?i.max:null)},standalone:!1,features:[C0([F8]),F1]})}return e})(),q8={provide:ve,useExisting:ie(()=>w3),multi:!0},w3=(()=>{class e extends $n{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=v0({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(t,i){t&2&&re("min",i._enabled?i.min:null)},standalone:!1,features:[C0([q8]),F1]})}return e})();var N8=(()=>{class e extends y2{angularDelegate=y0(D0);injector=y0(d0);environmentInjector=y0(J0);constructor(){super(ue)}create(n){return super.create(p0($1({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(t){return new(t||e)};static \u0275prov=X0({token:e,factory:e.\u0275fac})}return e})();var Xt=class extends y2{angularDelegate=y0(D0);injector=y0(d0);environmentInjector=y0(J0);constructor(){super(pe)}create(o){return super.create(p0($1({},o),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},f3=(()=>{class e extends y2{constructor(){super(me)}static \u0275fac=function(t){return new(t||e)};static \u0275prov=X0({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),$8=(e,o,n)=>()=>{let t=o.defaultView;if(t&&typeof window<"u"){we(p0($1({},e),{_zoneGate:a=>n.run(a)}));let i="__zone_symbol__addEventListener"in o.body?"__zone_symbol__addEventListener":"addEventListener";return h3(t,{exclude:["ion-tabs"],syncQueue:!0,raf:s3,jmp:a=>n.runOutsideAngular(a),ael(a,s,c,l){a[i](s,c,l)},rel(a,s,c,l){a.removeEventListener(s,c,l)}})}},U8=[ii,ai,si,ri,ci,li,di,vi,gi,hi,ui,pi,mi,wi,fi,xi,Mi,ki,yi,zi,Ci,_i,bi,Bi,Si,Ai,Li,Ii,ji,Vi,Oi,Hi,Di,Ei,Pi,Ti,Ri,Fi,qi,Ni,$i,Ui,Gi,Ki,Wi,Zi,Qi,Xi,Ji,Yi,e8,t8,n8,o8,i8,a8,s8,r8,c8,l8,d8,v8,g8,h8,u8,p8,m8,w8,f8,x8,M8,k8,y8,z8,C8,_8,p3,Ft,b8,B8,S8,A8,L8,I8,j8,V8],f7=[...U8,T8,R8,Jo,Yo,ei,ti,O8,z2,H8,D8,E8,P8,w3,m3],x3=(()=>{class e{static forRoot(n={}){return{ngModule:e,providers:[{provide:he,useValue:n},{provide:yn,useFactory:$8,multi:!0,deps:[he,xn,q]},D0,e3()]}}static \u0275fac=function(t){return new(t||e)};static \u0275mod=kn({type:e});static \u0275inj=fn({providers:[N8,Xt],imports:[k2]})}return e})();var Jt,K8=function(){if(typeof window>"u")return new Map;if(!Jt){var e=window;e.Ionicons=e.Ionicons||{},Jt=e.Ionicons.map=e.Ionicons.map||new Map}return Jt},Yt=function(e){Object.keys(e).forEach(function(o){M3(o,e[o]);var n=o.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g,"$1-$2").toLowerCase();o!==n&&M3(n,e[o])})},M3=function(e,o){var n=K8(),t=n.get(e);t===void 0?n.set(e,o):t!==o&&console.warn('[Ionicons Warning]: Multiple icons were mapped to name "'.concat(e,'". Ensure that multiple icons are not mapped to the same icon name.'))};var k3="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M384 224v184a40 40 0 01-40 40H104a40 40 0 01-40-40V168a40 40 0 0140-40h167.48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M459.94 53.25a16.06 16.06 0 00-23.22-.56L424.35 65a8 8 0 000 11.31l11.34 11.32a8 8 0 0011.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38zM399.34 90L218.82 270.2a9 9 0 00-2.31 3.93L208.16 299a3.91 3.91 0 004.86 4.86l24.85-8.35a9 9 0 003.93-2.31L422 112.66a9 9 0 000-12.66l-9.95-10a9 9 0 00-12.71 0z'/></svg>";var y3="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M80 112h352' class='ionicon-stroke-width'/><path d='M192 112V72h0a23.93 23.93 0 0124-24h80a23.93 23.93 0 0124 24h0v40M256 176v224M184 176l8 224M328 176l-8 224' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var j2=Z0(e4());var C2,W8=new Uint8Array(16);function t4(){if(!C2&&(C2=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!C2))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return C2(W8)}var _3=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function Z8(e){return typeof e=="string"&&_3.test(e)}var b3=Z8;var E1=[];for(_2=0;_2<256;++_2)E1.push((_2+256).toString(16).substr(1));var _2;function Q8(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=(E1[e[o+0]]+E1[e[o+1]]+E1[e[o+2]]+E1[e[o+3]]+"-"+E1[e[o+4]]+E1[e[o+5]]+"-"+E1[e[o+6]]+E1[e[o+7]]+"-"+E1[e[o+8]]+E1[e[o+9]]+"-"+E1[e[o+10]]+E1[e[o+11]]+E1[e[o+12]]+E1[e[o+13]]+E1[e[o+14]]+E1[e[o+15]]).toLowerCase();if(!b3(n))throw TypeError("Stringified UUID is invalid");return n}var B3=Q8;function X8(e,o,n){e=e||{};var t=e.random||(e.rng||t4)();if(t[6]=t[6]&15|64,t[8]=t[8]&63|128,o){n=n||0;for(var i=0;i<16;++i)o[n+i]=t[i];return o}return B3(t)}var o2=X8;var R4=Z0(S3()),c0=Z0(i4());function ia(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer||typeof Blob<"u"&&e instanceof Blob}function aa(e){return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type)}var g6=Function.prototype.toString,sa=g6.call(Object);function ra(e){var o=Object.getPrototypeOf(e);if(o===null)return!0;var n=o.constructor;return typeof n=="function"&&n instanceof n&&g6.call(n)==sa}function H1(e){var o,n,t;if(!e||typeof e!="object")return e;if(Array.isArray(e)){for(o=[],n=0,t=e.length;n<t;n++)o[n]=H1(e[n]);return o}if(e instanceof Date&&isFinite(e))return e.toISOString();if(ia(e))return aa(e);if(!ra(e))return e;o={};for(n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var i=H1(e[n]);typeof i<"u"&&(o[n]=i)}return o}function h6(e){var o=!1;return function(...n){if(o)throw new Error("once called more than once");o=!0,e.apply(this,n)}}function u6(e){return function(...o){o=H1(o);var n=this,t=typeof o[o.length-1]=="function"?o.pop():!1,i=new Promise(function(a,s){var c;try{var l=h6(function(x,k){x?s(x):a(k)});o.push(l),c=e.apply(n,o),c&&typeof c.then=="function"&&a(c)}catch(x){s(x)}});return t&&i.then(function(a){t(null,a)},t),i}}function ca(e,o,n){if(e.constructor.listeners("debug").length){for(var t=["api",e.name,o],i=0;i<n.length-1;i++)t.push(n[i]);e.constructor.emit("debug",t);var a=n[n.length-1];n[n.length-1]=function(s,c){var l=["api",e.name,o];l=l.concat(s?["error",s]:["success",c]),e.constructor.emit("debug",l),a(s,c)}}}function L1(e,o){return u6(function(...n){if(this._closed)return Promise.reject(new Error("database is closed"));if(this._destroyed)return Promise.reject(new Error("database is destroyed"));var t=this;return ca(t,e,n),this.taskqueue.isReady?o.apply(this,n):new Promise(function(i,a){t.taskqueue.addTask(function(s){s?a(s):i(t[e].apply(t,n))})})})}function c2(e,o){for(var n={},t=0,i=o.length;t<i;t++){var a=o[t];a in e&&(n[a]=e[a])}return n}var la=6;function T3(e){return e}function da(e){return[{ok:e}]}function p6(e,o,n){var t=o.docs,i=new Map;t.forEach(function(A){i.has(A.id)?i.get(A.id).push(A):i.set(A.id,[A])});var a=i.size,s=0,c=new Array(a);function l(){var A=[];c.forEach(function(z){z.docs.forEach(function(u){A.push({id:z.id,docs:[u]})})}),n(null,{results:A})}function x(){++s===a&&l()}function k(A,z,u){c[A]={id:z,docs:u},x()}var d=[];i.forEach(function(A,z){d.push(z)});var M=0;function _(){if(!(M>=d.length)){var A=Math.min(M+la,d.length),z=d.slice(M,A);B(z,M),M+=z.length}}function B(A,z){A.forEach(function(u,h){var r=z+h,v=i.get(u),w=c2(v[0],["atts_since","attachments"]);w.open_revs=v.map(function(O){return O.rev}),w.open_revs=w.open_revs.filter(T3);var f=T3;w.open_revs.length===0&&(delete w.open_revs,f=da),["revs","attachments","binary","ajax","latest"].forEach(function(O){O in o&&(w[O]=o[O])}),e.get(u,w,function(O,E){var V;O?V=[{error:O}]:V=f(E),k(r,u,V),_()})})}_()}var f4;try{localStorage.setItem("_pouch_check_localstorage",1),f4=!!localStorage.getItem("_pouch_check_localstorage")}catch{f4=!1}function V2(){return f4}var l0=typeof queueMicrotask=="function"?queueMicrotask:function(o){Promise.resolve().then(o)},x4=class extends c0.default{constructor(){super(),this._listeners={},V2()&&addEventListener("storage",o=>{this.emit(o.key)})}addListener(o,n,t,i){if(this._listeners[n])return;var a=!1,s=this;function c(){if(!s._listeners[n])return;if(a){a="waiting";return}a=!0;var l=c2(i,["style","include_docs","attachments","conflicts","filter","doc_ids","view","since","query_params","binary","return_docs"]);function x(){a=!1}t.changes(l).on("change",function(k){k.seq>i.since&&!i.cancelled&&(i.since=k.seq,i.onChange(k))}).on("complete",function(){a==="waiting"&&l0(c),a=!1}).on("error",x)}this._listeners[n]=c,this.on(o,c)}removeListener(o,n){n in this._listeners&&(super.removeListener(o,this._listeners[n]),delete this._listeners[n])}notifyLocalWindows(o){V2()&&(localStorage[o]=localStorage[o]==="a"?"b":"a")}notify(o){this.emit(o),this.notifyLocalWindows(o)}};function W1(e){if(typeof console<"u"&&typeof console[e]=="function"){var o=Array.prototype.slice.call(arguments,1);console[e].apply(console,o)}}function va(e,o){var n=6e5;e=parseInt(e,10)||0,o=parseInt(o,10),o!==o||o<=e?o=(e||1)<<1:o=o+1,o>n&&(e=n>>1,o=n);var t=Math.random(),i=o-e;return~~(i*t+e)}function ga(e){var o=0;return e||(o=2e3),va(e,o)}function M4(e,o){W1("info","The above "+e+" is totally normal. "+o)}var _1=class extends Error{constructor(o,n,t){super(),this.status=o,this.name=n,this.message=t,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}},T7=new _1(401,"unauthorized","Name or password is incorrect."),ha=new _1(400,"bad_request","Missing JSON list of 'docs'"),G1=new _1(404,"not_found","missing"),q0=new _1(409,"conflict","Document update conflict"),m6=new _1(400,"bad_request","_id field must contain a string"),ua=new _1(412,"missing_id","_id is required for puts"),pa=new _1(400,"bad_request","Only reserved document ids may start with underscore."),R7=new _1(412,"precondition_failed","Database not open"),F4=new _1(500,"unknown_error","Database encountered an unknown error"),w6=new _1(500,"badarg","Some query argument is invalid"),F7=new _1(400,"invalid_request","Request was invalid"),ma=new _1(400,"query_parse_error","Some query parameter is invalid"),R3=new _1(500,"doc_validation","Bad special document member"),N2=new _1(400,"bad_request","Something wrong with the request"),a4=new _1(400,"bad_request","Document must be a JSON object"),q7=new _1(404,"not_found","Database not found"),q4=new _1(500,"indexed_db_went_bad","unknown"),N7=new _1(500,"web_sql_went_bad","unknown"),$7=new _1(500,"levelDB_went_went_bad","unknown"),U7=new _1(403,"forbidden","Forbidden by design doc validate_doc_update function"),L2=new _1(400,"bad_request","Invalid rev format"),G7=new _1(412,"file_exists","The database could not be created, the file already exists."),wa=new _1(412,"missing_stub","A pre-existing attachment stub wasn't found"),K7=new _1(413,"invalid_url","Provided URL is invalid");function l1(e,o){function n(t){for(var i=Object.getOwnPropertyNames(e),a=0,s=i.length;a<s;a++)typeof e[i[a]]!="function"&&(this[i[a]]=e[i[a]]);this.stack===void 0&&(this.stack=new Error().stack),t!==void 0&&(this.reason=t)}return n.prototype=_1.prototype,new n(o)}function N0(e){if(typeof e!="object"){var o=e;e=F4,e.data=o}return"error"in e&&e.error==="conflict"&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=new Error().stack),e}function fa(e,o,n){try{return!e(o,n)}catch(i){var t="Filter function threw: "+i.toString();return l1(N2,t)}}function N4(e){var o={},n=e.filter&&typeof e.filter=="function";return o.query=e.query_params,function(i){i.doc||(i.doc={});var a=n&&fa(e.filter,i.doc,o);if(typeof a=="object")return a;if(a)return!1;if(!e.include_docs)delete i.doc;else if(!e.attachments)for(var s in i.doc._attachments)Object.prototype.hasOwnProperty.call(i.doc._attachments,s)&&(i.doc._attachments[s].stub=!0);return!0}}function f6(e){var o;if(e?typeof e!="string"?o=l1(m6):/^_/.test(e)&&!/^_(design|local)/.test(e)&&(o=l1(pa)):o=l1(ua),o)throw o}function r0(e){return typeof e._remote=="boolean"?e._remote:typeof e.type=="function"?(W1("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),e.type()==="http"):!1}function xa(e,o){return"listenerCount"in e?e.listenerCount(o):c0.default.listenerCount(e,o)}function k4(e){if(!e)return null;var o=e.split("/");return o.length===2?o:o.length===1?[e,e]:null}function F3(e){var o=k4(e);return o?o.join("/"):null}var q3=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],N3="queryKey",Ma=/(?:^|&)([^&=]*)=?([^&]*)/g,ka=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/;function x6(e){for(var o=ka.exec(e),n={},t=14;t--;){var i=q3[t],a=o[t]||"",s=["user","password"].indexOf(i)!==-1;n[i]=s?decodeURIComponent(a):a}return n[N3]={},n[q3[12]].replace(Ma,function(c,l,x){l&&(n[N3][l]=x)}),n}function $4(e,o){var n=[],t=[];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n.push(i),t.push(o[i]));return n.push(e),Function.apply(null,n).apply(null,t)}function O2(e,o,n){return e.get(o).catch(function(t){if(t.status!==404)throw t;return{}}).then(function(t){var i=t._rev,a=n(t);return a?(a._id=o,a._rev=i,ya(e,a,n)):{updated:!1,rev:i}})}function ya(e,o,n){return e.put(o).then(function(t){return{updated:!0,rev:t.rev}},function(t){if(t.status!==409)throw t;return O2(e,o._id,n)})}var U4=function(e){return atob(e)},l2=function(e){return btoa(e)};function G4(e,o){e=e||[],o=o||{};try{return new Blob(e,o)}catch(a){if(a.name!=="TypeError")throw a;for(var n=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,t=new n,i=0;i<e.length;i+=1)t.append(e[i]);return t.getBlob(o.type)}}function za(e){for(var o=e.length,n=new ArrayBuffer(o),t=new Uint8Array(n),i=0;i<o;i++)t[i]=e.charCodeAt(i);return n}function K4(e,o){return G4([za(e)],{type:o})}function W4(e,o){return K4(U4(e),o)}function Ca(e){for(var o="",n=new Uint8Array(e),t=n.byteLength,i=0;i<t;i++)o+=String.fromCharCode(n[i]);return o}function M6(e,o){var n=new FileReader,t=typeof n.readAsBinaryString=="function";n.onloadend=function(i){var a=i.target.result||"";if(t)return o(a);o(Ca(a))},t?n.readAsBinaryString(e):n.readAsArrayBuffer(e)}function k6(e,o){M6(e,function(n){o(n)})}function Z4(e,o){k6(e,function(n){o(l2(n))})}function _a(e,o){var n=new FileReader;n.onloadend=function(t){var i=t.target.result||new ArrayBuffer(0);o(i)},n.readAsArrayBuffer(e)}var ba=self.setImmediate||self.setTimeout,Ba=32768;function Sa(e){return l2(e)}function Aa(e,o,n,t,i){(n>0||t<o.size)&&(o=o.slice(n,t)),_a(o,function(a){e.append(a),i()})}function La(e,o,n,t,i){(n>0||t<o.length)&&(o=o.substring(n,t)),e.appendBinary(o),i()}function Q4(e,o){var n=typeof e=="string",t=n?e.length:e.size,i=Math.min(Ba,t),a=Math.ceil(t/i),s=0,c=n?new j2.default:new j2.default.ArrayBuffer,l=n?La:Aa;function x(){ba(d)}function k(){var M=c.end(!0),_=Sa(M);o(_),c.destroy()}function d(){var M=s*i,_=M+i;s++,s<a?l(c,e,M,_,x):l(c,e,M,_,k)}d()}function y6(e){return j2.default.hash(e)}function z6(e,o){if(!o)return o2().replace(/-/g,"").toLowerCase();var n=Object.assign({},e);return delete n._rev_tree,y6(JSON.stringify(n))}var $2=o2;function I0(e){for(var o,n,t,i=e.rev_tree.slice(),a;a=i.pop();){var s=a.ids,c=s[2],l=a.pos;if(c.length){for(var x=0,k=c.length;x<k;x++)i.push({pos:l+1,ids:c[x]});continue}var d=!!s[1].deleted,M=s[0];(!o||(t!==d?t:n!==l?n<l:o<M))&&(o=M,n=l,t=d)}return n+"-"+o}function j0(e,o){for(var n=e.slice(),t;t=n.pop();)for(var i=t.pos,a=t.ids,s=a[2],c=o(s.length===0,i,a[0],t.ctx,a[1]),l=0,x=s.length;l<x;l++)n.push({pos:i+1,ids:s[l],ctx:c})}function Ia(e,o){return e.pos-o.pos}function X4(e){var o=[];j0(e,function(i,a,s,c,l){i&&o.push({rev:a+"-"+s,pos:a,opts:l})}),o.sort(Ia).reverse();for(var n=0,t=o.length;n<t;n++)delete o[n].pos;return o}function J4(e){for(var o=I0(e),n=X4(e.rev_tree),t=[],i=0,a=n.length;i<a;i++){var s=n[i];s.rev!==o&&!s.opts.deleted&&t.push(s.rev)}return t}function ja(e){var o=[];return j0(e.rev_tree,function(n,t,i,a,s){s.status==="available"&&!n&&(o.push(t+"-"+i),s.status="missing")}),o}function Va(e,o){let n=[],t=e.slice(),i;for(;i=t.pop();){let{pos:a,ids:s}=i,c=`${a}-${s[0]}`,l=s[2];if(n.push(c),c===o){if(l.length!==0)throw new Error("The requested revision is not a leaf");return n.reverse()}(l.length===0||l.length>1)&&(n=[]);for(let x=0,k=l.length;x<k;x++)t.push({pos:a+1,ids:l[x]})}if(n.length===0)throw new Error("The requested revision does not exist");return n.reverse()}function C6(e){for(var o=[],n=e.slice(),t;t=n.pop();){var i=t.pos,a=t.ids,s=a[0],c=a[1],l=a[2],x=l.length===0,k=t.history?t.history.slice():[];k.push({id:s,opts:c}),x&&o.push({pos:i+1-k.length,ids:k});for(var d=0,M=l.length;d<M;d++)n.push({pos:i+1,ids:l[d],history:k})}return o.reverse()}function Oa(e,o){return e.pos-o.pos}function Ha(e,o,n){for(var t=0,i=e.length,a;t<i;)a=t+i>>>1,n(e[a],o)<0?t=a+1:i=a;return t}function Da(e,o,n){var t=Ha(e,o,n);e.splice(t,0,o)}function $3(e,o){for(var n,t,i=o,a=e.length;i<a;i++){var s=e[i],c=[s.id,s.opts,[]];t?(t[2].push(c),t=c):n=t=c}return n}function Ea(e,o){return e[0]<o[0]?-1:1}function U3(e,o){for(var n=[{tree1:e,tree2:o}],t=!1;n.length>0;){var i=n.pop(),a=i.tree1,s=i.tree2;(a[1].status||s[1].status)&&(a[1].status=a[1].status==="available"||s[1].status==="available"?"available":"missing");for(var c=0;c<s[2].length;c++){if(!a[2][0]){t="new_leaf",a[2][0]=s[2][c];continue}for(var l=!1,x=0;x<a[2].length;x++)a[2][x][0]===s[2][c][0]&&(n.push({tree1:a[2][x],tree2:s[2][c]}),l=!0);l||(t="new_branch",Da(a[2],s[2][c],Ea))}}return{conflicts:t,tree:e}}function _6(e,o,n){var t=[],i=!1,a=!1,s;if(!e.length)return{tree:[o],conflicts:"new_leaf"};for(var c=0,l=e.length;c<l;c++){var x=e[c];if(x.pos===o.pos&&x.ids[0]===o.ids[0])s=U3(x.ids,o.ids),t.push({pos:x.pos,ids:s.tree}),i=i||s.conflicts,a=!0;else if(n!==!0){var k=x.pos<o.pos?x:o,d=x.pos<o.pos?o:x,M=d.pos-k.pos,_=[],B=[];for(B.push({ids:k.ids,diff:M,parent:null,parentIdx:null});B.length>0;){var A=B.pop();if(A.diff===0){A.ids[0]===d.ids[0]&&_.push(A);continue}for(var z=A.ids[2],u=0,h=z.length;u<h;u++)B.push({ids:z[u],diff:A.diff-1,parent:A.ids,parentIdx:u})}var r=_[0];r?(s=U3(r.ids,d.ids),r.parent[2][r.parentIdx]=s.tree,t.push({pos:k.pos,ids:k.ids}),i=i||s.conflicts,a=!0):t.push(x)}else t.push(x)}return a||t.push(o),t.sort(Oa),{tree:t,conflicts:i||"internal_node"}}function Pa(e,o){for(var n=C6(e),t,i,a=0,s=n.length;a<s;a++){var c=n[a],l=c.ids,x;if(l.length>o){t||(t={});var k=l.length-o;x={pos:c.pos+k,ids:$3(l,k)};for(var d=0;d<k;d++){var M=c.pos+d+"-"+l[d].id;t[M]=!0}}else x={pos:c.pos,ids:$3(l,0)};i?i=_6(i,x,!0).tree:i=[x]}return t&&j0(i,function(_,B,A){delete t[B+"-"+A]}),{tree:i,revs:t?Object.keys(t):[]}}function b6(e,o,n){var t=_6(e,o),i=Pa(t.tree,n);return{tree:i.tree,stemmedRevs:i.revs,conflicts:t.conflicts}}function Ta(e,o){for(var n=e.slice(),t=o.split("-"),i=parseInt(t[0],10),a=t[1],s;s=n.pop();){if(s.pos===i&&s.ids[0]===a)return!0;for(var c=s.ids[2],l=0,x=c.length;l<x;l++)n.push({pos:s.pos+1,ids:c[l]})}return!1}function Ra(e){return e.ids}function g0(e,o){o||(o=I0(e));for(var n=o.substring(o.indexOf("-")+1),t=e.rev_tree.map(Ra),i;i=t.pop();){if(i[0]===n)return!!i[1].deleted;t=t.concat(i[2])}}function L0(e){return typeof e=="string"&&e.startsWith("_local/")}function Fa(e,o){for(var n=o.rev_tree.slice(),t;t=n.pop();){var i=t.pos,a=t.ids,s=a[0],c=a[1],l=a[2],x=l.length===0,k=t.history?t.history.slice():[];if(k.push({id:s,pos:i,opts:c}),x)for(var d=0,M=k.length;d<M;d++){var _=k[d],B=_.pos+"-"+_.id;if(B===e)return i+"-"+s}for(var A=0,z=l.length;A<z;A++)n.push({pos:i+1,ids:l[A],history:k})}throw new Error("Unable to resolve latest revision for id "+o.id+", rev "+e)}function qa(e,o,n,t){try{e.emit("change",o,n,t)}catch(i){W1("error",'Error in .on("change", function):',i)}}function Na(e,o,n){var t=[{rev:e._rev}];n.style==="all_docs"&&(t=X4(o.rev_tree).map(function(a){return{rev:a.rev}}));var i={id:o.id,changes:t,doc:e};return g0(o,e._rev)&&(i.deleted=!0),n.conflicts&&(i.doc._conflicts=J4(o),i.doc._conflicts.length||delete i.doc._conflicts),i}var y4=class extends c0.default{constructor(o,n,t){super(),this.db=o,n=n?H1(n):{};var i=n.complete=h6((c,l)=>{c?xa(this,"error")>0&&this.emit("error",c):this.emit("complete",l),this.removeAllListeners(),o.removeListener("destroyed",a)});t&&(this.on("complete",function(c){t(null,c)}),this.on("error",t));let a=()=>{this.cancel()};o.once("destroyed",a),n.onChange=(c,l,x)=>{this.isCancelled||qa(this,c,l,x)};var s=new Promise(function(c,l){n.complete=function(x,k){x?l(x):c(k)}});this.once("cancel",function(){o.removeListener("destroyed",a),n.complete(null,{status:"cancelled"})}),this.then=s.then.bind(s),this.catch=s.catch.bind(s),this.then(function(c){i(null,c)},i),o.taskqueue.isReady?this.validateChanges(n):o.taskqueue.addTask(c=>{c?n.complete(c):this.isCancelled?this.emit("cancel"):this.validateChanges(n)})}cancel(){this.isCancelled=!0,this.db.taskqueue.isReady&&this.emit("cancel")}validateChanges(o){var n=o.complete;v1._changesFilterPlugin?v1._changesFilterPlugin.validate(o,t=>{if(t)return n(t);this.doChanges(o)}):this.doChanges(o)}doChanges(o){var n=o.complete;if(o=H1(o),"live"in o&&!("continuous"in o)&&(o.continuous=o.live),o.processChange=Na,o.since==="latest"&&(o.since="now"),o.since||(o.since=0),o.since==="now"){this.db.info().then(i=>{if(this.isCancelled){n(null,{status:"cancelled"});return}o.since=i.update_seq,this.doChanges(o)},n);return}if(v1._changesFilterPlugin){if(v1._changesFilterPlugin.normalize(o),v1._changesFilterPlugin.shouldFilter(this,o))return v1._changesFilterPlugin.filter(this,o)}else["doc_ids","filter","selector","view"].forEach(function(i){i in o&&W1("warn",'The "'+i+'" option was passed in to changes/replicate, but pouchdb-changes-filter plugin is not installed, so it was ignored. Please install the plugin to enable filtering.')});"descending"in o||(o.descending=!1),o.limit=o.limit===0?1:o.limit,o.complete=n;var t=this.db._changes(o);if(t&&typeof t.cancel=="function"){let i=this.cancel;this.cancel=(...a)=>{t.cancel(),i.apply(this,a)}}}};function s4(e,o){return function(n,t){n||t[0]&&t[0].error?(n=n||t[0],n.docId=o,e(n)):e(null,t.length?t[0]:t)}}function $a(e){for(var o=0;o<e.length;o++){var n=e[o];if(n._deleted)delete n._attachments;else if(n._attachments)for(var t=Object.keys(n._attachments),i=0;i<t.length;i++){var a=t[i];n._attachments[a]=c2(n._attachments[a],["data","digest","content_type","length","revpos","stub"])}}}function Ua(e,o){if(e._id===o._id){let n=e._revisions?e._revisions.start:0,t=o._revisions?o._revisions.start:0;return n-t}return e._id<o._id?-1:1}function Ga(e){var o={},n=[];return j0(e,function(t,i,a,s){var c=i+"-"+a;return t&&(o[c]=0),s!==void 0&&n.push({from:s,to:c}),c}),n.reverse(),n.forEach(function(t){o[t.from]===void 0?o[t.from]=1+o[t.to]:o[t.from]=Math.min(o[t.from],1+o[t.to])}),o}function Ka(e){var o="limit"in e?e.keys.slice(e.skip,e.limit+e.skip):e.skip>0?e.keys.slice(e.skip):e.keys;e.keys=o,e.skip=0,delete e.limit,e.descending&&(o.reverse(),e.descending=!1)}function B6(e){var o=e._compactionQueue[0],n=o.opts,t=o.callback;e.get("_local/compaction").catch(function(){return!1}).then(function(i){i&&i.last_seq&&(n.last_seq=i.last_seq),e._compact(n,function(a,s){a?t(a):t(null,s),l0(function(){e._compactionQueue.shift(),e._compactionQueue.length&&B6(e)})})})}function Wa(e,o,n){return e.get("_local/purges").then(function(t){let i=t.purgeSeq+1;return t.purges.push({docId:o,rev:n,purgeSeq:i}),t.purges.length>self.purged_infos_limit&&t.purges.splice(0,t.purges.length-self.purged_infos_limit),t.purgeSeq=i,t}).catch(function(t){if(t.status!==404)throw t;return{_id:"_local/purges",purges:[{docId:o,rev:n,purgeSeq:0}],purgeSeq:0}}).then(function(t){return e.put(t)})}function Za(e){return e.charAt(0)==="_"?e+" is not a valid attachment name, attachment names cannot start with '_'":!1}function r4(e){return e===null||typeof e!="object"||Array.isArray(e)}var Qa=/^\d+-[^-]*$/;function c4(e){return typeof e=="string"&&Qa.test(e)}var H2=class extends c0.default{_setup(){this.post=L1("post",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),r4(o))return t(l1(a4));this.bulkDocs({docs:[o]},n,s4(t,o._id))}).bind(this),this.put=L1("put",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),r4(o))return t(l1(a4));if(f6(o._id),"_rev"in o&&!c4(o._rev))return t(l1(L2));if(L0(o._id)&&typeof this._putLocal=="function")return o._deleted?this._removeLocal(o,t):this._putLocal(o,t);let i=s=>{typeof this._put=="function"&&n.new_edits!==!1?this._put(o,n,s):this.bulkDocs({docs:[o]},n,s4(s,o._id))};n.force&&o._rev?(a(),i(function(s){var c=s?null:{ok:!0,id:o._id,rev:o._rev};t(s,c)})):i(t);function a(){var s=o._rev.split("-"),c=s[1],l=parseInt(s[0],10),x=l+1,k=z6();o._revisions={start:x,ids:[k,c]},o._rev=x+"-"+k,n.new_edits=!1}}).bind(this),this.putAttachment=L1("putAttachment",function(o,n,t,i,a){var s=this;typeof a=="function"&&(a=i,i=t,t=null),typeof a>"u"&&(a=i,i=t,t=null),a||W1("warn","Attachment",n,"on document",o,"is missing content_type");function c(l){var x="_rev"in l?parseInt(l._rev,10):0;return l._attachments=l._attachments||{},l._attachments[n]={content_type:a,data:i,revpos:++x},s.put(l)}return s.get(o).then(function(l){if(l._rev!==t)throw l1(q0);return c(l)},function(l){if(l.reason===G1.message)return c({_id:o});throw l})}).bind(this),this.removeAttachment=L1("removeAttachment",function(o,n,t,i){this.get(o,(a,s)=>{if(a){i(a);return}if(s._rev!==t){i(l1(q0));return}if(!s._attachments)return i();delete s._attachments[n],Object.keys(s._attachments).length===0&&delete s._attachments,this.put(s,i)})}).bind(this),this.remove=L1("remove",function(o,n,t,i){var a;typeof n=="string"?(a={_id:o,_rev:n},typeof t=="function"&&(i=t,t={})):(a=o,typeof n=="function"?(i=n,t={}):(i=t,t=n)),t=t||{},t.was_delete=!0;var s={_id:a._id,_rev:a._rev||t.rev};if(s._deleted=!0,L0(s._id)&&typeof this._removeLocal=="function")return this._removeLocal(a,i);this.bulkDocs({docs:[s]},t,s4(i,s._id))}).bind(this),this.revsDiff=L1("revsDiff",function(o,n,t){typeof n=="function"&&(t=n,n={});var i=Object.keys(o);if(!i.length)return t(null,{});var a=0,s=new Map;function c(x,k){s.has(x)||s.set(x,{missing:[]}),s.get(x).missing.push(k)}function l(x,k){var d=o[x].slice(0);j0(k,function(M,_,B,A,z){var u=_+"-"+B,h=d.indexOf(u);h!==-1&&(d.splice(h,1),z.status!=="available"&&c(x,u))}),d.forEach(function(M){c(x,M)})}i.forEach(function(x){this._getRevisionTree(x,function(k,d){if(k&&k.status===404&&k.message==="missing")s.set(x,{missing:o[x]});else{if(k)return t(k);l(x,d)}if(++a===i.length){var M={};return s.forEach(function(_,B){M[B]=_}),t(null,M)}})},this)}).bind(this),this.bulkGet=L1("bulkGet",function(o,n){p6(this,o,n)}).bind(this),this.compactDocument=L1("compactDocument",function(o,n,t){this._getRevisionTree(o,(i,a)=>{if(i)return t(i);var s=Ga(a),c=[],l=[];Object.keys(s).forEach(function(x){s[x]>n&&c.push(x)}),j0(a,function(x,k,d,M,_){var B=k+"-"+d;_.status==="available"&&c.indexOf(B)!==-1&&l.push(B)}),this._doCompaction(o,l,t)})}).bind(this),this.compact=L1("compact",function(o,n){typeof o=="function"&&(n=o,o={}),o=o||{},this._compactionQueue=this._compactionQueue||[],this._compactionQueue.push({opts:o,callback:n}),this._compactionQueue.length===1&&B6(this)}).bind(this),this.get=L1("get",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),n=n||{},typeof o!="string")return t(l1(m6));if(L0(o)&&typeof this._getLocal=="function")return this._getLocal(o,t);var i=[];let a=()=>{var l=[],x=i.length;if(!x)return t(null,l);i.forEach(k=>{this.get(o,{rev:k,revs:n.revs,latest:n.latest,attachments:n.attachments,binary:n.binary},function(d,M){if(d)l.push({missing:k});else{for(var _,B=0,A=l.length;B<A;B++)if(l[B].ok&&l[B].ok._rev===M._rev){_=!0;break}_||l.push({ok:M})}x--,x||t(null,l)})})};if(n.open_revs){if(n.open_revs==="all")this._getRevisionTree(o,function(l,x){if(l)return t(l);i=X4(x).map(function(k){return k.rev}),a()});else if(Array.isArray(n.open_revs)){i=n.open_revs;for(var s=0;s<i.length;s++){var c=i[s];if(!c4(c))return t(l1(L2))}a()}else return t(l1(F4,"function_clause"));return}return this._get(o,n,(l,x)=>{if(l)return l.docId=o,t(l);var k=x.doc,d=x.metadata,M=x.ctx;if(n.conflicts){var _=J4(d);_.length&&(k._conflicts=_)}if(g0(d,k._rev)&&(k._deleted=!0),n.revs||n.revs_info){for(var B=k._rev.split("-"),A=parseInt(B[0],10),z=B[1],u=C6(d.rev_tree),h=null,r=0;r<u.length;r++){var v=u[r];let e1=v.ids.findIndex(i1=>i1.id===z);var w=e1===A-1;(w||!h&&e1!==-1)&&(h=v)}if(!h)return l=new Error("invalid rev tree"),l.docId=o,t(l);let Y=k._rev.split("-")[1],c1=h.ids.findIndex(e1=>e1.id===Y)+1;var f=h.ids.length-c1;if(h.ids.splice(c1,f),h.ids.reverse(),n.revs&&(k._revisions={start:h.pos+h.ids.length-1,ids:h.ids.map(function(e1){return e1.id})}),n.revs_info){var O=h.pos+h.ids.length;k._revs_info=h.ids.map(function(e1){return O--,{rev:O+"-"+e1.id,status:e1.opts.status}})}}if(n.attachments&&k._attachments){var E=k._attachments,V=Object.keys(E).length;if(V===0)return t(null,k);Object.keys(E).forEach(Y=>{this._getAttachment(k._id,Y,E[Y],{binary:n.binary,metadata:d,ctx:M},function(c1,e1){var i1=k._attachments[Y];i1.data=e1,delete i1.stub,delete i1.length,--V||t(null,k)})})}else{if(k._attachments)for(var n1 in k._attachments)Object.prototype.hasOwnProperty.call(k._attachments,n1)&&(k._attachments[n1].stub=!0);t(null,k)}})}).bind(this),this.getAttachment=L1("getAttachment",function(o,n,t,i){t instanceof Function&&(i=t,t={}),this._get(o,t,(a,s)=>{if(a)return i(a);if(s.doc._attachments&&s.doc._attachments[n])t.ctx=s.ctx,t.binary=!0,t.metadata=s.metadata,this._getAttachment(o,n,s.doc._attachments[n],t,i);else return i(l1(G1))})}).bind(this),this.allDocs=L1("allDocs",function(o,n){if(typeof o=="function"&&(n=o,o={}),o.skip=typeof o.skip<"u"?o.skip:0,o.start_key&&(o.startkey=o.start_key),o.end_key&&(o.endkey=o.end_key),"keys"in o){if(!Array.isArray(o.keys))return n(new TypeError("options.keys must be an array"));var t=["startkey","endkey","key"].filter(function(i){return i in o})[0];if(t){n(l1(ma,"Query parameter `"+t+"` is not compatible with multi-get"));return}if(!r0(this)&&(Ka(o),o.keys.length===0))return this._allDocs({limit:0},n)}return this._allDocs(o,n)}).bind(this),this.close=L1("close",function(o){return this._closed=!0,this.emit("closed"),this._close(o)}).bind(this),this.info=L1("info",function(o){this._info((n,t)=>{if(n)return o(n);t.db_name=t.db_name||this.name,t.auto_compaction=!!(this.auto_compaction&&!r0(this)),t.adapter=this.adapter,o(null,t)})}).bind(this),this.id=L1("id",function(o){return this._id(o)}).bind(this),this.bulkDocs=L1("bulkDocs",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),n=n||{},Array.isArray(o)&&(o={docs:o}),!o||!o.docs||!Array.isArray(o.docs))return t(l1(ha));for(var i=0;i<o.docs.length;++i){let l=o.docs[i];if(r4(l))return t(l1(a4));if("_rev"in l&&!c4(l._rev))return t(l1(L2))}var a;if(o.docs.forEach(function(l){l._attachments&&Object.keys(l._attachments).forEach(function(x){a=a||Za(x),l._attachments[x].content_type||W1("warn","Attachment",x,"on document",l._id,"is missing content_type")})}),a)return t(l1(N2,a));"new_edits"in n||("new_edits"in o?n.new_edits=o.new_edits:n.new_edits=!0);var s=this;!n.new_edits&&!r0(s)&&o.docs.sort(Ua),$a(o.docs);var c=o.docs.map(function(l){return l._id});this._bulkDocs(o,n,function(l,x){if(l)return t(l);if(n.new_edits||(x=x.filter(function(M){return M.error})),!r0(s))for(var k=0,d=x.length;k<d;k++)x[k].id=x[k].id||c[k];t(null,x)})}).bind(this),this.registerDependentDatabase=L1("registerDependentDatabase",function(o,n){var t=H1(this.__opts);this.__opts.view_adapter&&(t.adapter=this.__opts.view_adapter);var i=new this.constructor(o,t);function a(s){return s.dependentDbs=s.dependentDbs||{},s.dependentDbs[o]?!1:(s.dependentDbs[o]=!0,s)}O2(this,"_local/_pouch_dependentDbs",a).then(function(){n(null,{db:i})}).catch(n)}).bind(this),this.destroy=L1("destroy",function(o,n){typeof o=="function"&&(n=o,o={});var t="use_prefix"in this?this.use_prefix:!0;let i=()=>{this._destroy(o,(a,s)=>{if(a)return n(a);this._destroyed=!0,this.emit("destroyed"),n(null,s||{ok:!0})})};if(r0(this))return i();this.get("_local/_pouch_dependentDbs",(a,s)=>{if(a)return a.status!==404?n(a):i();var c=s.dependentDbs,l=this.constructor,x=Object.keys(c).map(k=>{var d=t?k.replace(new RegExp("^"+l.prefix),""):k;return new l(d,this.__opts).destroy()});Promise.all(x).then(i,n)})}).bind(this)}_compact(o,n){var t={return_docs:!1,last_seq:o.last_seq||0,since:o.last_seq||0},i=[],a,s=0;let c=k=>{this.activeTasks.update(a,{completed_items:++s}),i.push(this.compactDocument(k.id,0))},l=k=>{this.activeTasks.remove(a,k),n(k)},x=k=>{var d=k.last_seq;Promise.all(i).then(()=>O2(this,"_local/compaction",M=>!M.last_seq||M.last_seq<d?(M.last_seq=d,M):!1)).then(()=>{this.activeTasks.remove(a),n(null,{ok:!0})}).catch(l)};this.info().then(k=>{a=this.activeTasks.add({name:"database_compaction",total_items:k.update_seq-t.last_seq}),this.changes(t).on("change",c).on("complete",x).on("error",l)})}changes(o,n){return typeof o=="function"&&(n=o,o={}),o=o||{},o.return_docs="return_docs"in o?o.return_docs:!o.live,new y4(this,o,n)}type(){return typeof this._type=="function"?this._type():this.adapter}};H2.prototype.purge=L1("_purge",function(e,o,n){if(typeof this._purge>"u")return n(l1(F4,"Purge is not implemented in the "+this.adapter+" adapter."));var t=this;t._getRevisionTree(e,(i,a)=>{if(i)return n(i);if(!a)return n(l1(G1));let s;try{s=Va(a,o)}catch(c){return n(c.message||c)}t._purge(e,s,(c,l)=>{if(c)return n(c);Wa(t,e,o).then(function(){return n(null,l)})})})});var z4=class{constructor(){this.isReady=!1,this.failed=!1,this.queue=[]}execute(){var o;if(this.failed)for(;o=this.queue.shift();)o(this.failed);else for(;o=this.queue.shift();)o()}fail(o){this.failed=o,this.execute()}ready(o){this.isReady=!0,this.db=o,this.execute()}addTask(o){this.queue.push(o),this.failed&&this.execute()}};function Xa(e,o){var n=e.match(/([a-z-]*):\/\/(.*)/);if(n)return{name:/https?/.test(n[1])?n[1]+"://"+n[2]:n[2],adapter:n[1]};var t=v1.adapters,i=v1.preferredAdapters,a=v1.prefix,s=o.adapter;if(!s)for(var c=0;c<i.length;++c){if(s=i[c],s==="idb"&&"websql"in t&&V2()&&localStorage["_pouch__websqldb_"+a+e]){W1("log",'PouchDB is downgrading "'+e+'" to WebSQL to avoid data loss, because it was already opened with WebSQL.');continue}break}var l=t[s],x=l&&"use_prefix"in l?l.use_prefix:!0;return{name:x?a+e:e,adapter:s}}function Ja(e,o){e.prototype=Object.create(o.prototype,{constructor:{value:e}})}function S6(e,o){let n=function(...t){if(!(this instanceof n))return new n(...t);o.apply(this,t)};return Ja(n,e),n}function Ya(e){function o(t){e.removeListener("closed",n),t||e.constructor.emit("destroyed",e.name)}function n(){e.removeListener("destroyed",o),e.constructor.emit("unref",e)}e.once("destroyed",o),e.once("closed",n),e.constructor.emit("ref",e)}var D2=class extends H2{constructor(o,n){super(),this._setup(o,n)}_setup(o,n){if(super._setup(),n=n||{},o&&typeof o=="object"&&(n=o,o=n.name,delete n.name),n.deterministic_revs===void 0&&(n.deterministic_revs=!0),this.__opts=n=H1(n),this.auto_compaction=n.auto_compaction,this.purged_infos_limit=n.purged_infos_limit||1e3,this.prefix=v1.prefix,typeof o!="string")throw new Error("Missing/invalid DB name");var t=(n.prefix||"")+o,i=Xa(t,n);if(n.name=i.name,n.adapter=n.adapter||i.adapter,this.name=o,this._adapter=n.adapter,v1.emit("debug",["adapter","Picked adapter: ",n.adapter]),!v1.adapters[n.adapter]||!v1.adapters[n.adapter].valid())throw new Error("Invalid Adapter: "+n.adapter);if(n.view_adapter&&(!v1.adapters[n.view_adapter]||!v1.adapters[n.view_adapter].valid()))throw new Error("Invalid View Adapter: "+n.view_adapter);this.taskqueue=new z4,this.adapter=n.adapter,v1.adapters[n.adapter].call(this,n,a=>{if(a)return this.taskqueue.fail(a);Ya(this),this.emit("created",this),v1.emit("created",this.name),this.taskqueue.ready(this)})}},v1=S6(D2,function(e,o){D2.prototype._setup.call(this,e,o)}),A6=fetch,R0=Headers,C4=class{constructor(){this.tasks={}}list(){return Object.values(this.tasks)}add(o){let n=o2();return this.tasks[n]={id:n,name:o.name,total_items:o.total_items,created_at:new Date().toJSON()},n}get(o){return this.tasks[o]}remove(o,n){return delete this.tasks[o],this.tasks}update(o,n){let t=this.tasks[o];if(typeof t<"u"){let i={id:t.id,name:t.name,created_at:t.created_at,total_items:n.total_items||t.total_items,completed_items:n.completed_items||t.completed_items,updated_at:new Date().toJSON()};this.tasks[o]=i}return this.tasks}};v1.adapters={};v1.preferredAdapters=[];v1.prefix="_pouch_";var G3=new c0.default;function es(e){Object.keys(c0.default.prototype).forEach(function(n){typeof c0.default.prototype[n]=="function"&&(e[n]=G3[n].bind(G3))});var o=e._destructionListeners=new Map;e.on("ref",function(t){o.has(t.name)||o.set(t.name,[]),o.get(t.name).push(t)}),e.on("unref",function(t){if(o.has(t.name)){var i=o.get(t.name),a=i.indexOf(t);a<0||(i.splice(a,1),i.length>1?o.set(t.name,i):o.delete(t.name))}}),e.on("destroyed",function(t){if(o.has(t)){var i=o.get(t);o.delete(t),i.forEach(function(a){a.emit("destroyed",!0)})}})}es(v1);v1.adapter=function(e,o,n){o.valid()&&(v1.adapters[e]=o,n&&v1.preferredAdapters.push(e))};v1.plugin=function(e){if(typeof e=="function")e(v1);else{if(typeof e!="object"||Object.keys(e).length===0)throw new Error('Invalid plugin: got "'+e+'", expected an object or a function');Object.keys(e).forEach(function(o){v1.prototype[o]=e[o]})}return this.__defaults&&(v1.__defaults=Object.assign({},this.__defaults)),v1};v1.defaults=function(e){let o=S6(v1,function(n,t){t=t||{},n&&typeof n=="object"&&(t=n,n=t.name,delete t.name),t=Object.assign({},o.__defaults,t),v1.call(this,n,t)});return o.preferredAdapters=v1.preferredAdapters.slice(),Object.keys(v1).forEach(function(n){n in o||(o[n]=v1[n])}),o.__defaults=Object.assign({},this.__defaults,e),o};v1.fetch=function(e,o){return A6(e,o)};v1.prototype.activeTasks=v1.activeTasks=new C4;var ts="9.0.0";function Y4(e,o){for(var n=e,t=0,i=o.length;t<i;t++){var a=o[t];if(n=n[a],!n)break}return n}function ns(e,o){return e<o?-1:e>o?1:0}function en(e){for(var o=[],n="",t=0,i=e.length;t<i;t++){var a=e[t];t>0&&e[t-1]==="\\"&&(a==="$"||a===".")?n=n.substring(0,n.length-1)+a:a==="."?(o.push(n),n=""):n+=a}return o.push(n),o}var os=["$or","$nor","$not"];function L6(e){return os.indexOf(e)>-1}function I6(e){return Object.keys(e)[0]}function is(e){return e[I6(e)]}function s2(e){var o={},n={$or:!0,$nor:!0};return e.forEach(function(t){Object.keys(t).forEach(function(i){var a=t[i];if(typeof a!="object"&&(a={$eq:a}),L6(i))if(a instanceof Array){if(n[i]){n[i]=!1,o[i]=a;return}var s=[];o[i].forEach(function(l){Object.keys(a).forEach(function(x){var k=a[x],d=Math.max(Object.keys(l).length,Object.keys(k).length),M=s2([l,k]);Object.keys(M).length<=d||s.push(M)})}),o[i]=s}else o[i]=s2([a]);else{var c=o[i]=o[i]||{};Object.keys(a).forEach(function(l){var x=a[l];if(l==="$gt"||l==="$gte")return as(l,x,c);if(l==="$lt"||l==="$lte")return ss(l,x,c);if(l==="$ne")return rs(x,c);if(l==="$eq")return cs(x,c);if(l==="$regex")return ls(x,c);c[l]=x})}})}),o}function as(e,o,n){typeof n.$eq<"u"||(typeof n.$gte<"u"?e==="$gte"?o>n.$gte&&(n.$gte=o):o>=n.$gte&&(delete n.$gte,n.$gt=o):typeof n.$gt<"u"?e==="$gte"?o>n.$gt&&(delete n.$gt,n.$gte=o):o>n.$gt&&(n.$gt=o):n[e]=o)}function ss(e,o,n){typeof n.$eq<"u"||(typeof n.$lte<"u"?e==="$lte"?o<n.$lte&&(n.$lte=o):o<=n.$lte&&(delete n.$lte,n.$lt=o):typeof n.$lt<"u"?e==="$lte"?o<n.$lt&&(delete n.$lt,n.$lte=o):o<n.$lt&&(n.$lt=o):n[e]=o)}function rs(e,o){"$ne"in o?o.$ne.push(e):o.$ne=[e]}function cs(e,o){delete o.$gt,delete o.$gte,delete o.$lt,delete o.$lte,delete o.$ne,o.$eq=e}function ls(e,o){"$regex"in o?o.$regex.push(e):o.$regex=[e]}function j6(e){for(var o in e){if(Array.isArray(e))for(var n in e)e[n].$and&&(e[n]=s2(e[n].$and));var t=e[o];typeof t=="object"&&j6(t)}return e}function V6(e,o){for(var n in e){n==="$and"&&(o=!0);var t=e[n];typeof t=="object"&&(o=V6(t,o))}return o}function ds(e){var o=H1(e);V6(o,!1)&&(o=j6(o),"$and"in o&&(o=s2(o.$and))),["$or","$nor"].forEach(function(s){s in o&&o[s].forEach(function(c){for(var l=Object.keys(c),x=0;x<l.length;x++){var k=l[x],d=c[k];(typeof d!="object"||d===null)&&(c[k]={$eq:d})}})}),"$not"in o&&(o.$not=s2([o.$not]));for(var n=Object.keys(o),t=0;t<n.length;t++){var i=n[t],a=o[i];(typeof a!="object"||a===null)&&(a={$eq:a}),o[i]=a}return _4(o),o}function _4(e){Object.keys(e).forEach(function(o){var n=e[o];Array.isArray(n)?n.forEach(function(t){t&&typeof t=="object"&&_4(t)}):o==="$ne"?e.$ne=[n]:o==="$regex"?e.$regex=[n]:n&&typeof n=="object"&&_4(n)})}function vs(e,o,n){for(var t="",i=n-e.length;t.length<i;)t+=o;return t}function gs(e,o,n){var t=vs(e,o,n);return t+e}var O6=-324,b4=3,B4="";function I1(e,o){if(e===o)return 0;e=V0(e),o=V0(o);var n=S4(e),t=S4(o);if(n-t!==0)return n-t;switch(typeof e){case"number":return e-o;case"boolean":return e<o?-1:1;case"string":return fs(e,o)}return Array.isArray(e)?ws(e,o):xs(e,o)}function V0(e){switch(typeof e){case"undefined":return null;case"number":return e===1/0||e===-1/0||isNaN(e)?null:e;case"object":var o=e;if(Array.isArray(e)){var n=e.length;e=new Array(n);for(var t=0;t<n;t++)e[t]=V0(o[t])}else{if(e instanceof Date)return e.toJSON();if(e!==null){e={};for(var i in o)if(Object.prototype.hasOwnProperty.call(o,i)){var a=o[i];typeof a<"u"&&(e[i]=V0(a))}}}}return e}function hs(e){if(e!==null)switch(typeof e){case"boolean":return e?1:0;case"number":return Ms(e);case"string":return e.replace(/\u0002/g,"").replace(/\u0001/g,"").replace(/\u0000/g,"");case"object":var o=Array.isArray(e),n=o?e:Object.keys(e),t=-1,i=n.length,a="";if(o)for(;++t<i;)a+=J1(n[t]);else for(;++t<i;){var s=n[t];a+=J1(s)+J1(e[s])}return a}return""}function J1(e){var o="\0";return e=V0(e),S4(e)+B4+hs(e)+o}function us(e,o){var n=o,t,i=e[o]==="1";if(i)t=0,o++;else{var a=e[o]==="0";o++;var s="",c=e.substring(o,o+b4),l=parseInt(c,10)+O6;for(a&&(l=-l),o+=b4;;){var x=e[o];if(x==="\0")break;s+=x,o++}s=s.split("."),s.length===1?t=parseInt(s,10):t=parseFloat(s[0]+"."+s[1]),a&&(t=t-10),l!==0&&(t=parseFloat(t+"e"+l))}return{num:t,length:o-n}}function ps(e,o){var n=e.pop();if(o.length){var t=o[o.length-1];n===t.element&&(o.pop(),t=o[o.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(n);else if(a===e.length-2){var s=e.pop();i[s]=n}else e.push(n)}}function ms(e){for(var o=[],n=[],t=0;;){var i=e[t++];if(i==="\0"){if(o.length===1)return o.pop();ps(o,n);continue}switch(i){case"1":o.push(null);break;case"2":o.push(e[t]==="1"),t++;break;case"3":var a=us(e,t);o.push(a.num),t+=a.length;break;case"4":for(var s="";;){var c=e[t];if(c==="\0")break;s+=c,t++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"").replace(/\u0002\u0002/g,""),o.push(s);break;case"5":var l={element:[],index:o.length};o.push(l.element),n.push(l);break;case"6":var x={element:{},index:o.length};o.push(x.element),n.push(x);break;default:throw new Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}function ws(e,o){for(var n=Math.min(e.length,o.length),t=0;t<n;t++){var i=I1(e[t],o[t]);if(i!==0)return i}return e.length===o.length?0:e.length>o.length?1:-1}function fs(e,o){return e===o?0:e>o?1:-1}function xs(e,o){for(var n=Object.keys(e),t=Object.keys(o),i=Math.min(n.length,t.length),a=0;a<i;a++){var s=I1(n[a],t[a]);if(s!==0||(s=I1(e[n[a]],o[t[a]]),s!==0))return s}return n.length===t.length?0:n.length>t.length?1:-1}function S4(e){var o=["boolean","number","string","object"],n=o.indexOf(typeof e);if(~n)return e===null?1:Array.isArray(e)?5:n<3?n+2:n+3;if(Array.isArray(e))return 5}function Ms(e){if(e===0)return"1";var o=e.toExponential().split(/e\+?/),n=parseInt(o[1],10),t=e<0,i=t?"0":"2",a=(t?-n:n)-O6,s=gs(a.toString(),"0",b4);i+=B4+s;var c=Math.abs(parseFloat(o[0]));t&&(c=10-c);var l=c.toFixed(20);return l=l.replace(/\.?0+$/,""),i+=B4+l,i}function ks(e){function o(n){return e.map(function(t){var i=I6(t),a=en(i),s=Y4(n,a);return s})}return function(n,t){var i=o(n.doc),a=o(t.doc),s=I1(i,a);return s!==0?s:ns(n.doc._id,t.doc._id)}}function ys(e,o,n){if(e=e.filter(function(s){return F0(s.doc,o.selector,n)}),o.sort){var t=ks(o.sort);e=e.sort(t),typeof o.sort[0]!="string"&&is(o.sort[0])==="desc"&&(e=e.reverse())}if("limit"in o||"skip"in o){var i=o.skip||0,a=("limit"in o?o.limit:e.length)+i;e=e.slice(i,a)}return e}function F0(e,o,n){return n.every(function(t){var i=o[t],a=en(t),s=Y4(e,a);return L6(t)?zs(t,i,e):E2(i,e,a,s)})}function E2(e,o,n,t){return e?typeof e=="object"?Object.keys(e).every(function(i){var a=e[i];if(i.indexOf("$")===0)return K3(i,o,a,n,t);var s=en(i);if(t===void 0&&typeof a!="object"&&s.length>0)return!1;var c=Y4(t,s);return typeof a=="object"?E2(a,o,n,c):K3("$eq",o,a,s,c)}):e===t:!0}function zs(e,o,n){return e==="$or"?o.some(function(t){return F0(n,t,Object.keys(t))}):e==="$not"?!F0(n,o,Object.keys(o)):!o.find(function(t){return F0(n,t,Object.keys(t))})}function K3(e,o,n,t,i){if(!Z3[e])throw new Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return Z3[e](o,n,t,i)}function i2(e){return typeof e<"u"&&e!==null}function _0(e){return typeof e<"u"}function Cs(e,o){if(typeof e!="number"||parseInt(e,10)!==e)return!1;var n=o[0],t=o[1];return e%n===t}function W3(e,o){return o.some(function(n){return e instanceof Array?e.some(function(t){return I1(n,t)===0}):I1(n,e)===0})}function _s(e,o){return o.every(function(n){return e.some(function(t){return I1(n,t)===0})})}function bs(e,o){return e.length===o}function Bs(e,o){var n=new RegExp(o);return n.test(e)}function Ss(e,o){switch(o){case"null":return e===null;case"boolean":return typeof e=="boolean";case"number":return typeof e=="number";case"string":return typeof e=="string";case"array":return e instanceof Array;case"object":return{}.toString.call(e)==="[object Object]"}}var Z3={$elemMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.some(function(i){return F0(i,o,Object.keys(o))}):t.some(function(i){return E2(o,e,n,i)})},$allMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.every(function(i){return F0(i,o,Object.keys(o))}):t.every(function(i){return E2(o,e,n,i)})},$eq:function(e,o,n,t){return _0(t)&&I1(t,o)===0},$gte:function(e,o,n,t){return _0(t)&&I1(t,o)>=0},$gt:function(e,o,n,t){return _0(t)&&I1(t,o)>0},$lte:function(e,o,n,t){return _0(t)&&I1(t,o)<=0},$lt:function(e,o,n,t){return _0(t)&&I1(t,o)<0},$exists:function(e,o,n,t){return o?_0(t):!_0(t)},$mod:function(e,o,n,t){return i2(t)&&Cs(t,o)},$ne:function(e,o,n,t){return o.every(function(i){return I1(t,i)!==0})},$in:function(e,o,n,t){return i2(t)&&W3(t,o)},$nin:function(e,o,n,t){return i2(t)&&!W3(t,o)},$size:function(e,o,n,t){return i2(t)&&Array.isArray(t)&&bs(t,o)},$all:function(e,o,n,t){return Array.isArray(t)&&_s(t,o)},$regex:function(e,o,n,t){return i2(t)&&typeof t=="string"&&o.every(function(i){return Bs(t,i)})},$type:function(e,o,n,t){return Ss(t,o)}};function As(e,o){if(typeof o!="object")throw new Error("Selector error: expected a JSON object");o=ds(o);var n={doc:e},t=ys([n],{selector:o},Object.keys(o));return t&&t.length===1}function Ls(e){return $4(`"use strict";
return `+e+";",{})}function Is(e){var o=["return function(doc) {",'  "use strict";',"  var emitted = false;","  var emit = function (a, b) {","    emitted = true;","  };","  var view = "+e+";","  view(doc);","  if (emitted) {","    return true;","  }","};"].join(`
`);return $4(o,{})}function js(e,o){if(e.selector&&e.filter&&e.filter!=="_selector"){var n=typeof e.filter=="string"?e.filter:"function";return o(new Error('selector invalid for filter "'+n+'"'))}o()}function Vs(e){e.view&&!e.filter&&(e.filter="_view"),e.selector&&!e.filter&&(e.filter="_selector"),e.filter&&typeof e.filter=="string"&&(e.filter==="_view"?e.view=F3(e.view):e.filter=F3(e.filter))}function Os(e,o){return o.filter&&typeof o.filter=="string"&&!o.doc_ids&&!r0(e.db)}function Hs(e,o){var n=o.complete;if(o.filter==="_view"){if(!o.view||typeof o.view!="string"){var t=l1(N2,"`view` filter parameter not found or invalid.");return n(t)}var i=k4(o.view);e.db.get("_design/"+i[0],function(s,c){if(e.isCancelled)return n(null,{status:"cancelled"});if(s)return n(N0(s));var l=c&&c.views&&c.views[i[1]]&&c.views[i[1]].map;if(!l)return n(l1(G1,c.views?"missing json key: "+i[1]:"missing json key: views"));o.filter=Is(l),e.doChanges(o)})}else if(o.selector)o.filter=function(s){return As(s,o.selector)},e.doChanges(o);else{var a=k4(o.filter);e.db.get("_design/"+a[0],function(s,c){if(e.isCancelled)return n(null,{status:"cancelled"});if(s)return n(N0(s));var l=c&&c.filters&&c.filters[a[1]];if(!l)return n(l1(G1,c&&c.filters?"missing json key: "+a[1]:"missing json key: filters"));o.filter=Ls(l),e.doChanges(o)})}}function Ds(e){e._changesFilterPlugin={validate:js,normalize:Vs,shouldFilter:Os,filter:Hs}}v1.plugin(Ds);v1.version=ts;function Es(e,o,n){return new Promise(function(t){var i=G4([""]);let a;if(typeof n=="function"){let c=n(i);a=e.objectStore(o).put(c)}else{let s=n;a=e.objectStore(o).put(i,s)}a.onsuccess=function(){var s=navigator.userAgent.match(/Chrome\/(\d+)/),c=navigator.userAgent.match(/Edge\//);t(c||!s||parseInt(s[1],10)>=43)},a.onerror=e.onabort=function(s){s.preventDefault(),s.stopPropagation(),t(!1)}}).catch(function(){return!1})}function H6(e){return e.reduce(function(o,n){return o[n]=!0,o},{})}var Ps=H6(["_id","_rev","_access","_attachments","_deleted","_revisions","_revs_info","_conflicts","_deleted_conflicts","_local_seq","_rev_tree","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats","_removed"]),Ts=H6(["_access","_attachments","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats"]);function Q3(e){if(!/^\d+-/.test(e))return l1(L2);var o=e.indexOf("-"),n=e.substring(0,o),t=e.substring(o+1);return{prefix:parseInt(n,10),id:t}}function Rs(e,o){for(var n=e.start-e.ids.length+1,t=e.ids,i=[t[0],o,[]],a=1,s=t.length;a<s;a++)i=[t[a],{status:"missing"},[i]];return[{pos:n,ids:i}]}function D6(e,o,n){n||(n={deterministic_revs:!0});var t,i,a,s={status:"available"};if(e._deleted&&(s.deleted=!0),o)if(e._id||(e._id=$2()),i=z6(e,n.deterministic_revs),e._rev){if(a=Q3(e._rev),a.error)return a;e._rev_tree=[{pos:a.prefix,ids:[a.id,{status:"missing"},[[i,s,[]]]]}],t=a.prefix+1}else e._rev_tree=[{pos:1,ids:[i,s,[]]}],t=1;else if(e._revisions&&(e._rev_tree=Rs(e._revisions,s),t=e._revisions.start,i=e._revisions.ids[0]),!e._rev_tree){if(a=Q3(e._rev),a.error)return a;t=a.prefix,i=a.id,e._rev_tree=[{pos:t,ids:[i,s,[]]}]}f6(e._id),e._rev=t+"-"+i;var c={metadata:{},data:{}};for(var l in e)if(Object.prototype.hasOwnProperty.call(e,l)){var x=l[0]==="_";if(x&&!Ps[l]){var k=l1(R3,l);throw k.message=R3.message+": "+l,k}else x&&!Ts[l]?c.metadata[l.slice(1)]=e[l]:c.data[l]=e[l]}return c}function Fs(e){try{return U4(e)}catch{var o=l1(w6,"Attachment is not a valid base64 string");return{error:o}}}function qs(e,o,n){var t=Fs(e.data);if(t.error)return n(t.error);e.length=t.length,o==="blob"?e.data=K4(t,e.content_type):o==="base64"?e.data=l2(t):e.data=t,Q4(t,function(i){e.digest="md5-"+i,n()})}function Ns(e,o,n){Q4(e.data,function(t){e.digest="md5-"+t,e.length=e.data.size||e.data.length||0,o==="binary"?k6(e.data,function(i){e.data=i,n()}):o==="base64"?Z4(e.data,function(i){e.data=i,n()}):n()})}function $s(e,o,n){if(e.stub)return n();typeof e.data=="string"?qs(e,o,n):Ns(e,o,n)}function Us(e,o,n){if(!e.length)return n();var t=0,i;e.forEach(function(s){var c=s.data&&s.data._attachments?Object.keys(s.data._attachments):[],l=0;if(!c.length)return a();function x(d){i=d,l++,l===c.length&&a()}for(var k in s.data._attachments)Object.prototype.hasOwnProperty.call(s.data._attachments,k)&&$s(s.data._attachments[k],o,x)});function a(){t++,e.length===t&&(i?n(i):n())}}function Gs(e,o,n,t,i,a,s,c){if(Ta(o.rev_tree,n.metadata.rev)&&!c)return t[i]=n,a();var l=o.winningRev||I0(o),x="deleted"in o?o.deleted:g0(o,l),k="deleted"in n.metadata?n.metadata.deleted:g0(n.metadata),d=/^1-/.test(n.metadata.rev);if(x&&!k&&c&&d){var M=n.data;M._rev=l,M._id=n.metadata.id,n=D6(M,c)}var _=b6(o.rev_tree,n.metadata.rev_tree[0],e),B=c&&(x&&k&&_.conflicts!=="new_leaf"||!x&&_.conflicts!=="new_leaf"||x&&!k&&_.conflicts==="new_branch");if(B){var A=l1(q0);return t[i]=A,a()}var z=n.metadata.rev;n.metadata.rev_tree=_.tree,n.stemmedRevs=_.stemmedRevs||[],o.rev_map&&(n.metadata.rev_map=o.rev_map);var u=I0(n.metadata),h=g0(n.metadata,u),r=x===h?0:x<h?-1:1,v;z===u?v=h:v=g0(n.metadata,z),s(n,u,h,v,!0,r,i,a)}function Ks(e){return e.metadata.rev_tree[0].ids[1].status==="missing"}function Ws(e,o,n,t,i,a,s,c,l){e=e||1e3;function x(A,z,u){var h=I0(A.metadata),r=g0(A.metadata,h);if("was_delete"in c&&r)return a[z]=l1(G1,"deleted"),u();var v=k&&Ks(A);if(v){var w=l1(q0);return a[z]=w,u()}var f=r?0:1;s(A,h,r,r,!1,f,z,u)}var k=c.new_edits,d=new Map,M=0,_=o.length;function B(){++M===_&&l&&l()}o.forEach(function(A,z){if(A._id&&L0(A._id)){var u=A._deleted?"_removeLocal":"_putLocal";n[u](A,{ctx:i},function(r,v){a[z]=r||v,B()});return}var h=A.metadata.id;d.has(h)?(_--,d.get(h).push([A,z])):d.set(h,[[A,z]])}),d.forEach(function(A,z){var u=0;function h(){++u<A.length?r():B()}function r(){var v=A[u],w=v[0],f=v[1];if(t.has(z))Gs(e,t.get(z),w,a,f,h,s,k);else{var O=b6([],w.metadata.rev_tree[0],e);w.metadata.rev_tree=O.tree,w.stemmedRevs=O.stemmedRevs||[],x(w,f,h)}}r()})}var Zs=5,j1="document-store",P1="by-sequence",K1="attach-store",S0="attach-seq-store",q1="meta-store",a0="local-store",l4="detect-blob-support";function Qs(e){try{return JSON.parse(e)}catch{return R4.default.parse(e)}}function Xs(e){try{return JSON.stringify(e)}catch{return R4.default.stringify(e)}}function s0(e){return function(o){var n="unknown_error";o.target&&o.target.error&&(n=o.target.error.name||o.target.error.message),e(l1(q4,n,o.type))}}function A4(e,o,n){return{data:Xs(e),winningRev:o,deletedOrLocal:n?"1":"0",seq:e.seq,id:e.id}}function A0(e){if(!e)return null;var o=Qs(e.data);return o.winningRev=e.winningRev,o.deleted=e.deletedOrLocal==="1",o.seq=e.seq,o}function P2(e){if(!e)return e;var o=e._doc_id_rev.lastIndexOf(":");return e._id=e._doc_id_rev.substring(0,o-1),e._rev=e._doc_id_rev.substring(o+1),delete e._doc_id_rev,e}function E6(e,o,n,t){n?t(e?typeof e!="string"?e:W4(e,o):G4([""],{type:o})):e?typeof e!="string"?M6(e,function(i){t(l2(i))}):t(e):t("")}function P6(e,o,n,t){var i=Object.keys(e._attachments||{});if(!i.length)return t&&t();var a=0;function s(){++a===i.length&&t&&t()}function c(l,x){var k=l._attachments[x],d=k.digest,M=n.objectStore(K1).get(d);M.onsuccess=function(_){k.body=_.target.result.body,s()}}i.forEach(function(l){o.attachments&&o.include_docs?c(e,l):(e._attachments[l].stub=!0,s())})}function L4(e,o){return Promise.all(e.map(function(n){if(n.doc&&n.doc._attachments){var t=Object.keys(n.doc._attachments);return Promise.all(t.map(function(i){var a=n.doc._attachments[i];if("body"in a){var s=a.body,c=a.content_type;return new Promise(function(l){E6(s,c,o,function(x){n.doc._attachments[i]=Object.assign(c2(a,["digest","content_type"]),{data:x}),l()})})}}))}}))}function T6(e,o,n){var t=[],i=n.objectStore(P1),a=n.objectStore(K1),s=n.objectStore(S0),c=e.length;function l(){c--,c||x()}function x(){t.length&&t.forEach(function(k){var d=s.index("digestSeq").count(IDBKeyRange.bound(k+"::",k+"::\uFFFF",!1,!1));d.onsuccess=function(M){var _=M.target.result;_||a.delete(k)}})}e.forEach(function(k){var d=i.index("_doc_id_rev"),M=o+"::"+k;d.getKey(M).onsuccess=function(_){var B=_.target.result;if(typeof B!="number")return l();i.delete(B);var A=s.index("seq").openCursor(IDBKeyRange.only(B));A.onsuccess=function(z){var u=z.target.result;if(u){var h=u.value.digestSeq.split("::")[0];t.push(h),s.delete(u.primaryKey),u.continue()}else l()}}})}function Y1(e,o,n){try{return{txn:e.transaction(o,n)}}catch(t){return{error:t}}}var a2=new x4;function Js(e,o,n,t,i,a){for(var s=o.docs,c,l,x,k,d,M,_,B,A=0,z=s.length;A<z;A++){var u=s[A];u._id&&L0(u._id)||(u=s[A]=D6(u,n.new_edits,e),u.error&&!_&&(_=u))}if(_)return a(_);var h=!1,r=0,v=new Array(s.length),w=new Map,f=!1,O=t._meta.blobSupport?"blob":"base64";Us(s,O,function(C){if(C)return a(C);E()});function E(){var C=[j1,P1,K1,a0,S0,q1],L=Y1(i,C,"readwrite");if(L.error)return a(L.error);c=L.txn,c.onabort=s0(a),c.ontimeout=s0(a),c.oncomplete=e1,l=c.objectStore(j1),x=c.objectStore(P1),k=c.objectStore(K1),d=c.objectStore(S0),M=c.objectStore(q1),M.get(q1).onsuccess=function(P){B=P.target.result,Y()},g1(function(P){if(P)return f=!0,a(P);c1()})}function V(){h=!0,Y()}function n1(){Ws(e.revs_limit,s,t,w,c,v,h1,n,V)}function Y(){!B||!h||(B.docCount+=r,M.put(B))}function c1(){if(!s.length)return;var C=0;function L(){++C===s.length&&n1()}function P(H){var $=A0(H.target.result);$&&w.set($.id,$),L()}for(var J=0,I=s.length;J<I;J++){var b=s[J];if(b._id&&L0(b._id)){L();continue}var S=l.get(b.metadata.id);S.onsuccess=P}}function e1(){f||(a2.notify(t._meta.name),a(null,v))}function i1(C,L){var P=k.get(C);P.onsuccess=function(J){if(J.target.result)L();else{var I=l1(wa,"unknown stub attachment with digest "+C);I.status=412,L(I)}}}function g1(C){var L=[];if(s.forEach(function(b){b.data&&b.data._attachments&&Object.keys(b.data._attachments).forEach(function(S){var H=b.data._attachments[S];H.stub&&L.push(H.digest)})}),!L.length)return C();var P=0,J;function I(){++P===L.length&&C(J)}L.forEach(function(b){i1(b,function(S){S&&!J&&(J=S),I()})})}function h1(C,L,P,J,I,b,S,H){C.metadata.winningRev=L,C.metadata.deleted=P;var $=C.data;$._id=C.metadata.id,$._rev=C.metadata.rev,J&&($._deleted=!0);var Z=$._attachments&&Object.keys($._attachments).length;if(Z)return g(C,L,P,I,S,H);r+=b,Y(),m1(C,L,P,I,S,H)}function m1(C,L,P,J,I,b){var S=C.data,H=C.metadata;S._doc_id_rev=H.id+"::"+H.rev,delete S._id,delete S._rev;function $(C1){var V1=C.stemmedRevs||[];J&&t.auto_compaction&&(V1=V1.concat(ja(C.metadata))),V1&&V1.length&&T6(V1,C.metadata.id,c),H.seq=C1.target.result;var R1=A4(H,L,P),o1=l.put(R1);o1.onsuccess=d1}function Z(C1){C1.preventDefault(),C1.stopPropagation();var V1=x.index("_doc_id_rev"),R1=V1.getKey(S._doc_id_rev);R1.onsuccess=function(o1){var t1=x.put(S,o1.target.result);t1.onsuccess=$}}function d1(){v[I]={ok:!0,id:H.id,rev:H.rev},w.set(C.metadata.id,C.metadata),p(C,H.seq,b)}var M1=x.put(S);M1.onsuccess=$,M1.onerror=Z}function g(C,L,P,J,I,b){var S=C.data,H=0,$=Object.keys(S._attachments);function Z(){H===$.length&&m1(C,L,P,J,I,b)}function d1(){H++,Z()}$.forEach(function(M1){var C1=C.data._attachments[M1];if(C1.stub)H++,Z();else{var V1=C1.data;delete C1.data,C1.revpos=parseInt(L,10);var R1=C1.digest;y(R1,V1,d1)}})}function p(C,L,P){var J=0,I=Object.keys(C.data._attachments||{});if(!I.length)return P();function b(){++J===I.length&&P()}function S($){var Z=C.data._attachments[$].digest,d1=d.put({seq:L,digestSeq:Z+"::"+L});d1.onsuccess=b,d1.onerror=function(M1){M1.preventDefault(),M1.stopPropagation(),b()}}for(var H=0;H<I.length;H++)S(I[H])}function y(C,L,P){var J=k.count(C);J.onsuccess=function(I){var b=I.target.result;if(b)return P();var S={digest:C,body:L},H=k.put(S);H.onsuccess=P}}}function R6(e,o,n,t,i){t===-1&&(t=1e3);var a=typeof e.getAll=="function"&&typeof e.getAllKeys=="function"&&t>1&&!n,s,c,l;function x(_){c=_.target.result,s&&i(s,c,l)}function k(_){s=_.target.result,c&&i(s,c,l)}function d(){if(!s.length)return i();var _=s[s.length-1],B;if(o&&o.upper)try{B=IDBKeyRange.bound(_,o.upper,!0,o.upperOpen)}catch(A){if(A.name==="DataError"&&A.code===0)return i()}else B=IDBKeyRange.lowerBound(_,!0);o=B,s=null,c=null,e.getAll(o,t).onsuccess=x,e.getAllKeys(o,t).onsuccess=k}function M(_){var B=_.target.result;if(!B)return i();i([B.key],[B.value],B)}a?(l={continue:d},e.getAll(o,t).onsuccess=x,e.getAllKeys(o,t).onsuccess=k):n?e.openCursor(o,"prev").onsuccess=M:e.openCursor(o).onsuccess=M}function Ys(e,o,n){if(typeof e.getAll=="function"){e.getAll(o).onsuccess=n;return}var t=[];function i(a){var s=a.target.result;s?(t.push(s.value),s.continue()):n({target:{result:t}})}e.openCursor(o).onsuccess=i}function e5(e,o,n){var t=new Array(e.length),i=0;e.forEach(function(a,s){o.get(a).onsuccess=function(c){c.target.result?t[s]=c.target.result:t[s]={key:a,error:"not_found"},i++,i===e.length&&n(e,t,{})}})}function t5(e,o,n,t,i){try{if(e&&o)return i?IDBKeyRange.bound(o,e,!n,!1):IDBKeyRange.bound(e,o,!1,!n);if(e)return i?IDBKeyRange.upperBound(e):IDBKeyRange.lowerBound(e);if(o)return i?IDBKeyRange.lowerBound(o,!n):IDBKeyRange.upperBound(o,!n);if(t)return IDBKeyRange.only(t)}catch(a){return{error:a}}return null}function n5(e,o,n){var t="startkey"in e?e.startkey:!1,i="endkey"in e?e.endkey:!1,a="key"in e?e.key:!1,s="keys"in e?e.keys:!1,c=e.skip||0,l=typeof e.limit=="number"?e.limit:-1,x=e.inclusive_end!==!1,k,d;if(!s&&(k=t5(t,i,x,a,e.descending),d=k&&k.error,d&&!(d.name==="DataError"&&d.code===0)))return n(l1(q4,d.name,d.message));var M=[j1,P1,q1];e.attachments&&M.push(K1);var _=Y1(o,M,"readonly");if(_.error)return n(_.error);var B=_.txn;B.oncomplete=c1,B.onabort=s0(n);var A=B.objectStore(j1),z=B.objectStore(P1),u=B.objectStore(q1),h=z.index("_doc_id_rev"),r=[],v,w;u.get(q1).onsuccess=function(e1){v=e1.target.result.docCount},e.update_seq&&(z.openKeyCursor(null,"prev").onsuccess=e1=>{var i1=e1.target.result;i1&&i1.key&&(w=i1.key)});function f(e1,i1,g1){var h1=e1.id+"::"+g1;h.get(h1).onsuccess=function(g){if(i1.doc=P2(g.target.result)||{},e.conflicts){var p=J4(e1);p.length&&(i1.doc._conflicts=p)}P6(i1.doc,e,B)}}function O(e1,i1){var g1={id:i1.id,key:i1.id,value:{rev:e1}},h1=i1.deleted;h1?s&&(r.push(g1),g1.value.deleted=!0,g1.doc=null):c--<=0&&(r.push(g1),e.include_docs&&f(i1,g1,e1))}function E(e1){for(var i1=0,g1=e1.length;i1<g1&&r.length!==l;i1++){var h1=e1[i1];if(h1.error&&s){r.push(h1);continue}var m1=A0(h1),g=m1.winningRev;O(g,m1)}}function V(e1,i1,g1){g1&&(E(i1),r.length<l&&g1.continue())}function n1(e1){var i1=e1.target.result;e.descending&&(i1=i1.reverse()),E(i1)}function Y(){var e1={total_rows:v,offset:e.skip,rows:r};e.update_seq&&w!==void 0&&(e1.update_seq=w),n(null,e1)}function c1(){e.attachments?L4(r,e.binary).then(Y):Y()}if(!(d||l===0)){if(s)return e5(s,A,V);if(l===-1)return Ys(A,k,n1);R6(A,k,e.descending,l+c,V)}}function o5(e,o){var n=e.objectStore(j1).index("deletedOrLocal");n.count(IDBKeyRange.only("0")).onsuccess=function(t){o(t.target.result)}}var I4=!1,j4=[];function i5(e,o,n,t){try{e(o,n)}catch(i){t.emit("error",i)}}function X3(){I4||!j4.length||(I4=!0,j4.shift()())}function a5(e,o,n){j4.push(function(){e(function(a,s){i5(o,a,s,n),I4=!1,l0(function(){X3(n)})})}),X3()}function s5(e,o,n,t){if(e=H1(e),e.continuous){var i=n+":"+$2();return a2.addListener(n,i,o,e),a2.notify(n),{cancel:function(){a2.removeListener(n,i)}}}var a=e.doc_ids&&new Set(e.doc_ids);e.since=e.since||0;var s=e.since,c="limit"in e?e.limit:-1;c===0&&(c=1);var l=[],x=0,k=N4(e),d=new Map,M,_,B,A;function z(E,V,n1){if(!n1||!E.length)return;var Y=new Array(E.length),c1=new Array(E.length);function e1(h1,m1){var g=e.processChange(m1,h1,e);s=g.seq=h1.seq;var p=k(g);return typeof p=="object"?Promise.reject(p):p?(x++,e.return_docs&&l.push(g),e.attachments&&e.include_docs?new Promise(function(y){P6(m1,e,M,function(){L4([g],e.binary).then(function(){y(g)})})}):Promise.resolve(g)):Promise.resolve()}function i1(){for(var h1=[],m1=0,g=Y.length;m1<g&&x!==c;m1++){var p=Y[m1];if(p){var y=c1[m1];h1.push(e1(y,p))}}Promise.all(h1).then(function(C){for(var L=0,P=C.length;L<P;L++)C[L]&&e.onChange(C[L])}).catch(e.complete),x!==c&&n1.continue()}var g1=0;V.forEach(function(h1,m1){var g=P2(h1),p=E[m1];h(g,p,function(y,C){c1[m1]=y,Y[m1]=C,++g1===E.length&&i1()})})}function u(E,V,n1,Y){if(n1.seq!==V)return Y();if(n1.winningRev===E._rev)return Y(n1,E);var c1=E._id+"::"+n1.winningRev,e1=A.get(c1);e1.onsuccess=function(i1){Y(n1,P2(i1.target.result))}}function h(E,V,n1){if(a&&!a.has(E._id))return n1();var Y=d.get(E._id);if(Y)return u(E,V,Y,n1);B.get(E._id).onsuccess=function(c1){Y=A0(c1.target.result),d.set(E._id,Y),u(E,V,Y,n1)}}function r(){e.complete(null,{results:l,last_seq:s})}function v(){!e.continuous&&e.attachments?L4(l).then(r):r()}var w=[j1,P1];e.attachments&&w.push(K1);var f=Y1(t,w,"readonly");if(f.error)return e.complete(f.error);M=f.txn,M.onabort=s0(e.complete),M.oncomplete=v,_=M.objectStore(P1),B=M.objectStore(j1),A=_.index("_doc_id_rev");var O=e.since&&!e.descending?IDBKeyRange.lowerBound(e.since,!0):null;R6(_,O,e.descending,c,z)}var T0=new Map,d4,v4=new Map;function F6(e,o){var n=this;a5(function(t){r5(n,e,t)},o,n.constructor)}function r5(e,o,n){var t=o.name,i=null,a=null;e._meta=null;function s(z){return function(u,h){u&&u instanceof Error&&!u.reason&&a&&(u.reason=a),z(u,h)}}function c(z){var u=z.createObjectStore(j1,{keyPath:"id"});z.createObjectStore(P1,{autoIncrement:!0}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0}),z.createObjectStore(K1,{keyPath:"digest"}),z.createObjectStore(q1,{keyPath:"id",autoIncrement:!1}),z.createObjectStore(l4),u.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),z.createObjectStore(a0,{keyPath:"_id"});var h=z.createObjectStore(S0,{autoIncrement:!0});h.createIndex("seq","seq"),h.createIndex("digestSeq","digestSeq",{unique:!0})}function l(z,u){var h=z.objectStore(j1);h.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),h.openCursor().onsuccess=function(r){var v=r.target.result;if(v){var w=v.value,f=g0(w);w.deletedOrLocal=f?"1":"0",h.put(w),v.continue()}else u()}}function x(z){z.createObjectStore(a0,{keyPath:"_id"}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0})}function k(z,u){var h=z.objectStore(a0),r=z.objectStore(j1),v=z.objectStore(P1),w=r.openCursor();w.onsuccess=function(f){var O=f.target.result;if(O){var E=O.value,V=E.id,n1=L0(V),Y=I0(E);if(n1){var c1=V+"::"+Y,e1=V+"::",i1=V+"::~",g1=v.index("_doc_id_rev"),h1=IDBKeyRange.bound(e1,i1,!1,!1),m1=g1.openCursor(h1);m1.onsuccess=function(g){if(m1=g.target.result,!m1)r.delete(O.primaryKey),O.continue();else{var p=m1.value;p._doc_id_rev===c1&&h.put(p),v.delete(m1.primaryKey),m1.continue()}}}else O.continue()}else u&&u()}}function d(z){var u=z.createObjectStore(S0,{autoIncrement:!0});u.createIndex("seq","seq"),u.createIndex("digestSeq","digestSeq",{unique:!0})}function M(z,u){var h=z.objectStore(P1),r=z.objectStore(K1),v=z.objectStore(S0),w=r.count();w.onsuccess=function(f){var O=f.target.result;if(!O)return u();h.openCursor().onsuccess=function(E){var V=E.target.result;if(!V)return u();for(var n1=V.value,Y=V.primaryKey,c1=Object.keys(n1._attachments||{}),e1={},i1=0;i1<c1.length;i1++){var g1=n1._attachments[c1[i1]];e1[g1.digest]=!0}var h1=Object.keys(e1);for(i1=0;i1<h1.length;i1++){var m1=h1[i1];v.put({seq:Y,digestSeq:m1+"::"+Y})}V.continue()}}}function _(z){function u(w){return w.data?A0(w):(w.deleted=w.deletedOrLocal==="1",w)}var h=z.objectStore(P1),r=z.objectStore(j1),v=r.openCursor();v.onsuccess=function(w){var f=w.target.result;if(!f)return;var O=u(f.value);O.winningRev=O.winningRev||I0(O);function E(){var n1=O.id+"::",Y=O.id+"::\uFFFF",c1=h.index("_doc_id_rev").openCursor(IDBKeyRange.bound(n1,Y)),e1=0;c1.onsuccess=function(i1){var g1=i1.target.result;if(!g1)return O.seq=e1,V();var h1=g1.primaryKey;h1>e1&&(e1=h1),g1.continue()}}function V(){var n1=A4(O,O.winningRev,O.deleted),Y=r.put(n1);Y.onsuccess=function(){f.continue()}}if(O.seq)return V();E()}}e._remote=!1,e.type=function(){return"idb"},e._id=u6(function(z){z(null,e._meta.instanceId)}),e._bulkDocs=function(u,h,r){Js(o,u,h,e,i,s(r))},e._get=function(u,h,r){var v,w,f,O=h.ctx;if(!O){var E=Y1(i,[j1,P1,K1],"readonly");if(E.error)return r(E.error);O=E.txn}function V(){r(f,{doc:v,metadata:w,ctx:O})}O.objectStore(j1).get(u).onsuccess=function(n1){if(w=A0(n1.target.result),!w)return f=l1(G1,"missing"),V();var Y;if(h.rev)Y=h.latest?Fa(h.rev,w):h.rev;else{Y=w.winningRev;var c1=g0(w);if(c1)return f=l1(G1,"deleted"),V()}var e1=O.objectStore(P1),i1=w.id+"::"+Y;e1.index("_doc_id_rev").get(i1).onsuccess=function(g1){if(v=g1.target.result,v&&(v=P2(v)),!v)return f=l1(G1,"missing"),V();V()}}},e._getAttachment=function(z,u,h,r,v){var w;if(r.ctx)w=r.ctx;else{var f=Y1(i,[j1,P1,K1],"readonly");if(f.error)return v(f.error);w=f.txn}var O=h.digest,E=h.content_type;w.objectStore(K1).get(O).onsuccess=function(V){var n1=V.target.result.body;E6(n1,E,r.binary,function(Y){v(null,Y)})}},e._info=function(u){var h,r,v=Y1(i,[q1,P1],"readonly");if(v.error)return u(v.error);var w=v.txn;w.objectStore(q1).get(q1).onsuccess=function(f){r=f.target.result.docCount},w.objectStore(P1).openKeyCursor(null,"prev").onsuccess=function(f){var O=f.target.result;h=O?O.key:0},w.oncomplete=function(){u(null,{doc_count:r,update_seq:h,idb_attachment_format:e._meta.blobSupport?"binary":"base64"})}},e._allDocs=function(u,h){n5(u,i,s(h))},e._changes=function(u){return s5(u,e,t,i)},e._close=function(z){i.close(),T0.delete(t),z()},e._getRevisionTree=function(z,u){var h=Y1(i,[j1],"readonly");if(h.error)return u(h.error);var r=h.txn,v=r.objectStore(j1).get(z);v.onsuccess=function(w){var f=A0(w.target.result);f?u(null,f.rev_tree):u(l1(G1))}},e._doCompaction=function(z,u,h){var r=[j1,P1,K1,S0],v=Y1(i,r,"readwrite");if(v.error)return h(v.error);var w=v.txn,f=w.objectStore(j1);f.get(z).onsuccess=function(O){var E=A0(O.target.result);j0(E.rev_tree,function(Y,c1,e1,i1,g1){var h1=c1+"-"+e1;u.indexOf(h1)!==-1&&(g1.status="missing")}),T6(u,z,w);var V=E.winningRev,n1=E.deleted;w.objectStore(j1).put(A4(E,V,n1))},w.onabort=s0(h),w.oncomplete=function(){h()}},e._getLocal=function(z,u){var h=Y1(i,[a0],"readonly");if(h.error)return u(h.error);var r=h.txn,v=r.objectStore(a0).get(z);v.onerror=s0(u),v.onsuccess=function(w){var f=w.target.result;f?(delete f._doc_id_rev,u(null,f)):u(l1(G1))}},e._putLocal=function(z,u,h){typeof u=="function"&&(h=u,u={}),delete z._revisions;var r=z._rev,v=z._id;r?z._rev="0-"+(parseInt(r.split("-")[1],10)+1):z._rev="0-1";var w=u.ctx,f;if(!w){var O=Y1(i,[a0],"readwrite");if(O.error)return h(O.error);w=O.txn,w.onerror=s0(h),w.oncomplete=function(){f&&h(null,f)}}var E=w.objectStore(a0),V;r?(V=E.get(v),V.onsuccess=function(n1){var Y=n1.target.result;if(!Y||Y._rev!==r)h(l1(q0));else{var c1=E.put(z);c1.onsuccess=function(){f={ok:!0,id:z._id,rev:z._rev},u.ctx&&h(null,f)}}}):(V=E.add(z),V.onerror=function(n1){h(l1(q0)),n1.preventDefault(),n1.stopPropagation()},V.onsuccess=function(){f={ok:!0,id:z._id,rev:z._rev},u.ctx&&h(null,f)})},e._removeLocal=function(z,u,h){typeof u=="function"&&(h=u,u={});var r=u.ctx;if(!r){var v=Y1(i,[a0],"readwrite");if(v.error)return h(v.error);r=v.txn,r.oncomplete=function(){w&&h(null,w)}}var w,f=z._id,O=r.objectStore(a0),E=O.get(f);E.onerror=s0(h),E.onsuccess=function(V){var n1=V.target.result;!n1||n1._rev!==z._rev?h(l1(G1)):(O.delete(f),w={ok:!0,id:f,rev:"0-0"},u.ctx&&h(null,w))}},e._destroy=function(z,u){a2.removeAllListeners(t);var h=v4.get(t);h&&h.result&&(h.result.close(),T0.delete(t));var r=indexedDB.deleteDatabase(t);r.onsuccess=function(){v4.delete(t),V2()&&t in localStorage&&delete localStorage[t],u(null,{ok:!0})},r.onerror=s0(u)};var B=T0.get(t);if(B)return i=B.idb,e._meta=B.global,l0(function(){n(null,e)});var A=indexedDB.open(t,Zs);v4.set(t,A),A.onupgradeneeded=function(z){var u=z.target.result;if(z.oldVersion<1)return c(u);var h=z.currentTarget.transaction;z.oldVersion<3&&x(u),z.oldVersion<4&&d(u);var r=[l,k,M,_],v=z.oldVersion;function w(){var f=r[v-1];v++,f&&f(h,w)}w()},A.onsuccess=function(z){i=z.target.result,i.onversionchange=function(){i.close(),T0.delete(t)},i.onabort=function(V){W1("error","Database has a global failure",V.target.error),a=V.target.error,i.close(),T0.delete(t)};var u=i.transaction([q1,l4,j1],"readwrite"),h=!1,r,v,w,f;function O(){typeof w>"u"||!h||(e._meta={name:t,instanceId:f,blobSupport:w},T0.set(t,{idb:i,global:e._meta}),n(null,e))}function E(){if(!(typeof v>"u"||typeof r>"u")){var V=t+"_id";V in r?f=r[V]:r[V]=f=$2(),r.docCount=v,u.objectStore(q1).put(r)}}u.objectStore(q1).get(q1).onsuccess=function(V){r=V.target.result||{id:q1},E()},o5(u,function(V){v=V,E()}),d4||(d4=Es(u,l4,"key")),d4.then(function(V){w=V,O()}),u.oncomplete=function(){h=!0,O()},u.onabort=s0(n)},A.onerror=function(z){var u=z.target.error&&z.target.error.message;u?u.indexOf("stored database is a higher version")!==-1&&(u=new Error('This DB was created with the newer "indexeddb" adapter, but you are trying to open it with the older "idb" adapter')):u="Failed to open indexedDB, are you in private browsing mode?",W1("error",u),n(l1(q4,u))}}F6.valid=function(){try{return typeof indexedDB<"u"&&typeof IDBKeyRange<"u"}catch{return!1}};function c5(e){e.adapter("idb",F6,!0)}function l5(e,o){return new Promise(function(n,t){var i=0,a=0,s=0,c=e.length,l;function x(){i++,e[a++]().then(d,M)}function k(){++s===c?l?t(l):n():_()}function d(){i--,k()}function M(B){i--,l=l||B,k()}function _(){for(;i<o&&a<c;)x()}_()})}var d5=25,v5=50,S2=5e3,g5=1e4,g4={};function h4(e){let o=e.doc||e.ok,n=o&&o._attachments;n&&Object.keys(n).forEach(function(t){let i=n[t];i.data=W4(i.data,i.content_type)})}function b0(e){return/^_design/.test(e)?"_design/"+encodeURIComponent(e.slice(8)):e.startsWith("_local/")?"_local/"+encodeURIComponent(e.slice(7)):encodeURIComponent(e)}function J3(e){return!e._attachments||!Object.keys(e._attachments)?Promise.resolve():Promise.all(Object.keys(e._attachments).map(function(o){let n=e._attachments[o];if(n.data&&typeof n.data!="string")return new Promise(function(t){Z4(n.data,t)}).then(function(t){n.data=t})}))}function h5(e){if(!e.prefix)return!1;let o=x6(e.prefix).protocol;return o==="http"||o==="https"}function u5(e,o){if(h5(o)){let i=o.name.substr(o.prefix.length);e=o.prefix.replace(/\/?$/,"/")+encodeURIComponent(i)}let n=x6(e);(n.user||n.password)&&(n.auth={username:n.user,password:n.password});let t=n.path.replace(/(^\/|\/$)/g,"").split("/");return n.db=t.pop(),n.db.indexOf("%")===-1&&(n.db=encodeURIComponent(n.db)),n.path=t.join("/"),n}function O1(e,o){return I2(e,e.db+"/"+o)}function I2(e,o){let n=e.path?"/":"";return e.protocol+"://"+e.host+(e.port?":"+e.port:"")+"/"+e.path+n+o}function A2(e){let o=Object.keys(e);return o.length===0?"":"?"+o.map(n=>n+"="+encodeURIComponent(e[n])).join("&")}function p5(e){let o=typeof navigator<"u"&&navigator.userAgent?navigator.userAgent.toLowerCase():"",n=o.indexOf("msie")!==-1,t=o.indexOf("trident")!==-1,i=o.indexOf("edge")!==-1,a=!("method"in e)||e.method==="GET";return(n||t||i)&&a}function V4(e,o){let n=this,t=u5(e.name,e),i=O1(t,"");e=H1(e);let a=function(d,M){return Q(this,null,function*(){if(M=M||{},M.headers=M.headers||new R0,M.credentials="include",e.auth||t.auth){let A=e.auth||t.auth,z=A.username+":"+A.password,u=l2(unescape(encodeURIComponent(z)));M.headers.set("Authorization","Basic "+u)}let _=e.headers||{};return Object.keys(_).forEach(function(A){M.headers.append(A,_[A])}),p5(M)&&(d+=(d.indexOf("?")===-1?"?":"&")+"_nonce="+Date.now()),yield(e.fetch||A6)(d,M)})};function s(d,M){return L1(d,function(..._){x().then(function(){return M.apply(this,_)}).catch(function(B){_.pop()(B)})}).bind(n)}function c(d,M){return Q(this,null,function*(){let _={};M=M||{},M.headers=M.headers||new R0,M.headers.get("Content-Type")||M.headers.set("Content-Type","application/json"),M.headers.get("Accept")||M.headers.set("Accept","application/json");let B=yield a(d,M);_.ok=B.ok,_.status=B.status;let A=yield B.json();if(_.data=A,!_.ok)throw _.data.status=_.status,N0(_.data);return Array.isArray(_.data)&&(_.data=_.data.map(function(z){return z.error||z.missing?N0(z):z})),_})}let l;function x(){return Q(this,null,function*(){return e.skip_setup?Promise.resolve():l||(l=c(i).catch(function(d){return d&&d.status&&d.status===404?(M4(404,"PouchDB is just detecting if the remote exists."),c(i,{method:"PUT"})):Promise.reject(d)}).catch(function(d){return d&&d.status&&d.status===412?!0:Promise.reject(d)}),l.catch(function(){l=null}),l)})}l0(function(){o(null,n)}),n._remote=!0,n.type=function(){return"http"},n.id=s("id",function(d){return Q(this,null,function*(){let M;try{M=yield(yield a(I2(t,""))).json()}catch{M={}}let _=M&&M.uuid?M.uuid+t.db:O1(t,"");d(null,_)})}),n.compact=s("compact",function(d,M){return Q(this,null,function*(){typeof d=="function"&&(M=d,d={}),d=H1(d),yield c(O1(t,"_compact"),{method:"POST"});function _(){n.info(function(B,A){A&&!A.compact_running?M(null,{ok:!0}):setTimeout(_,d.interval||200)})}_()})}),n.bulkGet=L1("bulkGet",function(d,M){let _=this;function B(h){return Q(this,null,function*(){let r={};d.revs&&(r.revs=!0),d.attachments&&(r.attachments=!0),d.latest&&(r.latest=!0);try{let v=yield c(O1(t,"_bulk_get"+A2(r)),{method:"POST",body:JSON.stringify({docs:d.docs})});d.attachments&&d.binary&&v.data.results.forEach(function(w){w.docs.forEach(h4)}),h(null,v.data)}catch(v){h(v)}})}function A(){let h=v5,r=Math.ceil(d.docs.length/h),v=0,w=new Array(r);function f(O){return function(E,V){w[O]=V.results,++v===r&&M(null,{results:w.flat()})}}for(let O=0;O<r;O++){let E=c2(d,["revs","attachments","binary","latest"]);E.docs=d.docs.slice(O*h,Math.min(d.docs.length,(O+1)*h)),p6(_,E,f(O))}}let z=I2(t,""),u=g4[z];typeof u!="boolean"?B(function(h,r){h?(g4[z]=!1,M4(h.status,"PouchDB is just detecting if the remote supports the _bulk_get API."),A()):(g4[z]=!0,M(null,r))}):u?B(M):A()}),n._info=function(d){return Q(this,null,function*(){try{yield x();let _=yield(yield a(O1(t,""))).json();_.host=O1(t,""),d(null,_)}catch(M){d(M)}})},n.fetch=function(d,M){return Q(this,null,function*(){yield x();let _=d.substring(0,1)==="/"?I2(t,d.substring(1)):O1(t,d);return a(_,M)})},n.get=s("get",function(d,M,_){return Q(this,null,function*(){typeof M=="function"&&(_=M,M={}),M=H1(M);let B={};M.revs&&(B.revs=!0),M.revs_info&&(B.revs_info=!0),M.latest&&(B.latest=!0),M.open_revs&&(M.open_revs!=="all"&&(M.open_revs=JSON.stringify(M.open_revs)),B.open_revs=M.open_revs),M.rev&&(B.rev=M.rev),M.conflicts&&(B.conflicts=M.conflicts),M.update_seq&&(B.update_seq=M.update_seq),d=b0(d);function A(h){let r=h._attachments,v=r&&Object.keys(r);if(!r||!v.length)return;function w(O){return Q(this,null,function*(){let E=r[O],V=b0(h._id)+"/"+k(O)+"?rev="+h._rev,n1=yield a(O1(t,V)),Y;"buffer"in n1?Y=yield n1.buffer():Y=yield n1.blob();let c1;if(M.binary){let e1=Object.getOwnPropertyDescriptor(Y.__proto__,"type");(!e1||e1.set)&&(Y.type=E.content_type),c1=Y}else c1=yield new Promise(function(e1){Z4(Y,e1)});delete E.stub,delete E.length,E.data=c1})}let f=v.map(function(O){return function(){return w(O)}});return l5(f,5)}function z(h){return Array.isArray(h)?Promise.all(h.map(function(r){if(r.ok)return A(r.ok)})):A(h)}let u=O1(t,d+A2(B));try{let h=yield c(u);M.attachments&&(yield z(h.data)),_(null,h.data)}catch(h){h.docId=d,_(h)}})}),n.remove=s("remove",function(d,M,_,B){return Q(this,null,function*(){let A;typeof M=="string"?(A={_id:d,_rev:M},typeof _=="function"&&(B=_,_={})):(A=d,typeof M=="function"?(B=M,_={}):(B=_,_=M));let z=A._rev||_.rev,u=O1(t,b0(A._id))+"?rev="+z;try{let h=yield c(u,{method:"DELETE"});B(null,h.data)}catch(h){B(h)}})});function k(d){return d.split("/").map(encodeURIComponent).join("/")}n.getAttachment=s("getAttachment",function(d,M,_,B){return Q(this,null,function*(){typeof _=="function"&&(B=_,_={});let A=_.rev?"?rev="+_.rev:"",z=O1(t,b0(d))+"/"+k(M)+A,u;try{let h=yield a(z,{method:"GET"});if(!h.ok)throw h;u=h.headers.get("content-type");let r;if(typeof process<"u"&&!process.browser&&typeof h.buffer=="function"?r=yield h.buffer():r=yield h.blob(),typeof process<"u"&&!process.browser){let v=Object.getOwnPropertyDescriptor(r.__proto__,"type");(!v||v.set)&&(r.type=u)}B(null,r)}catch(h){B(h)}})}),n.removeAttachment=s("removeAttachment",function(d,M,_,B){return Q(this,null,function*(){let A=O1(t,b0(d)+"/"+k(M))+"?rev="+_;try{let z=yield c(A,{method:"DELETE"});B(null,z.data)}catch(z){B(z)}})}),n.putAttachment=s("putAttachment",function(d,M,_,B,A,z){return Q(this,null,function*(){typeof A=="function"&&(z=A,A=B,B=_,_=null);let u=b0(d)+"/"+k(M),h=O1(t,u);if(_&&(h+="?rev="+_),typeof B=="string"){let r;try{r=U4(B)}catch{return z(l1(w6,"Attachment is not a valid base64 string"))}B=r?K4(r,A):""}try{let r=yield c(h,{headers:new R0({"Content-Type":A}),method:"PUT",body:B});z(null,r.data)}catch(r){z(r)}})}),n._bulkDocs=function(d,M,_){return Q(this,null,function*(){d.new_edits=M.new_edits;try{yield x(),yield Promise.all(d.docs.map(J3));let B=yield c(O1(t,"_bulk_docs"),{method:"POST",body:JSON.stringify(d)});_(null,B.data)}catch(B){_(B)}})},n._put=function(d,M,_){return Q(this,null,function*(){try{yield x(),yield J3(d);let B=yield c(O1(t,b0(d._id)),{method:"PUT",body:JSON.stringify(d)});_(null,B.data)}catch(B){B.docId=d&&d._id,_(B)}})},n.allDocs=s("allDocs",function(d,M){return Q(this,null,function*(){typeof d=="function"&&(M=d,d={}),d=H1(d);let _={},B,A="GET";d.conflicts&&(_.conflicts=!0),d.update_seq&&(_.update_seq=!0),d.descending&&(_.descending=!0),d.include_docs&&(_.include_docs=!0),d.attachments&&(_.attachments=!0),d.key&&(_.key=JSON.stringify(d.key)),d.start_key&&(d.startkey=d.start_key),d.startkey&&(_.startkey=JSON.stringify(d.startkey)),d.end_key&&(d.endkey=d.end_key),d.endkey&&(_.endkey=JSON.stringify(d.endkey)),typeof d.inclusive_end<"u"&&(_.inclusive_end=!!d.inclusive_end),typeof d.limit<"u"&&(_.limit=d.limit),typeof d.skip<"u"&&(_.skip=d.skip);let z=A2(_);typeof d.keys<"u"&&(A="POST",B={keys:d.keys});try{let u=yield c(O1(t,"_all_docs"+z),{method:A,body:JSON.stringify(B)});d.include_docs&&d.attachments&&d.binary&&u.data.rows.forEach(h4),M(null,u.data)}catch(u){M(u)}})}),n._changes=function(d){let M="batch_size"in d?d.batch_size:d5;d=H1(d),d.continuous&&!("heartbeat"in d)&&(d.heartbeat=g5);let _="timeout"in d?d.timeout:30*1e3;"timeout"in d&&d.timeout&&_-d.timeout<S2&&(_=d.timeout+S2),"heartbeat"in d&&d.heartbeat&&_-d.heartbeat<S2&&(_=d.heartbeat+S2);let B={};"timeout"in d&&d.timeout&&(B.timeout=d.timeout);let A=typeof d.limit<"u"?d.limit:!1,z=A;if(d.style&&(B.style=d.style),(d.include_docs||d.filter&&typeof d.filter=="function")&&(B.include_docs=!0),d.attachments&&(B.attachments=!0),d.continuous&&(B.feed="longpoll"),d.seq_interval&&(B.seq_interval=d.seq_interval),d.conflicts&&(B.conflicts=!0),d.descending&&(B.descending=!0),d.update_seq&&(B.update_seq=!0),"heartbeat"in d&&d.heartbeat&&(B.heartbeat=d.heartbeat),d.filter&&typeof d.filter=="string"&&(B.filter=d.filter),d.view&&typeof d.view=="string"&&(B.filter="_view",B.view=d.view),d.query_params&&typeof d.query_params=="object")for(let E in d.query_params)Object.prototype.hasOwnProperty.call(d.query_params,E)&&(B[E]=d.query_params[E]);let u="GET",h;d.doc_ids?(B.filter="_doc_ids",u="POST",h={doc_ids:d.doc_ids}):d.selector&&(B.filter="_selector",u="POST",h={selector:d.selector});let r=new AbortController,v,w=function(E,V){return Q(this,null,function*(){if(d.aborted)return;B.since=E,typeof B.since=="object"&&(B.since=JSON.stringify(B.since)),d.descending?A&&(B.limit=z):B.limit=!A||z>M?M:z;let n1=O1(t,"_changes"+A2(B)),Y={signal:r.signal,method:u,body:JSON.stringify(h)};if(v=E,!d.aborted)try{yield x();let c1=yield c(n1,Y);V(null,c1.data)}catch(c1){V(c1)}})},f={results:[]},O=function(E,V){if(d.aborted)return;let n1=0;if(V&&V.results){n1=V.results.length,f.last_seq=V.last_seq;let c1=null,e1=null;typeof V.pending=="number"&&(c1=V.pending),(typeof f.last_seq=="string"||typeof f.last_seq=="number")&&(e1=f.last_seq);let i1={};i1.query=d.query_params,V.results=V.results.filter(function(g1){z--;let h1=N4(d)(g1);return h1&&(d.include_docs&&d.attachments&&d.binary&&h4(g1),d.return_docs&&f.results.push(g1),d.onChange(g1,c1,e1)),h1})}else if(E){d.aborted=!0,d.complete(E);return}V&&V.last_seq&&(v=V.last_seq);let Y=A&&z<=0||V&&n1<M||d.descending;d.continuous&&!(A&&z<=0)||!Y?l0(function(){w(v,O)}):d.complete(null,f)};return w(d.since||0,O),{cancel:function(){d.aborted=!0,r.abort()}}},n.revsDiff=s("revsDiff",function(d,M,_){return Q(this,null,function*(){typeof M=="function"&&(_=M,M={});try{let B=yield c(O1(t,"_revs_diff"),{method:"POST",body:JSON.stringify(d)});_(null,B.data)}catch(B){_(B)}})}),n._close=function(d){d()},n._destroy=function(d,M){return Q(this,null,function*(){try{let _=yield c(O1(t,""),{method:"DELETE"});M(null,_)}catch(_){_.status===404?M(null,{ok:!0}):M(_)}})}}V4.valid=function(){return!0};function m5(e){e.adapter("http",V4,!1),e.adapter("https",V4,!1)}var B0=class e extends Error{constructor(o){super(),this.status=400,this.name="query_parse_error",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},T2=class e extends Error{constructor(o){super(),this.status=404,this.name="not_found",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},R2=class e extends Error{constructor(o){super(),this.status=500,this.name="invalid_value",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}};function q6(e,o){return o&&e.then(function(n){l0(function(){o(null,n)})},function(n){l0(function(){o(n)})}),e}function w5(e){return function(...o){var n=o.pop(),t=e.apply(this,o);return typeof n=="function"&&q6(t,n),t}}function f5(e,o){return e.then(function(n){return o().then(function(){return n})},function(n){return o().then(function(){throw n})})}function u4(e,o){return function(){var n=arguments,t=this;return e.add(function(){return o.apply(t,n)})}}function Y3(e){var o=new Set(e),n=new Array(o.size),t=-1;return o.forEach(function(i){n[++t]=i}),n}function p4(e){var o=new Array(e.size),n=-1;return e.forEach(function(t,i){o[++n]=i}),o}function e6(e){var o="builtin "+e+" function requires map values to be numbers or number arrays";return new R2(o)}function O4(e){for(var o=0,n=0,t=e.length;n<t;n++){var i=e[n];if(typeof i!="number")if(Array.isArray(i)){o=typeof o=="number"?[o]:o;for(var a=0,s=i.length;a<s;a++){var c=i[a];if(typeof c!="number")throw e6("_sum");typeof o[a]>"u"?o.push(c):o[a]+=c}}else throw e6("_sum");else typeof o=="number"?o+=i:o[0]+=i}return o}var x5=W1.bind(null,"log"),M5=Array.isArray,k5=JSON.parse;function N6(e,o){return $4("return ("+e.replace(/;\s*$/,"")+");",{emit:o,sum:O4,log:x5,isArray:M5,toJSON:k5})}var r2=class{constructor(){this.promise=Promise.resolve()}add(o){return this.promise=this.promise.catch(()=>{}).then(()=>o()),this.promise}finish(){return this.promise}};function t6(e){if(!e)return"undefined";switch(typeof e){case"function":return e.toString();case"string":return e.toString();default:return JSON.stringify(e)}}function y5(e,o){return t6(e)+t6(o)+"undefined"}function n6(e,o,n,t,i,a){return Q(this,null,function*(){let s=y5(n,t),c;if(!i&&(c=e._cachedViews=e._cachedViews||{},c[s]))return c[s];let l=e.info().then(function(x){return Q(this,null,function*(){let k=x.db_name+"-mrview-"+(i?"temp":y6(s));function d(z){z.views=z.views||{};let u=o;u.indexOf("/")===-1&&(u=o+"/"+o);let h=z.views[u]=z.views[u]||{};if(!h[k])return h[k]=!0,z}yield O2(e,"_local/"+a,d);let _=(yield e.registerDependentDatabase(k)).db;_.auto_compaction=!0;let B={name:k,db:_,sourceDB:e,adapter:e.adapter,mapFun:n,reduceFun:t},A;try{A=yield B.db.get("_local/lastSeq")}catch(z){if(z.status!==404)throw z}return B.seq=A?A.seq:0,c&&B.db.once("destroyed",function(){delete c[s]}),B})});return c&&(c[s]=l),l})}var o6={},i6=new r2,z5=50;function m4(e){return e.indexOf("/")===-1?[e,e]:e.split("/")}function C5(e){return e.length===1&&/^1-/.test(e[0].rev)}function a6(e,o,n){try{e.emit("error",o)}catch{W1("error",`The user's map/reduce function threw an uncaught error.
You can debug this error by doing:
myDatabase.on('error', function (err) { debugger; });
Please double-check your map/reduce function.`),W1("error",o,n)}}function _5(e,o,n,t){function i(g,p,y){try{p(y)}catch(C){a6(g,C,{fun:p,doc:y})}}function a(g,p,y,C,L){try{return{output:p(y,C,L)}}catch(P){return a6(g,P,{fun:p,keys:y,values:C,rereduce:L}),{error:P}}}function s(g,p){let y=I1(g.key,p.key);return y!==0?y:I1(g.value,p.value)}function c(g,p,y){return y=y||0,typeof p=="number"?g.slice(y,p+y):y>0?g.slice(y):g}function l(g){let p=g.value;return p&&typeof p=="object"&&p._id||g.id}function x(g){for(let p of g.rows){let y=p.doc&&p.doc._attachments;if(y)for(let C of Object.keys(y)){let L=y[C];y[C].data=W4(L.data,L.content_type)}}}function k(g){return function(p){return g.include_docs&&g.attachments&&g.binary&&x(p),p}}function d(g,p,y,C){let L=p[g];typeof L<"u"&&(C&&(L=encodeURIComponent(JSON.stringify(L))),y.push(g+"="+L))}function M(g){if(typeof g<"u"){let p=Number(g);return!isNaN(p)&&p===parseInt(g,10)?p:g}}function _(g){return g.group_level=M(g.group_level),g.limit=M(g.limit),g.skip=M(g.skip),g}function B(g){if(g){if(typeof g!="number")return new B0(`Invalid value for integer: "${g}"`);if(g<0)return new B0(`Invalid value for positive integer: "${g}"`)}}function A(g,p){let y=g.descending?"endkey":"startkey",C=g.descending?"startkey":"endkey";if(typeof g[y]<"u"&&typeof g[C]<"u"&&I1(g[y],g[C])>0)throw new B0("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(p.reduce&&g.reduce!==!1){if(g.include_docs)throw new B0("{include_docs:true} is invalid for reduce");if(g.keys&&g.keys.length>1&&!g.group&&!g.group_level)throw new B0("Multi-key fetches for reduce views must use {group: true}")}for(let L of["group_level","limit","skip"]){let P=B(g[L]);if(P)throw P}}function z(g,p,y){return Q(this,null,function*(){let C=[],L,P="GET",J;if(d("reduce",y,C),d("include_docs",y,C),d("attachments",y,C),d("limit",y,C),d("descending",y,C),d("group",y,C),d("group_level",y,C),d("skip",y,C),d("stale",y,C),d("conflicts",y,C),d("startkey",y,C,!0),d("start_key",y,C,!0),d("endkey",y,C,!0),d("end_key",y,C,!0),d("inclusive_end",y,C),d("key",y,C,!0),d("update_seq",y,C),C=C.join("&"),C=C===""?"":"?"+C,typeof y.keys<"u"){let H=`keys=${encodeURIComponent(JSON.stringify(y.keys))}`;H.length+C.length+1<=2e3?C+=(C[0]==="?"?"&":"?")+H:(P="POST",typeof p=="string"?L={keys:y.keys}:p.keys=y.keys)}if(typeof p=="string"){let S=m4(p),H=yield g.fetch("_design/"+S[0]+"/_view/"+S[1]+C,{headers:new R0({"Content-Type":"application/json"}),method:P,body:JSON.stringify(L)});J=H.ok;let $=yield H.json();if(!J)throw $.status=H.status,N0($);for(let Z of $.rows)if(Z.value&&Z.value.error&&Z.value.error==="builtin_reduce_error")throw new Error(Z.reason);return new Promise(function(Z){Z($)}).then(k(y))}L=L||{};for(let S of Object.keys(p))Array.isArray(p[S])?L[S]=p[S]:L[S]=p[S].toString();let I=yield g.fetch("_temp_view"+C,{headers:new R0({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(L)});J=I.ok;let b=yield I.json();if(!J)throw b.status=I.status,N0(b);return new Promise(function(S){S(b)}).then(k(y))})}function u(g,p,y){return new Promise(function(C,L){g._query(p,y,function(P,J){if(P)return L(P);C(J)})})}function h(g){return new Promise(function(p,y){g._viewCleanup(function(C,L){if(C)return y(C);p(L)})})}function r(g){return function(p){if(p.status===404)return g;throw p}}function v(g,p,y){return Q(this,null,function*(){let C="_local/doc_"+g,L={_id:C,keys:[]},P=y.get(g),J=P[0],I=P[1];function b(){return C5(I)?Promise.resolve(L):p.db.get(C).catch(r(L))}function S(d1){return d1.keys.length?p.db.allDocs({keys:d1.keys,include_docs:!0}):Promise.resolve({rows:[]})}function H(d1,M1){let C1=[],V1=new Set;for(let o1 of M1.rows){let t1=o1.doc;if(t1&&(C1.push(t1),V1.add(t1._id),t1._deleted=!J.has(t1._id),!t1._deleted)){let a1=J.get(t1._id);"value"in a1&&(t1.value=a1.value)}}let R1=p4(J);for(let o1 of R1)if(!V1.has(o1)){let t1={_id:o1},a1=J.get(o1);"value"in a1&&(t1.value=a1.value),C1.push(t1)}return d1.keys=Y3(R1.concat(d1.keys)),C1.push(d1),C1}let $=yield b(),Z=yield S($);return H($,Z)})}function w(g){return g.sourceDB.get("_local/purges").then(function(p){let y=p.purgeSeq;return g.db.get("_local/purgeSeq").then(function(C){return C._rev}).catch(r(void 0)).then(function(C){return g.db.put({_id:"_local/purgeSeq",_rev:C,purgeSeq:y})})}).catch(function(p){if(p.status!==404)throw p})}function f(g,p,y){var C="_local/lastSeq";return g.db.get(C).catch(r({_id:C,seq:0})).then(function(L){var P=p4(p);return Promise.all(P.map(function(J){return v(J,g,p)})).then(function(J){var I=J.flat();return L.seq=y,I.push(L),g.db.bulkDocs({docs:I})}).then(()=>w(g))})}function O(g){let p=typeof g=="string"?g:g.name,y=o6[p];return y||(y=o6[p]=new r2),y}function E(g,p){return Q(this,null,function*(){return u4(O(g),function(){return V(g,p)})()})}function V(g,p){return Q(this,null,function*(){let y,C,L;function P(o1,t1){let a1={id:C._id,key:V0(o1)};typeof t1<"u"&&t1!==null&&(a1.value=V0(t1)),y.push(a1)}let J=o(g.mapFun,P),I=g.seq||0;function b(){return g.sourceDB.info().then(function(o1){L=g.sourceDB.activeTasks.add({name:"view_indexing",total_items:o1.update_seq-I})})}function S(o1,t1){return function(){return f(g,o1,t1)}}let H=0,$={view:g.name,indexed_docs:H};g.sourceDB.emit("indexing",$);let Z=new r2;function d1(){return Q(this,null,function*(){let o1=yield g.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:I,limit:p.changes_batch_size}),t1=yield M1();return C1(o1,t1)})}function M1(){return g.db.get("_local/purgeSeq").then(function(o1){return o1.purgeSeq}).catch(r(-1)).then(function(o1){return g.sourceDB.get("_local/purges").then(function(t1){let a1=t1.purges.filter(function(B1,p1){return p1>o1}).map(B1=>B1.docId),S1=a1.filter(function(B1,p1){return a1.indexOf(B1)===p1});return Promise.all(S1.map(function(B1){return g.sourceDB.get(B1).then(function(p1){return{docId:B1,doc:p1}}).catch(r({docId:B1}))}))}).catch(r([]))})}function C1(o1,t1){let a1=o1.results;if(!a1.length&&!t1.length)return;for(let p1 of t1)if(a1.findIndex(function(Q1){return Q1.id===p1.docId})<0){let Q1={_id:p1.docId,doc:{_id:p1.docId,_deleted:1},changes:[]};p1.doc&&(Q1.doc=p1.doc,Q1.changes.push({rev:p1.doc._rev})),a1.push(Q1)}let S1=V1(a1);Z.add(S(S1,I)),H=H+a1.length;let B1={view:g.name,last_seq:o1.last_seq,results_count:a1.length,indexed_docs:H};if(g.sourceDB.emit("indexing",B1),g.sourceDB.activeTasks.update(L,{completed_items:H}),!(a1.length<p.changes_batch_size))return d1()}function V1(o1){let t1=new Map;for(let a1 of o1){if(a1.doc._id[0]!=="_"){y=[],C=a1.doc,C._deleted||i(g.sourceDB,J,C),y.sort(s);let S1=R1(y);t1.set(a1.doc._id,[S1,a1.changes])}I=a1.seq}return t1}function R1(o1){let t1=new Map,a1;for(let S1=0,B1=o1.length;S1<B1;S1++){let p1=o1[S1],k0=[p1.key,p1.id];S1>0&&I1(p1.key,a1)===0&&k0.push(S1),t1.set(J1(k0),p1),a1=p1.key}return t1}try{yield b(),yield d1(),yield Z.finish(),g.seq=I,g.sourceDB.activeTasks.remove(L)}catch(o1){g.sourceDB.activeTasks.remove(L,o1)}})}function n1(g,p,y){y.group_level===0&&delete y.group_level;let C=y.group||y.group_level,L=n(g.reduceFun),P=[],J=isNaN(y.group_level)?Number.POSITIVE_INFINITY:y.group_level;for(let I of p){let b=P[P.length-1],S=C?I.key:null;if(C&&Array.isArray(S)&&(S=S.slice(0,J)),b&&I1(b.groupKey,S)===0){b.keys.push([I.key,I.id]),b.values.push(I.value);continue}P.push({keys:[[I.key,I.id]],values:[I.value],groupKey:S})}p=[];for(let I of P){let b=a(g.sourceDB,L,I.keys,I.values,!1);if(b.error&&b.error instanceof R2)throw b.error;p.push({value:b.error?null:b.output,key:I.groupKey})}return{rows:c(p,y.limit,y.skip)}}function Y(g,p){return u4(O(g),function(){return c1(g,p)})()}function c1(g,p){return Q(this,null,function*(){let y,C=g.reduceFun&&p.reduce!==!1,L=p.skip||0;typeof p.keys<"u"&&!p.keys.length&&(p.limit=0,delete p.keys);function P(I){return Q(this,null,function*(){I.include_docs=!0;let b=yield g.db.allDocs(I);return y=b.total_rows,b.rows.map(function(S){if("value"in S.doc&&typeof S.doc.value=="object"&&S.doc.value!==null){let $=Object.keys(S.doc.value).sort(),Z=["id","key","value"];if(!($<Z||$>Z))return S.doc.value}let H=ms(S.doc._id);return{key:H[0],id:H[1],value:"value"in S.doc?S.doc.value:null}})})}function J(I){return Q(this,null,function*(){let b;if(C?b=n1(g,I,p):typeof p.keys>"u"?b={total_rows:y,offset:L,rows:I}:b={total_rows:y,offset:L,rows:c(I,p.limit,p.skip)},p.update_seq&&(b.update_seq=g.seq),p.include_docs){let S=Y3(I.map(l)),H=yield g.sourceDB.allDocs({keys:S,include_docs:!0,conflicts:p.conflicts,attachments:p.attachments,binary:p.binary}),$=new Map;for(let Z of H.rows)$.set(Z.id,Z.doc);for(let Z of I){let d1=l(Z),M1=$.get(d1);M1&&(Z.doc=M1)}}return b})}if(typeof p.keys<"u"){let b=p.keys.map(function($){let Z={startkey:J1([$]),endkey:J1([$,{}])};return p.update_seq&&(Z.update_seq=!0),P(Z)}),H=(yield Promise.all(b)).flat();return J(H)}else{let I={descending:p.descending};p.update_seq&&(I.update_seq=!0);let b,S;if("start_key"in p&&(b=p.start_key),"startkey"in p&&(b=p.startkey),"end_key"in p&&(S=p.end_key),"endkey"in p&&(S=p.endkey),typeof b<"u"&&(I.startkey=p.descending?J1([b,{}]):J1([b])),typeof S<"u"){let $=p.inclusive_end!==!1;p.descending&&($=!$),I.endkey=J1($?[S,{}]:[S])}if(typeof p.key<"u"){let $=J1([p.key]),Z=J1([p.key,{}]);I.descending?(I.endkey=$,I.startkey=Z):(I.startkey=$,I.endkey=Z)}C||(typeof p.limit=="number"&&(I.limit=p.limit),I.skip=L);let H=yield P(I);return J(H)}})}function e1(g){return Q(this,null,function*(){return(yield g.fetch("_view_cleanup",{headers:new R0({"Content-Type":"application/json"}),method:"POST"})).json()})}function i1(g){return Q(this,null,function*(){try{let p=yield g.get("_local/"+e),y=new Map;for(let b of Object.keys(p.views)){let S=m4(b),H="_design/"+S[0],$=S[1],Z=y.get(H);Z||(Z=new Set,y.set(H,Z)),Z.add($)}let C={keys:p4(y),include_docs:!0},L=yield g.allDocs(C),P={};for(let b of L.rows){let S=b.key.substring(8);for(let H of y.get(b.key)){let $=S+"/"+H;p.views[$]||($=H);let Z=Object.keys(p.views[$]),d1=b.doc&&b.doc.views&&b.doc.views[H];for(let M1 of Z)P[M1]=P[M1]||d1}}let I=Object.keys(P).filter(function(b){return!P[b]}).map(function(b){return u4(O(b),function(){return new g.constructor(b,g.__opts).destroy()})()});return Promise.all(I).then(function(){return{ok:!0}})}catch(p){if(p.status===404)return{ok:!0};throw p}})}function g1(g,p,y){return Q(this,null,function*(){if(typeof g._query=="function")return u(g,p,y);if(r0(g))return z(g,p,y);let C={changes_batch_size:g.__opts.view_update_changes_batch_size||z5};if(typeof p!="string")return A(y,p),i6.add(function(){return Q(this,null,function*(){let L=yield n6(g,"temp_view/temp_view",p.map,p.reduce,!0,e);return f5(E(L,C).then(function(){return Y(L,y)}),function(){return L.db.destroy()})})}),i6.finish();{let L=p,P=m4(L),J=P[0],I=P[1],b=yield g.get("_design/"+J);if(p=b.views&&b.views[I],!p)throw new T2(`ddoc ${b._id} has no view named ${I}`);t(b,I),A(y,p);let S=yield n6(g,L,p.map,p.reduce,!1,e);return y.stale==="ok"||y.stale==="update_after"?(y.stale==="update_after"&&l0(function(){E(S,C)}),Y(S,y)):(yield E(S,C),Y(S,y))}})}function h1(g,p,y){let C=this;typeof p=="function"&&(y=p,p={}),p=p?_(p):{},typeof g=="function"&&(g={map:g});let L=Promise.resolve().then(function(){return g1(C,g,p)});return q6(L,y),L}let m1=w5(function(){let g=this;return typeof g._viewCleanup=="function"?h(g):r0(g)?e1(g):i1(g)});return{query:h1,viewCleanup:m1}}var w4={_sum:function(e,o){return O4(o)},_count:function(e,o){return o.length},_stats:function(e,o){function n(t){for(var i=0,a=0,s=t.length;a<s;a++){var c=t[a];i+=c*c}return i}return{sum:O4(o),min:Math.min.apply(null,o),max:Math.max.apply(null,o),count:o.length,sumsqr:n(o)}}};function b5(e){if(/^_sum/.test(e))return w4._sum;if(/^_count/.test(e))return w4._count;if(/^_stats/.test(e))return w4._stats;if(/^_/.test(e))throw new Error(e+" is not a supported reduce function.")}function B5(e,o){if(typeof e=="function"&&e.length===2){var n=e;return function(t){return n(t,o)}}else return N6(e.toString(),o)}function S5(e){var o=e.toString(),n=b5(o);return n||N6(o)}function A5(e,o){var n=e.views&&e.views[o];if(typeof n.map!="string")throw new T2("ddoc "+e._id+" has no string view named "+o+", instead found object of type: "+typeof n.map)}var L5="mrviews",$6=_5(L5,B5,S5,A5);function I5(e,o,n){return $6.query.call(this,e,o,n)}function j5(e){return $6.viewCleanup.call(this,e)}var V5={query:I5,viewCleanup:j5};function O5(e,o,n){return!e._attachments||!e._attachments[n]||e._attachments[n].digest!==o._attachments[n].digest}function s6(e,o){var n=Object.keys(o._attachments);return Promise.all(n.map(function(t){return e.getAttachment(o._id,t,{rev:o._rev})}))}function H5(e,o,n){var t=r0(o)&&!r0(e),i=Object.keys(n._attachments);return t?e.get(n._id).then(function(a){return Promise.all(i.map(function(s){return O5(a,n,s)?o.getAttachment(n._id,s):e.getAttachment(a._id,s)}))}).catch(function(a){if(a.status!==404)throw a;return s6(o,n)}):s6(o,n)}function D5(e){var o=[];return Object.keys(e).forEach(function(n){var t=e[n].missing;t.forEach(function(i){o.push({id:n,rev:i})})}),{docs:o,revs:!0,latest:!0}}function E5(e,o,n,t){n=H1(n);var i=[],a=!0;function s(){var l=D5(n);if(l.docs.length)return e.bulkGet(l).then(function(x){if(t.cancelled)throw new Error("cancelled");return Promise.all(x.results.map(function(k){return Promise.all(k.docs.map(function(d){var M=d.ok;return d.error&&(a=!1),!M||!M._attachments?M:H5(o,e,M).then(_=>{var B=Object.keys(M._attachments);return _.forEach(function(A,z){var u=M._attachments[B[z]];delete u.stub,delete u.length,u.data=A}),M})}))})).then(function(k){i=i.concat(k.flat().filter(Boolean))})})}function c(){return{ok:a,docs:i}}return Promise.resolve().then(s).then(c)}var r6=1,c6="pouchdb",P5=5,X1=0;function H4(e,o,n,t,i){return e.get(o).catch(function(a){if(a.status===404)return(e.adapter==="http"||e.adapter==="https")&&M4(404,"PouchDB is just checking if a remote checkpoint exists."),{session_id:t,_id:o,history:[],replicator:c6,version:r6};throw a}).then(function(a){if(!i.cancelled&&a.last_seq!==n)return a.history=(a.history||[]).filter(function(s){return s.session_id!==t}),a.history.unshift({last_seq:n,session_id:t}),a.history=a.history.slice(0,P5),a.version=r6,a.replicator=c6,a.session_id=t,a.last_seq=n,e.put(a).catch(function(s){if(s.status===409)return H4(e,o,n,t,i);throw s})})}var F2=class{constructor(o,n,t,i,a={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0}){this.src=o,this.target=n,this.id=t,this.returnValue=i,this.opts=a,typeof a.writeSourceCheckpoint>"u"&&(a.writeSourceCheckpoint=!0),typeof a.writeTargetCheckpoint>"u"&&(a.writeTargetCheckpoint=!0)}writeCheckpoint(o,n){var t=this;return this.updateTarget(o,n).then(function(){return t.updateSource(o,n)})}updateTarget(o,n){return this.opts.writeTargetCheckpoint?H4(this.target,this.id,o,n,this.returnValue):Promise.resolve(!0)}updateSource(o,n){if(this.opts.writeSourceCheckpoint){var t=this;return H4(this.src,this.id,o,n,this.returnValue).catch(function(i){if(d6(i))return t.opts.writeSourceCheckpoint=!1,!0;throw i})}else return Promise.resolve(!0)}getCheckpoint(){var o=this;return!o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?Promise.resolve(X1):o.opts&&o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?o.src.get(o.id).then(function(n){return n.last_seq||X1}).catch(function(n){if(n.status!==404)throw n;return X1}):o.target.get(o.id).then(function(n){return o.opts&&o.opts.writeTargetCheckpoint&&!o.opts.writeSourceCheckpoint?n.last_seq||X1:o.src.get(o.id).then(function(t){if(n.version!==t.version)return X1;var i;return n.version?i=n.version.toString():i="undefined",i in l6?l6[i](n,t):X1},function(t){if(t.status===404&&n.last_seq)return o.src.put({_id:o.id,last_seq:X1}).then(function(){return X1},function(i){return d6(i)?(o.opts.writeSourceCheckpoint=!1,n.last_seq):X1});throw t})}).catch(function(n){if(n.status!==404)throw n;return X1})}},l6={undefined:function(e,o){return I1(e.last_seq,o.last_seq)===0?o.last_seq:0},1:function(e,o){return T5(o,e).last_seq}};function T5(e,o){return e.session_id===o.session_id?{last_seq:e.last_seq,history:e.history}:U6(e.history,o.history)}function U6(e,o){var n=e[0],t=e.slice(1),i=o[0],a=o.slice(1);if(!n||o.length===0)return{last_seq:X1,history:[]};var s=n.session_id;if(D4(s,o))return{last_seq:n.last_seq,history:e};var c=i.session_id;return D4(c,t)?{last_seq:i.last_seq,history:a}:U6(t,a)}function D4(e,o){var n=o[0],t=o.slice(1);return!e||o.length===0?!1:e===n.session_id?!0:D4(e,t)}function d6(e){return typeof e.status=="number"&&Math.floor(e.status/100)===4}function G6(e,o,n,t,i){return this instanceof F2?G6:new F2(e,o,n,t,i)}var v6=0;function R5(e,o,n,t){if(e.retry===!1){o.emit("error",n),o.removeAllListeners();return}if(typeof e.back_off_function!="function"&&(e.back_off_function=ga),o.emit("requestError",n),o.state==="active"||o.state==="pending"){o.emit("paused",n),o.state="stopped";var i=function(){e.current_back_off=v6},a=function(){o.removeListener("active",i)};o.once("paused",a),o.once("active",i)}e.current_back_off=e.current_back_off||v6,e.current_back_off=e.back_off_function(e.current_back_off),setTimeout(t,e.current_back_off)}function F5(e){return Object.keys(e).sort(I1).reduce(function(o,n){return o[n]=e[n],o},{})}function q5(e,o,n){var t=n.doc_ids?n.doc_ids.sort(I1):"",i=n.filter?n.filter.toString():"",a="",s="",c="";return n.selector&&(c=JSON.stringify(n.selector)),n.filter&&n.query_params&&(a=JSON.stringify(F5(n.query_params))),n.filter&&n.filter==="_view"&&(s=n.view.toString()),Promise.all([e.id(),o.id()]).then(function(l){var x=l[0]+l[1]+i+s+a+t+c;return new Promise(function(k){Q4(x,k)})}).then(function(l){return l=l.replace(/\//g,".").replace(/\+/g,"_"),"_local/"+l})}function K6(e,o,n,t,i){var a=[],s,c={seq:0,changes:[],docs:[]},l=!1,x=!1,k=!1,d=0,M=0,_=n.continuous||n.live||!1,B=n.batch_size||100,A=n.batches_limit||10,z=n.style||"all_docs",u=!1,h=n.doc_ids,r=n.selector,v,w,f=[],O=$2(),E;i=i||{ok:!0,start_time:new Date().toISOString(),docs_read:0,docs_written:0,doc_write_failures:0,errors:[]};var V={};t.ready(e,o);function n1(){return w?Promise.resolve():q5(e,o,n).then(function(b){v=b;var S={};n.checkpoint===!1?S={writeSourceCheckpoint:!1,writeTargetCheckpoint:!1}:n.checkpoint==="source"?S={writeSourceCheckpoint:!0,writeTargetCheckpoint:!1}:n.checkpoint==="target"?S={writeSourceCheckpoint:!1,writeTargetCheckpoint:!0}:S={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0},w=new G6(e,o,v,t,S)})}function Y(){if(f=[],s.docs.length!==0){var b=s.docs,S={timeout:n.timeout};return o.bulkDocs({docs:b,new_edits:!1},S).then(function(H){if(t.cancelled)throw g(),new Error("cancelled");var $=Object.create(null);H.forEach(function(d1){d1.error&&($[d1.id]=d1)});var Z=Object.keys($).length;i.doc_write_failures+=Z,i.docs_written+=b.length-Z,b.forEach(function(d1){var M1=$[d1._id];if(M1){i.errors.push(M1);var C1=(M1.name||"").toLowerCase();if(C1==="unauthorized"||C1==="forbidden")t.emit("denied",H1(M1));else throw M1}else f.push(d1)})},function(H){throw i.doc_write_failures+=b.length,H})}}function c1(){if(s.error)throw new Error("There was a problem getting docs.");i.last_seq=M=s.seq;var b=H1(i);return f.length&&(b.docs=f,typeof s.pending=="number"&&(b.pending=s.pending,delete s.pending),t.emit("change",b)),l=!0,e.info().then(function(S){var H=e.activeTasks.get(E);if(!(!s||!H)){var $=H.completed_items||0,Z=parseInt(S.update_seq,10)-parseInt(d,10);e.activeTasks.update(E,{completed_items:$+s.changes.length,total_items:Z})}}),w.writeCheckpoint(s.seq,O).then(function(){if(t.emit("checkpoint",{checkpoint:s.seq}),l=!1,t.cancelled)throw g(),new Error("cancelled");s=void 0,L()}).catch(function(S){throw I(S),S})}function e1(){var b={};return s.changes.forEach(function(S){t.emit("checkpoint",{revs_diff:S}),S.id!=="_user/"&&(b[S.id]=S.changes.map(function(H){return H.rev}))}),o.revsDiff(b).then(function(S){if(t.cancelled)throw g(),new Error("cancelled");s.diffs=S})}function i1(){return E5(e,o,s.diffs,t).then(function(b){s.error=!b.ok,b.docs.forEach(function(S){delete s.diffs[S._id],i.docs_read++,s.docs.push(S)})})}function g1(){if(!(t.cancelled||s)){if(a.length===0){h1(!0);return}s=a.shift(),t.emit("checkpoint",{start_next_batch:s.seq}),e1().then(i1).then(Y).then(c1).then(g1).catch(function(b){m1("batch processing terminated with error",b)})}}function h1(b){if(c.changes.length===0){a.length===0&&!s&&((_&&V.live||x)&&(t.state="pending",t.emit("paused")),x&&g());return}(b||x||c.changes.length>=B)&&(a.push(c),c={seq:0,changes:[],docs:[]},(t.state==="pending"||t.state==="stopped")&&(t.state="active",t.emit("active")),g1())}function m1(b,S){k||(S.message||(S.message=b),i.ok=!1,i.status="aborting",a=[],c={seq:0,changes:[],docs:[]},g(S))}function g(b){if(!k&&!(t.cancelled&&(i.status="cancelled",l)))if(i.status=i.status||"complete",i.end_time=new Date().toISOString(),i.last_seq=M,k=!0,e.activeTasks.remove(E,b),b){b=l1(b),b.result=i;var S=(b.name||"").toLowerCase();S==="unauthorized"||S==="forbidden"?(t.emit("error",b),t.removeAllListeners()):R5(n,t,b,function(){K6(e,o,n,t)})}else t.emit("complete",i),t.removeAllListeners()}function p(b,S,H){if(t.cancelled)return g();typeof S=="number"&&(c.pending=S);var $=N4(n)(b);if(!$){var Z=e.activeTasks.get(E);if(Z){var d1=Z.completed_items||0;e.activeTasks.update(E,{completed_items:++d1})}return}c.seq=b.seq||H,c.changes.push(b),t.emit("checkpoint",{pending_batch:c.seq}),l0(function(){h1(a.length===0&&V.live)})}function y(b){if(u=!1,t.cancelled)return g();if(b.results.length>0)V.since=b.results[b.results.length-1].seq,L(),h1(!0);else{var S=function(){_?(V.live=!0,L()):x=!0,h1(!0)};!s&&b.results.length===0?(l=!0,w.writeCheckpoint(b.last_seq,O).then(function(){if(l=!1,i.last_seq=M=b.last_seq,t.cancelled)throw g(),new Error("cancelled");S()}).catch(I)):S()}}function C(b){if(u=!1,t.cancelled)return g();m1("changes rejected",b)}function L(){if(!(!u&&!x&&a.length<A))return;u=!0;function b(){H.cancel()}function S(){t.removeListener("cancel",b)}t._changes&&(t.removeListener("cancel",t._abortChanges),t._changes.cancel()),t.once("cancel",b);var H=e.changes(V).on("change",p);H.then(S,S),H.then(y).catch(C),n.retry&&(t._changes=H,t._abortChanges=b)}function P(b){return e.info().then(function(S){var H=typeof n.since>"u"?parseInt(S.update_seq,10)-parseInt(b,10):parseInt(S.update_seq,10);return E=e.activeTasks.add({name:`${_?"continuous ":""}replication from ${S.db_name}`,total_items:H}),b})}function J(){n1().then(function(){if(t.cancelled){g();return}return w.getCheckpoint().then(P).then(function(b){M=b,d=b,V={since:M,limit:B,batch_size:B,style:z,doc_ids:h,selector:r,return_docs:!0},n.filter&&(typeof n.filter!="string"?V.include_docs=!0:V.filter=n.filter),"heartbeat"in n&&(V.heartbeat=n.heartbeat),"timeout"in n&&(V.timeout=n.timeout),n.query_params&&(V.query_params=n.query_params),n.view&&(V.view=n.view),L()})}).catch(function(b){m1("getCheckpoint rejected with ",b)})}function I(b){l=!1,m1("writeCheckpoint completed with error",b)}if(t.cancelled){g();return}t._addedListeners||(t.once("cancel",g),typeof n.complete=="function"&&(t.once("error",n.complete),t.once("complete",function(b){n.complete(null,b)})),t._addedListeners=!0),typeof n.since>"u"?J():n1().then(function(){return l=!0,w.writeCheckpoint(n.since,O)}).then(function(){if(l=!1,t.cancelled){g();return}M=n.since,J()}).catch(I)}var E4=class extends c0.default{constructor(){super(),this.cancelled=!1,this.state="pending";let o=new Promise((n,t)=>{this.once("complete",n),this.once("error",t)});this.then=function(n,t){return o.then(n,t)},this.catch=function(n){return o.catch(n)},this.catch(function(){})}cancel(){this.cancelled=!0,this.state="cancelled",this.emit("cancel")}ready(o,n){if(this._readyCalled)return;this._readyCalled=!0;let t=()=>{this.cancel()};o.once("destroyed",t),n.once("destroyed",t);function i(){o.removeListener("destroyed",t),n.removeListener("destroyed",t)}this.once("complete",i),this.once("error",i)}};function q2(e,o){var n=o.PouchConstructor;return typeof e=="string"?new n(e,o):e}function P4(e,o,n,t){if(typeof n=="function"&&(t=n,n={}),typeof n>"u"&&(n={}),n.doc_ids&&!Array.isArray(n.doc_ids))throw l1(N2,"`doc_ids` filter parameter is not a list.");n.complete=t,n=H1(n),n.continuous=n.continuous||n.live,n.retry="retry"in n?n.retry:!1,n.PouchConstructor=n.PouchConstructor||this;var i=new E4(n),a=q2(e,n),s=q2(o,n);return K6(a,s,n,i),i}function N5(e,o,n,t){return typeof n=="function"&&(t=n,n={}),typeof n>"u"&&(n={}),n=H1(n),n.PouchConstructor=n.PouchConstructor||this,e=q2(e,n),o=q2(o,n),new T4(e,o,n,t)}var T4=class extends c0.default{constructor(o,n,t,i){super(),this.canceled=!1;let a=t.push?Object.assign({},t,t.push):t,s=t.pull?Object.assign({},t,t.pull):t;this.push=P4(o,n,a),this.pull=P4(n,o,s),this.pushPaused=!0,this.pullPaused=!0;let c=r=>{this.emit("change",{direction:"pull",change:r})},l=r=>{this.emit("change",{direction:"push",change:r})},x=r=>{this.emit("denied",{direction:"push",doc:r})},k=r=>{this.emit("denied",{direction:"pull",doc:r})},d=()=>{this.pushPaused=!0,this.pullPaused&&this.emit("paused")},M=()=>{this.pullPaused=!0,this.pushPaused&&this.emit("paused")},_=()=>{this.pushPaused=!1,this.pullPaused&&this.emit("active",{direction:"push"})},B=()=>{this.pullPaused=!1,this.pushPaused&&this.emit("active",{direction:"pull"})},A={},z=r=>(v,w)=>{(v==="change"&&(w===c||w===l)||v==="denied"&&(w===k||w===x)||v==="paused"&&(w===M||w===d)||v==="active"&&(w===B||w===_))&&(v in A||(A[v]={}),A[v][r]=!0,Object.keys(A[v]).length===2&&this.removeAllListeners(v))};t.live&&(this.push.on("complete",this.pull.cancel.bind(this.pull)),this.pull.on("complete",this.push.cancel.bind(this.push)));function u(r,v,w){r.listeners(v).indexOf(w)==-1&&r.on(v,w)}this.on("newListener",function(r){r==="change"?(u(this.pull,"change",c),u(this.push,"change",l)):r==="denied"?(u(this.pull,"denied",k),u(this.push,"denied",x)):r==="active"?(u(this.pull,"active",B),u(this.push,"active",_)):r==="paused"&&(u(this.pull,"paused",M),u(this.push,"paused",d))}),this.on("removeListener",function(r){r==="change"?(this.pull.removeListener("change",c),this.push.removeListener("change",l)):r==="denied"?(this.pull.removeListener("denied",k),this.push.removeListener("denied",x)):r==="active"?(this.pull.removeListener("active",B),this.push.removeListener("active",_)):r==="paused"&&(this.pull.removeListener("paused",M),this.push.removeListener("paused",d))}),this.pull.on("removeListener",z("pull")),this.push.on("removeListener",z("push"));let h=Promise.all([this.push,this.pull]).then(r=>{let v={push:r[0],pull:r[1]};return this.emit("complete",v),i&&i(null,v),this.removeAllListeners(),v},r=>{if(this.cancel(),i?i(r):this.emit("error",r),this.removeAllListeners(),i)throw r});this.then=function(r,v){return h.then(r,v)},this.catch=function(r){return h.catch(r)}}cancel(){this.canceled||(this.canceled=!0,this.push.cancel(),this.pull.cancel())}};function $5(e){e.replicate=P4,e.sync=N5,Object.defineProperty(e.prototype,"replicate",{get:function(){var o=this;return typeof this.replicateMethods>"u"&&(this.replicateMethods={from:function(n,t,i){return o.constructor.replicate(n,o,t,i)},to:function(n,t,i){return o.constructor.replicate(o,n,t,i)}}),this.replicateMethods}}),e.prototype.sync=function(o,n,t){return this.constructor.sync(this,o,n,t)}}v1.plugin(c5).plugin(m5).plugin(V5).plugin($5);var tn=v1;var b1=class extends Error{constructor(o,n,t){super(),this.status=o,this.name=n,this.message=t,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}},Q7=new b1(401,"unauthorized","Name or password is incorrect."),X7=new b1(400,"bad_request","Missing JSON list of 'docs'"),J7=new b1(404,"not_found","missing"),Y7=new b1(409,"conflict","Document update conflict"),U5=new b1(400,"bad_request","_id field must contain a string"),G5=new b1(412,"missing_id","_id is required for puts"),K5=new b1(400,"bad_request","Only reserved document ids may start with underscore."),e9=new b1(412,"precondition_failed","Database not open"),W5=new b1(500,"unknown_error","Database encountered an unknown error"),t9=new b1(500,"badarg","Some query argument is invalid"),n9=new b1(400,"invalid_request","Request was invalid"),o9=new b1(400,"query_parse_error","Some query parameter is invalid"),i9=new b1(500,"doc_validation","Bad special document member"),Z5=new b1(400,"bad_request","Something wrong with the request"),a9=new b1(400,"bad_request","Document must be a JSON object"),s9=new b1(404,"not_found","Database not found"),r9=new b1(500,"indexed_db_went_bad","unknown"),c9=new b1(500,"web_sql_went_bad","unknown"),l9=new b1(500,"levelDB_went_went_bad","unknown"),d9=new b1(403,"forbidden","Forbidden by design doc validate_doc_update function"),v9=new b1(400,"bad_request","Invalid rev format"),g9=new b1(412,"file_exists","The database could not be created, the file already exists."),h9=new b1(412,"missing_stub","A pre-existing attachment stub wasn't found"),u9=new b1(413,"invalid_url","Provided URL is invalid");function nn(e,o){function n(t){for(var i=Object.getOwnPropertyNames(e),a=0,s=i.length;a<s;a++)typeof e[i[a]]!="function"&&(this[i[a]]=e[i[a]]);this.stack===void 0&&(this.stack=new Error().stack),t!==void 0&&(this.reason=t)}return n.prototype=b1.prototype,new n(o)}function d2(e){if(typeof e!="object"){var o=e;e=W5,e.data=o}return"error"in e&&e.error==="conflict"&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=new Error().stack),e}var $0=Headers;var Q5=function(e){return atob(e)};function X5(e,o){e=e||[],o=o||{};try{return new Blob(e,o)}catch(a){if(a.name!=="TypeError")throw a;for(var n=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,t=new n,i=0;i<e.length;i+=1)t.append(e[i]);return t.getBlob(o.type)}}function J5(e){for(var o=e.length,n=new ArrayBuffer(o),t=new Uint8Array(n),i=0;i<o;i++)t[i]=e.charCodeAt(i);return n}function Y5(e,o){return X5([J5(e)],{type:o})}function W6(e,o){return Y5(Q5(e),o)}function er(e,o,n){for(var t="",i=n-e.length;t.length<i;)t+=o;return t}function tr(e,o,n){var t=er(e,o,n);return t+e}var Z6=-324,on=3,an="";function A1(e,o){if(e===o)return 0;e=f0(e),o=f0(o);var n=sn(e),t=sn(o);if(n-t!==0)return n-t;switch(typeof e){case"number":return e-o;case"boolean":return e<o?-1:1;case"string":return sr(e,o)}return Array.isArray(e)?ar(e,o):rr(e,o)}function f0(e){switch(typeof e){case"undefined":return null;case"number":return e===1/0||e===-1/0||isNaN(e)?null:e;case"object":var o=e;if(Array.isArray(e)){var n=e.length;e=new Array(n);for(var t=0;t<n;t++)e[t]=f0(o[t])}else{if(e instanceof Date)return e.toJSON();if(e!==null){e={};for(var i in o)if(Object.prototype.hasOwnProperty.call(o,i)){var a=o[i];typeof a<"u"&&(e[i]=f0(a))}}}}return e}function nr(e){if(e!==null)switch(typeof e){case"boolean":return e?1:0;case"number":return cr(e);case"string":return e.replace(/\u0002/g,"").replace(/\u0001/g,"").replace(/\u0000/g,"");case"object":var o=Array.isArray(e),n=o?e:Object.keys(e),t=-1,i=n.length,a="";if(o)for(;++t<i;)a+=Z1(n[t]);else for(;++t<i;){var s=n[t];a+=Z1(s)+Z1(e[s])}return a}return""}function Z1(e){var o="\0";return e=f0(e),sn(e)+an+nr(e)+o}function or(e,o){var n=o,t,i=e[o]==="1";if(i)t=0,o++;else{var a=e[o]==="0";o++;var s="",c=e.substring(o,o+on),l=parseInt(c,10)+Z6;for(a&&(l=-l),o+=on;;){var x=e[o];if(x==="\0")break;s+=x,o++}s=s.split("."),s.length===1?t=parseInt(s,10):t=parseFloat(s[0]+"."+s[1]),a&&(t=t-10),l!==0&&(t=parseFloat(t+"e"+l))}return{num:t,length:o-n}}function ir(e,o){var n=e.pop();if(o.length){var t=o[o.length-1];n===t.element&&(o.pop(),t=o[o.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(n);else if(a===e.length-2){var s=e.pop();i[s]=n}else e.push(n)}}function Q6(e){for(var o=[],n=[],t=0;;){var i=e[t++];if(i==="\0"){if(o.length===1)return o.pop();ir(o,n);continue}switch(i){case"1":o.push(null);break;case"2":o.push(e[t]==="1"),t++;break;case"3":var a=or(e,t);o.push(a.num),t+=a.length;break;case"4":for(var s="";;){var c=e[t];if(c==="\0")break;s+=c,t++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"").replace(/\u0002\u0002/g,""),o.push(s);break;case"5":var l={element:[],index:o.length};o.push(l.element),n.push(l);break;case"6":var x={element:{},index:o.length};o.push(x.element),n.push(x);break;default:throw new Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}function ar(e,o){for(var n=Math.min(e.length,o.length),t=0;t<n;t++){var i=A1(e[t],o[t]);if(i!==0)return i}return e.length===o.length?0:e.length>o.length?1:-1}function sr(e,o){return e===o?0:e>o?1:-1}function rr(e,o){for(var n=Object.keys(e),t=Object.keys(o),i=Math.min(n.length,t.length),a=0;a<i;a++){var s=A1(n[a],t[a]);if(s!==0||(s=A1(e[n[a]],o[t[a]]),s!==0))return s}return n.length===t.length?0:n.length>t.length?1:-1}function sn(e){var o=["boolean","number","string","object"],n=o.indexOf(typeof e);if(~n)return e===null?1:Array.isArray(e)?5:n<3?n+2:n+3;if(Array.isArray(e))return 5}function cr(e){if(e===0)return"1";var o=e.toExponential().split(/e\+?/),n=parseInt(o[1],10),t=e<0,i=t?"0":"2",a=(t?-n:n)-Z6,s=tr(a.toString(),"0",on);i+=an+s;var c=Math.abs(parseFloat(o[0]));t&&(c=10-c);var l=c.toFixed(20);return l=l.replace(/\.?0+$/,""),i+=an+l,i}var lr=Z0(i4());var X6=Z0(e4()),y9=self.setImmediate||self.setTimeout;function v2(e){return X6.default.hash(e)}function dr(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer||typeof Blob<"u"&&e instanceof Blob}function vr(e){return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type)}var eo=Function.prototype.toString,gr=eo.call(Object);function hr(e){var o=Object.getPrototypeOf(e);if(o===null)return!0;var n=o.constructor;return typeof n=="function"&&n instanceof n&&eo.call(n)==gr}function h0(e){var o,n,t;if(!e||typeof e!="object")return e;if(Array.isArray(e)){for(o=[],n=0,t=e.length;n<t;n++)o[n]=h0(e[n]);return o}if(e instanceof Date&&isFinite(e))return e.toISOString();if(dr(e))return vr(e);if(!hr(e))return e;o={};for(n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var i=h0(e[n]);typeof i<"u"&&(o[n]=i)}return o}var J6;try{localStorage.setItem("_pouch_check_localstorage",1),J6=!!localStorage.getItem("_pouch_check_localstorage")}catch{J6=!1}var g2=typeof queueMicrotask=="function"?queueMicrotask:function(o){Promise.resolve().then(o)};function U2(e){if(typeof console<"u"&&typeof console[e]=="function"){var o=Array.prototype.slice.call(arguments,1);console[e].apply(console,o)}}function ur(){}var pr=ur.name,Y6;pr?Y6=function(e){return e.name}:Y6=function(e){var o=e.toString().match(/^\s*function\s*(?:(\S+)\s*)?\(/);return o&&o[1]?o[1]:""};function u0(e){return typeof e._remote=="boolean"?e._remote:typeof e.type=="function"?(U2("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),e.type()==="http"):!1}function U0(e,o,n){return e.get(o).catch(function(t){if(t.status!==404)throw t;return{}}).then(function(t){var i=t._rev,a=n(t);return a?(a._id=o,a._rev=i,mr(e,a,n)):{updated:!1,rev:i}})}function mr(e,o,n){return e.put(o).then(function(t){return{updated:!0,rev:t.rev}},function(t){if(t.status!==409)throw t;return U0(e,o._id,n)})}var x0=class e extends Error{constructor(o){super(),this.status=400,this.name="query_parse_error",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},G2=class e extends Error{constructor(o){super(),this.status=404,this.name="not_found",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},K2=class e extends Error{constructor(o){super(),this.status=500,this.name="invalid_value",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}};function rn(e,o){return o&&e.then(function(n){g2(function(){o(null,n)})},function(n){g2(function(){o(n)})}),e}function to(e){return function(...o){var n=o.pop(),t=e.apply(this,o);return typeof n=="function"&&rn(t,n),t}}function no(e,o){return e.then(function(n){return o().then(function(){return n})},function(n){return o().then(function(){throw n})})}function W2(e,o){return function(){var n=arguments,t=this;return e.add(function(){return o.apply(t,n)})}}function cn(e){var o=new Set(e),n=new Array(o.size),t=-1;return o.forEach(function(i){n[++t]=i}),n}function Z2(e){var o=new Array(e.size),n=-1;return e.forEach(function(t,i){o[++n]=i}),o}var h2=class{constructor(){this.promise=Promise.resolve()}add(o){return this.promise=this.promise.catch(()=>{}).then(()=>o()),this.promise}finish(){return this.promise}};function oo(e){if(!e)return"undefined";switch(typeof e){case"function":return e.toString();case"string":return e.toString();default:return JSON.stringify(e)}}function wr(e,o){return oo(e)+oo(o)+"undefined"}function io(e,o,n,t,i,a){return Q(this,null,function*(){let s=wr(n,t),c;if(!i&&(c=e._cachedViews=e._cachedViews||{},c[s]))return c[s];let l=e.info().then(function(x){return Q(this,null,function*(){let k=x.db_name+"-mrview-"+(i?"temp":v2(s));function d(z){z.views=z.views||{};let u=o;u.indexOf("/")===-1&&(u=o+"/"+o);let h=z.views[u]=z.views[u]||{};if(!h[k])return h[k]=!0,z}yield U0(e,"_local/"+a,d);let _=(yield e.registerDependentDatabase(k)).db;_.auto_compaction=!0;let B={name:k,db:_,sourceDB:e,adapter:e.adapter,mapFun:n,reduceFun:t},A;try{A=yield B.db.get("_local/lastSeq")}catch(z){if(z.status!==404)throw z}return B.seq=A?A.seq:0,c&&B.db.once("destroyed",function(){delete c[s]}),B})});return c&&(c[s]=l),l})}var ao={},so=new h2,fr=50;function ln(e){return e.indexOf("/")===-1?[e,e]:e.split("/")}function xr(e){return e.length===1&&/^1-/.test(e[0].rev)}function ro(e,o,n){try{e.emit("error",o)}catch{U2("error",`The user's map/reduce function threw an uncaught error.
You can debug this error by doing:
myDatabase.on('error', function (err) { debugger; });
Please double-check your map/reduce function.`),U2("error",o,n)}}function Mr(e,o,n,t){function i(g,p,y){try{p(y)}catch(C){ro(g,C,{fun:p,doc:y})}}function a(g,p,y,C,L){try{return{output:p(y,C,L)}}catch(P){return ro(g,P,{fun:p,keys:y,values:C,rereduce:L}),{error:P}}}function s(g,p){let y=A1(g.key,p.key);return y!==0?y:A1(g.value,p.value)}function c(g,p,y){return y=y||0,typeof p=="number"?g.slice(y,p+y):y>0?g.slice(y):g}function l(g){let p=g.value;return p&&typeof p=="object"&&p._id||g.id}function x(g){for(let p of g.rows){let y=p.doc&&p.doc._attachments;if(y)for(let C of Object.keys(y)){let L=y[C];y[C].data=W6(L.data,L.content_type)}}}function k(g){return function(p){return g.include_docs&&g.attachments&&g.binary&&x(p),p}}function d(g,p,y,C){let L=p[g];typeof L<"u"&&(C&&(L=encodeURIComponent(JSON.stringify(L))),y.push(g+"="+L))}function M(g){if(typeof g<"u"){let p=Number(g);return!isNaN(p)&&p===parseInt(g,10)?p:g}}function _(g){return g.group_level=M(g.group_level),g.limit=M(g.limit),g.skip=M(g.skip),g}function B(g){if(g){if(typeof g!="number")return new x0(`Invalid value for integer: "${g}"`);if(g<0)return new x0(`Invalid value for positive integer: "${g}"`)}}function A(g,p){let y=g.descending?"endkey":"startkey",C=g.descending?"startkey":"endkey";if(typeof g[y]<"u"&&typeof g[C]<"u"&&A1(g[y],g[C])>0)throw new x0("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(p.reduce&&g.reduce!==!1){if(g.include_docs)throw new x0("{include_docs:true} is invalid for reduce");if(g.keys&&g.keys.length>1&&!g.group&&!g.group_level)throw new x0("Multi-key fetches for reduce views must use {group: true}")}for(let L of["group_level","limit","skip"]){let P=B(g[L]);if(P)throw P}}function z(g,p,y){return Q(this,null,function*(){let C=[],L,P="GET",J;if(d("reduce",y,C),d("include_docs",y,C),d("attachments",y,C),d("limit",y,C),d("descending",y,C),d("group",y,C),d("group_level",y,C),d("skip",y,C),d("stale",y,C),d("conflicts",y,C),d("startkey",y,C,!0),d("start_key",y,C,!0),d("endkey",y,C,!0),d("end_key",y,C,!0),d("inclusive_end",y,C),d("key",y,C,!0),d("update_seq",y,C),C=C.join("&"),C=C===""?"":"?"+C,typeof y.keys<"u"){let H=`keys=${encodeURIComponent(JSON.stringify(y.keys))}`;H.length+C.length+1<=2e3?C+=(C[0]==="?"?"&":"?")+H:(P="POST",typeof p=="string"?L={keys:y.keys}:p.keys=y.keys)}if(typeof p=="string"){let S=ln(p),H=yield g.fetch("_design/"+S[0]+"/_view/"+S[1]+C,{headers:new $0({"Content-Type":"application/json"}),method:P,body:JSON.stringify(L)});J=H.ok;let $=yield H.json();if(!J)throw $.status=H.status,d2($);for(let Z of $.rows)if(Z.value&&Z.value.error&&Z.value.error==="builtin_reduce_error")throw new Error(Z.reason);return new Promise(function(Z){Z($)}).then(k(y))}L=L||{};for(let S of Object.keys(p))Array.isArray(p[S])?L[S]=p[S]:L[S]=p[S].toString();let I=yield g.fetch("_temp_view"+C,{headers:new $0({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(L)});J=I.ok;let b=yield I.json();if(!J)throw b.status=I.status,d2(b);return new Promise(function(S){S(b)}).then(k(y))})}function u(g,p,y){return new Promise(function(C,L){g._query(p,y,function(P,J){if(P)return L(P);C(J)})})}function h(g){return new Promise(function(p,y){g._viewCleanup(function(C,L){if(C)return y(C);p(L)})})}function r(g){return function(p){if(p.status===404)return g;throw p}}function v(g,p,y){return Q(this,null,function*(){let C="_local/doc_"+g,L={_id:C,keys:[]},P=y.get(g),J=P[0],I=P[1];function b(){return xr(I)?Promise.resolve(L):p.db.get(C).catch(r(L))}function S(d1){return d1.keys.length?p.db.allDocs({keys:d1.keys,include_docs:!0}):Promise.resolve({rows:[]})}function H(d1,M1){let C1=[],V1=new Set;for(let o1 of M1.rows){let t1=o1.doc;if(t1&&(C1.push(t1),V1.add(t1._id),t1._deleted=!J.has(t1._id),!t1._deleted)){let a1=J.get(t1._id);"value"in a1&&(t1.value=a1.value)}}let R1=Z2(J);for(let o1 of R1)if(!V1.has(o1)){let t1={_id:o1},a1=J.get(o1);"value"in a1&&(t1.value=a1.value),C1.push(t1)}return d1.keys=cn(R1.concat(d1.keys)),C1.push(d1),C1}let $=yield b(),Z=yield S($);return H($,Z)})}function w(g){return g.sourceDB.get("_local/purges").then(function(p){let y=p.purgeSeq;return g.db.get("_local/purgeSeq").then(function(C){return C._rev}).catch(r(void 0)).then(function(C){return g.db.put({_id:"_local/purgeSeq",_rev:C,purgeSeq:y})})}).catch(function(p){if(p.status!==404)throw p})}function f(g,p,y){var C="_local/lastSeq";return g.db.get(C).catch(r({_id:C,seq:0})).then(function(L){var P=Z2(p);return Promise.all(P.map(function(J){return v(J,g,p)})).then(function(J){var I=J.flat();return L.seq=y,I.push(L),g.db.bulkDocs({docs:I})}).then(()=>w(g))})}function O(g){let p=typeof g=="string"?g:g.name,y=ao[p];return y||(y=ao[p]=new h2),y}function E(g,p){return Q(this,null,function*(){return W2(O(g),function(){return V(g,p)})()})}function V(g,p){return Q(this,null,function*(){let y,C,L;function P(o1,t1){let a1={id:C._id,key:f0(o1)};typeof t1<"u"&&t1!==null&&(a1.value=f0(t1)),y.push(a1)}let J=o(g.mapFun,P),I=g.seq||0;function b(){return g.sourceDB.info().then(function(o1){L=g.sourceDB.activeTasks.add({name:"view_indexing",total_items:o1.update_seq-I})})}function S(o1,t1){return function(){return f(g,o1,t1)}}let H=0,$={view:g.name,indexed_docs:H};g.sourceDB.emit("indexing",$);let Z=new h2;function d1(){return Q(this,null,function*(){let o1=yield g.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:I,limit:p.changes_batch_size}),t1=yield M1();return C1(o1,t1)})}function M1(){return g.db.get("_local/purgeSeq").then(function(o1){return o1.purgeSeq}).catch(r(-1)).then(function(o1){return g.sourceDB.get("_local/purges").then(function(t1){let a1=t1.purges.filter(function(B1,p1){return p1>o1}).map(B1=>B1.docId),S1=a1.filter(function(B1,p1){return a1.indexOf(B1)===p1});return Promise.all(S1.map(function(B1){return g.sourceDB.get(B1).then(function(p1){return{docId:B1,doc:p1}}).catch(r({docId:B1}))}))}).catch(r([]))})}function C1(o1,t1){let a1=o1.results;if(!a1.length&&!t1.length)return;for(let p1 of t1)if(a1.findIndex(function(Q1){return Q1.id===p1.docId})<0){let Q1={_id:p1.docId,doc:{_id:p1.docId,_deleted:1},changes:[]};p1.doc&&(Q1.doc=p1.doc,Q1.changes.push({rev:p1.doc._rev})),a1.push(Q1)}let S1=V1(a1);Z.add(S(S1,I)),H=H+a1.length;let B1={view:g.name,last_seq:o1.last_seq,results_count:a1.length,indexed_docs:H};if(g.sourceDB.emit("indexing",B1),g.sourceDB.activeTasks.update(L,{completed_items:H}),!(a1.length<p.changes_batch_size))return d1()}function V1(o1){let t1=new Map;for(let a1 of o1){if(a1.doc._id[0]!=="_"){y=[],C=a1.doc,C._deleted||i(g.sourceDB,J,C),y.sort(s);let S1=R1(y);t1.set(a1.doc._id,[S1,a1.changes])}I=a1.seq}return t1}function R1(o1){let t1=new Map,a1;for(let S1=0,B1=o1.length;S1<B1;S1++){let p1=o1[S1],k0=[p1.key,p1.id];S1>0&&A1(p1.key,a1)===0&&k0.push(S1),t1.set(Z1(k0),p1),a1=p1.key}return t1}try{yield b(),yield d1(),yield Z.finish(),g.seq=I,g.sourceDB.activeTasks.remove(L)}catch(o1){g.sourceDB.activeTasks.remove(L,o1)}})}function n1(g,p,y){y.group_level===0&&delete y.group_level;let C=y.group||y.group_level,L=n(g.reduceFun),P=[],J=isNaN(y.group_level)?Number.POSITIVE_INFINITY:y.group_level;for(let I of p){let b=P[P.length-1],S=C?I.key:null;if(C&&Array.isArray(S)&&(S=S.slice(0,J)),b&&A1(b.groupKey,S)===0){b.keys.push([I.key,I.id]),b.values.push(I.value);continue}P.push({keys:[[I.key,I.id]],values:[I.value],groupKey:S})}p=[];for(let I of P){let b=a(g.sourceDB,L,I.keys,I.values,!1);if(b.error&&b.error instanceof K2)throw b.error;p.push({value:b.error?null:b.output,key:I.groupKey})}return{rows:c(p,y.limit,y.skip)}}function Y(g,p){return W2(O(g),function(){return c1(g,p)})()}function c1(g,p){return Q(this,null,function*(){let y,C=g.reduceFun&&p.reduce!==!1,L=p.skip||0;typeof p.keys<"u"&&!p.keys.length&&(p.limit=0,delete p.keys);function P(I){return Q(this,null,function*(){I.include_docs=!0;let b=yield g.db.allDocs(I);return y=b.total_rows,b.rows.map(function(S){if("value"in S.doc&&typeof S.doc.value=="object"&&S.doc.value!==null){let $=Object.keys(S.doc.value).sort(),Z=["id","key","value"];if(!($<Z||$>Z))return S.doc.value}let H=Q6(S.doc._id);return{key:H[0],id:H[1],value:"value"in S.doc?S.doc.value:null}})})}function J(I){return Q(this,null,function*(){let b;if(C?b=n1(g,I,p):typeof p.keys>"u"?b={total_rows:y,offset:L,rows:I}:b={total_rows:y,offset:L,rows:c(I,p.limit,p.skip)},p.update_seq&&(b.update_seq=g.seq),p.include_docs){let S=cn(I.map(l)),H=yield g.sourceDB.allDocs({keys:S,include_docs:!0,conflicts:p.conflicts,attachments:p.attachments,binary:p.binary}),$=new Map;for(let Z of H.rows)$.set(Z.id,Z.doc);for(let Z of I){let d1=l(Z),M1=$.get(d1);M1&&(Z.doc=M1)}}return b})}if(typeof p.keys<"u"){let b=p.keys.map(function($){let Z={startkey:Z1([$]),endkey:Z1([$,{}])};return p.update_seq&&(Z.update_seq=!0),P(Z)}),H=(yield Promise.all(b)).flat();return J(H)}else{let I={descending:p.descending};p.update_seq&&(I.update_seq=!0);let b,S;if("start_key"in p&&(b=p.start_key),"startkey"in p&&(b=p.startkey),"end_key"in p&&(S=p.end_key),"endkey"in p&&(S=p.endkey),typeof b<"u"&&(I.startkey=p.descending?Z1([b,{}]):Z1([b])),typeof S<"u"){let $=p.inclusive_end!==!1;p.descending&&($=!$),I.endkey=Z1($?[S,{}]:[S])}if(typeof p.key<"u"){let $=Z1([p.key]),Z=Z1([p.key,{}]);I.descending?(I.endkey=$,I.startkey=Z):(I.startkey=$,I.endkey=Z)}C||(typeof p.limit=="number"&&(I.limit=p.limit),I.skip=L);let H=yield P(I);return J(H)}})}function e1(g){return Q(this,null,function*(){return(yield g.fetch("_view_cleanup",{headers:new $0({"Content-Type":"application/json"}),method:"POST"})).json()})}function i1(g){return Q(this,null,function*(){try{let p=yield g.get("_local/"+e),y=new Map;for(let b of Object.keys(p.views)){let S=ln(b),H="_design/"+S[0],$=S[1],Z=y.get(H);Z||(Z=new Set,y.set(H,Z)),Z.add($)}let C={keys:Z2(y),include_docs:!0},L=yield g.allDocs(C),P={};for(let b of L.rows){let S=b.key.substring(8);for(let H of y.get(b.key)){let $=S+"/"+H;p.views[$]||($=H);let Z=Object.keys(p.views[$]),d1=b.doc&&b.doc.views&&b.doc.views[H];for(let M1 of Z)P[M1]=P[M1]||d1}}let I=Object.keys(P).filter(function(b){return!P[b]}).map(function(b){return W2(O(b),function(){return new g.constructor(b,g.__opts).destroy()})()});return Promise.all(I).then(function(){return{ok:!0}})}catch(p){if(p.status===404)return{ok:!0};throw p}})}function g1(g,p,y){return Q(this,null,function*(){if(typeof g._query=="function")return u(g,p,y);if(u0(g))return z(g,p,y);let C={changes_batch_size:g.__opts.view_update_changes_batch_size||fr};if(typeof p!="string")return A(y,p),so.add(function(){return Q(this,null,function*(){let L=yield io(g,"temp_view/temp_view",p.map,p.reduce,!0,e);return no(E(L,C).then(function(){return Y(L,y)}),function(){return L.db.destroy()})})}),so.finish();{let L=p,P=ln(L),J=P[0],I=P[1],b=yield g.get("_design/"+J);if(p=b.views&&b.views[I],!p)throw new G2(`ddoc ${b._id} has no view named ${I}`);t(b,I),A(y,p);let S=yield io(g,L,p.map,p.reduce,!1,e);return y.stale==="ok"||y.stale==="update_after"?(y.stale==="update_after"&&g2(function(){E(S,C)}),Y(S,y)):(yield E(S,C),Y(S,y))}})}function h1(g,p,y){let C=this;typeof p=="function"&&(y=p,p={}),p=p?_(p):{},typeof g=="function"&&(g={map:g});let L=Promise.resolve().then(function(){return g1(C,g,p)});return rn(L,y),L}let m1=to(function(){let g=this;return typeof g._viewCleanup=="function"?h(g):u0(g)?e1(g):i1(g)});return{query:h1,viewCleanup:m1}}var co=Mr;function K0(e,o){for(var n=e,t=0,i=o.length;t<i;t++){var a=o[t];if(n=n[a],!n)break}return n}function ho(e,o,n){for(var t=0,i=o.length;t<i-1;t++){var a=o[t];e=e[a]=e[a]||{}}e[o[i-1]]=n}function X2(e,o){return e<o?-1:e>o?1:0}function M0(e){for(var o=[],n="",t=0,i=e.length;t<i;t++){var a=e[t];t>0&&e[t-1]==="\\"&&(a==="$"||a===".")?n=n.substring(0,n.length-1)+a:a==="."?(o.push(n),n=""):n+=a}return o.push(n),o}var kr=["$or","$nor","$not"];function uo(e){return kr.indexOf(e)>-1}function N1(e){return Object.keys(e)[0]}function J2(e){return e[N1(e)]}function p2(e){var o={},n={$or:!0,$nor:!0};return e.forEach(function(t){Object.keys(t).forEach(function(i){var a=t[i];if(typeof a!="object"&&(a={$eq:a}),uo(i))if(a instanceof Array){if(n[i]){n[i]=!1,o[i]=a;return}var s=[];o[i].forEach(function(l){Object.keys(a).forEach(function(x){var k=a[x],d=Math.max(Object.keys(l).length,Object.keys(k).length),M=p2([l,k]);Object.keys(M).length<=d||s.push(M)})}),o[i]=s}else o[i]=p2([a]);else{var c=o[i]=o[i]||{};Object.keys(a).forEach(function(l){var x=a[l];if(l==="$gt"||l==="$gte")return yr(l,x,c);if(l==="$lt"||l==="$lte")return zr(l,x,c);if(l==="$ne")return Cr(x,c);if(l==="$eq")return _r(x,c);if(l==="$regex")return br(x,c);c[l]=x})}})}),o}function yr(e,o,n){typeof n.$eq<"u"||(typeof n.$gte<"u"?e==="$gte"?o>n.$gte&&(n.$gte=o):o>=n.$gte&&(delete n.$gte,n.$gt=o):typeof n.$gt<"u"?e==="$gte"?o>n.$gt&&(delete n.$gt,n.$gte=o):o>n.$gt&&(n.$gt=o):n[e]=o)}function zr(e,o,n){typeof n.$eq<"u"||(typeof n.$lte<"u"?e==="$lte"?o<n.$lte&&(n.$lte=o):o<=n.$lte&&(delete n.$lte,n.$lt=o):typeof n.$lt<"u"?e==="$lte"?o<n.$lt&&(delete n.$lt,n.$lte=o):o<n.$lt&&(n.$lt=o):n[e]=o)}function Cr(e,o){"$ne"in o?o.$ne.push(e):o.$ne=[e]}function _r(e,o){delete o.$gt,delete o.$gte,delete o.$lt,delete o.$lte,delete o.$ne,o.$eq=e}function br(e,o){"$regex"in o?o.$regex.push(e):o.$regex=[e]}function po(e){for(var o in e){if(Array.isArray(e))for(var n in e)e[n].$and&&(e[n]=p2(e[n].$and));var t=e[o];typeof t=="object"&&po(t)}return e}function mo(e,o){for(var n in e){n==="$and"&&(o=!0);var t=e[n];typeof t=="object"&&(o=mo(t,o))}return o}function Y2(e){var o=h0(e);mo(o,!1)&&(o=po(o),"$and"in o&&(o=p2(o.$and))),["$or","$nor"].forEach(function(s){s in o&&o[s].forEach(function(c){for(var l=Object.keys(c),x=0;x<l.length;x++){var k=l[x],d=c[k];(typeof d!="object"||d===null)&&(c[k]={$eq:d})}})}),"$not"in o&&(o.$not=p2([o.$not]));for(var n=Object.keys(o),t=0;t<n.length;t++){var i=n[t],a=o[i];(typeof a!="object"||a===null)&&(a={$eq:a}),o[i]=a}return dn(o),o}function dn(e){Object.keys(e).forEach(function(o){var n=e[o];Array.isArray(n)?n.forEach(function(t){t&&typeof t=="object"&&dn(t)}):o==="$ne"?e.$ne=[n]:o==="$regex"?e.$regex=[n]:n&&typeof n=="object"&&dn(n)})}function Br(e){function o(n){return e.map(function(t){var i=N1(t),a=M0(i),s=K0(n,a);return s})}return function(n,t){var i=o(n.doc),a=o(t.doc),s=A1(i,a);return s!==0?s:X2(n.doc._id,t.doc._id)}}function vn(e,o,n){if(e=e.filter(function(s){return G0(s.doc,o.selector,n)}),o.sort){var t=Br(o.sort);e=e.sort(t),typeof o.sort[0]!="string"&&J2(o.sort[0])==="desc"&&(e=e.reverse())}if("limit"in o||"skip"in o){var i=o.skip||0,a=("limit"in o?o.limit:e.length)+i;e=e.slice(i,a)}return e}function G0(e,o,n){return n.every(function(t){var i=o[t],a=M0(t),s=K0(e,a);return uo(t)?Sr(t,i,e):Q2(i,e,a,s)})}function Q2(e,o,n,t){return e?typeof e=="object"?Object.keys(e).every(function(i){var a=e[i];if(i.indexOf("$")===0)return lo(i,o,a,n,t);var s=M0(i);if(t===void 0&&typeof a!="object"&&s.length>0)return!1;var c=K0(t,s);return typeof a=="object"?Q2(a,o,n,c):lo("$eq",o,a,s,c)}):e===t:!0}function Sr(e,o,n){return e==="$or"?o.some(function(t){return G0(n,t,Object.keys(t))}):e==="$not"?!G0(n,o,Object.keys(o)):!o.find(function(t){return G0(n,t,Object.keys(t))})}function lo(e,o,n,t,i){if(!go[e])throw new Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return go[e](o,n,t,i)}function u2(e){return typeof e<"u"&&e!==null}function O0(e){return typeof e<"u"}function Ar(e,o){if(typeof e!="number"||parseInt(e,10)!==e)return!1;var n=o[0],t=o[1];return e%n===t}function vo(e,o){return o.some(function(n){return e instanceof Array?e.some(function(t){return A1(n,t)===0}):A1(n,e)===0})}function Lr(e,o){return o.every(function(n){return e.some(function(t){return A1(n,t)===0})})}function Ir(e,o){return e.length===o}function jr(e,o){var n=new RegExp(o);return n.test(e)}function Vr(e,o){switch(o){case"null":return e===null;case"boolean":return typeof e=="boolean";case"number":return typeof e=="number";case"string":return typeof e=="string";case"array":return e instanceof Array;case"object":return{}.toString.call(e)==="[object Object]"}}var go={$elemMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.some(function(i){return G0(i,o,Object.keys(o))}):t.some(function(i){return Q2(o,e,n,i)})},$allMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.every(function(i){return G0(i,o,Object.keys(o))}):t.every(function(i){return Q2(o,e,n,i)})},$eq:function(e,o,n,t){return O0(t)&&A1(t,o)===0},$gte:function(e,o,n,t){return O0(t)&&A1(t,o)>=0},$gt:function(e,o,n,t){return O0(t)&&A1(t,o)>0},$lte:function(e,o,n,t){return O0(t)&&A1(t,o)<=0},$lt:function(e,o,n,t){return O0(t)&&A1(t,o)<0},$exists:function(e,o,n,t){return o?O0(t):!O0(t)},$mod:function(e,o,n,t){return u2(t)&&Ar(t,o)},$ne:function(e,o,n,t){return o.every(function(i){return A1(t,i)!==0})},$in:function(e,o,n,t){return u2(t)&&vo(t,o)},$nin:function(e,o,n,t){return u2(t)&&!vo(t,o)},$size:function(e,o,n,t){return u2(t)&&Array.isArray(t)&&Ir(t,o)},$all:function(e,o,n,t){return Array.isArray(t)&&Lr(t,o)},$regex:function(e,o,n,t){return u2(t)&&typeof t=="string"&&o.every(function(i){return jr(t,i)})},$type:function(e,o,n,t){return Vr(t,o)}};function m2(e,o){if(typeof o!="object")throw new Error("Selector error: expected a JSON object");o=Y2(o);var n={doc:e},t=vn([n],{selector:o},Object.keys(o));return t&&t.length===1}var Or=(...e)=>e.flat(1/0),fo=(...e)=>{let o=[];for(let n of e)Array.isArray(n)?o=o.concat(fo(...n)):o.push(n);return o},xo=typeof Array.prototype.flat=="function"?Or:fo;function un(e){let o={};for(let n of e)Object.assign(o,n);return o}function Hr(e,o){let n={};for(let t of o){let i=M0(t),a=K0(e,i);typeof a<"u"&&ho(n,i,a)}return n}function Mo(e,o){for(let n=0,t=Math.min(e.length,o.length);n<t;n++)if(e[n]!==o[n])return!1;return!0}function Dr(e,o){return e.length>o.length?!1:Mo(e,o)}function Er(e,o){e=e.slice();for(let n of o){if(!e.length)break;let t=e.indexOf(n);if(t===-1)return!1;e.splice(t,1)}return!0}function Pr(e){let o={};for(let n of e)o[n]=!0;return o}function Tr(e,o){let n=null,t=-1;for(let i of e){let a=o(i);a>t&&(t=a,n=i)}return n}function wo(e,o){if(e.length!==o.length)return!1;for(let n=0,t=e.length;n<t;n++)if(e[n]!==o[n])return!1;return!0}function Rr(e){return Array.from(new Set(e))}function w2(e){return function(...o){let n=o[o.length-1];if(typeof n=="function"){let t=n.bind(null,null),i=n.bind(null);e.apply(this,o.slice(0,-1)).then(t,i)}else return e.apply(this,o)}}function ko(e){e=h0(e),e.index||(e.index={});for(let o of["type","name","ddoc"])e.index[o]&&(e[o]=e.index[o],delete e.index[o]);return e.fields&&(e.index.fields=e.fields,delete e.fields),e.type||(e.type="json"),e}function ee(e){return typeof e=="object"&&e!==null}function Fr(e,o,n){let t="",i=o,a=!0;if(["$in","$nin","$or","$and","$mod","$nor","$all"].indexOf(e)!==-1&&(Array.isArray(o)||(t="Query operator "+e+" must be an array.")),["$not","$elemMatch","$allMatch"].indexOf(e)!==-1&&(!Array.isArray(o)&&ee(o)||(t="Query operator "+e+" must be an object.")),e==="$mod"&&Array.isArray(o))if(o.length!==2)t="Query operator $mod must be in the format [divisor, remainder], where divisor and remainder are both integers.";else{let s=o[0],c=o[1];s===0&&(t="Query operator $mod's divisor cannot be 0, cannot divide by zero.",a=!1),(typeof s!="number"||parseInt(s,10)!==s)&&(t="Query operator $mod's divisor is not an integer.",i=s),parseInt(c,10)!==c&&(t="Query operator $mod's remainder is not an integer.",i=c)}if(e==="$exists"&&typeof o!="boolean"&&(t="Query operator $exists must be a boolean."),e==="$type"){let s=["null","boolean","number","string","array","object"],c='"'+s.slice(0,s.length-1).join('", "')+'", or "'+s[s.length-1]+'"';(typeof o!="string"||s.indexOf(o)==-1)&&(t="Query operator $type must be a string. Supported values: "+c+".")}if(e==="$size"&&parseInt(o,10)!==o&&(t="Query operator $size must be a integer."),e==="$regex"&&typeof o!="string"&&(n?t="Query operator $regex must be a string.":o instanceof RegExp||(t="Query operator $regex must be a string or an instance of a javascript regular expression.")),t){if(a){let s=i===null?" ":Array.isArray(i)?" array":" "+typeof i,c=ee(i)?JSON.stringify(i,null,"	"):i;t+=" Received"+s+": "+c}throw new Error(t)}}var qr=["$all","$allMatch","$and","$elemMatch","$exists","$in","$mod","$nin","$nor","$not","$or","$regex","$size","$type"],Nr=["$in","$nin","$mod","$all"],$r=["$eq","$gt","$gte","$lt","$lte"];function te(e,o){if(Array.isArray(e))for(let n of e)ee(n)&&te(n,o);else for(let[n,t]of Object.entries(e))qr.indexOf(n)!==-1&&Fr(n,t,o),$r.indexOf(n)===-1&&Nr.indexOf(n)===-1&&ee(t)&&te(t,o)}function f2(e,o,n){return Q(this,null,function*(){n.body&&(n.body=JSON.stringify(n.body),n.headers=new $0({"Content-type":"application/json"}));let t=yield e.fetch(o,n),i=yield t.json();if(!t.ok){i.status=t.status;let a=nn(i);throw d2(a)}return i})}function Ur(e,o){return Q(this,null,function*(){return yield f2(e,"_index",{method:"POST",body:ko(o)})})}function Gr(e,o){return Q(this,null,function*(){return te(o.selector,!0),yield f2(e,"_find",{method:"POST",body:o})})}function Kr(e,o){return Q(this,null,function*(){return yield f2(e,"_explain",{method:"POST",body:o})})}function Wr(e){return Q(this,null,function*(){return yield f2(e,"_index",{method:"GET"})})}function Zr(e,o){return Q(this,null,function*(){let n=o.ddoc,t=o.type||"json",i=o.name;if(!n)throw new Error("you must provide an index's ddoc");if(!i)throw new Error("you must provide an index's name");let a="_index/"+[n,t,i].map(encodeURIComponent).join("/");return yield f2(e,a,{method:"DELETE"})})}function yo(e,o){for(let n of o)if(e=e[n],e===void 0)return;return e}function Qr(e,o,n){return function(t){if(n&&!m2(t,n))return;let i=[];for(let a of e){let s=yo(t,M0(a));if(s===void 0)return;i.push(s)}o(i)}}function Xr(e,o,n){let t=M0(e);return function(i){if(n&&!m2(i,n))return;let a=yo(i,t);a!==void 0&&o(a)}}function Jr(e,o,n){return function(t){n&&!m2(t,n)||o(t[e])}}function Yr(e,o,n){return function(t){if(n&&!m2(t,n))return;let i=e.map(a=>t[a]);o(i)}}function ec(e){return e.every(o=>o.indexOf(".")===-1)}function tc(e,o,n){let t=ec(e),i=e.length===1;return t?i?Jr(e[0],o,n):Yr(e,o,n):i?Xr(e[0],o,n):Qr(e,o,n)}function nc(e,o){let n=Object.keys(e.fields),t=e.partial_filter_selector;return tc(n,o,t)}function oc(){throw new Error("reduce not supported")}function ic(e,o){let n=e.views[o];if(!n.map||!n.map.fields)throw new Error("ddoc "+e._id+" with view "+o+" doesn't have map.fields defined. maybe it wasn't created by this plugin?")}var gn=co("indexes",nc,oc,ic);function pn(e){return e._customFindAbstractMapper?{query:function(n,t){let i=gn.query.bind(this);return e._customFindAbstractMapper.query.call(this,n,t,i)},viewCleanup:function(){let n=gn.viewCleanup.bind(this);return e._customFindAbstractMapper.viewCleanup.call(this,n)}}:gn}function ac(e){if(!Array.isArray(e))throw new Error("invalid sort json - should be an array");return e.map(function(o){if(typeof o=="string"){let n={};return n[o]="asc",n}else return o})}var sc=/^_design\//;function rc(e){let o=[];return typeof e=="string"?o.push(e):o=e,o.map(function(n){return n.replace(sc,"")})}function zo(e){return e.fields=e.fields.map(function(o){if(typeof o=="string"){let n={};return n[o]="asc",n}return o}),e.partial_filter_selector&&(e.partial_filter_selector=Y2(e.partial_filter_selector)),e}function cc(e,o){return o.def.fields.map(n=>{let t=N1(n);return K0(e,M0(t))})}function lc(e,o,n){let t=n.def.fields,i=0;for(let a of e){let s=cc(a.doc,n);if(t.length===1)s=s[0];else for(;s.length>o.length;)s.pop();if(Math.abs(A1(s,o))>0)break;++i}return i>0?e.slice(i):e}function dc(e){let o=h0(e);return delete o.startkey,delete o.endkey,delete o.inclusive_start,delete o.inclusive_end,"endkey"in e&&(o.startkey=e.endkey),"startkey"in e&&(o.endkey=e.startkey),"inclusive_start"in e&&(o.inclusive_end=e.inclusive_start),"inclusive_end"in e&&(o.inclusive_start=e.inclusive_end),o}function vc(e){let o=e.fields.filter(function(n){return J2(n)==="asc"});if(o.length!==0&&o.length!==e.fields.length)throw new Error("unsupported mixed sorting")}function gc(e,o){if(o.defaultUsed&&e.sort){let n=e.sort.filter(function(t){return Object.keys(t)[0]!=="_id"}).map(function(t){return Object.keys(t)[0]});if(n.length>0)throw new Error('Cannot sort on field(s) "'+n.join(",")+'" when using the default index')}o.defaultUsed}function hc(e){if(typeof e.selector!="object")throw new Error("you must provide a selector when you find()")}function uc(e,o){let n=Object.keys(e),t=o?o.map(N1):[],i;return n.length>=t.length?i=n:i=t,t.length===0?{fields:i}:(i=i.sort(function(a,s){let c=t.indexOf(a);c===-1&&(c=Number.MAX_VALUE);let l=t.indexOf(s);return l===-1&&(l=Number.MAX_VALUE),c<l?-1:c>l?1:0}),{fields:i,sortOrder:o.map(N1)})}function pc(e,o){return Q(this,null,function*(){o=ko(o);let n=h0(o.index);o.index=zo(o.index),vc(o.index);let t;function i(){return t||(t=v2(JSON.stringify(o)))}let a=o.name||"idx-"+i(),s=o.ddoc||"idx-"+i(),c="_design/"+s,l=!1,x=!1;function k(M){return M._rev&&M.language!=="query"&&(l=!0),M.language="query",M.views=M.views||{},x=!!M.views[a],x?!1:(M.views[a]={map:{fields:un(o.index.fields),partial_filter_selector:o.index.partial_filter_selector},reduce:"_count",options:{def:n}},M)}if(e.constructor.emit("debug",["find","creating index",c]),yield U0(e,c,k),l)throw new Error('invalid language for ddoc with id "'+c+'" (should be "query")');let d=s+"/"+a;return yield pn(e).query.call(e,d,{limit:0,reduce:!1}),{id:c,name:a,result:x?"exists":"created"}})}function Co(e){return Q(this,null,function*(){let o=yield e.allDocs({startkey:"_design/",endkey:"_design/\uFFFF",include_docs:!0}),n={indexes:[{ddoc:null,name:"_all_docs",type:"special",def:{fields:[{_id:"asc"}]}}]};return n.indexes=xo(n.indexes,o.rows.filter(function(t){return t.doc.language==="query"}).map(function(t){return(t.doc.views!==void 0?Object.keys(t.doc.views):[]).map(function(a){let s=t.doc.views[a];return{ddoc:t.id,name:a,type:"json",def:zo(s.options.def)}})})),n.indexes.sort(function(t,i){return X2(t.name,i.name)}),n.total_rows=n.indexes.length,n})}var ne=null,hn={"\uFFFF":{}},mc={queryOpts:{limit:0,startkey:hn,endkey:ne},inMemoryFields:[]};function wc(e,o){return e.def.fields.some(n=>N1(n)===o)}function fc(e,o){let n=e[o];return N1(n)!=="$eq"}function _o(e,o){let n=o.def.fields.map(N1);return e.slice().sort(function(t,i){let a=n.indexOf(t),s=n.indexOf(i);return a===-1&&(a=Number.MAX_VALUE),s===-1&&(s=Number.MAX_VALUE),X2(a,s)})}function xc(e,o,n){n=_o(n,e);let t=!1;for(let i=0,a=n.length;i<a;i++){let s=n[i];if(t||!wc(e,s))return n.slice(i);i<a-1&&fc(o,s)&&(t=!0)}return[]}function Mc(e){let o=[];for(let[n,t]of Object.entries(e))for(let i of Object.keys(t))i==="$ne"&&o.push(n);return o}function kc(e,o,n,t){let i=xo(e,xc(o,n,t),Mc(n));return _o(Rr(i),o)}function yc(e,o,n){if(o){let t=Dr(o,e),i=Mo(n,e);return t&&i}return Er(n,e)}var zc=["$eq","$gt","$gte","$lt","$lte"];function bo(e){return zc.indexOf(e)===-1}function Cc(e,o){let n=e[0],t=o[n];return typeof t>"u"?!0:!(Object.keys(t).length===1&&N1(t)==="$ne")}function _c(e,o,n,t){let i=e.def.fields.map(N1);return yc(i,o,n)?Cc(i,t):!1}function bc(e,o,n,t){return t.filter(function(i){return _c(i,n,o,e)})}function Bc(e,o,n,t,i){let a=bc(e,o,n,t);if(a.length===0){if(i)throw{error:"no_usable_index",message:"There is no index available for this selector."};let l=t[0];return l.defaultUsed=!0,l}if(a.length===1&&!i)return a[0];let s=Pr(o);function c(l){let x=l.def.fields.map(N1),k=0;for(let d of x)s[d]&&k++;return k}if(i){let l="_design/"+i[0],x=i.length===2?i[1]:!1,k=a.find(function(d){return!!(x&&d.ddoc===l&&x===d.name||d.ddoc===l)});if(!k)throw{error:"unknown_error",message:"Could not find that index or could not use that index for the query"};return k}return Tr(a,c)}function Sc(e,o){switch(e){case"$eq":return{key:o};case"$lte":return{endkey:o};case"$gte":return{startkey:o};case"$lt":return{endkey:o,inclusive_end:!1};case"$gt":return{startkey:o,inclusive_start:!1}}return{startkey:ne}}function Ac(e,o){let n=N1(o.def.fields[0]),t=e[n]||{},i=[],a=Object.keys(t),s;for(let c of a){bo(c)&&i.push(n);let l=t[c],x=Sc(c,l);s?s=un([s,x]):s=x}return{queryOpts:s,inMemoryFields:i}}function Lc(e,o){switch(e){case"$eq":return{startkey:o,endkey:o};case"$lte":return{endkey:o};case"$gte":return{startkey:o};case"$lt":return{endkey:o,inclusive_end:!1};case"$gt":return{startkey:o,inclusive_start:!1}}}function Ic(e,o){let n=o.def.fields.map(N1),t=[],i=[],a=[],s,c;function l(k){s!==!1&&i.push(ne),c!==!1&&a.push(hn),t=n.slice(k)}for(let k=0,d=n.length;k<d;k++){let M=n[k],_=e[M];if(!_||!Object.keys(_).length){l(k);break}else if(Object.keys(_).some(bo)){l(k);break}else if(k>0){let z="$gt"in _||"$gte"in _||"$lt"in _||"$lte"in _,u=Object.keys(e[n[k-1]]),h=wo(u,["$eq"]),r=wo(u,Object.keys(_));if(z&&!h&&!r){l(k);break}}let B=Object.keys(_),A=null;for(let z of B){let u=_[z],h=Lc(z,u);A?A=un([A,h]):A=h}i.push("startkey"in A?A.startkey:ne),a.push("endkey"in A?A.endkey:hn),"inclusive_start"in A&&(s=A.inclusive_start),"inclusive_end"in A&&(c=A.inclusive_end)}let x={startkey:i,endkey:a};return typeof s<"u"&&(x.inclusive_start=s),typeof c<"u"&&(x.inclusive_end=c),{queryOpts:x,inMemoryFields:t}}function jc(e){return Object.keys(e).map(function(n){return e[n]}).some(function(n){return typeof n=="object"&&Object.keys(n).length===0})}function Vc(e){return{queryOpts:{startkey:null},inMemoryFields:[Object.keys(e)]}}function Oc(e,o){return o.defaultUsed?Vc(e,o):o.def.fields.length===1?Ac(e,o):Ic(e,o)}function Hc(e,o){let n=e.selector,t=e.sort;if(jc(n))return Object.assign({},mc,{index:o[0]});let i=uc(n,t),a=i.fields,s=i.sortOrder,c=Bc(n,a,s,o,e.use_index),l=Oc(n,c),x=l.queryOpts,k=l.inMemoryFields,d=kc(k,c,n,a);return{queryOpts:x,index:c,inMemoryFields:d}}function Dc(e){return e.ddoc.substring(8)+"/"+e.name}function Ec(e,o){return Q(this,null,function*(){let n=h0(o);n.descending?("endkey"in n&&typeof n.endkey!="string"&&(n.endkey=""),"startkey"in n&&typeof n.startkey!="string"&&(n.limit=0)):("startkey"in n&&typeof n.startkey!="string"&&(n.startkey=""),"endkey"in n&&typeof n.endkey!="string"&&(n.limit=0)),"key"in n&&typeof n.key!="string"&&(n.limit=0),n.limit>0&&n.indexes_count&&(n.original_limit=n.limit,n.limit+=n.indexes_count);let t=yield e.allDocs(n);return t.rows=t.rows.filter(function(i){return!/^_design\//.test(i.id)}),n.original_limit&&(n.limit=n.original_limit),t.rows=t.rows.slice(0,n.limit),t})}function Pc(e,o,n){return Q(this,null,function*(){return n.name==="_all_docs"?Ec(e,o):pn(e).query.call(e,Dc(n),o)})}function Bo(e,o,n){return Q(this,null,function*(){o.selector&&(te(o.selector,!1),o.selector=Y2(o.selector)),o.sort&&(o.sort=ac(o.sort)),o.use_index&&(o.use_index=rc(o.use_index)),"limit"in o||(o.limit=25),hc(o);let t=yield Co(e);e.constructor.emit("debug",["find","planning query",o]);let i=Hc(o,t.indexes);e.constructor.emit("debug",["find","query plan",i]);let a=i.index;gc(o,a);let s=Object.assign({include_docs:!0,reduce:!1,indexes_count:t.total_rows},i.queryOpts);if("startkey"in s&&"endkey"in s&&A1(s.startkey,s.endkey)>0)return{docs:[]};if(o.sort&&typeof o.sort[0]!="string"&&J2(o.sort[0])==="desc"&&(s.descending=!0,s=dc(s)),i.inMemoryFields.length||(s.limit=o.limit,"skip"in o&&(s.skip=o.skip)),n)return Promise.resolve(i,s);let l=yield Pc(e,s,a);s.inclusive_start===!1&&(l.rows=lc(l.rows,s.startkey,a)),i.inMemoryFields.length&&(l.rows=vn(l.rows,o,i.inMemoryFields));let x={docs:l.rows.map(function(k){let d=k.doc;return o.fields?Hr(d,o.fields):d})};return a.defaultUsed&&(x.warning="No matching index found, create an index to optimize query time."),x})}function Tc(e,o){return Q(this,null,function*(){let n=yield Bo(e,o,!0);return{dbname:e.name,index:n.index,selector:o.selector,range:{start_key:n.queryOpts.startkey,end_key:n.queryOpts.endkey},opts:{use_index:o.use_index||[],bookmark:"nil",limit:o.limit,skip:o.skip,sort:o.sort||{},fields:o.fields,conflicts:!1,r:[49]},limit:o.limit,skip:o.skip||0,fields:o.fields}})}function Rc(e,o){return Q(this,null,function*(){if(!o.ddoc)throw new Error("you must supply an index.ddoc when deleting");if(!o.name)throw new Error("you must supply an index.name when deleting");let n=o.ddoc,t=o.name;function i(a){return Object.keys(a.views).length===1&&a.views[t]?{_id:n,_deleted:!0}:(delete a.views[t],a)}return yield U0(e,n,i),yield pn(e).viewCleanup.apply(e),{ok:!0}})}var W0={};W0.createIndex=w2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide an index to create");return(u0(this)?Ur:pc)(this,e)})});W0.find=w2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide search parameters to find()");return(u0(this)?Gr:Bo)(this,e)})});W0.explain=w2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide search parameters to explain()");return(u0(this)?Kr:Tc)(this,e)})});W0.getIndexes=w2(function(){return Q(this,null,function*(){return(u0(this)?Wr:Co)(this)})});W0.deleteIndex=w2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide an index to delete");return(u0(this)?Zr:Rc)(this,e)})});var So=W0;function Ao(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let o=Math.random()*16|0;return(e==="x"?o:o&3|8).toString(16)})}var Lo=(()=>{let o=class o{constructor(){tn.plugin(So),this.initDB("register_patient")}initDB(t){this.db=new tn(t,{adapter:"idb"}),console.log(` PouchDB Initialized: ${t}`)}addRecord(t){return t._id=Ao(),e0(this.db.put(t)).pipe(t0(i=>i),n0(this.handleError))}getAllRecords(){return e0(this.db.allDocs({include_docs:!0})).pipe(t0(t=>t.rows.map(i=>i.doc)),n0(this.handleError))}getRecordById(t){return e0(this.db.get(t)).pipe(t0(i=>i),n0(this.handleError))}updateRecord(t){return!t._id||!t._rev?x2(()=>new Error(" Record must have _id and _rev to update")):e0(this.db.put(t)).pipe(t0(i=>i),n0(this.handleError))}deleteRecord(t){return!t._id||!t._rev?x2(()=>new Error(" Record must have _id and _rev to delete")):e0(this.db.remove(t._id,t._rev)).pipe(t0(i=>i),n0(this.handleError))}addOrUpdateMasterData(t){let i="master_data";return e0(this.db.get(i).then(a=>this.db.put(p0($1($1({},a),t),{_id:i,_rev:a._rev}))).catch(()=>this.db.put($1({_id:i},t)))).pipe(t0(a=>a),n0(this.handleError))}getMasterData(){return e0(this.db.get("master_data")).pipe(t0(t=>t),n0(this.handleError))}getMasterTable(t){return e0(this.db.get("master_data")).pipe(t0(i=>i[t]||[]),n0(this.handleError))}getRecordsByType(t){return e0(this.db.allDocs({include_docs:!0})).pipe(t0(i=>i.rows.map(a=>a.doc).filter(a=>a.type===t)),n0(this.handleError))}syncWithServer(){return new mn(t=>{if(!this.remoteDB){t.error(" Remote DB not configured!");return}let i=this.db.sync(this.remoteDB,{live:!0,retry:!0}).on("change",a=>t.next(a)).on("paused",a=>console.log(" Sync paused:",a)).on("active",()=>console.log(" Sync active")).on("error",a=>t.error(a));return()=>i.cancel()})}handleError(t){return console.error(" PouchDB Error:",t),x2(()=>t)}};o.\u0275fac=function(i){return new(i||o)},o.\u0275prov=X0({token:o,factory:o.\u0275fac,providedIn:"root"});let e=o;return e})();var qc=["video"],Nc=["canvas"],$c=["uploadVideo"];function Uc(e,o){if(e&1){let n=T1();D(0,"div",17)(1,"h3"),X(2,"Basic Information"),j(),D(3,"div",18)(4,"div",19)(5,"label"),X(6,"First Name"),j(),w1(7,"input",20),j(),D(8,"div",19)(9,"label"),X(10,"Last Name"),j(),w1(11,"input",21),j(),D(12,"div",19)(13,"label"),X(14,"Date of Birth"),j(),w1(15,"input",22),j()(),D(16,"div",18)(17,"div",19)(18,"label"),X(19,"Gender"),j(),D(20,"select",23)(21,"option",24),X(22,"Select Gender"),j(),D(23,"option",25),X(24,"Male"),j(),D(25,"option",26),X(26,"Female"),j(),D(27,"option",27),X(28,"Other"),j()()(),D(29,"div",19)(30,"label"),X(31,"Contact Number"),j(),w1(32,"input",28),j(),D(33,"div",19)(34,"label"),X(35,"Email ID"),j(),w1(36,"input",29),j()(),D(37,"div",18)(38,"div",30)(39,"label"),X(40,"Address"),j(),w1(41,"textarea",31),j()(),D(42,"div",32),w1(43,"span"),D(44,"button",33),f1("click",function(){k1(n);let i=s1();return y1(i.nextStep())}),X(45,"Next "),w1(46,"img",34),j()()()}}function Gc(e,o){if(e&1&&(D(0,"option",51),X(1),j()),e&2){let n=o.$implicit;u1("value",n.CountryId),r1(),U1(n.Country)}}function Kc(e,o){if(e&1&&(D(0,"option",51),X(1),j()),e&2){let n=o.$implicit;u1("value",n.StateId),r1(),U1(n.State)}}function Wc(e,o){if(e&1&&(D(0,"option",51),X(1),j()),e&2){let n=o.$implicit;u1("value",n),r1(),U1(n)}}function Zc(e,o){if(e&1){let n=T1();D(0,"div",17)(1,"h3"),X(2,"Additional Information"),j(),D(3,"div",18)(4,"div",19)(5,"label"),X(6,"Relationship Status"),j(),D(7,"select",35)(8,"option",36),X(9,"Select a Relationship Status *"),j(),D(10,"option",37),X(11,"Single"),j(),D(12,"option",38),X(13,"Married"),j()()(),D(14,"div",19)(15,"label"),X(16,"Height"),j(),w1(17,"input",39),j(),D(18,"div",19)(19,"label"),X(20,"Weight"),j(),w1(21,"input",40),j()(),D(22,"div",18)(23,"div",19)(24,"label"),X(25,"Country"),j(),D(26,"select",41),f1("change",function(i){k1(n);let a=s1();return y1(a.onCountryChange(i))}),D(27,"option",36),X(28,"Select Country *"),j(),D1(29,Gc,2,2,"option",42),j()(),D(30,"div",19)(31,"label"),X(32,"State"),j(),D(33,"select",43),f1("change",function(i){k1(n);let a=s1();return y1(a.onStateChange(i))}),D(34,"option",36),X(35,"Select State *"),j(),D1(36,Kc,2,2,"option",42),j()(),D(37,"div",19)(38,"label"),X(39,"District"),j(),D(40,"select",44)(41,"option",45),X(42,"Select District *"),j(),D1(43,Wc,2,2,"option",42),j()()(),D(44,"div",18)(45,"div",19)(46,"label"),X(47,"City"),j(),w1(48,"input",46),j(),D(49,"div",19)(50,"label"),X(51,"Taluk"),j(),w1(52,"input",47),j(),D(53,"div",19)(54,"label"),X(55,"UID"),j(),w1(56,"input",48),j()(),D(57,"div",32)(58,"button",49),f1("click",function(){k1(n);let i=s1();return y1(i.prevStep())}),w1(59,"img",50),X(60," Back"),j(),D(61,"button",33),f1("click",function(){k1(n);let i=s1();return y1(i.nextStep())}),X(62,"Next "),w1(63,"img",34),j()()()}if(e&2){let n=s1();r1(29),u1("ngForOf",n.countryList),r1(7),u1("ngForOf",n.stateList),r1(7),u1("ngForOf",n.districtList)}}function Qc(e,o){e&1&&w1(0,"video",63,1)}function Xc(e,o){if(e&1&&w1(0,"img",64),e&2){let n=s1(2);u1("src",n.photoPreviewUrl,se)}}function Jc(e,o){if(e&1){let n=T1();D(0,"div",65)(1,"button",66),f1("click",function(){k1(n);let i=s1(2);return y1(i.openCamera())}),w1(2,"img",67),X(3," Open Camera "),j()()}}function Yc(e,o){if(e&1){let n=T1();D(0,"button",68),f1("click",function(){k1(n);let i=s1(2);return y1(i.retakeImage())}),X(1," \u{1F504} Retake Image "),j()}}function el(e,o){e&1&&(D(0,"span",79),X(1,"\u2713"),j())}function tl(e,o){if(e&1){let n=T1();D(0,"div",77),f1("click",function(){let i=k1(n).$implicit,a=s1(4);return y1(a.selectCamera(i.deviceId))}),D(1,"span"),X(2),j(),D1(3,el,2,0,"span",78),j()}if(e&2){let n=o.$implicit,t=o.index,i=s1(4);w0("selected",i.selectedCameraId===n.deviceId),r1(2),U1(n.label||"Camera "+(t+1)),r1(),u1("ngIf",i.selectedCameraId===n.deviceId)}}function nl(e,o){if(e&1&&(D(0,"div",74)(1,"div",75),X(2,"Select Camera"),j(),D1(3,tl,4,4,"div",76),j()),e&2){let n=s1(3);r1(3),u1("ngForOf",n.videoDevices)}}function ol(e,o){if(e&1){let n=T1();D(0,"div",69)(1,"div",70),f1("click",function(){k1(n);let i=s1(2);return y1(i.captureImage())}),w1(2,"img",67),j(),D(3,"div",71),f1("click",function(){k1(n);let i=s1(2);return y1(i.toggleCameraDropdown())}),w1(4,"img",72),j(),D1(5,nl,4,1,"div",73),j()}if(e&2){let n=s1(2);r1(5),u1("ngIf",n.showCameraDropdown)}}function il(e,o){if(e&1){let n=T1();D(0,"button",80),f1("click",function(){k1(n);let i=s1(2);return y1(i.captureImage())}),X(1," Save "),j()}}function al(e,o){if(e&1){let n=T1();D(0,"div",17)(1,"h3"),X(2,"Capture Patient\u2019s Image"),j(),D(3,"div",52)(4,"div",53)(5,"div",54),D1(6,Qc,2,0,"video",55)(7,Xc,1,1,"img",56)(8,Jc,4,0,"div",57),w1(9,"canvas",58,0),j(),D(11,"div",59),D1(12,Yc,2,0,"button",60)(13,ol,6,1,"div",61)(14,il,2,0,"button",62),j()()(),D(15,"div",32)(16,"button",49),f1("click",function(){k1(n);let i=s1();return y1(i.prevStep())}),w1(17,"img",50),X(18," Back"),j(),D(19,"button",33),f1("click",function(){k1(n);let i=s1();return y1(i.nextStep())}),X(20,"Next "),w1(21,"img",34),j()()()}if(e&2){let n=s1();r1(5),w0("captured",n.photoPreviewUrl),r1(),u1("ngIf",!n.photoPreviewUrl&&n.isCameraActive),r1(),u1("ngIf",n.photoPreviewUrl),r1(),u1("ngIf",!n.photoPreviewUrl&&!n.isCameraActive),r1(4),u1("ngIf",n.photoPreviewUrl),r1(),u1("ngIf",n.isCameraActive&&!n.photoPreviewUrl),r1(),u1("ngIf",n.isCameraActive&&!n.photoPreviewUrl)}}function sl(e,o){if(e&1&&(D(0,"div",100)(1,"label",101),X(2,"Document Type"),j(),D(3,"select",102)(4,"option",45),X(5,"Select Document Type"),j(),D(6,"option",103),X(7,"Aadhaar"),j(),D(8,"option",104),X(9,"PAN"),j(),D(10,"option",105),X(11,"Passport"),j(),D(12,"option",106),X(13,"Driving License"),j(),D(14,"option",107),X(15,"Voter ID"),j(),D(16,"option",27),X(17,"Other"),j()()()),e&2){let n=s1().index,t=s1(3);r1(3),u1("formControl",t.getDocTypeControl(n))}}function rl(e,o){if(e&1&&(D(0,"div",108)(1,"div",109)(2,"span",110),X(3),j(),D(4,"span",111),X(5),zn(6,"date"),j(),D(7,"span",111),X(8),j()()()),e&2){let n=s1().$implicit;r1(3),U1(n.type||"Unknown"),r1(2),U1(Cn(6,3,n.date,"dd MMM yyyy")),r1(3),U1(n.time)}}function cl(e,o){if(e&1){let n=T1();Y0(0),D(1,"button",112),f1("click",function(){k1(n);let i=s1().index,a=s1(3);return y1(a.cancelEdit(i))}),w1(2,"img",113),j(),D(3,"button",114),f1("click",function(){k1(n);let i=s1().index,a=s1(3);return y1(a.saveEdit(i))}),w1(4,"img",115),j(),e2()}}function ll(e,o){if(e&1){let n=T1();Y0(0),D(1,"button",116),f1("click",function(){k1(n);let i=s1().index,a=s1(3);return y1(a.editDoc(i))}),w1(2,"img",117),j(),D(3,"button",118),f1("click",function(){k1(n);let i=s1().index,a=s1(3);return y1(a.deleteDoc(i))}),w1(4,"img",119),j(),e2()}}function dl(e,o){if(e&1&&(D(0,"div",94),w1(1,"img",95),D1(2,sl,18,1,"div",96)(3,rl,9,6,"div",97),D(4,"div",98),D1(5,cl,5,0,"ng-container",99)(6,ll,5,0,"ng-container",99),j()()),e&2){let n=o.$implicit;w0("editing-mode",n.isEditing)("view-mode",!n.isEditing),r1(),u1("src",n.preview,se),r1(),u1("ngIf",n.isEditing),r1(),u1("ngIf",!n.isEditing),r1(2),u1("ngIf",n.isEditing),r1(),u1("ngIf",!n.isEditing)}}function vl(e,o){if(e&1){let n=T1();D(0,"div",84)(1,"div",85),D1(2,dl,7,9,"div",86),j(),D(3,"div",87),f1("drop",function(i){k1(n);let a=s1(2);return y1(a.onFileDrop(i))})("dragover",function(i){k1(n);let a=s1(2);return y1(a.onDragOver(i))}),D(4,"div",88),X(5,"\u2B06\uFE0F"),j(),D(6,"p",89),X(7,"Upload Documents"),j(),D(8,"p",90),X(9," Drag & Drop / "),D(10,"span",91),f1("click",function(){k1(n);let i=le(16);return y1(i.click())}),X(11,"Choose File"),j(),X(12," / "),D(13,"span",92),f1("click",function(){k1(n);let i=s1(2);return y1(i.startCapture())}),X(14,"Capture Image"),j()(),D(15,"input",93,2),f1("change",function(i){k1(n);let a=s1(2);return y1(a.onFileSelected(i))}),j()()()}if(e&2){let n=s1(2);r1(2),u1("ngForOf",n.uploadedDocs)}}function gl(e,o){if(e&1){let n=T1();D(0,"div",120),f1("drop",function(i){k1(n);let a=s1(2);return y1(a.onFileDrop(i))})("dragover",function(i){k1(n);let a=s1(2);return y1(a.onDragOver(i))}),D(1,"div",88),w1(2,"img",121),j(),D(3,"p",89),X(4,"Upload Documents"),j(),D(5,"div",122)(6,"p",90),X(7," Drag & Drop / "),D(8,"span",91),f1("click",function(){k1(n);let i=le(15);return y1(i.click())}),X(9,"Choose File"),j(),X(10," / "),D(11,"span",92),f1("click",function(){k1(n);let i=s1(2);return y1(i.startCapture())}),X(12,"Capture Image"),j(),X(13," to upload "),j()(),D(14,"input",93,2),f1("change",function(i){k1(n);let a=s1(2);return y1(a.onFileSelected(i))}),j()()}}function hl(e,o){if(e&1){let n=T1();D(0,"div",17)(1,"h3"),X(2,"Upload Documents"),j(),D1(3,vl,17,1,"div",81)(4,gl,16,0,"div",82),D(5,"div",32)(6,"button",49),f1("click",function(){k1(n);let i=s1();return y1(i.prevStep())}),w1(7,"img",50),X(8," Back"),j(),D(9,"button",83),X(10,"Register \u2714"),j()()()}if(e&2){let n=s1();r1(3),u1("ngIf",n.uploadedDocs.length>0),r1(),u1("ngIf",n.uploadedDocs.length===0)}}var gd=(()=>{let o=class o{constructor(t,i,a,s){this.objPouchdbService=t,this.fb=i,this.toastController=a,this.http=s,this.submittedPatients=[],this.editIndex=null,this.activeSection="section0",this.currentStep=1,this.videoDevices=[],this.selectedCameraId="",this.photoPreviewUrl=null,this.isCameraActive=!1,this.showCameraDropdown=!1,this.uploadedDocs=[],this.docTypeControls=[],this.capturing=!1,this.countryList=[],this.stateList=[],this.districtList=[],this.masterData={},Yt({"create-outline":k3,"trash-outline":y3})}nextStep(){this.currentStep<4&&this.currentStep++,this.currentStep===3&&this.openCamera()}prevStep(){this.currentStep>1&&this.currentStep--,this.currentStep===3?this.openCamera():(this.stopCamera(),this.isCameraActive=!1,this.showCameraDropdown=!1)}onDragOver(t){t.preventDefault()}onFileDrop(t){t.preventDefault(),t.dataTransfer?.files&&this.addFiles(t.dataTransfer.files)}onFileSelected(t){let i=t.target;i.files&&this.addFiles(i.files)}addFiles(t){Array.from(t).forEach(i=>{let a=new FileReader;a.onload=()=>{let s=new Date,c=this.uploadedDocs.length,l=new ge("");this.docTypeControls.push(l),this.uploadedDocs.push({preview:a.result,type:"",draftType:"",date:s,time:s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"}),isEditing:!0}),console.log("Document added in edit mode:",this.uploadedDocs[c])},a.readAsDataURL(i)})}getDocTypeControl(t){for(;this.docTypeControls.length<=t;)this.docTypeControls.push(new ge(""));return this.docTypeControls[t]}editDoc(t){let i=this.uploadedDocs[t];i.isEditing=!0,this.getDocTypeControl(t).setValue(i.type),console.log("Editing document at index:",t)}deleteDoc(t){this.uploadedDocs.splice(t,1),t<this.docTypeControls.length&&this.docTypeControls.splice(t,1),t<this.documents.length&&this.documents.removeAt(t),console.log("Document deleted at index:",t)}cancelEdit(t){let i=this.uploadedDocs[t];i.isEditing=!1,this.getDocTypeControl(t).setValue(i.type),console.log("Edit cancelled for document at index:",t)}saveEdit(t){let i=this.uploadedDocs[t],a=this.getDocTypeControl(t).value;if(a&&a.trim()!==""){i.type=a,i.isEditing=!1,console.log("Document type saved:",i.type,"at index:",t);let s=this.documents.controls.findIndex(c=>c.value.fileName===`Document-${t}-${i.date.getTime()}.png`);s===-1?this.documents.push(this.fb.group({fileName:`Document-${t}-${i.date.getTime()}.png`,fileType:"image/png",data:i.preview,type:i.type})):this.documents.at(s).patchValue({type:i.type})}else console.warn("Please select a document type before saving")}startCapture(){return Q(this,null,function*(){console.log("Start capture clicked");try{let t=yield navigator.mediaDevices.getUserMedia({video:!0});console.log("Camera stream started for document capture"),t.getTracks().forEach(i=>i.stop())}catch(t){console.error("Error starting camera for document capture:",t)}})}openCamera(){return Q(this,null,function*(){this.isCameraActive=!0,this.showCameraDropdown=!1,yield this.startCamera(this.selectedCameraId)})}retakeImage(){this.photoPreviewUrl=null,this.isCameraActive=!0,this.showCameraDropdown=!1,this.startCamera(this.selectedCameraId)}toggleCameraDropdown(){this.showCameraDropdown=!this.showCameraDropdown}selectCamera(t){this.selectedCameraId=t,this.showCameraDropdown=!1,this.startCamera(t)}ngOnInit(){return Q(this,null,function*(){if(this.patientForm=this.fb.group({_id:[""],_rev:[null],domainwisepid:[0],patientid:[0],first_name:[""],last_name:[""],date_of_birth:[""],age:[""],ageYears:[""],gender:[""],maritalstatus:[""],height:[""],weight:[""],mobile:[""],email:[""],head_of_household_fname:[""],head_of_household_lname:[""],country:[""],state:[""],district:[""],block:[""],village:[""],address:[""],projid:[""],head_of_household_mobile:[""],isAbhaPatient:[!1],profile:this.fb.group({patientid:[0],imagepath:[""],S3URL:[""]}),pastrecord:[null],createdat:[""],createdby:[""],domain:[0],uid:[""],prefix:[null],EhealthId:[""],MRN:[""],password:[""],consentformcheckstatus:[0],fingerPrintTemplate:[""],health_number:[""],health_address:[""],unique_id:[null],nationalId:[null],ethnicity:[null],subscriptionDetails:this.fb.group({subscribedId:[0],familycardid:[null],freeSubcriptionAllocated:[0],completedFreeSubcrition:[0],remainingSubcription:[0],isActive:[null],subcriptionName:[null],subscriptionPlanActivatedOn:[null],subscriptionExpiredOn:[null],isExpaired:[0]}),localId:[""],patient_status:[null],patient_title:[null],postCode:[null],centerName:[null],status:[null],isSync:[!1],documents:this.fb.array([]),type:["patient"]}),yield this.loadPatients(),yield this.loadMasterData(),navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices)try{let t=yield navigator.mediaDevices.enumerateDevices();this.videoDevices=t.filter(i=>i.kind==="videoinput"),this.videoDevices.length>0&&(this.selectedCameraId=this.videoDevices[0].deviceId)}catch(t){console.error("Error enumerating devices:",t)}else console.warn("Media Devices API not supported.")})}loadMasterData(){return Q(this,null,function*(){try{this.objPouchdbService.getMasterData().subscribe({next:t=>{console.log(" Master Data already exists in PouchDB"),this.processMasterData(t)},error:()=>{console.log("\u26A0 Master Data not found \u2192 Loading from assets..."),this.http.get("assets/RemediNovaAPI.json").subscribe(t=>{this.objPouchdbService.addOrUpdateMasterData(t).subscribe(()=>{console.log(" Master Data saved in PouchDB"),this.processMasterData(t)})})}})}catch(t){console.error(" Error loading master data:",t)}})}processMasterData(t){try{console.log(" Processing master data:",t),t.data&&Array.isArray(t.data)?(console.log(" Found data array with",t.data.length,"items"),t.data.forEach((i,a)=>{i.tblcountry&&(this.masterData.tblcountry=i.tblcountry,console.log(" Found tblcountry at index",a,"with",i.tblcountry.length,"countries")),i.tblstate&&(this.masterData.tblstate=i.tblstate,console.log(" Found tblstate at index",a,"with",i.tblstate.length,"states")),i.tbldistrict&&(this.masterData.tbldistrict=i.tbldistrict,console.log(" Found tbldistrict at index",a,"with",i.tbldistrict.length,"districts"))})):(console.log(" Using direct data structure"),this.masterData=t),this.masterData.tblcountry?(this.countryList=this.masterData.tblcountry.filter(i=>i.IsDeleted==="0"),console.log(" Countries loaded:",this.countryList.length),console.log(" Sample countries:",this.countryList.slice(0,3)),this.countryList.length>0&&console.log(" SUCCESS: Countries dropdown should now work!")):(console.log(" No tblcountry found in master data"),console.log(" Available keys in masterData:",Object.keys(this.masterData))),console.log(" Master Data processed successfully"),console.log(" Master data structure:",Object.keys(this.masterData))}catch(i){console.error(" Error processing master data:",i)}}loadPatients(){return Q(this,null,function*(){try{this.objPouchdbService.getRecordsByType("patient").subscribe({next:t=>this.submittedPatients=t,error:t=>console.error("Failed to load patients:",t)})}catch(t){console.error("Failed to load patients:",t)}})}get documents(){return this.patientForm.get("documents")}toggleSection(t){this.activeSection=this.activeSection===t?"":t}onCountryChange(t){let i=t.target.value;this.patientForm.patchValue({state:"",district:""}),this.masterData.tblstate?(this.stateList=this.masterData.tblstate.filter(a=>a.CountryId===i&&a.IsDeleted==="0"),console.log(" States loaded for country:",i,this.stateList.length)):(this.stateList=[],console.log(" No states data available")),this.districtList=[]}onStateChange(t){let i=t.target.value;this.patientForm.patchValue({district:""}),this.masterData.tbldistrict?(this.districtList=this.masterData.tbldistrict.filter(a=>a.StateId===i&&a.IsDeleted==="0").map(a=>a.District),console.log(" Districts loaded for state:",i,this.districtList.length)):(this.districtList=[],console.log(" No districts data available"))}startCamera(t){return Q(this,null,function*(){if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia){alert("Camera API not supported");return}try{let i=yield navigator.mediaDevices.getUserMedia({video:t?{deviceId:{exact:t}}:!0});this.mediaStream=i,this.video.nativeElement.srcObject=i,yield this.video.nativeElement.play()}catch(i){console.error("Error starting camera:",i)}})}switchCamera(t){this.selectedCameraId=t.target.value,this.selectedCameraId&&this.startCamera(this.selectedCameraId)}captureImage(){let t=this.video.nativeElement,i=this.canvas.nativeElement;i.width=t.videoWidth,i.height=t.videoHeight;let a=i.getContext("2d");if(!a){console.error("Failed to get canvas context.");return}a.drawImage(t,0,0,i.width,i.height);let s=i.toDataURL("image/png"),l=`patientdata/images/profilepic/${Date.now()}_pat_${this.patientForm.get("patientid")?.value||"new"}`;this.patientForm.get("profile")?.patchValue({imagepath:l,S3URL:"",patientid:this.patientForm.get("patientid")?.value||0}),this.photoPreviewUrl=s,this.stopCamera(),this.isCameraActive=!1,this.showCameraDropdown=!1,this.video&&this.video.nativeElement&&(this.video.nativeElement.srcObject=null),console.log("Patient image captured and stored in profile structure for backend compatibility"),console.log("Profile data:",this.patientForm.get("profile")?.value)}stopCamera(){this.mediaStream&&(this.mediaStream.getTracks().forEach(t=>t.stop()),this.mediaStream=null),this.isCameraActive=!1,this.showCameraDropdown=!1}removeDocument(t){this.documents.removeAt(t)}preparePatientDataForBackend(t){let i=$1({},t);return delete i.patientImage,delete i.imageType,delete i._doc_id_rev,delete i.isSync,delete i.type,i.profile&&(i.profile={id:i.profile.id||0,patientid:i.profile.patientid||i.patientid||0,imagepath:i.profile.imagepath||"",S3URL:i.profile.S3URL||""}),i.documents&&Array.isArray(i.documents)&&(i.documents=i.documents.map(a=>({id:a.id||0,patientid:a.patientid||i.patientid||0,imagepath:a.imagepath||"",S3URL:a.S3URL||"",fileName:a.fileName||"",fileType:a.fileType||"",type:a.type||""}))),i}syncPatientWithBackend(t){return Q(this,null,function*(){try{let i=this.preparePatientDataForBackend(t);console.log("\u{1F680} Patient data prepared for backend API:",i),console.log("\u2705 Profile structure:",i.profile),console.log("\u2705 Documents structure:",i.documents)}catch(i){throw console.error("\u274C Failed to sync patient with backend:",i),i}})}savePatients(){return Q(this,null,function*(){try{let t=this.patientForm.value,i=$1({},t);if(delete i.patientImage,delete i.imageType,delete i._doc_id_rev,i.type="patient",this.documents.value&&this.documents.value.length>0&&(i.documents=this.documents.value.map(a=>({fileName:a.fileName,fileType:a.fileType,data:a.data,type:a.type,imagepath:a.imagepath||"",S3URL:a.S3URL||""}))),console.log("Patient data prepared for backend:",{profile:i.profile,documents:i.documents,hasPatientImage:!!i.profile?.imagepath}),this.editIndex===null)i._rev||delete i._rev,(!i._id||i._id.trim()==="")&&delete i._id,yield Q0(this.objPouchdbService.addRecord(i)),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient saved successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present();else{if(!i._id)throw new Error("Invalid _id for update");let a=yield Q0(this.objPouchdbService.getRecordById(i._id));i._rev=a._rev,yield Q0(this.objPouchdbService.updateRecord(i)),this.editIndex=null,yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient edited successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}this.patientForm.reset(),this.photoPreviewUrl=null}catch(t){console.error("Failed to save patient:",t),yield(yield this.toastController.create({message:"Failed to save patient.",duration:3e3,color:"danger"})).present()}})}editPatients(t,i){this.editIndex=i,this.patientForm.patchValue(p0($1({},t),{_id:t._id||"",_rev:t._rev||""})),this.photoPreviewUrl=t.profile?.S3URL||t.profile?.imagepath||null}delete(t){return Q(this,null,function*(){try{yield Q0(this.objPouchdbService.deleteRecord(t)),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient deleted successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}catch(i){console.error("Failed to delete patient:",i),yield(yield this.toastController.create({message:"Failed to delete patient.",duration:3e3,color:"danger"})).present()}})}};o.\u0275fac=function(i){return new(i||o)(m(Lo),m(Gn),m(f3),m(Sn))},o.\u0275cmp=T({type:o,selectors:[["app-patient-entry"]],viewQuery:function(i,a){if(i&1&&(z0(qc,5),z0(Nc,5),z0($c,5)),i&2){let s;o0(s=i0())&&(a.video=s.first),o0(s=i0())&&(a.canvas=s.first),o0(s=i0())&&(a.uploadVideo=s.first)}},decls:41,vars:23,consts:[["canvas",""],["video",""],["fileInput",""],[3,"ngSubmit","formGroup"],[1,"popup-overlay"],[1,"container"],[1,"form-wrapper"],[1,"header-container"],[1,"title"],["src","assets/icon/cross.png","alt","",1,"close-btn"],[1,"progress-steps"],[1,"step"],[1,"dot"],[1,"label"],[1,"step-title"],[1,"step-status"],["class","form-section",4,"ngIf"],[1,"form-section"],[1,"form-row"],[1,"form-group"],["type","text","formControlName","first_name","placeholder","Enter Patient\u2019s First Name *","required",""],["type","text","formControlName","last_name","placeholder","Enter Patient\u2019s Last Name *","required",""],["type","date","formControlName","date_of_birth","required",""],["formControlName","gender"],["disabled","","disabled",""],["value","Male"],["value","Female"],["value","Other"],["type","text","formControlName","mobile","placeholder","Enter Contact Number *","required",""],["type","email","formControlName","email","placeholder","Enter Email ID"],[1,"form-group","full-width"],["formControlName","address","placeholder","Enter Patient\u2019s Address Here"],[1,"form-actions"],["type","button",1,"btn-next",3,"click"],["src","assets/icon/next.png"],["formControlName","maritalstatus"],["value","","disabled","","selected",""],["value","Single"],["value","Married"],["type","number","formControlName","height","placeholder","Enter Patient\u2019s Height (Cms) *"],["type","number","formControlName","weight","placeholder","Enter Patient\u2019s Weight (Kgs) *"],["formControlName","country",3,"change"],[3,"value",4,"ngFor","ngForOf"],["formControlName","state",3,"change"],["formControlName","district"],["value","","disabled",""],["type","text","formControlName","village","placeholder","Enter City *"],["type","text","formControlName","block","placeholder","Enter Taluk *"],["type","text","formControlName","uid","placeholder","Enter UID *"],["type","button",1,"btn-back",3,"click"],["src","assets/icon/left-arraw.png"],[3,"value"],[1,"image-container"],[1,"img-2"],[1,"image-box"],["autoplay","","muted","","playsinline","",4,"ngIf"],["class","captured-image",3,"src",4,"ngIf"],["class","open-camera-container",4,"ngIf"],["hidden",""],[1,"actions"],["type","button","class","btn-retake",3,"click",4,"ngIf"],["class","capture-controls",4,"ngIf"],["type","button","class","btn-save",3,"click",4,"ngIf"],["autoplay","","muted","","playsinline",""],[1,"captured-image",3,"src"],[1,"open-camera-container"],["type","button",1,"btn-open-camera",3,"click"],["src","assets/icon/camera.png"],["type","button",1,"btn-retake",3,"click"],[1,"capture-controls"],[1,"btn-camera",3,"click"],[1,"btn-dropdown",3,"click"],["src","assets/icon/down-arraw.png"],["class","camera-dropdown",4,"ngIf"],[1,"camera-dropdown"],[1,"dropdown-header"],["class","dropdown-item",3,"selected","click",4,"ngFor","ngForOf"],[1,"dropdown-item",3,"click"],["class","check-icon",4,"ngIf"],[1,"check-icon"],["type","button",1,"btn-save",3,"click"],["class","upload-docs-wrapper",4,"ngIf"],["class","upload-box-full",3,"drop","dragover",4,"ngIf"],["type","submit",1,"btn-register"],[1,"upload-docs-wrapper"],[1,"uploaded-docs"],["class","uploaded-item",3,"editing-mode","view-mode",4,"ngFor","ngForOf"],[1,"upload-box",3,"drop","dragover"],[1,"upload-icon"],[1,"upload-text"],[1,"upload-subtext"],[1,"choose-file",3,"click"],[1,"capture-image",3,"click"],["type","file","hidden","","multiple","",3,"change"],[1,"uploaded-item"],[1,"doc-preview",3,"src"],["class","doc-info-edit",4,"ngIf"],["class","doc-info-view",4,"ngIf"],[1,"doc-actions"],[4,"ngIf"],[1,"doc-info-edit"],[1,"doc-type-label"],[1,"doc-type-select",3,"formControl"],["value","Aadhaar"],["value","PAN"],["value","Passport"],["value","Driving License"],["value","Voter ID"],[1,"doc-info-view"],[1,"doc-details-flex"],[1,"doc-name"],[1,"doc-meta"],["type","button",1,"icon-btn","cancel-btn",3,"click"],["src","assets/icon/cross.png","alt","Cancel"],["type","button",1,"icon-btn","save-btn",3,"click"],["src","assets/icon/right.png","alt","Save"],["type","button",1,"icon-btn","edit-btn",3,"click"],["src","assets/icon/edit.png","alt","Edit"],["type","button",1,"icon-btn","delete-btn",3,"click"],["src","assets/icon/delete.png","alt","Delete"],[1,"upload-box-full",3,"drop","dragover"],["src","assets/icon/upload-img.png"],[2,"display","flex","justify-content","center"]],template:function(i,a){i&1&&(D(0,"form",3),f1("ngSubmit",function(){return a.savePatients()}),D(1,"div",4)(2,"div",5)(3,"div",6)(4,"div",7)(5,"h2",8),X(6,"Register New Patient"),j(),w1(7,"img",9),j(),D(8,"div",10)(9,"div",11),w1(10,"span",12),D(11,"div",13)(12,"p",14),X(13,"Basic Information"),j(),D(14,"p",15),X(15),j()()(),D(16,"div",11),w1(17,"span",12),D(18,"div",13)(19,"p",14),X(20,"Additional Information"),j(),D(21,"p",15),X(22),j()()(),D(23,"div",11),w1(24,"span",12),D(25,"div",13)(26,"p",14),X(27,"Patient\u2019s Image"),j(),D(28,"p",15),X(29),j()()(),D(30,"div",11),w1(31,"span",12),D(32,"div",13)(33,"p",14),X(34,"Upload Documents"),j(),D(35,"p",15),X(36),j()()()(),D1(37,Uc,47,0,"div",16)(38,Zc,64,3,"div",16)(39,al,22,8,"div",16)(40,hl,11,2,"div",16),j()()()()),i&2&&(u1("formGroup",a.patientForm),r1(9),w0("active",a.currentStep===1)("completed",a.currentStep>1),r1(6),U1(a.currentStep===1?"In Progress":a.currentStep>1?"Completed":"Pending"),r1(),w0("active",a.currentStep===2)("completed",a.currentStep>2),r1(6),U1(a.currentStep===2?"In Progress":a.currentStep>2?"Completed":"Pending"),r1(),w0("active",a.currentStep===3)("completed",a.currentStep>3),r1(6),U1(a.currentStep===3?"In Progress":a.currentStep>3?"Completed":"Pending"),r1(),w0("active",a.currentStep===4),r1(6),U1(a.currentStep===4?"In Progress":"Pending"),r1(),u1("ngIf",a.currentStep===1),r1(),u1("ngIf",a.currentStep===2),r1(),u1("ngIf",a.currentStep===3),r1(),u1("ngIf",a.currentStep===4))},dependencies:[Wn,Hn,Fn,qn,jn,Dn,Rn,Vn,On,Un,En,Pn,Tn,Kn,x3,k2,bn,H0,An,Bn],styles:['@charset "UTF-8";@font-face{font-family:DM Sans;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu6-K6h9Q.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:DM Sans;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K4.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:DM Sans;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu6-K6h9Q.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:DM Sans;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K4.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:DM Sans;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu6-K6h9Q.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:DM Sans;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K4.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}.popup-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#0006;display:flex;justify-content:center;align-items:center;z-index:1000}.container[_ngcontent-%COMP%]{width:94%;height:90vh;background:#fff;border-radius:8px;box-shadow:0 2px 6px #00000014;display:flex;flex-direction:column}.header-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:16px;border-bottom:1px solid #E5E7EB;background:#fff;position:sticky;top:0;z-index:100}.title[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:500;font-size:20px;line-height:130%;letter-spacing:0;vertical-align:middle;color:#111827;margin:0}.close-btn[_ngcontent-%COMP%]{height:15px;width:15px}.form-wrapper[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;height:100%;padding:32px}.progress-steps[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:flex-start;padding:24px 32px 32px;border-bottom:1px solid #E5E7EB;background:#fff;position:sticky;top:0;z-index:100;margin-top:16px;min-height:80px;width:61%}.step[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:10px;opacity:.5;position:relative;z-index:2;flex:1;min-width:0;margin-right:20px}.step[_ngcontent-%COMP%]:not(:last-child):after{content:"";position:absolute;top:7px;left:calc(50% + 7px);width:calc(100% - 14px);height:2px;background:#e5e7eb;z-index:1}.step.active[_ngcontent-%COMP%]{opacity:1}.step.active[_ngcontent-%COMP%]:not(:last-child):after{background:#f59e0b}.step.completed[_ngcontent-%COMP%]{opacity:1}.step.completed[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{background:#10b981;display:flex;align-items:center;justify-content:center}.step.completed[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]:after{content:"\\2713";color:#fff;font-size:9px;font-weight:700;line-height:1;position:absolute;top:50%;left:50%;transform:translate(-50%,-50%)}.step.completed[_ngcontent-%COMP%]:not(:last-child):after{background:#10b981}.step.active[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{background:#f59e0b}.step[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{width:14px;height:14px;border-radius:50%;background:#e5e7eb;position:relative;z-index:3;display:flex;align-items:center;justify-content:center;margin-right:25px}.label[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:4px}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;color:#111827;text-align:center;margin:0;white-space:nowrap}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:400;font-size:11px;line-height:140%;letter-spacing:.2px;color:#6b7280;text-align:center;margin:0;white-space:nowrap}.step.completed[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{color:#10b981}.step.active[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{color:#f59e0b}input[type=date][_ngcontent-%COMP%]{font-size:14px;color:#374151}input[_ngcontent-%COMP%]::placeholder{font-size:14px;color:#374151}select[_ngcontent-%COMP%]   option[disabled][_ngcontent-%COMP%]{color:#374151;font-size:14px}select[_ngcontent-%COMP%]{font-size:14px;color:#374151}.form-section[_ngcontent-%COMP%]{margin-top:24px;overflow-y:auto;flex:1;-ms-overflow-style:none;scrollbar-width:none}.form-section[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:400;font-size:16px;line-height:150%;letter-spacing:0;vertical-align:middle;color:#4a4a48;padding:8px 16px}.form-row[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px}.form-group[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:6px;min-width:200px}.form-group.full-width[_ngcontent-%COMP%]{flex:1 1 100%}label[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:500;font-size:13px;line-height:140%;letter-spacing:.2px;color:#374151;margin-top:29px;margin-bottom:11px;margin-left:6px}input[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{width:100%;height:48px;border-radius:8px;gap:10px;opacity:1;padding:12px 16px;border-width:1px;border-style:solid;border-color:#d1d5db}textarea[_ngcontent-%COMP%]{width:100%;min-width:320px;height:96px;min-height:96px;max-height:160px;border-radius:8px;gap:10px;opacity:1;padding:12px 16px;border-width:1px;border-style:solid;border-color:#d1d5db}input[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus{border-color:#999}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:33px;border-top:1px solid #E5E7EB;padding:20px;gap:16px}.btn-back[_ngcontent-%COMP%]{background:none;border:none;color:#007aff;cursor:pointer;min-width:80px;height:48px;display:flex;align-items:center;justify-content:center;gap:8px;font-family:DM Sans,system-ui,-apple-system,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;text-align:center;padding:12px 16px;border-radius:8px;transition:all .3s ease}.btn-back[_ngcontent-%COMP%]:hover{background:#f0f8ff}.btn-back[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:14px;height:14px}.btn-next[_ngcontent-%COMP%]{min-width:120px;height:48px;border-radius:8px;display:inline-flex;align-items:center;justify-content:center;gap:8px;opacity:1;padding:12px 24px;border:1px solid #007AFF;background-color:transparent;color:#007aff;box-sizing:border-box;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;cursor:pointer;transition:all .3s ease}.btn-register[_ngcontent-%COMP%]{background:#007aff;color:#fff;padding:12px 24px;font-size:14px;border:none;border-radius:8px;cursor:pointer;min-width:120px;height:48px;font-family:DM Sans,sans-serif;font-weight:600;transition:all .3s ease}.btn-register[_ngcontent-%COMP%]:hover{background:#0056cc}.image-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px;margin-top:20px}.img-2[_ngcontent-%COMP%]{width:402.67px;height:366px;display:flex;flex-direction:column;gap:16px;opacity:1}.image-box[_ngcontent-%COMP%]{width:402.67px;height:302px;border-radius:8px;opacity:1;background-color:#d1d5db;display:flex;align-items:center;justify-content:center;position:relative;overflow:hidden}.image-box[_ngcontent-%COMP%]   video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:8px}.image-box[_ngcontent-%COMP%]   .captured-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:8px}.open-camera-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.btn-open-camera[_ngcontent-%COMP%]{width:160px;height:48px;border-radius:8px;border:1px solid #007AFF;background:transparent;color:#007aff;display:flex;align-items:center;justify-content:center;gap:8px;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;cursor:pointer;transition:all .3s ease}.btn-open-camera[_ngcontent-%COMP%]:hover{background:#007aff;color:#fff}.btn-open-camera[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:20px;height:20px}.actions[_ngcontent-%COMP%]{display:flex;gap:20px;align-items:center;justify-content:space-between}.btn-retake[_ngcontent-%COMP%]{width:140px;min-width:140px;height:48px;min-height:48px;border-radius:8px;display:inline-flex;align-items:center;justify-content:center;gap:8px;padding:12px 24px;box-sizing:border-box;border:1px solid #007AFF;opacity:1;background:transparent;color:#007aff;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;cursor:pointer;transition:all .3s ease}.btn-retake[_ngcontent-%COMP%]:hover{background:#007aff;color:#fff}.btn-save[_ngcontent-%COMP%]{width:120px;min-width:120px;height:48px;min-height:48px;border-radius:8px;display:inline-flex;align-items:center;justify-content:center;gap:8px;padding:12px 24px;box-sizing:border-box;border:1px solid #007AFF;opacity:1;background:#007aff;color:#fff;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;cursor:pointer;transition:all .3s ease}.btn-save[_ngcontent-%COMP%]:hover{background:#0056cc}.capture-controls[_ngcontent-%COMP%]{display:flex;gap:0;width:96px;height:48px;position:relative}.btn-camera[_ngcontent-%COMP%]{width:48px;height:48px;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;border-top-left-radius:8px;border-bottom-left-radius:8px;opacity:1;cursor:pointer;background-color:#007aff;border:1px solid #007AFF;transition:all .3s ease}.btn-camera[_ngcontent-%COMP%]:hover{background-color:#0056cc}.btn-dropdown[_ngcontent-%COMP%]{width:48px;height:48px;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;border-top-right-radius:8px;border-bottom-right-radius:8px;border:1px solid #007AFF;opacity:1;background:transparent;cursor:pointer;transition:all .3s ease}.btn-dropdown[_ngcontent-%COMP%]:hover{background:#f0f8ff}.btn-camera[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .btn-dropdown[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:24px;max-height:24px}.camera-dropdown[_ngcontent-%COMP%]{position:absolute;top:52px;right:0;width:250px;background:#fff;border:1px solid #E5E7EB;border-radius:8px;box-shadow:0 4px 12px #00000026;z-index:1000;overflow:hidden}.dropdown-header[_ngcontent-%COMP%]{padding:12px 16px;background:#f9fafb;border-bottom:1px solid #E5E7EB;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;color:#374151}.dropdown-item[_ngcontent-%COMP%]{padding:12px 16px;display:flex;align-items:center;justify-content:space-between;cursor:pointer;font-family:DM Sans,sans-serif;font-size:14px;color:#374151;transition:background .2s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background:#f3f4f6}.dropdown-item.selected[_ngcontent-%COMP%]{background:#ebf8ff;color:#007aff}.check-icon[_ngcontent-%COMP%]{color:#007aff;font-weight:700;font-size:16px}.upload-box[_ngcontent-%COMP%], .upload-box-full[_ngcontent-%COMP%]{display:flex;border:1px dashed #c7c7c7;border-radius:6px;padding:40px;text-align:center;cursor:pointer;background:#fff;transition:background .3s ease;margin-top:20px;flex-direction:column;align-items:center}.upload-box[_ngcontent-%COMP%]:hover, .upload-box-full[_ngcontent-%COMP%]:hover{background:#f9f9f9}.upload-icon[_ngcontent-%COMP%]{font-size:40px;color:#1976d2}.upload-text[_ngcontent-%COMP%]{font-family:DM Sans,system-ui,-apple-system,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;text-align:center;vertical-align:middle;color:#374151}.upload-subtext[_ngcontent-%COMP%]{margin-top:4px;font-size:14px;color:#555;width:392px;height:21px;display:flex;gap:8px;align-items:center;opacity:1;transform:rotate(0)}.doc-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;margin-left:16px}.choose-file[_ngcontent-%COMP%], .capture-image[_ngcontent-%COMP%]{color:#1976d2;cursor:pointer;text-decoration:underline}.uploaded-docs[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:12px}.uploaded-item[_ngcontent-%COMP%]{display:flex;align-items:center;background:#f8f9fb;border-radius:6px;padding:10px 16px;gap:12px}.doc-preview[_ngcontent-%COMP%]{width:40px;height:40px;object-fit:cover;border-radius:4px}.doc-info[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:4px}.flex-info[_ngcontent-%COMP%]{flex-direction:row;gap:24px;align-items:center}.doc-name[_ngcontent-%COMP%]{font-weight:600}.doc-meta[_ngcontent-%COMP%]{font-size:13px;color:#666}.doc-type[_ngcontent-%COMP%]{padding:6px;font-size:14px;border-radius:4px;border:1px solid #ccc}.icon-btn[_ngcontent-%COMP%]{background:none;border:none;cursor:pointer;padding:4px}.upload-docs-wrapper[_ngcontent-%COMP%]{display:flex;gap:24px;margin-top:20px;height:366px;background:transparent}.uploaded-docs[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:12px}.uploaded-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;background:#f8f9fb;border-radius:6px;padding:8px 12px}.doc-preview[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:4px;object-fit:cover}.doc-type[_ngcontent-%COMP%]{flex:1;padding:6px;font-size:14px;border:1px solid #ccc;border-radius:4px}.doc-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;position:relative;bottom:34px}.icon-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:16px;cursor:pointer}.upload-box[_ngcontent-%COMP%]{flex:1}@media (max-width: 768px){.upload-docs-wrapper[_ngcontent-%COMP%]{flex-direction:column}.upload-box[_ngcontent-%COMP%]{width:100%}}.uploaded-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;background:#f9fafb;border-radius:8px;padding:16px;margin-bottom:12px;width:545px;transition:all .3s ease;border:1px solid #E5E7EB}.uploaded-item.editing-mode[_ngcontent-%COMP%]{height:122px;background:#f9fafb}.uploaded-item.view-mode[_ngcontent-%COMP%]{height:64px;background:#f9fafb;justify-content:space-between}.doc-preview[_ngcontent-%COMP%]{width:48px;height:48px;border-radius:8px;object-fit:cover;flex-shrink:0}.doc-info-edit[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:8px}.doc-type-label[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-size:14px;font-weight:500;color:#374151;margin:0}.doc-type-select[_ngcontent-%COMP%]{width:100%;height:48px;padding:12px 16px;border:1px solid #D1D5DB;border-radius:8px;font-family:DM Sans,sans-serif;font-size:14px;background:#fff;color:#374151;outline:none;transition:border-color .2s ease}.doc-type-select[_ngcontent-%COMP%]:focus{border-color:#3b82f6;box-shadow:0 0 0 3px #3b82f61a}.doc-info-view[_ngcontent-%COMP%]{flex:1;display:flex;align-items:center}.doc-details-flex[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px}.doc-name[_ngcontent-%COMP%], .doc-meta[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:500;font-style:normal;font-size:16px;line-height:1.5;letter-spacing:0;vertical-align:middle;color:#374151}.doc-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;flex-shrink:0;top:1px}.icon-btn[_ngcontent-%COMP%]{background:none;border:none;padding:8px;cursor:pointer;border-radius:6px;transition:background-color .2s ease;display:flex;align-items:center;justify-content:center}.icon-btn[_ngcontent-%COMP%]:hover{background-color:#f3f4f6}.icon-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:18.74px;height:18.74px}.save-btn[_ngcontent-%COMP%]:hover{background-color:#dcfce7}.cancel-btn[_ngcontent-%COMP%]:hover{background-color:#fee2e2}.edit-btn[_ngcontent-%COMP%]:hover{background-color:#dbeafe}.delete-btn[_ngcontent-%COMP%]:hover{background-color:#fee2e2}.camera-box[_ngcontent-%COMP%]{margin-top:20px;text-align:center}.preview-box[_ngcontent-%COMP%]{width:300px;height:200px;background:#d5d8de;margin:0 auto 12px;border-radius:6px}.capture-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:12px}.btn-outline[_ngcontent-%COMP%]{background:none;border:1px solid #ccc;border-radius:4px;padding:8px 16px;cursor:pointer}@media (max-width: 1024px){.form-row[_ngcontent-%COMP%]{flex-direction:column}}@media (max-width: 768px){.progress-steps[_ngcontent-%COMP%]{flex-direction:row;gap:8px;padding:12px 16px 24px;justify-content:space-between}.step[_ngcontent-%COMP%]{flex:1;min-width:0;max-width:120px}.step[_ngcontent-%COMP%]:not(:last-child):after{width:calc(100% - 20px);left:60%;right:-40%}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-size:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.form-actions[_ngcontent-%COMP%]{flex-direction:row;justify-content:space-between;align-items:center;gap:16px;padding:16px 20px}.btn-back[_ngcontent-%COMP%], .btn-next[_ngcontent-%COMP%], .btn-register[_ngcontent-%COMP%]{flex:1;max-width:150px;min-width:100px}}@media (max-width: 480px){.container[_ngcontent-%COMP%]{width:100%;height:100vh;border-radius:0}.header-container[_ngcontent-%COMP%]{padding:12px 16px}.title[_ngcontent-%COMP%]{font-size:18px}.progress-steps[_ngcontent-%COMP%]{padding:8px 12px 16px;gap:4px}.step[_ngcontent-%COMP%]{flex:1;min-width:0}.step[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{width:12px;height:12px}.step[_ngcontent-%COMP%]:not(:last-child):after{height:2px;top:5px;width:calc(100% - 10px);left:55%;right:-45%}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:10px;line-height:1.2}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-size:8px;line-height:1.2}.form-section[_ngcontent-%COMP%]{padding:0 12px 12px}.form-actions[_ngcontent-%COMP%]{padding:12px 16px;gap:12px}.btn-back[_ngcontent-%COMP%]{min-width:70px;padding:10px 12px;font-size:13px}.btn-next[_ngcontent-%COMP%], .btn-register[_ngcontent-%COMP%]{min-width:90px;padding:10px 16px;font-size:13px}.image-box[_ngcontent-%COMP%]{width:90%;height:200px}.img-2[_ngcontent-%COMP%]{width:100%;height:auto}.preview-box[_ngcontent-%COMP%]{width:90%;height:160px}.actions[_ngcontent-%COMP%]{flex-direction:column;gap:12px;align-items:stretch}.btn-retake[_ngcontent-%COMP%], .btn-save[_ngcontent-%COMP%], .btn-open-camera[_ngcontent-%COMP%]{width:100%}.capture-controls[_ngcontent-%COMP%]{width:100%;justify-content:center}.camera-dropdown[_ngcontent-%COMP%]{width:100%;right:auto;left:0}}@media (max-width: 768px) and (min-width: 481px){.container[_ngcontent-%COMP%]{width:100%;height:100vh;border-radius:0}.header-container[_ngcontent-%COMP%]{padding:16px 20px}.progress-steps[_ngcontent-%COMP%]{padding:12px 20px 20px;gap:12px}.step[_ngcontent-%COMP%]:not(:last-child):after{width:calc(100% - 30px);left:65%;right:-35%}.form-actions[_ngcontent-%COMP%]{padding:16px 20px}.image-container[_ngcontent-%COMP%]{margin-top:10px}.img-2[_ngcontent-%COMP%]{width:100%;max-width:350px}.image-box[_ngcontent-%COMP%]{width:100%;height:250px}.btn-open-camera[_ngcontent-%COMP%]{width:180px}.actions[_ngcontent-%COMP%]{justify-content:center;gap:16px}.camera-dropdown[_ngcontent-%COMP%]{width:280px}}@media (max-width: 360px){.progress-steps[_ngcontent-%COMP%]{padding:6px 8px 12px;gap:2px}.step[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{width:10px;height:10px}.step[_ngcontent-%COMP%]:not(:last-child):after{height:1px;top:4px}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:9px}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-size:7px}.form-actions[_ngcontent-%COMP%]{padding:10px 12px}.btn-back[_ngcontent-%COMP%]{min-width:60px;font-size:12px}.btn-next[_ngcontent-%COMP%], .btn-register[_ngcontent-%COMP%]{min-width:80px;font-size:12px}}']});let e=o;return e})();export{Ci as a,x3 as b,gd as c};
