import{b as ue}from"./chunk-JIT2VFLB.js";import{e as ve,f as ge,g as he}from"./chunk-PK2JKDOR.js";import{g as o3,r as i3}from"./chunk-EHNA26RN.js";import{$ as J,$a as Fn,A as fn,B as d0,C as R1,D as P1,E as q,F as xn,G as ae,H as f1,I as P,J as O,K as x1,L as Mn,M as kn,Ma as Y0,N as se,Na as Sn,Oa as le,Pa as An,Q as F1,Qa as Ln,R as M1,Ra as In,S as h1,Sa as jn,T as N,Ta as Vn,U as F,Ua as Hn,V as f2,Va as On,W as y0,Wa as Dn,X as n0,Xa as En,Y as o0,Ya as Pn,Z as re,Za as Tn,_ as z0,_a as Rn,a as hn,aa as v0,ab as qn,b as K,ba as C0,bb as Nn,c as Y1,ca as U,cb as $n,d as w2,da as yn,db as Un,e as Q0,ea as zn,eb as de,f as e0,fa as O0,fb as D0,g as un,ga as ce,h as t0,ha as x2,hb as Gn,i as ne,ib as Kn,j as X0,ja as Cn,jb as Wn,k as pn,ka as _n,kb as Zn,l as k0,la as bn,lb as Qn,mb as Xn,n as J0,na as Bn,nb as Jn,o as k1,ob as Yn,p as y1,pb as e3,q as l0,qb as t3,r as mn,rb as e2,s as m0,sb as n3,t as oe,u as R,ub as M2,v as ie,w as g1,x as m,y as wn,z as T}from"./chunk-CA3KZVYD.js";import{a as J1,b as p0,e as te,f as Z0,g as Q}from"./chunk-2R6CW7ES.js";var Xt=te((f3,x3)=>{"use strict";(function(e){if(typeof f3=="object")x3.exports=e();else if(typeof define=="function"&&define.amd)define(e);else{var o;try{o=window}catch{o=self}o.SparkMD5=e()}})(function(e){"use strict";var o=function(u,h){return u+h&4294967295},n=["0","1","2","3","4","5","6","7","8","9","a","b","c","d","e","f"];function t(u,h,r,v,w,f){return h=o(o(h,u),o(v,f)),o(h<<w|h>>>32-w,r)}function i(u,h){var r=u[0],v=u[1],w=u[2],f=u[3];r+=(v&w|~v&f)+h[0]-680876936|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[1]-389564586|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[2]+606105819|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[3]-1044525330|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[4]-176418897|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[5]+1200080426|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[6]-1473231341|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[7]-45705983|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[8]+1770035416|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[9]-1958414417|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[10]-42063|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[11]-1990404162|0,v=(v<<22|v>>>10)+w|0,r+=(v&w|~v&f)+h[12]+1804603682|0,r=(r<<7|r>>>25)+v|0,f+=(r&v|~r&w)+h[13]-40341101|0,f=(f<<12|f>>>20)+r|0,w+=(f&r|~f&v)+h[14]-1502002290|0,w=(w<<17|w>>>15)+f|0,v+=(w&f|~w&r)+h[15]+1236535329|0,v=(v<<22|v>>>10)+w|0,r+=(v&f|w&~f)+h[1]-165796510|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[6]-1069501632|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[11]+643717713|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[0]-373897302|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[5]-701558691|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[10]+38016083|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[15]-660478335|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[4]-405537848|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[9]+568446438|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[14]-1019803690|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[3]-187363961|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[8]+1163531501|0,v=(v<<20|v>>>12)+w|0,r+=(v&f|w&~f)+h[13]-1444681467|0,r=(r<<5|r>>>27)+v|0,f+=(r&w|v&~w)+h[2]-51403784|0,f=(f<<9|f>>>23)+r|0,w+=(f&v|r&~v)+h[7]+1735328473|0,w=(w<<14|w>>>18)+f|0,v+=(w&r|f&~r)+h[12]-1926607734|0,v=(v<<20|v>>>12)+w|0,r+=(v^w^f)+h[5]-378558|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[8]-2022574463|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[11]+1839030562|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[14]-35309556|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[1]-1530992060|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[4]+1272893353|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[7]-155497632|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[10]-1094730640|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[13]+681279174|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[0]-358537222|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[3]-722521979|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[6]+76029189|0,v=(v<<23|v>>>9)+w|0,r+=(v^w^f)+h[9]-640364487|0,r=(r<<4|r>>>28)+v|0,f+=(r^v^w)+h[12]-421815835|0,f=(f<<11|f>>>21)+r|0,w+=(f^r^v)+h[15]+530742520|0,w=(w<<16|w>>>16)+f|0,v+=(w^f^r)+h[2]-995338651|0,v=(v<<23|v>>>9)+w|0,r+=(w^(v|~f))+h[0]-198630844|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[7]+1126891415|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[14]-1416354905|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[5]-57434055|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[12]+1700485571|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[3]-1894986606|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[10]-1051523|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[1]-2054922799|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[8]+1873313359|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[15]-30611744|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[6]-1560198380|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[13]+1309151649|0,v=(v<<21|v>>>11)+w|0,r+=(w^(v|~f))+h[4]-145523070|0,r=(r<<6|r>>>26)+v|0,f+=(v^(r|~w))+h[11]-1120210379|0,f=(f<<10|f>>>22)+r|0,w+=(r^(f|~v))+h[2]+718787259|0,w=(w<<15|w>>>17)+f|0,v+=(f^(w|~r))+h[9]-343485551|0,v=(v<<21|v>>>11)+w|0,u[0]=r+u[0]|0,u[1]=v+u[1]|0,u[2]=w+u[2]|0,u[3]=f+u[3]|0}function a(u){var h=[],r;for(r=0;r<64;r+=4)h[r>>2]=u.charCodeAt(r)+(u.charCodeAt(r+1)<<8)+(u.charCodeAt(r+2)<<16)+(u.charCodeAt(r+3)<<24);return h}function s(u){var h=[],r;for(r=0;r<64;r+=4)h[r>>2]=u[r]+(u[r+1]<<8)+(u[r+2]<<16)+(u[r+3]<<24);return h}function c(u){var h=u.length,r=[1732584193,-271733879,-1732584194,271733878],v,w,f,V,D,j;for(v=64;v<=h;v+=64)i(r,a(u.substring(v-64,v)));for(u=u.substring(v-64),w=u.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],v=0;v<w;v+=1)f[v>>2]|=u.charCodeAt(v)<<(v%4<<3);if(f[v>>2]|=128<<(v%4<<3),v>55)for(i(r,f),v=0;v<16;v+=1)f[v]=0;return V=h*8,V=V.toString(16).match(/(.*?)(.{0,8})$/),D=parseInt(V[2],16),j=parseInt(V[1],16)||0,f[14]=D,f[15]=j,i(r,f),r}function d(u){var h=u.length,r=[1732584193,-271733879,-1732584194,271733878],v,w,f,V,D,j;for(v=64;v<=h;v+=64)i(r,s(u.subarray(v-64,v)));for(u=v-64<h?u.subarray(v-64):new Uint8Array(0),w=u.length,f=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],v=0;v<w;v+=1)f[v>>2]|=u[v]<<(v%4<<3);if(f[v>>2]|=128<<(v%4<<3),v>55)for(i(r,f),v=0;v<16;v+=1)f[v]=0;return V=h*8,V=V.toString(16).match(/(.*?)(.{0,8})$/),D=parseInt(V[2],16),j=parseInt(V[1],16)||0,f[14]=D,f[15]=j,i(r,f),r}function x(u){var h="",r;for(r=0;r<4;r+=1)h+=n[u>>r*8+4&15]+n[u>>r*8&15];return h}function k(u){var h;for(h=0;h<u.length;h+=1)u[h]=x(u[h]);return u.join("")}k(c("hello"))!=="5d41402abc4b2a76b9719d911017c592"&&(o=function(u,h){var r=(u&65535)+(h&65535),v=(u>>16)+(h>>16)+(r>>16);return v<<16|r&65535}),typeof ArrayBuffer<"u"&&!ArrayBuffer.prototype.slice&&function(){function u(h,r){return h=h|0||0,h<0?Math.max(h+r,0):Math.min(h,r)}ArrayBuffer.prototype.slice=function(h,r){var v=this.byteLength,w=u(h,v),f=v,V,D,j,n1;return r!==e&&(f=u(r,v)),w>f?new ArrayBuffer(0):(V=f-w,D=new ArrayBuffer(V),j=new Uint8Array(D),n1=new Uint8Array(this,w,V),j.set(n1),D)}}();function l(u){return/[\u0080-\uFFFF]/.test(u)&&(u=unescape(encodeURIComponent(u))),u}function M(u,h){var r=u.length,v=new ArrayBuffer(r),w=new Uint8Array(v),f;for(f=0;f<r;f+=1)w[f]=u.charCodeAt(f);return h?w:v}function _(u){return String.fromCharCode.apply(null,new Uint8Array(u))}function B(u,h,r){var v=new Uint8Array(u.byteLength+h.byteLength);return v.set(new Uint8Array(u)),v.set(new Uint8Array(h),u.byteLength),r?v:v.buffer}function A(u){var h=[],r=u.length,v;for(v=0;v<r-1;v+=2)h.push(parseInt(u.substr(v,2),16));return String.fromCharCode.apply(String,h)}function z(){this.reset()}return z.prototype.append=function(u){return this.appendBinary(l(u)),this},z.prototype.appendBinary=function(u){this._buff+=u,this._length+=u.length;var h=this._buff.length,r;for(r=64;r<=h;r+=64)i(this._hash,a(this._buff.substring(r-64,r)));return this._buff=this._buff.substring(r-64),this},z.prototype.end=function(u){var h=this._buff,r=h.length,v,w=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],f;for(v=0;v<r;v+=1)w[v>>2]|=h.charCodeAt(v)<<(v%4<<3);return this._finish(w,r),f=k(this._hash),u&&(f=A(f)),this.reset(),f},z.prototype.reset=function(){return this._buff="",this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},z.prototype.getState=function(){return{buff:this._buff,length:this._length,hash:this._hash.slice()}},z.prototype.setState=function(u){return this._buff=u.buff,this._length=u.length,this._hash=u.hash,this},z.prototype.destroy=function(){delete this._hash,delete this._buff,delete this._length},z.prototype._finish=function(u,h){var r=h,v,w,f;if(u[r>>2]|=128<<(r%4<<3),r>55)for(i(this._hash,u),r=0;r<16;r+=1)u[r]=0;v=this._length*8,v=v.toString(16).match(/(.*?)(.{0,8})$/),w=parseInt(v[2],16),f=parseInt(v[1],16)||0,u[14]=w,u[15]=f,i(this._hash,u)},z.hash=function(u,h){return z.hashBinary(l(u),h)},z.hashBinary=function(u,h){var r=c(u),v=k(r);return h?A(v):v},z.ArrayBuffer=function(){this.reset()},z.ArrayBuffer.prototype.append=function(u){var h=B(this._buff.buffer,u,!0),r=h.length,v;for(this._length+=u.byteLength,v=64;v<=r;v+=64)i(this._hash,s(h.subarray(v-64,v)));return this._buff=v-64<r?new Uint8Array(h.buffer.slice(v-64)):new Uint8Array(0),this},z.ArrayBuffer.prototype.end=function(u){var h=this._buff,r=h.length,v=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],w,f;for(w=0;w<r;w+=1)v[w>>2]|=h[w]<<(w%4<<3);return this._finish(v,r),f=k(this._hash),u&&(f=A(f)),this.reset(),f},z.ArrayBuffer.prototype.reset=function(){return this._buff=new Uint8Array(0),this._length=0,this._hash=[1732584193,-271733879,-1732584194,271733878],this},z.ArrayBuffer.prototype.getState=function(){var u=z.prototype.getState.call(this);return u.buff=_(u.buff),u},z.ArrayBuffer.prototype.setState=function(u){return u.buff=M(u.buff,!0),z.prototype.setState.call(this,u)},z.ArrayBuffer.prototype.destroy=z.prototype.destroy,z.ArrayBuffer.prototype._finish=z.prototype._finish,z.ArrayBuffer.hash=function(u,h){var r=d(new Uint8Array(u)),v=k(r);return h?A(v):v},z})});var z3=te(Yt=>{"use strict";Yt.stringify=function(o){var n=[];n.push({obj:o});for(var t="",i,a,s,c,d,x,k,l,M,_,B;i=n.pop();)if(a=i.obj,s=i.prefix||"",c=i.val||"",t+=s,c)t+=c;else if(typeof a!="object")t+=typeof a>"u"?null:JSON.stringify(a);else if(a===null)t+="null";else if(Array.isArray(a)){for(n.push({val:"]"}),d=a.length-1;d>=0;d--)x=d===0?"":",",n.push({obj:a[d],prefix:x});n.push({val:"["})}else{k=[];for(l in a)a.hasOwnProperty(l)&&k.push(l);for(n.push({val:"}"}),d=k.length-1;d>=0;d--)M=k[d],_=a[M],B=d>0?",":"",B+=JSON.stringify(M)+":",n.push({obj:_,prefix:B});n.push({val:"{"})}return t};function E0(e,o,n){var t=n[n.length-1];e===t.element&&(n.pop(),t=n[n.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(e);else if(a===o.length-2){var s=o.pop();i[s]=e}else o.push(e)}Yt.parse=function(e){for(var o=[],n=[],t=0,i,a,s,c,d,x,k,l,M;;){if(i=e[t++],i==="}"||i==="]"||typeof i>"u"){if(o.length===1)return o.pop();E0(o.pop(),o,n);continue}switch(i){case" ":case"	":case`
`:case":":case",":break;case"n":t+=3,E0(null,o,n);break;case"t":t+=3,E0(!0,o,n);break;case"f":t+=4,E0(!1,o,n);break;case"0":case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":case"-":for(a="",t--;;)if(s=e[t++],/[\d\.\-e\+]/.test(s))a+=s;else{t--;break}E0(parseFloat(a),o,n);break;case'"':for(c="",d=void 0,x=0;k=e[t++],k!=='"'||d==="\\"&&x%2===1;)c+=k,d=k,d==="\\"?x++:x=0;E0(JSON.parse('"'+c+'"'),o,n);break;case"[":l={element:[],index:o.length},o.push(l.element),n.push(l);break;case"{":M={element:{},index:o.length},o.push(M.element),n.push(M);break;default:throw new Error("unexpectedly reached end of input: "+i)}}}});var t4=te((S7,e4)=>{"use strict";var P0=typeof Reflect=="object"?Reflect:null,C3=P0&&typeof P0.apply=="function"?P0.apply:function(o,n,t){return Function.prototype.apply.call(o,n,t)},C2;P0&&typeof P0.ownKeys=="function"?C2=P0.ownKeys:Object.getOwnPropertySymbols?C2=function(o){return Object.getOwnPropertyNames(o).concat(Object.getOwnPropertySymbols(o))}:C2=function(o){return Object.getOwnPropertyNames(o)};function K8(e){console&&console.warn&&console.warn(e)}var b3=Number.isNaN||function(o){return o!==o};function z1(){z1.init.call(this)}e4.exports=z1;e4.exports.once=X8;z1.EventEmitter=z1;z1.prototype._events=void 0;z1.prototype._eventsCount=0;z1.prototype._maxListeners=void 0;var _3=10;function _2(e){if(typeof e!="function")throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}Object.defineProperty(z1,"defaultMaxListeners",{enumerable:!0,get:function(){return _3},set:function(e){if(typeof e!="number"||e<0||b3(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");_3=e}});z1.init=function(){(this._events===void 0||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0};z1.prototype.setMaxListeners=function(o){if(typeof o!="number"||o<0||b3(o))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+o+".");return this._maxListeners=o,this};function B3(e){return e._maxListeners===void 0?z1.defaultMaxListeners:e._maxListeners}z1.prototype.getMaxListeners=function(){return B3(this)};z1.prototype.emit=function(o){for(var n=[],t=1;t<arguments.length;t++)n.push(arguments[t]);var i=o==="error",a=this._events;if(a!==void 0)i=i&&a.error===void 0;else if(!i)return!1;if(i){var s;if(n.length>0&&(s=n[0]),s instanceof Error)throw s;var c=new Error("Unhandled error."+(s?" ("+s.message+")":""));throw c.context=s,c}var d=a[o];if(d===void 0)return!1;if(typeof d=="function")C3(d,this,n);else for(var x=d.length,k=j3(d,x),t=0;t<x;++t)C3(k[t],this,n);return!0};function S3(e,o,n,t){var i,a,s;if(_2(n),a=e._events,a===void 0?(a=e._events=Object.create(null),e._eventsCount=0):(a.newListener!==void 0&&(e.emit("newListener",o,n.listener?n.listener:n),a=e._events),s=a[o]),s===void 0)s=a[o]=n,++e._eventsCount;else if(typeof s=="function"?s=a[o]=t?[n,s]:[s,n]:t?s.unshift(n):s.push(n),i=B3(e),i>0&&s.length>i&&!s.warned){s.warned=!0;var c=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(o)+" listeners added. Use emitter.setMaxListeners() to increase limit");c.name="MaxListenersExceededWarning",c.emitter=e,c.type=o,c.count=s.length,K8(c)}return e}z1.prototype.addListener=function(o,n){return S3(this,o,n,!1)};z1.prototype.on=z1.prototype.addListener;z1.prototype.prependListener=function(o,n){return S3(this,o,n,!0)};function W8(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length===0?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function A3(e,o,n){var t={fired:!1,wrapFn:void 0,target:e,type:o,listener:n},i=W8.bind(t);return i.listener=n,t.wrapFn=i,i}z1.prototype.once=function(o,n){return _2(n),this.on(o,A3(this,o,n)),this};z1.prototype.prependOnceListener=function(o,n){return _2(n),this.prependListener(o,A3(this,o,n)),this};z1.prototype.removeListener=function(o,n){var t,i,a,s,c;if(_2(n),i=this._events,i===void 0)return this;if(t=i[o],t===void 0)return this;if(t===n||t.listener===n)--this._eventsCount===0?this._events=Object.create(null):(delete i[o],i.removeListener&&this.emit("removeListener",o,t.listener||n));else if(typeof t!="function"){for(a=-1,s=t.length-1;s>=0;s--)if(t[s]===n||t[s].listener===n){c=t[s].listener,a=s;break}if(a<0)return this;a===0?t.shift():Z8(t,a),t.length===1&&(i[o]=t[0]),i.removeListener!==void 0&&this.emit("removeListener",o,c||n)}return this};z1.prototype.off=z1.prototype.removeListener;z1.prototype.removeAllListeners=function(o){var n,t,i;if(t=this._events,t===void 0)return this;if(t.removeListener===void 0)return arguments.length===0?(this._events=Object.create(null),this._eventsCount=0):t[o]!==void 0&&(--this._eventsCount===0?this._events=Object.create(null):delete t[o]),this;if(arguments.length===0){var a=Object.keys(t),s;for(i=0;i<a.length;++i)s=a[i],s!=="removeListener"&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(n=t[o],typeof n=="function")this.removeListener(o,n);else if(n!==void 0)for(i=n.length-1;i>=0;i--)this.removeListener(o,n[i]);return this};function L3(e,o,n){var t=e._events;if(t===void 0)return[];var i=t[o];return i===void 0?[]:typeof i=="function"?n?[i.listener||i]:[i]:n?Q8(i):j3(i,i.length)}z1.prototype.listeners=function(o){return L3(this,o,!0)};z1.prototype.rawListeners=function(o){return L3(this,o,!1)};z1.listenerCount=function(e,o){return typeof e.listenerCount=="function"?e.listenerCount(o):I3.call(e,o)};z1.prototype.listenerCount=I3;function I3(e){var o=this._events;if(o!==void 0){var n=o[e];if(typeof n=="function")return 1;if(n!==void 0)return n.length}return 0}z1.prototype.eventNames=function(){return this._eventsCount>0?C2(this._events):[]};function j3(e,o){for(var n=new Array(o),t=0;t<o;++t)n[t]=e[t];return n}function Z8(e,o){for(;o+1<e.length;o++)e[o]=e[o+1];e.pop()}function Q8(e){for(var o=new Array(e.length),n=0;n<o.length;++n)o[n]=e[n].listener||e[n];return o}function X8(e,o){return new Promise(function(n,t){function i(s){e.removeListener(o,a),t(s)}function a(){typeof e.removeListener=="function"&&e.removeListener("error",i),n([].slice.call(arguments))}V3(e,o,a,{once:!0}),o!=="error"&&J8(e,i,{once:!0})})}function J8(e,o,n){typeof e.on=="function"&&V3(e,"error",o,n)}function V3(e,o,n,t){if(typeof e.on=="function")t.once?e.once(o,n):e.on(o,n);else if(typeof e.addEventListener=="function")e.addEventListener(o,function i(a){t.once&&e.removeEventListener(o,i),n(a)});else throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}});var c3=(e,o)=>Q(null,null,function*(){if(!(typeof window>"u"))return yield o3(),i3(JSON.parse('[["ion-menu_3",[[33,"ion-menu-button",{"color":[513],"disabled":[4],"menu":[1],"autoHide":[4,"auto-hide"],"type":[1],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]],[33,"ion-menu",{"contentId":[513,"content-id"],"menuId":[513,"menu-id"],"type":[1025],"disabled":[1028],"side":[513],"swipeGesture":[4,"swipe-gesture"],"maxEdgeStart":[2,"max-edge-start"],"isPaneVisible":[32],"isEndSide":[32],"isOpen":[64],"isActive":[64],"open":[64],"close":[64],"toggle":[64],"setOpen":[64]},[[16,"ionSplitPaneVisible","onSplitPaneChanged"],[2,"click","onBackdropClick"]],{"type":["typeChanged"],"disabled":["disabledChanged"],"side":["sideChanged"],"swipeGesture":["swipeGestureChanged"]}],[1,"ion-menu-toggle",{"menu":[1],"autoHide":[4,"auto-hide"],"visible":[32]},[[16,"ionMenuChange","visibilityChanged"],[16,"ionSplitPaneVisible","visibilityChanged"]]]]],["ion-input-password-toggle",[[33,"ion-input-password-toggle",{"color":[513],"showIcon":[1,"show-icon"],"hideIcon":[1,"hide-icon"],"type":[1025]},null,{"type":["onTypeChange"]}]]],["ion-fab_3",[[33,"ion-fab-button",{"color":[513],"activated":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1],"show":[4],"translucent":[4],"type":[1],"size":[1],"closeIcon":[1,"close-icon"]}],[1,"ion-fab",{"horizontal":[1],"vertical":[1],"edge":[4],"activated":[1028],"close":[64],"toggle":[64]},null,{"activated":["activatedChanged"]}],[1,"ion-fab-list",{"activated":[4],"side":[1]},null,{"activated":["activatedChanged"]}]]],["ion-refresher_2",[[0,"ion-refresher-content",{"pullingIcon":[1025,"pulling-icon"],"pullingText":[1,"pulling-text"],"refreshingSpinner":[1025,"refreshing-spinner"],"refreshingText":[1,"refreshing-text"]}],[32,"ion-refresher",{"pullMin":[2,"pull-min"],"pullMax":[2,"pull-max"],"closeDuration":[1,"close-duration"],"snapbackDuration":[1,"snapback-duration"],"pullFactor":[2,"pull-factor"],"disabled":[4],"nativeRefresher":[32],"state":[32],"complete":[64],"cancel":[64],"getProgress":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-back-button",[[33,"ion-back-button",{"color":[513],"defaultHref":[1025,"default-href"],"disabled":[516],"icon":[1],"text":[1],"type":[1],"routerAnimation":[16,"router-animation"]}]]],["ion-toast",[[33,"ion-toast",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"color":[513],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"duration":[2],"header":[1],"layout":[1],"message":[1],"keyboardClose":[4,"keyboard-close"],"position":[1],"positionAnchor":[1,"position-anchor"],"buttons":[16],"translucent":[4],"animated":[4],"icon":[1],"htmlAttributes":[16,"html-attributes"],"swipeGesture":[1,"swipe-gesture"],"isOpen":[4,"is-open"],"trigger":[1],"revealContentToScreenReader":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"swipeGesture":["swipeGestureChanged"],"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-card_5",[[33,"ion-card",{"color":[513],"button":[4],"type":[1],"disabled":[4],"download":[1],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}],[32,"ion-card-content"],[33,"ion-card-header",{"color":[513],"translucent":[4]}],[33,"ion-card-subtitle",{"color":[513]}],[33,"ion-card-title",{"color":[513]}]]],["ion-item-option_3",[[33,"ion-item-option",{"color":[513],"disabled":[4],"download":[1],"expandable":[4],"href":[1],"rel":[1],"target":[1],"type":[1]}],[32,"ion-item-options",{"side":[1],"fireSwipeEvent":[64]}],[0,"ion-item-sliding",{"disabled":[4],"state":[32],"getOpenAmount":[64],"getSlidingRatio":[64],"open":[64],"close":[64],"closeOpened":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-accordion_2",[[49,"ion-accordion",{"value":[1],"disabled":[4],"readonly":[4],"toggleIcon":[1,"toggle-icon"],"toggleIconSlot":[1,"toggle-icon-slot"],"state":[32],"isNext":[32],"isPrevious":[32]},null,{"value":["valueChanged"]}],[33,"ion-accordion-group",{"animated":[4],"multiple":[4],"value":[1025],"disabled":[4],"readonly":[4],"expand":[1],"requestAccordionToggle":[64],"getAccordions":[64]},[[0,"keydown","onKeydown"]],{"value":["valueChanged"],"disabled":["disabledChanged"],"readonly":["readonlyChanged"]}]]],["ion-infinite-scroll_2",[[32,"ion-infinite-scroll-content",{"loadingSpinner":[1025,"loading-spinner"],"loadingText":[1,"loading-text"]}],[0,"ion-infinite-scroll",{"threshold":[1],"disabled":[4],"position":[1],"isLoading":[32],"complete":[64]},null,{"threshold":["thresholdChanged"],"disabled":["disabledChanged"]}]]],["ion-reorder_2",[[33,"ion-reorder",null,[[2,"click","onClick"]]],[0,"ion-reorder-group",{"disabled":[4],"state":[32],"complete":[64]},null,{"disabled":["disabledChanged"]}]]],["ion-segment_2",[[33,"ion-segment-button",{"contentId":[513,"content-id"],"disabled":[1028],"layout":[1],"type":[1],"value":[8],"checked":[32],"setFocus":[64]},null,{"value":["valueChanged"]}],[33,"ion-segment",{"color":[513],"disabled":[4],"scrollable":[4],"swipeGesture":[4,"swipe-gesture"],"value":[1032],"selectOnFocus":[4,"select-on-focus"],"activated":[32]},[[16,"ionSegmentViewScroll","handleSegmentViewScroll"],[0,"keydown","onKeyDown"]],{"color":["colorChanged"],"swipeGesture":["swipeGestureChanged"],"value":["valueChanged"],"disabled":["disabledChanged"]}]]],["ion-chip",[[33,"ion-chip",{"color":[513],"outline":[4],"disabled":[4]}]]],["ion-input",[[38,"ion-input",{"color":[513],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"autofocus":[4],"clearInput":[4,"clear-input"],"clearInputIcon":[1,"clear-input-icon"],"clearOnEdit":[4,"clear-on-edit"],"counter":[4],"counterFormatter":[16,"counter-formatter"],"debounce":[2],"disabled":[516],"enterkeyhint":[1],"errorText":[1,"error-text"],"fill":[1],"inputmode":[1],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"max":[8],"maxlength":[2],"min":[8],"minlength":[2],"multiple":[4],"name":[1],"pattern":[1],"placeholder":[1],"readonly":[516],"required":[4],"shape":[1],"spellcheck":[4],"step":[1],"type":[1],"value":[1032],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"type":["onTypeChange"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-searchbar",[[34,"ion-searchbar",{"color":[513],"animated":[4],"autocapitalize":[1],"autocomplete":[1],"autocorrect":[1],"cancelButtonIcon":[1,"cancel-button-icon"],"cancelButtonText":[1,"cancel-button-text"],"clearIcon":[1,"clear-icon"],"debounce":[2],"disabled":[4],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"searchIcon":[1,"search-icon"],"showCancelButton":[1,"show-cancel-button"],"showClearButton":[1,"show-clear-button"],"spellcheck":[4],"type":[1],"value":[1025],"focused":[32],"noAnimate":[32],"setFocus":[64],"getInputElement":[64]},null,{"lang":["onLangChanged"],"dir":["onDirChanged"],"debounce":["debounceChanged"],"value":["valueChanged"],"showCancelButton":["showCancelButtonChanged"]}]]],["ion-toggle",[[33,"ion-toggle",{"color":[513],"name":[1],"checked":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[1],"enableOnOffLabels":[4,"enable-on-off-labels"],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"activated":[32]},null,{"disabled":["disabledChanged"]}]]],["ion-nav_2",[[1,"ion-nav",{"delegate":[16],"swipeGesture":[1028,"swipe-gesture"],"animated":[4],"animation":[16],"rootParams":[16,"root-params"],"root":[1],"push":[64],"insert":[64],"insertPages":[64],"pop":[64],"popTo":[64],"popToRoot":[64],"removeIndex":[64],"setRoot":[64],"setPages":[64],"setRouteId":[64],"getRouteId":[64],"getActive":[64],"getByIndex":[64],"canGoBack":[64],"getPrevious":[64],"getLength":[64]},null,{"swipeGesture":["swipeGestureChanged"],"root":["rootChanged"]}],[0,"ion-nav-link",{"component":[1],"componentProps":[16,"component-props"],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}]]],["ion-tab_2",[[1,"ion-tab",{"active":[1028],"delegate":[16],"tab":[1],"component":[1],"setActive":[64]},null,{"active":["changeActive"]}],[1,"ion-tabs",{"useRouter":[1028,"use-router"],"selectedTab":[32],"select":[64],"getTab":[64],"getSelected":[64],"setRouteId":[64],"getRouteId":[64]}]]],["ion-textarea",[[38,"ion-textarea",{"color":[513],"autocapitalize":[1],"autofocus":[4],"clearOnEdit":[4,"clear-on-edit"],"debounce":[2],"disabled":[4],"fill":[1],"inputmode":[1],"enterkeyhint":[1],"maxlength":[2],"minlength":[2],"name":[1],"placeholder":[1],"readonly":[4],"required":[4],"spellcheck":[4],"cols":[514],"rows":[2],"wrap":[1],"autoGrow":[516,"auto-grow"],"value":[1025],"counter":[4],"counterFormatter":[16,"counter-formatter"],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"label":[1],"labelPlacement":[1,"label-placement"],"shape":[1],"hasFocus":[32],"setFocus":[64],"getInputElement":[64]},[[2,"click","onClickCapture"]],{"debounce":["debounceChanged"],"value":["valueChanged"],"dir":["onDirChanged"]}]]],["ion-backdrop",[[33,"ion-backdrop",{"visible":[4],"tappable":[4],"stopPropagation":[4,"stop-propagation"]},[[2,"click","onMouseDown"]]]]],["ion-loading",[[34,"ion-loading",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"message":[1],"cssClass":[1,"css-class"],"duration":[2],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"spinner":[1025],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-breadcrumb_2",[[33,"ion-breadcrumb",{"collapsed":[4],"last":[4],"showCollapsedIndicator":[4,"show-collapsed-indicator"],"color":[1],"active":[4],"disabled":[4],"download":[1],"href":[1],"rel":[1],"separator":[4],"target":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"]}],[33,"ion-breadcrumbs",{"color":[513],"maxItems":[2,"max-items"],"itemsBeforeCollapse":[2,"items-before-collapse"],"itemsAfterCollapse":[2,"items-after-collapse"],"collapsed":[32],"activeChanged":[32]},[[0,"collapsedClick","onCollapsedClick"]],{"maxItems":["maxItemsChanged"],"itemsBeforeCollapse":["maxItemsChanged"],"itemsAfterCollapse":["maxItemsChanged"]}]]],["ion-tab-bar_2",[[33,"ion-tab-button",{"disabled":[4],"download":[1],"href":[1],"rel":[1],"layout":[1025],"selected":[1028],"tab":[1],"target":[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]],[33,"ion-tab-bar",{"color":[513],"selectedTab":[1,"selected-tab"],"translucent":[4],"keyboardVisible":[32]},null,{"selectedTab":["selectedTabChanged"]}]]],["ion-datetime-button",[[33,"ion-datetime-button",{"color":[513],"disabled":[516],"datetime":[1],"datetimePresentation":[32],"dateText":[32],"timeText":[32],"datetimeActive":[32],"selectedButton":[32]}]]],["ion-route_4",[[0,"ion-route",{"url":[1],"component":[1],"componentProps":[16,"component-props"],"beforeLeave":[16,"before-leave"],"beforeEnter":[16,"before-enter"]},null,{"url":["onUpdate"],"component":["onUpdate"],"componentProps":["onComponentProps"]}],[0,"ion-route-redirect",{"from":[1],"to":[1]},null,{"from":["propDidChange"],"to":["propDidChange"]}],[0,"ion-router",{"root":[1],"useHash":[4,"use-hash"],"canTransition":[64],"push":[64],"back":[64],"printDebug":[64],"navChanged":[64]},[[8,"popstate","onPopState"],[4,"ionBackButton","onBackButton"]]],[1,"ion-router-link",{"color":[513],"href":[1],"rel":[1],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"target":[1]}]]],["ion-avatar_3",[[33,"ion-avatar"],[33,"ion-badge",{"color":[513]}],[1,"ion-thumbnail"]]],["ion-col_3",[[1,"ion-col",{"offset":[1],"offsetXs":[1,"offset-xs"],"offsetSm":[1,"offset-sm"],"offsetMd":[1,"offset-md"],"offsetLg":[1,"offset-lg"],"offsetXl":[1,"offset-xl"],"pull":[1],"pullXs":[1,"pull-xs"],"pullSm":[1,"pull-sm"],"pullMd":[1,"pull-md"],"pullLg":[1,"pull-lg"],"pullXl":[1,"pull-xl"],"push":[1],"pushXs":[1,"push-xs"],"pushSm":[1,"push-sm"],"pushMd":[1,"push-md"],"pushLg":[1,"push-lg"],"pushXl":[1,"push-xl"],"size":[1],"sizeXs":[1,"size-xs"],"sizeSm":[1,"size-sm"],"sizeMd":[1,"size-md"],"sizeLg":[1,"size-lg"],"sizeXl":[1,"size-xl"]},[[9,"resize","onResize"]]],[1,"ion-grid",{"fixed":[4]}],[1,"ion-row"]]],["ion-img",[[1,"ion-img",{"alt":[1],"src":[1],"loadSrc":[32],"loadError":[32]},null,{"src":["srcChanged"]}]]],["ion-input-otp",[[38,"ion-input-otp",{"autocapitalize":[1],"color":[513],"disabled":[516],"fill":[1],"inputmode":[1],"length":[2],"pattern":[1],"readonly":[516],"separators":[1],"shape":[1],"size":[1],"type":[1],"value":[1032],"inputValues":[32],"hasFocus":[32],"previousInputValues":[32],"setFocus":[64]},null,{"value":["valueChanged"],"separators":["processSeparators"],"length":["processSeparators"]}]]],["ion-progress-bar",[[33,"ion-progress-bar",{"type":[1],"reversed":[4],"value":[2],"buffer":[2],"color":[513]}]]],["ion-range",[[33,"ion-range",{"color":[513],"debounce":[2],"name":[1],"label":[1],"dualKnobs":[4,"dual-knobs"],"min":[2],"max":[2],"pin":[4],"pinFormatter":[16,"pin-formatter"],"snaps":[4],"step":[2],"ticks":[4],"activeBarStart":[1026,"active-bar-start"],"disabled":[4],"value":[1026],"labelPlacement":[1,"label-placement"],"ratioA":[32],"ratioB":[32],"pressedKnob":[32]},null,{"debounce":["debounceChanged"],"min":["minChanged"],"max":["maxChanged"],"step":["stepChanged"],"activeBarStart":["activeBarStartChanged"],"disabled":["disabledChanged"],"value":["valueChanged"]}]]],["ion-segment-content",[[1,"ion-segment-content"]]],["ion-segment-view",[[33,"ion-segment-view",{"disabled":[4],"isManualScroll":[32],"setContent":[64]},[[1,"scroll","handleScroll"],[1,"touchstart","handleScrollStart"],[1,"touchend","handleTouchEnd"]]]]],["ion-split-pane",[[33,"ion-split-pane",{"contentId":[513,"content-id"],"disabled":[4],"when":[8],"visible":[32],"isVisible":[64]},null,{"visible":["visibleChanged"],"disabled":["updateState"],"when":["updateState"]}]]],["ion-text",[[1,"ion-text",{"color":[513]}]]],["ion-select-modal",[[34,"ion-select-modal",{"header":[1],"multiple":[4],"options":[16]}]]],["ion-datetime_3",[[33,"ion-datetime",{"color":[1],"name":[1],"disabled":[4],"formatOptions":[16,"format-options"],"readonly":[4],"isDateEnabled":[16,"is-date-enabled"],"showAdjacentDays":[4,"show-adjacent-days"],"min":[1025],"max":[1025],"presentation":[1],"cancelText":[1,"cancel-text"],"doneText":[1,"done-text"],"clearText":[1,"clear-text"],"yearValues":[8,"year-values"],"monthValues":[8,"month-values"],"dayValues":[8,"day-values"],"hourValues":[8,"hour-values"],"minuteValues":[8,"minute-values"],"locale":[1],"firstDayOfWeek":[2,"first-day-of-week"],"titleSelectedDatesFormatter":[16,"title-selected-dates-formatter"],"multiple":[4],"highlightedDates":[16,"highlighted-dates"],"value":[1025],"showDefaultTitle":[4,"show-default-title"],"showDefaultButtons":[4,"show-default-buttons"],"showClearButton":[4,"show-clear-button"],"showDefaultTimeLabel":[4,"show-default-time-label"],"hourCycle":[1,"hour-cycle"],"size":[1],"preferWheel":[4,"prefer-wheel"],"showMonthAndYear":[32],"activeParts":[32],"workingParts":[32],"isTimePopoverOpen":[32],"forceRenderDate":[32],"confirm":[64],"reset":[64],"cancel":[64]},null,{"formatOptions":["formatOptionsChanged"],"disabled":["disabledChanged"],"min":["minChanged"],"max":["maxChanged"],"presentation":["presentationChanged"],"yearValues":["yearValuesChanged"],"monthValues":["monthValuesChanged"],"dayValues":["dayValuesChanged"],"hourValues":["hourValuesChanged"],"minuteValues":["minuteValuesChanged"],"value":["valueChanged"]}],[34,"ion-picker-legacy",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"columns":[16],"cssClass":[1,"css-class"],"duration":[2],"showBackdrop":[4,"show-backdrop"],"backdropDismiss":[4,"backdrop-dismiss"],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"getColumn":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}],[32,"ion-picker-legacy-column",{"col":[16]},null,{"col":["colChanged"]}]]],["ion-action-sheet",[[34,"ion-action-sheet",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"buttons":[16],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"header":[1],"subHeader":[1,"sub-header"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-alert",[[34,"ion-alert",{"overlayIndex":[2,"overlay-index"],"delegate":[16],"hasController":[4,"has-controller"],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"cssClass":[1,"css-class"],"header":[1],"subHeader":[1,"sub-header"],"message":[1],"buttons":[16],"inputs":[1040],"backdropDismiss":[4,"backdrop-dismiss"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64]},[[4,"keydown","onKeydown"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"],"buttons":["buttonsChanged"],"inputs":["inputsChanged"]}]]],["ion-modal",[[33,"ion-modal",{"hasController":[4,"has-controller"],"overlayIndex":[2,"overlay-index"],"delegate":[16],"keyboardClose":[4,"keyboard-close"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"breakpoints":[16],"expandToScroll":[4,"expand-to-scroll"],"initialBreakpoint":[2,"initial-breakpoint"],"backdropBreakpoint":[2,"backdrop-breakpoint"],"handle":[4],"handleBehavior":[1,"handle-behavior"],"component":[1],"componentProps":[16,"component-props"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"showBackdrop":[4,"show-backdrop"],"animated":[4],"presentingElement":[16,"presenting-element"],"htmlAttributes":[16,"html-attributes"],"isOpen":[4,"is-open"],"trigger":[1],"keepContentsMounted":[4,"keep-contents-mounted"],"focusTrap":[4,"focus-trap"],"canDismiss":[4,"can-dismiss"],"presented":[32],"present":[64],"dismiss":[64],"onDidDismiss":[64],"onWillDismiss":[64],"setCurrentBreakpoint":[64],"getCurrentBreakpoint":[64]},[[9,"resize","onWindowResize"]],{"isOpen":["onIsOpenChange"],"trigger":["triggerChanged"]}]]],["ion-picker",[[33,"ion-picker",{"exitInputMode":[64]},[[1,"touchstart","preventTouchStartPropagation"]]]]],["ion-picker-column",[[1,"ion-picker-column",{"disabled":[4],"value":[1032],"color":[513],"numericInput":[4,"numeric-input"],"ariaLabel":[32],"isActive":[32],"scrollActiveItemIntoView":[64],"setValue":[64],"setFocus":[64]},null,{"aria-label":["ariaLabelChanged"],"value":["valueChange"]}]]],["ion-picker-column-option",[[33,"ion-picker-column-option",{"disabled":[4],"value":[8],"color":[513],"ariaLabel":[32]},null,{"aria-label":["onAriaLabelChange"]}]]],["ion-popover",[[33,"ion-popover",{"hasController":[4,"has-controller"],"delegate":[16],"overlayIndex":[2,"overlay-index"],"enterAnimation":[16,"enter-animation"],"leaveAnimation":[16,"leave-animation"],"component":[1],"componentProps":[16,"component-props"],"keyboardClose":[4,"keyboard-close"],"cssClass":[1,"css-class"],"backdropDismiss":[4,"backdrop-dismiss"],"event":[8],"showBackdrop":[4,"show-backdrop"],"translucent":[4],"animated":[4],"htmlAttributes":[16,"html-attributes"],"triggerAction":[1,"trigger-action"],"trigger":[1],"size":[1],"dismissOnSelect":[4,"dismiss-on-select"],"reference":[1],"side":[1],"alignment":[1025],"arrow":[4],"isOpen":[4,"is-open"],"keyboardEvents":[4,"keyboard-events"],"focusTrap":[4,"focus-trap"],"keepContentsMounted":[4,"keep-contents-mounted"],"presented":[32],"presentFromTrigger":[64],"present":[64],"dismiss":[64],"getParentPopover":[64],"onDidDismiss":[64],"onWillDismiss":[64]},null,{"trigger":["onTriggerChange"],"triggerAction":["onTriggerChange"],"isOpen":["onIsOpenChange"]}]]],["ion-checkbox",[[33,"ion-checkbox",{"color":[513],"name":[1],"checked":[1028],"indeterminate":[1028],"disabled":[4],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"required":[4],"setFocus":[64]}]]],["ion-item_8",[[33,"ion-item-divider",{"color":[513],"sticky":[4]}],[32,"ion-item-group"],[33,"ion-note",{"color":[513]}],[1,"ion-skeleton-text",{"animated":[4]}],[38,"ion-label",{"color":[513],"position":[1],"noAnimate":[32]},null,{"color":["colorChanged"],"position":["positionChanged"]}],[33,"ion-list-header",{"color":[513],"lines":[1]}],[33,"ion-item",{"color":[513],"button":[4],"detail":[4],"detailIcon":[1,"detail-icon"],"disabled":[516],"download":[1],"href":[1],"rel":[1],"lines":[1],"routerAnimation":[16,"router-animation"],"routerDirection":[1,"router-direction"],"target":[1],"type":[1],"multipleInputs":[32],"focusable":[32],"isInteractive":[32]},[[0,"ionColor","labelColorChanged"],[0,"ionStyle","itemStyle"]],{"button":["buttonChanged"]}],[32,"ion-list",{"lines":[1],"inset":[4],"closeSlidingItems":[64]}]]],["ion-app_8",[[0,"ion-app",{"setFocus":[64]}],[36,"ion-footer",{"collapse":[1],"translucent":[4],"keyboardVisible":[32]}],[1,"ion-router-outlet",{"mode":[1025],"delegate":[16],"animated":[4],"animation":[16],"swipeHandler":[16,"swipe-handler"],"commit":[64],"setRouteId":[64],"getRouteId":[64]},null,{"swipeHandler":["swipeHandlerChanged"]}],[1,"ion-content",{"color":[513],"fullscreen":[4],"fixedSlotPlacement":[1,"fixed-slot-placement"],"forceOverscroll":[1028,"force-overscroll"],"scrollX":[4,"scroll-x"],"scrollY":[4,"scroll-y"],"scrollEvents":[4,"scroll-events"],"getScrollElement":[64],"getBackgroundElement":[64],"scrollToTop":[64],"scrollToBottom":[64],"scrollByPoint":[64],"scrollToPoint":[64]},[[9,"resize","onResize"]]],[36,"ion-header",{"collapse":[1],"translucent":[4]}],[33,"ion-title",{"color":[513],"size":[1]},null,{"size":["sizeChanged"]}],[33,"ion-toolbar",{"color":[513]},[[0,"ionStyle","childrenStyle"]]],[38,"ion-buttons",{"collapse":[4]}]]],["ion-select_3",[[33,"ion-select",{"cancelText":[1,"cancel-text"],"color":[513],"compareWith":[1,"compare-with"],"disabled":[4],"fill":[1],"errorText":[1,"error-text"],"helperText":[1,"helper-text"],"interface":[1],"interfaceOptions":[8,"interface-options"],"justify":[1],"label":[1],"labelPlacement":[1,"label-placement"],"multiple":[4],"name":[1],"okText":[1,"ok-text"],"placeholder":[1],"selectedText":[1,"selected-text"],"toggleIcon":[1,"toggle-icon"],"expandedIcon":[1,"expanded-icon"],"shape":[1],"value":[1032],"required":[4],"isExpanded":[32],"hasFocus":[32],"open":[64]},null,{"disabled":["styleChanged"],"isExpanded":["styleChanged"],"placeholder":["styleChanged"],"value":["styleChanged"]}],[1,"ion-select-option",{"disabled":[4],"value":[8]}],[34,"ion-select-popover",{"header":[1],"subHeader":[1,"sub-header"],"message":[1],"multiple":[4],"options":[16]}]]],["ion-spinner",[[1,"ion-spinner",{"color":[513],"duration":[2],"name":[1],"paused":[4]}]]],["ion-radio_2",[[33,"ion-radio",{"color":[513],"name":[1],"disabled":[4],"value":[8],"labelPlacement":[1,"label-placement"],"justify":[1],"alignment":[1],"checked":[32],"buttonTabindex":[32],"setFocus":[64],"setButtonTabindex":[64]},null,{"value":["valueChanged"]}],[36,"ion-radio-group",{"allowEmptySelection":[4,"allow-empty-selection"],"compareWith":[1,"compare-with"],"name":[1],"value":[1032],"helperText":[1,"helper-text"],"errorText":[1,"error-text"],"setFocus":[64]},[[4,"keydown","onKeydown"]],{"value":["valueChanged"]}]]],["ion-ripple-effect",[[1,"ion-ripple-effect",{"type":[1],"addRipple":[64]}]]],["ion-button_2",[[33,"ion-button",{"color":[513],"buttonType":[1025,"button-type"],"disabled":[516],"expand":[513],"fill":[1537],"routerDirection":[1,"router-direction"],"routerAnimation":[16,"router-animation"],"download":[1],"href":[1],"rel":[1],"shape":[513],"size":[513],"strong":[4],"target":[1],"type":[1],"form":[1],"isCircle":[32]},null,{"disabled":["disabledChanged"],"aria-checked":["onAriaChanged"],"aria-label":["onAriaChanged"]}],[1,"ion-icon",{"mode":[1025],"color":[1],"ios":[1],"md":[1],"flipRtl":[4,"flip-rtl"],"name":[513],"src":[1],"icon":[8],"size":[1],"lazy":[4],"sanitize":[4],"svgContent":[32],"isVisible":[32]},null,{"name":["loadIcon"],"src":["loadIcon"],"icon":["loadIcon"],"ios":["loadIcon"],"md":["loadIcon"]}]]]]'),o)});(function(){if(typeof window<"u"&&window.Reflect!==void 0&&window.customElements!==void 0){var e=HTMLElement;window.HTMLElement=function(){return Reflect.construct(e,[],this.constructor)},HTMLElement.prototype=e.prototype,HTMLElement.prototype.constructor=HTMLElement,Object.setPrototypeOf(HTMLElement,e)}})();var G=["*"],To=["outletContent"],Ro=["outlet"],Fo=[[["","slot","top"]],"*",[["ion-tab"]]],qo=["[slot=top]","*","ion-tab"];function No(e,o){if(e&1){let n=F1();P(0,"ion-router-outlet",5,1),M1("stackWillChange",function(i){k1(n);let a=h1();return y1(a.onStackWillChange(i))})("stackDidChange",function(i){k1(n);let a=h1();return y1(a.onStackDidChange(i))}),O()}}function $o(e,o){e&1&&F(0,2,["*ngIf","tabs.length > 0"])}function Uo(e,o){if(e&1&&(P(0,"div",1),se(1,2),O()),e&2){let n=h1();g1(),f1("ngTemplateOutlet",n.template)}}function Go(e,o){if(e&1&&se(0,1),e&2){let n=h1();f1("ngTemplateOutlet",n.template)}}var Ko=(()=>{class e extends e2{constructor(n,t){super(n,t)}writeValue(n){this.elementRef.nativeElement.checked=this.lastValue=n,n3(this.elementRef)}_handleIonChange(n){this.handleValueChange(n,n.checked)}static \u0275fac=function(t){return new(t||e)(m(l0),m(R))};static \u0275dir=d0({type:e,selectors:[["ion-checkbox"],["ion-toggle"]],hostBindings:function(t,i){t&1&&M1("ionChange",function(s){return i._handleIonChange(s.target)})},standalone:!1,features:[C0([{provide:Y0,useExisting:e,multi:!0}]),R1]})}return e})(),Wo=(()=>{class e extends e2{el;constructor(n,t){super(n,t),this.el=t}handleInputEvent(n){this.handleValueChange(n,n.value)}registerOnChange(n){this.el.nativeElement.tagName==="ION-INPUT"||this.el.nativeElement.tagName==="ION-INPUT-OTP"?super.registerOnChange(t=>{n(t===""?null:parseFloat(t))}):super.registerOnChange(n)}static \u0275fac=function(t){return new(t||e)(m(l0),m(R))};static \u0275dir=d0({type:e,selectors:[["ion-input","type","number"],["ion-input-otp",3,"type","text"],["ion-range"]],hostBindings:function(t,i){t&1&&M1("ionInput",function(s){return i.handleInputEvent(s.target)})},standalone:!1,features:[C0([{provide:Y0,useExisting:e,multi:!0}]),R1]})}return e})(),Zo=(()=>{class e extends e2{constructor(n,t){super(n,t)}_handleChangeEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(t){return new(t||e)(m(l0),m(R))};static \u0275dir=d0({type:e,selectors:[["ion-select"],["ion-radio-group"],["ion-segment"],["ion-datetime"]],hostBindings:function(t,i){t&1&&M1("ionChange",function(s){return i._handleChangeEvent(s.target)})},standalone:!1,features:[C0([{provide:Y0,useExisting:e,multi:!0}]),R1]})}return e})(),Qo=(()=>{class e extends e2{constructor(n,t){super(n,t)}_handleInputEvent(n){this.handleValueChange(n,n.value)}static \u0275fac=function(t){return new(t||e)(m(l0),m(R))};static \u0275dir=d0({type:e,selectors:[["ion-input",3,"type","number"],["ion-input-otp","type","text"],["ion-textarea"],["ion-searchbar"]],hostBindings:function(t,i){t&1&&M1("ionInput",function(s){return i._handleInputEvent(s.target)})},standalone:!1,features:[C0([{provide:Y0,useExisting:e,multi:!0}]),R1]})}return e})(),Xo=(e,o)=>{let n=e.prototype;o.forEach(t=>{Object.defineProperty(n,t,{get(){return this.el[t]},set(i){this.z.runOutsideAngular(()=>this.el[t]=i)},configurable:!0})})},Jo=(e,o)=>{let n=e.prototype;o.forEach(t=>{n[t]=function(){let i=arguments;return this.z.runOutsideAngular(()=>this.el[t].apply(this.el,i))}})},m1=(e,o,n)=>{n.forEach(t=>e[t]=un(o,t))};function W(e){return function(n){let{defineCustomElementFn:t,inputs:i,methods:a}=e;return t!==void 0&&t(),i&&Xo(n,i),a&&Jo(n,a),n}}var Yo=(()=>{let e=class pe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||pe)(m(U),m(R),m(q))};static \u0275cmp=T({type:pe,selectors:[["ion-accordion"]],inputs:{disabled:"disabled",mode:"mode",readonly:"readonly",toggleIcon:"toggleIcon",toggleIconSlot:"toggleIconSlot",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","mode","readonly","toggleIcon","toggleIconSlot","value"]})],e),e})(),ei=(()=>{let e=class me{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||me)(m(U),m(R),m(q))};static \u0275cmp=T({type:me,selectors:[["ion-accordion-group"]],inputs:{animated:"animated",disabled:"disabled",expand:"expand",mode:"mode",multiple:"multiple",readonly:"readonly",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","disabled","expand","mode","multiple","readonly","value"]})],e),e})(),ti=(()=>{let e=class we{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionActionSheetDidPresent","ionActionSheetWillPresent","ionActionSheetWillDismiss","ionActionSheetDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||we)(m(U),m(R),m(q))};static \u0275cmp=T({type:we,selectors:[["ion-action-sheet"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),ni=(()=>{let e=class fe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionAlertDidPresent","ionAlertWillPresent","ionAlertWillDismiss","ionAlertDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||fe)(m(U),m(R),m(q))};static \u0275cmp=T({type:fe,selectors:[["ion-alert"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",cssClass:"cssClass",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",inputs:"inputs",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",subHeader:"subHeader",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","buttons","cssClass","enterAnimation","header","htmlAttributes","inputs","isOpen","keyboardClose","leaveAnimation","message","mode","subHeader","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),oi=(()=>{let e=class xe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||xe)(m(U),m(R),m(q))};static \u0275cmp=T({type:xe,selectors:[["ion-app"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({methods:["setFocus"]})],e),e})(),ii=(()=>{let e=class Me{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Me)(m(U),m(R),m(q))};static \u0275cmp=T({type:Me,selectors:[["ion-avatar"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),ai=(()=>{let e=class ke{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionBackdropTap"])}static \u0275fac=function(t){return new(t||ke)(m(U),m(R),m(q))};static \u0275cmp=T({type:ke,selectors:[["ion-backdrop"]],inputs:{stopPropagation:"stopPropagation",tappable:"tappable",visible:"visible"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["stopPropagation","tappable","visible"]})],e),e})(),si=(()=>{let e=class ye{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ye)(m(U),m(R),m(q))};static \u0275cmp=T({type:ye,selectors:[["ion-badge"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),ri=(()=>{let e=class ze{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||ze)(m(U),m(R),m(q))};static \u0275cmp=T({type:ze,selectors:[["ion-breadcrumb"]],inputs:{active:"active",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",separator:"separator",target:"target"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["active","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","separator","target"]})],e),e})(),ci=(()=>{let e=class Ce{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionCollapsedClick"])}static \u0275fac=function(t){return new(t||Ce)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ce,selectors:[["ion-breadcrumbs"]],inputs:{color:"color",itemsAfterCollapse:"itemsAfterCollapse",itemsBeforeCollapse:"itemsBeforeCollapse",maxItems:"maxItems",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","itemsAfterCollapse","itemsBeforeCollapse","maxItems","mode"]})],e),e})(),li=(()=>{let e=class _e{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||_e)(m(U),m(R),m(q))};static \u0275cmp=T({type:_e,selectors:[["ion-button"]],inputs:{buttonType:"buttonType",color:"color",disabled:"disabled",download:"download",expand:"expand",fill:"fill",form:"form",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",shape:"shape",size:"size",strong:"strong",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["buttonType","color","disabled","download","expand","fill","form","href","mode","rel","routerAnimation","routerDirection","shape","size","strong","target","type"]})],e),e})(),di=(()=>{let e=class be{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||be)(m(U),m(R),m(q))};static \u0275cmp=T({type:be,selectors:[["ion-buttons"]],inputs:{collapse:"collapse"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["collapse"]})],e),e})(),vi=(()=>{let e=class Be{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Be)(m(U),m(R),m(q))};static \u0275cmp=T({type:Be,selectors:[["ion-card"]],inputs:{button:"button",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["button","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),gi=(()=>{let e=class Se{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Se)(m(U),m(R),m(q))};static \u0275cmp=T({type:Se,selectors:[["ion-card-content"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["mode"]})],e),e})(),hi=(()=>{let e=class Ae{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ae)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ae,selectors:[["ion-card-header"]],inputs:{color:"color",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","translucent"]})],e),e})(),ui=(()=>{let e=class Le{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Le)(m(U),m(R),m(q))};static \u0275cmp=T({type:Le,selectors:[["ion-card-subtitle"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),pi=(()=>{let e=class Ie{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ie)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ie,selectors:[["ion-card-title"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),mi=(()=>{let e=class je{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||je)(m(U),m(R),m(q))};static \u0275cmp=T({type:je,selectors:[["ion-checkbox"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",errorText:"errorText",helperText:"helperText",indeterminate:"indeterminate",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alignment","checked","color","disabled","errorText","helperText","indeterminate","justify","labelPlacement","mode","name","required","value"]})],e),e})(),wi=(()=>{let e=class Ve{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ve)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ve,selectors:[["ion-chip"]],inputs:{color:"color",disabled:"disabled",mode:"mode",outline:"outline"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","mode","outline"]})],e),e})(),fi=(()=>{let e=class He{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||He)(m(U),m(R),m(q))};static \u0275cmp=T({type:He,selectors:[["ion-col"]],inputs:{offset:"offset",offsetLg:"offsetLg",offsetMd:"offsetMd",offsetSm:"offsetSm",offsetXl:"offsetXl",offsetXs:"offsetXs",pull:"pull",pullLg:"pullLg",pullMd:"pullMd",pullSm:"pullSm",pullXl:"pullXl",pullXs:"pullXs",push:"push",pushLg:"pushLg",pushMd:"pushMd",pushSm:"pushSm",pushXl:"pushXl",pushXs:"pushXs",size:"size",sizeLg:"sizeLg",sizeMd:"sizeMd",sizeSm:"sizeSm",sizeXl:"sizeXl",sizeXs:"sizeXs"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["offset","offsetLg","offsetMd","offsetSm","offsetXl","offsetXs","pull","pullLg","pullMd","pullSm","pullXl","pullXs","push","pushLg","pushMd","pushSm","pushXl","pushXs","size","sizeLg","sizeMd","sizeSm","sizeXl","sizeXs"]})],e),e})(),xi=(()=>{let e=class Oe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionScrollStart","ionScroll","ionScrollEnd"])}static \u0275fac=function(t){return new(t||Oe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Oe,selectors:[["ion-content"]],inputs:{color:"color",fixedSlotPlacement:"fixedSlotPlacement",forceOverscroll:"forceOverscroll",fullscreen:"fullscreen",scrollEvents:"scrollEvents",scrollX:"scrollX",scrollY:"scrollY"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","fixedSlotPlacement","forceOverscroll","fullscreen","scrollEvents","scrollX","scrollY"],methods:["getScrollElement","scrollToTop","scrollToBottom","scrollByPoint","scrollToPoint"]})],e),e})(),Mi=(()=>{let e=class De{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionCancel","ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||De)(m(U),m(R),m(q))};static \u0275cmp=T({type:De,selectors:[["ion-datetime"]],inputs:{cancelText:"cancelText",clearText:"clearText",color:"color",dayValues:"dayValues",disabled:"disabled",doneText:"doneText",firstDayOfWeek:"firstDayOfWeek",formatOptions:"formatOptions",highlightedDates:"highlightedDates",hourCycle:"hourCycle",hourValues:"hourValues",isDateEnabled:"isDateEnabled",locale:"locale",max:"max",min:"min",minuteValues:"minuteValues",mode:"mode",monthValues:"monthValues",multiple:"multiple",name:"name",preferWheel:"preferWheel",presentation:"presentation",readonly:"readonly",showAdjacentDays:"showAdjacentDays",showClearButton:"showClearButton",showDefaultButtons:"showDefaultButtons",showDefaultTimeLabel:"showDefaultTimeLabel",showDefaultTitle:"showDefaultTitle",size:"size",titleSelectedDatesFormatter:"titleSelectedDatesFormatter",value:"value",yearValues:"yearValues"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["cancelText","clearText","color","dayValues","disabled","doneText","firstDayOfWeek","formatOptions","highlightedDates","hourCycle","hourValues","isDateEnabled","locale","max","min","minuteValues","mode","monthValues","multiple","name","preferWheel","presentation","readonly","showAdjacentDays","showClearButton","showDefaultButtons","showDefaultTimeLabel","showDefaultTitle","size","titleSelectedDatesFormatter","value","yearValues"],methods:["confirm","reset","cancel"]})],e),e})(),ki=(()=>{let e=class Ee{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ee)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ee,selectors:[["ion-datetime-button"]],inputs:{color:"color",datetime:"datetime",disabled:"disabled",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","datetime","disabled","mode"]})],e),e})(),yi=(()=>{let e=class Pe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Pe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Pe,selectors:[["ion-fab"]],inputs:{activated:"activated",edge:"edge",horizontal:"horizontal",vertical:"vertical"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activated","edge","horizontal","vertical"],methods:["close"]})],e),e})(),zi=(()=>{let e=class Te{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Te)(m(U),m(R),m(q))};static \u0275cmp=T({type:Te,selectors:[["ion-fab-button"]],inputs:{activated:"activated",closeIcon:"closeIcon",color:"color",disabled:"disabled",download:"download",href:"href",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",show:"show",size:"size",target:"target",translucent:"translucent",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activated","closeIcon","color","disabled","download","href","mode","rel","routerAnimation","routerDirection","show","size","target","translucent","type"]})],e),e})(),Ci=(()=>{let e=class Re{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Re)(m(U),m(R),m(q))};static \u0275cmp=T({type:Re,selectors:[["ion-fab-list"]],inputs:{activated:"activated",side:"side"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activated","side"]})],e),e})(),_i=(()=>{let e=class Fe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Fe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Fe,selectors:[["ion-footer"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["collapse","mode","translucent"]})],e),e})(),bi=(()=>{let e=class qe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||qe)(m(U),m(R),m(q))};static \u0275cmp=T({type:qe,selectors:[["ion-grid"]],inputs:{fixed:"fixed"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["fixed"]})],e),e})(),Bi=(()=>{let e=class Ne{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ne)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ne,selectors:[["ion-header"]],inputs:{collapse:"collapse",mode:"mode",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["collapse","mode","translucent"]})],e),e})(),Si=(()=>{let e=class $e{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||$e)(m(U),m(R),m(q))};static \u0275cmp=T({type:$e,selectors:[["ion-icon"]],inputs:{color:"color",flipRtl:"flipRtl",icon:"icon",ios:"ios",lazy:"lazy",md:"md",mode:"mode",name:"name",sanitize:"sanitize",size:"size",src:"src"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","flipRtl","icon","ios","lazy","md","mode","name","sanitize","size","src"]})],e),e})(),Ai=(()=>{let e=class Ue{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionImgWillLoad","ionImgDidLoad","ionError"])}static \u0275fac=function(t){return new(t||Ue)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ue,selectors:[["ion-img"]],inputs:{alt:"alt",src:"src"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alt","src"]})],e),e})(),Li=(()=>{let e=class Ge{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionInfinite"])}static \u0275fac=function(t){return new(t||Ge)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ge,selectors:[["ion-infinite-scroll"]],inputs:{disabled:"disabled",position:"position",threshold:"threshold"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","position","threshold"],methods:["complete"]})],e),e})(),Ii=(()=>{let e=class Ke{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ke)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ke,selectors:[["ion-infinite-scroll-content"]],inputs:{loadingSpinner:"loadingSpinner",loadingText:"loadingText"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["loadingSpinner","loadingText"]})],e),e})(),ji=(()=>{let e=class We{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionInput","ionChange","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||We)(m(U),m(R),m(q))};static \u0275cmp=T({type:We,selectors:[["ion-input"]],inputs:{autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",autofocus:"autofocus",clearInput:"clearInput",clearInputIcon:"clearInputIcon",clearOnEdit:"clearOnEdit",color:"color",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",max:"max",maxlength:"maxlength",min:"min",minlength:"minlength",mode:"mode",multiple:"multiple",name:"name",pattern:"pattern",placeholder:"placeholder",readonly:"readonly",required:"required",shape:"shape",spellcheck:"spellcheck",step:"step",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autocapitalize","autocomplete","autocorrect","autofocus","clearInput","clearInputIcon","clearOnEdit","color","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","max","maxlength","min","minlength","mode","multiple","name","pattern","placeholder","readonly","required","shape","spellcheck","step","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),Vi=(()=>{let e=class Ze{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionInput","ionChange","ionComplete","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||Ze)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ze,selectors:[["ion-input-otp"]],inputs:{autocapitalize:"autocapitalize",color:"color",disabled:"disabled",fill:"fill",inputmode:"inputmode",length:"length",pattern:"pattern",readonly:"readonly",separators:"separators",shape:"shape",size:"size",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autocapitalize","color","disabled","fill","inputmode","length","pattern","readonly","separators","shape","size","type","value"],methods:["setFocus"]})],e),e})(),Hi=(()=>{let e=class Qe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Qe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Qe,selectors:[["ion-input-password-toggle"]],inputs:{color:"color",hideIcon:"hideIcon",mode:"mode",showIcon:"showIcon"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","hideIcon","mode","showIcon"]})],e),e})(),Oi=(()=>{let e=class Xe{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Xe)(m(U),m(R),m(q))};static \u0275cmp=T({type:Xe,selectors:[["ion-item"]],inputs:{button:"button",color:"color",detail:"detail",detailIcon:"detailIcon",disabled:"disabled",download:"download",href:"href",lines:"lines",mode:"mode",rel:"rel",routerAnimation:"routerAnimation",routerDirection:"routerDirection",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["button","color","detail","detailIcon","disabled","download","href","lines","mode","rel","routerAnimation","routerDirection","target","type"]})],e),e})(),Di=(()=>{let e=class Je{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Je)(m(U),m(R),m(q))};static \u0275cmp=T({type:Je,selectors:[["ion-item-divider"]],inputs:{color:"color",mode:"mode",sticky:"sticky"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","sticky"]})],e),e})(),Ei=(()=>{let e=class Ye{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ye)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ye,selectors:[["ion-item-group"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),Pi=(()=>{let e=class et{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||et)(m(U),m(R),m(q))};static \u0275cmp=T({type:et,selectors:[["ion-item-option"]],inputs:{color:"color",disabled:"disabled",download:"download",expandable:"expandable",href:"href",mode:"mode",rel:"rel",target:"target",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","download","expandable","href","mode","rel","target","type"]})],e),e})(),Ti=(()=>{let e=class tt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionSwipe"])}static \u0275fac=function(t){return new(t||tt)(m(U),m(R),m(q))};static \u0275cmp=T({type:tt,selectors:[["ion-item-options"]],inputs:{side:"side"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["side"]})],e),e})(),Ri=(()=>{let e=class nt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionDrag"])}static \u0275fac=function(t){return new(t||nt)(m(U),m(R),m(q))};static \u0275cmp=T({type:nt,selectors:[["ion-item-sliding"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled"],methods:["getOpenAmount","getSlidingRatio","open","close","closeOpened"]})],e),e})(),Fi=(()=>{let e=class ot{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ot)(m(U),m(R),m(q))};static \u0275cmp=T({type:ot,selectors:[["ion-label"]],inputs:{color:"color",mode:"mode",position:"position"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","position"]})],e),e})(),qi=(()=>{let e=class it{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||it)(m(U),m(R),m(q))};static \u0275cmp=T({type:it,selectors:[["ion-list"]],inputs:{inset:"inset",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["inset","lines","mode"],methods:["closeSlidingItems"]})],e),e})(),Ni=(()=>{let e=class at{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||at)(m(U),m(R),m(q))};static \u0275cmp=T({type:at,selectors:[["ion-list-header"]],inputs:{color:"color",lines:"lines",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","lines","mode"]})],e),e})(),$i=(()=>{let e=class st{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionLoadingDidPresent","ionLoadingWillPresent","ionLoadingWillDismiss","ionLoadingDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||st)(m(U),m(R),m(q))};static \u0275cmp=T({type:st,selectors:[["ion-loading"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",showBackdrop:"showBackdrop",spinner:"spinner",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","message","mode","showBackdrop","spinner","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),Ui=(()=>{let e=class rt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionWillOpen","ionWillClose","ionDidOpen","ionDidClose"])}static \u0275fac=function(t){return new(t||rt)(m(U),m(R),m(q))};static \u0275cmp=T({type:rt,selectors:[["ion-menu"]],inputs:{contentId:"contentId",disabled:"disabled",maxEdgeStart:"maxEdgeStart",menuId:"menuId",side:"side",swipeGesture:"swipeGesture",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["contentId","disabled","maxEdgeStart","menuId","side","swipeGesture","type"],methods:["isOpen","isActive","open","close","toggle","setOpen"]})],e),e})(),Gi=(()=>{let e=class ct{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ct)(m(U),m(R),m(q))};static \u0275cmp=T({type:ct,selectors:[["ion-menu-button"]],inputs:{autoHide:"autoHide",color:"color",disabled:"disabled",menu:"menu",mode:"mode",type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autoHide","color","disabled","menu","mode","type"]})],e),e})(),Ki=(()=>{let e=class lt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||lt)(m(U),m(R),m(q))};static \u0275cmp=T({type:lt,selectors:[["ion-menu-toggle"]],inputs:{autoHide:"autoHide",menu:"menu"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autoHide","menu"]})],e),e})(),Wi=(()=>{let e=class dt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||dt)(m(U),m(R),m(q))};static \u0275cmp=T({type:dt,selectors:[["ion-nav-link"]],inputs:{component:"component",componentProps:"componentProps",routerAnimation:"routerAnimation",routerDirection:"routerDirection"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["component","componentProps","routerAnimation","routerDirection"]})],e),e})(),Zi=(()=>{let e=class vt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||vt)(m(U),m(R),m(q))};static \u0275cmp=T({type:vt,selectors:[["ion-note"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),Qi=(()=>{let e=class gt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||gt)(m(U),m(R),m(q))};static \u0275cmp=T({type:gt,selectors:[["ion-picker"]],inputs:{mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["mode"]})],e),e})(),Xi=(()=>{let e=class ht{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||ht)(m(U),m(R),m(q))};static \u0275cmp=T({type:ht,selectors:[["ion-picker-column"]],inputs:{color:"color",disabled:"disabled",mode:"mode",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","mode","value"],methods:["setFocus"]})],e),e})(),Ji=(()=>{let e=class ut{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||ut)(m(U),m(R),m(q))};static \u0275cmp=T({type:ut,selectors:[["ion-picker-column-option"]],inputs:{color:"color",disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","value"]})],e),e})(),Yi=(()=>{let e=class pt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionPickerDidPresent","ionPickerWillPresent","ionPickerWillDismiss","ionPickerDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||pt)(m(U),m(R),m(q))};static \u0275cmp=T({type:pt,selectors:[["ion-picker-legacy"]],inputs:{animated:"animated",backdropDismiss:"backdropDismiss",buttons:"buttons",columns:"columns",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",htmlAttributes:"htmlAttributes",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","backdropDismiss","buttons","columns","cssClass","duration","enterAnimation","htmlAttributes","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss","getColumn"]})],e),e})(),e8=(()=>{let e=class mt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||mt)(m(U),m(R),m(q))};static \u0275cmp=T({type:mt,selectors:[["ion-progress-bar"]],inputs:{buffer:"buffer",color:"color",mode:"mode",reversed:"reversed",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["buffer","color","mode","reversed","type","value"]})],e),e})(),t8=(()=>{let e=class wt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||wt)(m(U),m(R),m(q))};static \u0275cmp=T({type:wt,selectors:[["ion-radio"]],inputs:{alignment:"alignment",color:"color",disabled:"disabled",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alignment","color","disabled","justify","labelPlacement","mode","name","value"]})],e),e})(),n8=(()=>{let e=class ft{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||ft)(m(U),m(R),m(q))};static \u0275cmp=T({type:ft,selectors:[["ion-radio-group"]],inputs:{allowEmptySelection:"allowEmptySelection",compareWith:"compareWith",errorText:"errorText",helperText:"helperText",name:"name",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["allowEmptySelection","compareWith","errorText","helperText","name","value"]})],e),e})(),o8=(()=>{let e=class xt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange","ionInput","ionFocus","ionBlur","ionKnobMoveStart","ionKnobMoveEnd"])}static \u0275fac=function(t){return new(t||xt)(m(U),m(R),m(q))};static \u0275cmp=T({type:xt,selectors:[["ion-range"]],inputs:{activeBarStart:"activeBarStart",color:"color",debounce:"debounce",disabled:"disabled",dualKnobs:"dualKnobs",label:"label",labelPlacement:"labelPlacement",max:"max",min:"min",mode:"mode",name:"name",pin:"pin",pinFormatter:"pinFormatter",snaps:"snaps",step:"step",ticks:"ticks",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["activeBarStart","color","debounce","disabled","dualKnobs","label","labelPlacement","max","min","mode","name","pin","pinFormatter","snaps","step","ticks","value"]})],e),e})(),i8=(()=>{let e=class Mt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionRefresh","ionPull","ionStart"])}static \u0275fac=function(t){return new(t||Mt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Mt,selectors:[["ion-refresher"]],inputs:{closeDuration:"closeDuration",disabled:"disabled",mode:"mode",pullFactor:"pullFactor",pullMax:"pullMax",pullMin:"pullMin",snapbackDuration:"snapbackDuration"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["closeDuration","disabled","mode","pullFactor","pullMax","pullMin","snapbackDuration"],methods:["complete","cancel","getProgress"]})],e),e})(),a8=(()=>{let e=class kt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||kt)(m(U),m(R),m(q))};static \u0275cmp=T({type:kt,selectors:[["ion-refresher-content"]],inputs:{pullingIcon:"pullingIcon",pullingText:"pullingText",refreshingSpinner:"refreshingSpinner",refreshingText:"refreshingText"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["pullingIcon","pullingText","refreshingSpinner","refreshingText"]})],e),e})(),s8=(()=>{let e=class yt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||yt)(m(U),m(R),m(q))};static \u0275cmp=T({type:yt,selectors:[["ion-reorder"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),r8=(()=>{let e=class zt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionItemReorder"])}static \u0275fac=function(t){return new(t||zt)(m(U),m(R),m(q))};static \u0275cmp=T({type:zt,selectors:[["ion-reorder-group"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled"],methods:["complete"]})],e),e})(),c8=(()=>{let e=class Ct{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ct)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ct,selectors:[["ion-ripple-effect"]],inputs:{type:"type"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["type"],methods:["addRipple"]})],e),e})(),l8=(()=>{let e=class _t{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||_t)(m(U),m(R),m(q))};static \u0275cmp=T({type:_t,selectors:[["ion-row"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),d8=(()=>{let e=class bt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionInput","ionChange","ionCancel","ionClear","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||bt)(m(U),m(R),m(q))};static \u0275cmp=T({type:bt,selectors:[["ion-searchbar"]],inputs:{animated:"animated",autocapitalize:"autocapitalize",autocomplete:"autocomplete",autocorrect:"autocorrect",cancelButtonIcon:"cancelButtonIcon",cancelButtonText:"cancelButtonText",clearIcon:"clearIcon",color:"color",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",inputmode:"inputmode",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",searchIcon:"searchIcon",showCancelButton:"showCancelButton",showClearButton:"showClearButton",spellcheck:"spellcheck",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","autocapitalize","autocomplete","autocorrect","cancelButtonIcon","cancelButtonText","clearIcon","color","debounce","disabled","enterkeyhint","inputmode","maxlength","minlength","mode","name","placeholder","searchIcon","showCancelButton","showClearButton","spellcheck","type","value"],methods:["setFocus","getInputElement"]})],e),e})(),v8=(()=>{let e=class Bt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange"])}static \u0275fac=function(t){return new(t||Bt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Bt,selectors:[["ion-segment"]],inputs:{color:"color",disabled:"disabled",mode:"mode",scrollable:"scrollable",selectOnFocus:"selectOnFocus",swipeGesture:"swipeGesture",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","disabled","mode","scrollable","selectOnFocus","swipeGesture","value"]})],e),e})(),g8=(()=>{let e=class St{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||St)(m(U),m(R),m(q))};static \u0275cmp=T({type:St,selectors:[["ion-segment-button"]],inputs:{contentId:"contentId",disabled:"disabled",layout:"layout",mode:"mode",type:"type",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["contentId","disabled","layout","mode","type","value"]})],e),e})(),h8=(()=>{let e=class At{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||At)(m(U),m(R),m(q))};static \u0275cmp=T({type:At,selectors:[["ion-segment-content"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),u8=(()=>{let e=class Lt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionSegmentViewScroll"])}static \u0275fac=function(t){return new(t||Lt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Lt,selectors:[["ion-segment-view"]],inputs:{disabled:"disabled"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled"]})],e),e})(),p8=(()=>{let e=class It{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange","ionCancel","ionDismiss","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||It)(m(U),m(R),m(q))};static \u0275cmp=T({type:It,selectors:[["ion-select"]],inputs:{cancelText:"cancelText",color:"color",compareWith:"compareWith",disabled:"disabled",errorText:"errorText",expandedIcon:"expandedIcon",fill:"fill",helperText:"helperText",interface:"interface",interfaceOptions:"interfaceOptions",justify:"justify",label:"label",labelPlacement:"labelPlacement",mode:"mode",multiple:"multiple",name:"name",okText:"okText",placeholder:"placeholder",required:"required",selectedText:"selectedText",shape:"shape",toggleIcon:"toggleIcon",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["cancelText","color","compareWith","disabled","errorText","expandedIcon","fill","helperText","interface","interfaceOptions","justify","label","labelPlacement","mode","multiple","name","okText","placeholder","required","selectedText","shape","toggleIcon","value"],methods:["open"]})],e),e})(),m8=(()=>{let e=class jt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||jt)(m(U),m(R),m(q))};static \u0275cmp=T({type:jt,selectors:[["ion-select-modal"]],inputs:{header:"header",multiple:"multiple",options:"options"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["header","multiple","options"]})],e),e})(),w8=(()=>{let e=class Vt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Vt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Vt,selectors:[["ion-select-option"]],inputs:{disabled:"disabled",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","value"]})],e),e})(),f8=(()=>{let e=class Ht{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ht)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ht,selectors:[["ion-skeleton-text"]],inputs:{animated:"animated"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated"]})],e),e})(),x8=(()=>{let e=class Ot{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ot)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ot,selectors:[["ion-spinner"]],inputs:{color:"color",duration:"duration",name:"name",paused:"paused"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","duration","name","paused"]})],e),e})(),M8=(()=>{let e=class Dt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionSplitPaneVisible"])}static \u0275fac=function(t){return new(t||Dt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Dt,selectors:[["ion-split-pane"]],inputs:{contentId:"contentId",disabled:"disabled",when:"when"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["contentId","disabled","when"]})],e),e})(),d3=(()=>{let e=class Et{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Et)(m(U),m(R),m(q))};static \u0275cmp=T({type:Et,selectors:[["ion-tab"]],inputs:{component:"component",tab:"tab"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["component","tab"],methods:["setActive"]})],e),e})(),Pt=(()=>{let e=class Tt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Tt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Tt,selectors:[["ion-tab-bar"]],inputs:{color:"color",mode:"mode",selectedTab:"selectedTab",translucent:"translucent"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode","selectedTab","translucent"]})],e),e})(),k8=(()=>{let e=class Rt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Rt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Rt,selectors:[["ion-tab-button"]],inputs:{disabled:"disabled",download:"download",href:"href",layout:"layout",mode:"mode",rel:"rel",selected:"selected",tab:"tab",target:"target"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["disabled","download","href","layout","mode","rel","selected","tab","target"]})],e),e})(),y8=(()=>{let e=class Ft{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Ft)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ft,selectors:[["ion-text"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),z8=(()=>{let e=class qt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange","ionInput","ionBlur","ionFocus"])}static \u0275fac=function(t){return new(t||qt)(m(U),m(R),m(q))};static \u0275cmp=T({type:qt,selectors:[["ion-textarea"]],inputs:{autoGrow:"autoGrow",autocapitalize:"autocapitalize",autofocus:"autofocus",clearOnEdit:"clearOnEdit",color:"color",cols:"cols",counter:"counter",counterFormatter:"counterFormatter",debounce:"debounce",disabled:"disabled",enterkeyhint:"enterkeyhint",errorText:"errorText",fill:"fill",helperText:"helperText",inputmode:"inputmode",label:"label",labelPlacement:"labelPlacement",maxlength:"maxlength",minlength:"minlength",mode:"mode",name:"name",placeholder:"placeholder",readonly:"readonly",required:"required",rows:"rows",shape:"shape",spellcheck:"spellcheck",value:"value",wrap:"wrap"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["autoGrow","autocapitalize","autofocus","clearOnEdit","color","cols","counter","counterFormatter","debounce","disabled","enterkeyhint","errorText","fill","helperText","inputmode","label","labelPlacement","maxlength","minlength","mode","name","placeholder","readonly","required","rows","shape","spellcheck","value","wrap"],methods:["setFocus","getInputElement"]})],e),e})(),C8=(()=>{let e=class Nt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Nt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Nt,selectors:[["ion-thumbnail"]],standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({})],e),e})(),_8=(()=>{let e=class $t{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||$t)(m(U),m(R),m(q))};static \u0275cmp=T({type:$t,selectors:[["ion-title"]],inputs:{color:"color",size:"size"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","size"]})],e),e})(),b8=(()=>{let e=class Ut{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionToastDidPresent","ionToastWillPresent","ionToastWillDismiss","ionToastDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(t){return new(t||Ut)(m(U),m(R),m(q))};static \u0275cmp=T({type:Ut,selectors:[["ion-toast"]],inputs:{animated:"animated",buttons:"buttons",color:"color",cssClass:"cssClass",duration:"duration",enterAnimation:"enterAnimation",header:"header",htmlAttributes:"htmlAttributes",icon:"icon",isOpen:"isOpen",keyboardClose:"keyboardClose",layout:"layout",leaveAnimation:"leaveAnimation",message:"message",mode:"mode",position:"position",positionAnchor:"positionAnchor",swipeGesture:"swipeGesture",translucent:"translucent",trigger:"trigger"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["animated","buttons","color","cssClass","duration","enterAnimation","header","htmlAttributes","icon","isOpen","keyboardClose","layout","leaveAnimation","message","mode","position","positionAnchor","swipeGesture","translucent","trigger"],methods:["present","dismiss","onDidDismiss","onWillDismiss"]})],e),e})(),B8=(()=>{let e=class Gt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement,m1(this,this.el,["ionChange","ionFocus","ionBlur"])}static \u0275fac=function(t){return new(t||Gt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Gt,selectors:[["ion-toggle"]],inputs:{alignment:"alignment",checked:"checked",color:"color",disabled:"disabled",enableOnOffLabels:"enableOnOffLabels",errorText:"errorText",helperText:"helperText",justify:"justify",labelPlacement:"labelPlacement",mode:"mode",name:"name",required:"required",value:"value"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["alignment","checked","color","disabled","enableOnOffLabels","errorText","helperText","justify","labelPlacement","mode","name","required","value"]})],e),e})(),S8=(()=>{let e=class Kt{z;el;constructor(n,t,i){this.z=i,n.detach(),this.el=t.nativeElement}static \u0275fac=function(t){return new(t||Kt)(m(U),m(R),m(q))};static \u0275cmp=T({type:Kt,selectors:[["ion-toolbar"]],inputs:{color:"color",mode:"mode"},standalone:!1,ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})};return e=K([W({inputs:["color","mode"]})],e),e})(),k2=(()=>{class e extends Wn{parentOutlet;outletContent;constructor(n,t,i,a,s,c,d,x){super(n,t,i,a,s,c,d,x),this.parentOutlet=x}static \u0275fac=function(t){return new(t||e)(oe("name"),oe("tabs"),m(yn),m(R),m(Bn),m(q),m(bn),m(e,12))};static \u0275cmp=T({type:e,selectors:[["ion-router-outlet"]],viewQuery:function(t,i){if(t&1&&y0(To,7,wn),t&2){let a;n0(a=o0())&&(i.outletContent=a.first)}},standalone:!1,features:[R1],ngContentSelectors:G,decls:3,vars:0,consts:[["outletContent",""]],template:function(t,i){t&1&&(N(),Mn(0,null,0),F(2),kn())},encapsulation:2})}return e})(),A8=(()=>{class e extends e3{outlet;tabBar;tabBars;tabs;static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275cmp=T({type:e,selectors:[["ion-tabs"]],contentQueries:function(t,i,a){if(t&1&&(f2(a,Pt,5),f2(a,Pt,4),f2(a,d3,4)),t&2){let s;n0(s=o0())&&(i.tabBar=s.first),n0(s=o0())&&(i.tabBars=s),n0(s=o0())&&(i.tabs=s)}},viewQuery:function(t,i){if(t&1&&y0(Ro,5,k2),t&2){let a;n0(a=o0())&&(i.outlet=a.first)}},standalone:!1,features:[R1],ngContentSelectors:qo,decls:6,vars:2,consts:[["tabsInner",""],["outlet",""],[1,"tabs-inner"],["tabs","true",3,"stackWillChange","stackDidChange",4,"ngIf"],[4,"ngIf"],["tabs","true",3,"stackWillChange","stackDidChange"]],template:function(t,i){t&1&&(N(Fo),F(0),P(1,"div",2,0),P1(3,No,2,0,"ion-router-outlet",3)(4,$o,1,0,"ng-content",4),O(),F(5,1)),t&2&&(g1(3),f1("ngIf",i.tabs.length===0),g1(),f1("ngIf",i.tabs.length>0))},dependencies:[O0,k2],styles:["[_nghost-%COMP%]{display:flex;position:absolute;inset:0;flex-direction:column;width:100%;height:100%;contain:layout size style}.tabs-inner[_ngcontent-%COMP%]{position:relative;flex:1;contain:layout size style}"]})}return e})(),L8=(()=>{class e extends Qn{constructor(n,t,i,a,s,c){super(n,t,i,a,s,c)}static \u0275fac=function(t){return new(t||e)(m(k2,8),m($n),m(Un),m(R),m(q),m(U))};static \u0275cmp=T({type:e,selectors:[["ion-back-button"]],standalone:!1,features:[R1],ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})}return e})(),I8=(()=>{class e extends Yn{constructor(n,t,i,a,s,c){super(n,t,i,a,s,c)}static \u0275fac=function(t){return new(t||e)(m(R),m(J0),m(l0),m(D0),m(q),m(U))};static \u0275cmp=T({type:e,selectors:[["ion-nav"]],standalone:!1,features:[R1],ngContentSelectors:G,decls:1,vars:0,template:function(t,i){t&1&&(N(),F(0))},encapsulation:2,changeDetection:0})}return e})(),j8=(()=>{class e extends Xn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=d0({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],standalone:!1,features:[R1]})}return e})(),V8=(()=>{class e extends Jn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=d0({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],standalone:!1,features:[R1]})}return e})(),H8=(()=>{class e extends Kn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275cmp=T({type:e,selectors:[["ion-modal"]],standalone:!1,features:[R1],decls:1,vars:1,consts:[["class","ion-delegate-host ion-page",4,"ngIf"],[1,"ion-delegate-host","ion-page"],[3,"ngTemplateOutlet"]],template:function(t,i){t&1&&P1(0,Uo,2,1,"div",0),t&2&&f1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[O0,ce],encapsulation:2,changeDetection:0})}return e})(),O8=(()=>{class e extends Gn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275cmp=T({type:e,selectors:[["ion-popover"]],standalone:!1,features:[R1],decls:1,vars:1,consts:[[3,"ngTemplateOutlet",4,"ngIf"],[3,"ngTemplateOutlet"]],template:function(t,i){t&1&&P1(0,Go,1,1,"ng-container",0),t&2&&f1("ngIf",i.isCmpOpen||i.keepContentsMounted)},dependencies:[O0,ce],encapsulation:2,changeDetection:0})}return e})(),D8={provide:le,useExisting:ne(()=>v3),multi:!0},v3=(()=>{class e extends Pn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=d0({type:e,selectors:[["ion-input","type","number","max","","formControlName",""],["ion-input","type","number","max","","formControl",""],["ion-input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(t,i){t&2&&ae("max",i._enabled?i.max:null)},standalone:!1,features:[C0([D8]),R1]})}return e})(),E8={provide:le,useExisting:ne(()=>g3),multi:!0},g3=(()=>{class e extends Tn{static \u0275fac=(()=>{let n;return function(i){return(n||(n=m0(e)))(i||e)}})();static \u0275dir=d0({type:e,selectors:[["ion-input","type","number","min","","formControlName",""],["ion-input","type","number","min","","formControl",""],["ion-input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(t,i){t&2&&ae("min",i._enabled?i.min:null)},standalone:!1,features:[C0([E8]),R1]})}return e})();var P8=(()=>{class e extends M2{angularDelegate=k0(D0);injector=k0(l0);environmentInjector=k0(J0);constructor(){super(ve)}create(n){return super.create(p0(J1({},n),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"modal")}))}static \u0275fac=function(t){return new(t||e)};static \u0275prov=X0({token:e,factory:e.\u0275fac})}return e})();var Wt=class extends M2{angularDelegate=k0(D0);injector=k0(l0);environmentInjector=k0(J0);constructor(){super(ge)}create(o){return super.create(p0(J1({},o),{delegate:this.angularDelegate.create(this.environmentInjector,this.injector,"popover")}))}},h3=(()=>{class e extends M2{constructor(){super(he)}static \u0275fac=function(t){return new(t||e)};static \u0275prov=X0({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),T8=(e,o,n)=>()=>{let t=o.defaultView;if(t&&typeof window<"u"){ue(p0(J1({},e),{_zoneGate:a=>n.run(a)}));let i="__zone_symbol__addEventListener"in o.body?"__zone_symbol__addEventListener":"addEventListener";return c3(t,{exclude:["ion-tabs"],syncQueue:!0,raf:t3,jmp:a=>n.runOutsideAngular(a),ael(a,s,c,d){a[i](s,c,d)},rel(a,s,c,d){a.removeEventListener(s,c,d)}})}},R8=[Yo,ei,ti,ni,oi,ii,ai,si,ri,ci,li,di,vi,gi,hi,ui,pi,mi,wi,fi,xi,Mi,ki,yi,zi,Ci,_i,bi,Bi,Si,Ai,Li,Ii,ji,Vi,Hi,Oi,Di,Ei,Pi,Ti,Ri,Fi,qi,Ni,$i,Ui,Gi,Ki,Wi,Zi,Qi,Xi,Ji,Yi,e8,t8,n8,o8,i8,a8,s8,r8,c8,l8,d8,v8,g8,h8,u8,p8,m8,w8,f8,x8,M8,d3,Pt,k8,y8,z8,C8,_8,b8,B8,S8],l7=[...R8,H8,O8,Ko,Wo,Zo,Qo,A8,k2,L8,I8,j8,V8,g3,v3],u3=(()=>{class e{static forRoot(n={}){return{ngModule:e,providers:[{provide:de,useValue:n},{provide:xn,useFactory:T8,multi:!0,deps:[de,mn,q]},D0,Zn()]}}static \u0275fac=function(t){return new(t||e)};static \u0275mod=fn({type:e});static \u0275inj=pn({providers:[P8,Wt],imports:[x2]})}return e})();var Zt,q8=function(){if(typeof window>"u")return new Map;if(!Zt){var e=window;e.Ionicons=e.Ionicons||{},Zt=e.Ionicons.map=e.Ionicons.map||new Map}return Zt},Qt=function(e){Object.keys(e).forEach(function(o){p3(o,e[o]);var n=o.replace(/([a-z0-9]|(?=[A-Z]))([A-Z0-9])/g,"$1-$2").toLowerCase();o!==n&&p3(n,e[o])})},p3=function(e,o){var n=q8(),t=n.get(e);t===void 0?n.set(e,o):t!==o&&console.warn('[Ionicons Warning]: Multiple icons were mapped to name "'.concat(e,'". Ensure that multiple icons are not mapped to the same icon name.'))};var m3="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M384 224v184a40 40 0 01-40 40H104a40 40 0 01-40-40V168a40 40 0 0140-40h167.48' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path d='M459.94 53.25a16.06 16.06 0 00-23.22-.56L424.35 65a8 8 0 000 11.31l11.34 11.32a8 8 0 0011.34 0l12.06-12c6.1-6.09 6.67-16.01.85-22.38zM399.34 90L218.82 270.2a9 9 0 00-2.31 3.93L208.16 299a3.91 3.91 0 004.86 4.86l24.85-8.35a9 9 0 003.93-2.31L422 112.66a9 9 0 000-12.66l-9.95-10a9 9 0 00-12.71 0z'/></svg>";var w3="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M112 112l20 320c.95 18.49 14.4 32 32 32h184c17.67 0 30.87-13.51 32-32l20-320' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/><path stroke-linecap='round' stroke-miterlimit='10' d='M80 112h352' class='ionicon-stroke-width'/><path d='M192 112V72h0a23.93 23.93 0 0124-24h80a23.93 23.93 0 0124 24h0v40M256 176v224M184 176l8 224M328 176l-8 224' stroke-linecap='round' stroke-linejoin='round' class='ionicon-fill-none ionicon-stroke-width'/></svg>";var L2=Z0(Xt());var y2,N8=new Uint8Array(16);function Jt(){if(!y2&&(y2=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||typeof msCrypto<"u"&&typeof msCrypto.getRandomValues=="function"&&msCrypto.getRandomValues.bind(msCrypto),!y2))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return y2(N8)}var M3=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;function $8(e){return typeof e=="string"&&M3.test(e)}var k3=$8;var D1=[];for(z2=0;z2<256;++z2)D1.push((z2+256).toString(16).substr(1));var z2;function U8(e){var o=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=(D1[e[o+0]]+D1[e[o+1]]+D1[e[o+2]]+D1[e[o+3]]+"-"+D1[e[o+4]]+D1[e[o+5]]+"-"+D1[e[o+6]]+D1[e[o+7]]+"-"+D1[e[o+8]]+D1[e[o+9]]+"-"+D1[e[o+10]]+D1[e[o+11]]+D1[e[o+12]]+D1[e[o+13]]+D1[e[o+14]]+D1[e[o+15]]).toLowerCase();if(!k3(n))throw TypeError("Stringified UUID is invalid");return n}var y3=U8;function G8(e,o,n){e=e||{};var t=e.random||(e.rng||Jt)();if(t[6]=t[6]&15|64,t[8]=t[8]&63|128,o){n=n||0;for(var i=0;i<16;++i)o[n+i]=t[i];return o}return y3(t)}var t2=G8;var E4=Z0(z3()),r0=Z0(t4());function Y8(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer||typeof Blob<"u"&&e instanceof Blob}function ea(e){return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type)}var r6=Function.prototype.toString,ta=r6.call(Object);function na(e){var o=Object.getPrototypeOf(e);if(o===null)return!0;var n=o.constructor;return typeof n=="function"&&n instanceof n&&r6.call(n)==ta}function O1(e){var o,n,t;if(!e||typeof e!="object")return e;if(Array.isArray(e)){for(o=[],n=0,t=e.length;n<t;n++)o[n]=O1(e[n]);return o}if(e instanceof Date&&isFinite(e))return e.toISOString();if(Y8(e))return ea(e);if(!na(e))return e;o={};for(n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var i=O1(e[n]);typeof i<"u"&&(o[n]=i)}return o}function c6(e){var o=!1;return function(...n){if(o)throw new Error("once called more than once");o=!0,e.apply(this,n)}}function l6(e){return function(...o){o=O1(o);var n=this,t=typeof o[o.length-1]=="function"?o.pop():!1,i=new Promise(function(a,s){var c;try{var d=c6(function(x,k){x?s(x):a(k)});o.push(d),c=e.apply(n,o),c&&typeof c.then=="function"&&a(c)}catch(x){s(x)}});return t&&i.then(function(a){t(null,a)},t),i}}function oa(e,o,n){if(e.constructor.listeners("debug").length){for(var t=["api",e.name,o],i=0;i<n.length-1;i++)t.push(n[i]);e.constructor.emit("debug",t);var a=n[n.length-1];n[n.length-1]=function(s,c){var d=["api",e.name,o];d=d.concat(s?["error",s]:["success",c]),e.constructor.emit("debug",d),a(s,c)}}}function L1(e,o){return l6(function(...n){if(this._closed)return Promise.reject(new Error("database is closed"));if(this._destroyed)return Promise.reject(new Error("database is destroyed"));var t=this;return oa(t,e,n),this.taskqueue.isReady?o.apply(this,n):new Promise(function(i,a){t.taskqueue.addTask(function(s){s?a(s):i(t[e].apply(t,n))})})})}function s2(e,o){for(var n={},t=0,i=o.length;t<i;t++){var a=o[t];a in e&&(n[a]=e[a])}return n}var ia=6;function H3(e){return e}function aa(e){return[{ok:e}]}function d6(e,o,n){var t=o.docs,i=new Map;t.forEach(function(A){i.has(A.id)?i.get(A.id).push(A):i.set(A.id,[A])});var a=i.size,s=0,c=new Array(a);function d(){var A=[];c.forEach(function(z){z.docs.forEach(function(u){A.push({id:z.id,docs:[u]})})}),n(null,{results:A})}function x(){++s===a&&d()}function k(A,z,u){c[A]={id:z,docs:u},x()}var l=[];i.forEach(function(A,z){l.push(z)});var M=0;function _(){if(!(M>=l.length)){var A=Math.min(M+ia,l.length),z=l.slice(M,A);B(z,M),M+=z.length}}function B(A,z){A.forEach(function(u,h){var r=z+h,v=i.get(u),w=s2(v[0],["atts_since","attachments"]);w.open_revs=v.map(function(V){return V.rev}),w.open_revs=w.open_revs.filter(H3);var f=H3;w.open_revs.length===0&&(delete w.open_revs,f=aa),["revs","attachments","binary","ajax","latest"].forEach(function(V){V in o&&(w[V]=o[V])}),e.get(u,w,function(V,D){var j;V?j=[{error:V}]:j=f(D),k(r,u,j),_()})})}_()}var p4;try{localStorage.setItem("_pouch_check_localstorage",1),p4=!!localStorage.getItem("_pouch_check_localstorage")}catch{p4=!1}function I2(){return p4}var c0=typeof queueMicrotask=="function"?queueMicrotask:function(o){Promise.resolve().then(o)},m4=class extends r0.default{constructor(){super(),this._listeners={},I2()&&addEventListener("storage",o=>{this.emit(o.key)})}addListener(o,n,t,i){if(this._listeners[n])return;var a=!1,s=this;function c(){if(!s._listeners[n])return;if(a){a="waiting";return}a=!0;var d=s2(i,["style","include_docs","attachments","conflicts","filter","doc_ids","view","since","query_params","binary","return_docs"]);function x(){a=!1}t.changes(d).on("change",function(k){k.seq>i.since&&!i.cancelled&&(i.since=k.seq,i.onChange(k))}).on("complete",function(){a==="waiting"&&c0(c),a=!1}).on("error",x)}this._listeners[n]=c,this.on(o,c)}removeListener(o,n){n in this._listeners&&(super.removeListener(o,this._listeners[n]),delete this._listeners[n])}notifyLocalWindows(o){I2()&&(localStorage[o]=localStorage[o]==="a"?"b":"a")}notify(o){this.emit(o),this.notifyLocalWindows(o)}};function G1(e){if(typeof console<"u"&&typeof console[e]=="function"){var o=Array.prototype.slice.call(arguments,1);console[e].apply(console,o)}}function sa(e,o){var n=6e5;e=parseInt(e,10)||0,o=parseInt(o,10),o!==o||o<=e?o=(e||1)<<1:o=o+1,o>n&&(e=n>>1,o=n);var t=Math.random(),i=o-e;return~~(i*t+e)}function ra(e){var o=0;return e||(o=2e3),sa(e,o)}function w4(e,o){G1("info","The above "+e+" is totally normal. "+o)}var _1=class extends Error{constructor(o,n,t){super(),this.status=o,this.name=n,this.message=t,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}},L7=new _1(401,"unauthorized","Name or password is incorrect."),ca=new _1(400,"bad_request","Missing JSON list of 'docs'"),$1=new _1(404,"not_found","missing"),q0=new _1(409,"conflict","Document update conflict"),v6=new _1(400,"bad_request","_id field must contain a string"),la=new _1(412,"missing_id","_id is required for puts"),da=new _1(400,"bad_request","Only reserved document ids may start with underscore."),I7=new _1(412,"precondition_failed","Database not open"),P4=new _1(500,"unknown_error","Database encountered an unknown error"),g6=new _1(500,"badarg","Some query argument is invalid"),j7=new _1(400,"invalid_request","Request was invalid"),va=new _1(400,"query_parse_error","Some query parameter is invalid"),O3=new _1(500,"doc_validation","Bad special document member"),F2=new _1(400,"bad_request","Something wrong with the request"),n4=new _1(400,"bad_request","Document must be a JSON object"),V7=new _1(404,"not_found","Database not found"),T4=new _1(500,"indexed_db_went_bad","unknown"),H7=new _1(500,"web_sql_went_bad","unknown"),O7=new _1(500,"levelDB_went_went_bad","unknown"),D7=new _1(403,"forbidden","Forbidden by design doc validate_doc_update function"),S2=new _1(400,"bad_request","Invalid rev format"),E7=new _1(412,"file_exists","The database could not be created, the file already exists."),ga=new _1(412,"missing_stub","A pre-existing attachment stub wasn't found"),P7=new _1(413,"invalid_url","Provided URL is invalid");function r1(e,o){function n(t){for(var i=Object.getOwnPropertyNames(e),a=0,s=i.length;a<s;a++)typeof e[i[a]]!="function"&&(this[i[a]]=e[i[a]]);this.stack===void 0&&(this.stack=new Error().stack),t!==void 0&&(this.reason=t)}return n.prototype=_1.prototype,new n(o)}function N0(e){if(typeof e!="object"){var o=e;e=P4,e.data=o}return"error"in e&&e.error==="conflict"&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=new Error().stack),e}function ha(e,o,n){try{return!e(o,n)}catch(i){var t="Filter function threw: "+i.toString();return r1(F2,t)}}function R4(e){var o={},n=e.filter&&typeof e.filter=="function";return o.query=e.query_params,function(i){i.doc||(i.doc={});var a=n&&ha(e.filter,i.doc,o);if(typeof a=="object")return a;if(a)return!1;if(!e.include_docs)delete i.doc;else if(!e.attachments)for(var s in i.doc._attachments)Object.prototype.hasOwnProperty.call(i.doc._attachments,s)&&(i.doc._attachments[s].stub=!0);return!0}}function h6(e){var o;if(e?typeof e!="string"?o=r1(v6):/^_/.test(e)&&!/^_(design|local)/.test(e)&&(o=r1(da)):o=r1(la),o)throw o}function s0(e){return typeof e._remote=="boolean"?e._remote:typeof e.type=="function"?(G1("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),e.type()==="http"):!1}function ua(e,o){return"listenerCount"in e?e.listenerCount(o):r0.default.listenerCount(e,o)}function f4(e){if(!e)return null;var o=e.split("/");return o.length===2?o:o.length===1?[e,e]:null}function D3(e){var o=f4(e);return o?o.join("/"):null}var E3=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],P3="queryKey",pa=/(?:^|&)([^&=]*)=?([^&]*)/g,ma=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/;function u6(e){for(var o=ma.exec(e),n={},t=14;t--;){var i=E3[t],a=o[t]||"",s=["user","password"].indexOf(i)!==-1;n[i]=s?decodeURIComponent(a):a}return n[P3]={},n[E3[12]].replace(pa,function(c,d,x){d&&(n[P3][d]=x)}),n}function F4(e,o){var n=[],t=[];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(n.push(i),t.push(o[i]));return n.push(e),Function.apply(null,n).apply(null,t)}function j2(e,o,n){return e.get(o).catch(function(t){if(t.status!==404)throw t;return{}}).then(function(t){var i=t._rev,a=n(t);return a?(a._id=o,a._rev=i,wa(e,a,n)):{updated:!1,rev:i}})}function wa(e,o,n){return e.put(o).then(function(t){return{updated:!0,rev:t.rev}},function(t){if(t.status!==409)throw t;return j2(e,o._id,n)})}var q4=function(e){return atob(e)},r2=function(e){return btoa(e)};function N4(e,o){e=e||[],o=o||{};try{return new Blob(e,o)}catch(a){if(a.name!=="TypeError")throw a;for(var n=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,t=new n,i=0;i<e.length;i+=1)t.append(e[i]);return t.getBlob(o.type)}}function fa(e){for(var o=e.length,n=new ArrayBuffer(o),t=new Uint8Array(n),i=0;i<o;i++)t[i]=e.charCodeAt(i);return n}function $4(e,o){return N4([fa(e)],{type:o})}function U4(e,o){return $4(q4(e),o)}function xa(e){for(var o="",n=new Uint8Array(e),t=n.byteLength,i=0;i<t;i++)o+=String.fromCharCode(n[i]);return o}function p6(e,o){var n=new FileReader,t=typeof n.readAsBinaryString=="function";n.onloadend=function(i){var a=i.target.result||"";if(t)return o(a);o(xa(a))},t?n.readAsBinaryString(e):n.readAsArrayBuffer(e)}function m6(e,o){p6(e,function(n){o(n)})}function G4(e,o){m6(e,function(n){o(r2(n))})}function Ma(e,o){var n=new FileReader;n.onloadend=function(t){var i=t.target.result||new ArrayBuffer(0);o(i)},n.readAsArrayBuffer(e)}var ka=self.setImmediate||self.setTimeout,ya=32768;function za(e){return r2(e)}function Ca(e,o,n,t,i){(n>0||t<o.size)&&(o=o.slice(n,t)),Ma(o,function(a){e.append(a),i()})}function _a(e,o,n,t,i){(n>0||t<o.length)&&(o=o.substring(n,t)),e.appendBinary(o),i()}function K4(e,o){var n=typeof e=="string",t=n?e.length:e.size,i=Math.min(ya,t),a=Math.ceil(t/i),s=0,c=n?new L2.default:new L2.default.ArrayBuffer,d=n?_a:Ca;function x(){ka(l)}function k(){var M=c.end(!0),_=za(M);o(_),c.destroy()}function l(){var M=s*i,_=M+i;s++,s<a?d(c,e,M,_,x):d(c,e,M,_,k)}l()}function w6(e){return L2.default.hash(e)}function f6(e,o){if(!o)return t2().replace(/-/g,"").toLowerCase();var n=Object.assign({},e);return delete n._rev_tree,w6(JSON.stringify(n))}var q2=t2;function I0(e){for(var o,n,t,i=e.rev_tree.slice(),a;a=i.pop();){var s=a.ids,c=s[2],d=a.pos;if(c.length){for(var x=0,k=c.length;x<k;x++)i.push({pos:d+1,ids:c[x]});continue}var l=!!s[1].deleted,M=s[0];(!o||(t!==l?t:n!==d?n<d:o<M))&&(o=M,n=d,t=l)}return n+"-"+o}function j0(e,o){for(var n=e.slice(),t;t=n.pop();)for(var i=t.pos,a=t.ids,s=a[2],c=o(s.length===0,i,a[0],t.ctx,a[1]),d=0,x=s.length;d<x;d++)n.push({pos:i+1,ids:s[d],ctx:c})}function ba(e,o){return e.pos-o.pos}function W4(e){var o=[];j0(e,function(i,a,s,c,d){i&&o.push({rev:a+"-"+s,pos:a,opts:d})}),o.sort(ba).reverse();for(var n=0,t=o.length;n<t;n++)delete o[n].pos;return o}function Z4(e){for(var o=I0(e),n=W4(e.rev_tree),t=[],i=0,a=n.length;i<a;i++){var s=n[i];s.rev!==o&&!s.opts.deleted&&t.push(s.rev)}return t}function Ba(e){var o=[];return j0(e.rev_tree,function(n,t,i,a,s){s.status==="available"&&!n&&(o.push(t+"-"+i),s.status="missing")}),o}function Sa(e,o){let n=[],t=e.slice(),i;for(;i=t.pop();){let{pos:a,ids:s}=i,c=`${a}-${s[0]}`,d=s[2];if(n.push(c),c===o){if(d.length!==0)throw new Error("The requested revision is not a leaf");return n.reverse()}(d.length===0||d.length>1)&&(n=[]);for(let x=0,k=d.length;x<k;x++)t.push({pos:a+1,ids:d[x]})}if(n.length===0)throw new Error("The requested revision does not exist");return n.reverse()}function x6(e){for(var o=[],n=e.slice(),t;t=n.pop();){var i=t.pos,a=t.ids,s=a[0],c=a[1],d=a[2],x=d.length===0,k=t.history?t.history.slice():[];k.push({id:s,opts:c}),x&&o.push({pos:i+1-k.length,ids:k});for(var l=0,M=d.length;l<M;l++)n.push({pos:i+1,ids:d[l],history:k})}return o.reverse()}function Aa(e,o){return e.pos-o.pos}function La(e,o,n){for(var t=0,i=e.length,a;t<i;)a=t+i>>>1,n(e[a],o)<0?t=a+1:i=a;return t}function Ia(e,o,n){var t=La(e,o,n);e.splice(t,0,o)}function T3(e,o){for(var n,t,i=o,a=e.length;i<a;i++){var s=e[i],c=[s.id,s.opts,[]];t?(t[2].push(c),t=c):n=t=c}return n}function ja(e,o){return e[0]<o[0]?-1:1}function R3(e,o){for(var n=[{tree1:e,tree2:o}],t=!1;n.length>0;){var i=n.pop(),a=i.tree1,s=i.tree2;(a[1].status||s[1].status)&&(a[1].status=a[1].status==="available"||s[1].status==="available"?"available":"missing");for(var c=0;c<s[2].length;c++){if(!a[2][0]){t="new_leaf",a[2][0]=s[2][c];continue}for(var d=!1,x=0;x<a[2].length;x++)a[2][x][0]===s[2][c][0]&&(n.push({tree1:a[2][x],tree2:s[2][c]}),d=!0);d||(t="new_branch",Ia(a[2],s[2][c],ja))}}return{conflicts:t,tree:e}}function M6(e,o,n){var t=[],i=!1,a=!1,s;if(!e.length)return{tree:[o],conflicts:"new_leaf"};for(var c=0,d=e.length;c<d;c++){var x=e[c];if(x.pos===o.pos&&x.ids[0]===o.ids[0])s=R3(x.ids,o.ids),t.push({pos:x.pos,ids:s.tree}),i=i||s.conflicts,a=!0;else if(n!==!0){var k=x.pos<o.pos?x:o,l=x.pos<o.pos?o:x,M=l.pos-k.pos,_=[],B=[];for(B.push({ids:k.ids,diff:M,parent:null,parentIdx:null});B.length>0;){var A=B.pop();if(A.diff===0){A.ids[0]===l.ids[0]&&_.push(A);continue}for(var z=A.ids[2],u=0,h=z.length;u<h;u++)B.push({ids:z[u],diff:A.diff-1,parent:A.ids,parentIdx:u})}var r=_[0];r?(s=R3(r.ids,l.ids),r.parent[2][r.parentIdx]=s.tree,t.push({pos:k.pos,ids:k.ids}),i=i||s.conflicts,a=!0):t.push(x)}else t.push(x)}return a||t.push(o),t.sort(Aa),{tree:t,conflicts:i||"internal_node"}}function Va(e,o){for(var n=x6(e),t,i,a=0,s=n.length;a<s;a++){var c=n[a],d=c.ids,x;if(d.length>o){t||(t={});var k=d.length-o;x={pos:c.pos+k,ids:T3(d,k)};for(var l=0;l<k;l++){var M=c.pos+l+"-"+d[l].id;t[M]=!0}}else x={pos:c.pos,ids:T3(d,0)};i?i=M6(i,x,!0).tree:i=[x]}return t&&j0(i,function(_,B,A){delete t[B+"-"+A]}),{tree:i,revs:t?Object.keys(t):[]}}function k6(e,o,n){var t=M6(e,o),i=Va(t.tree,n);return{tree:i.tree,stemmedRevs:i.revs,conflicts:t.conflicts}}function Ha(e,o){for(var n=e.slice(),t=o.split("-"),i=parseInt(t[0],10),a=t[1],s;s=n.pop();){if(s.pos===i&&s.ids[0]===a)return!0;for(var c=s.ids[2],d=0,x=c.length;d<x;d++)n.push({pos:s.pos+1,ids:c[d]})}return!1}function Oa(e){return e.ids}function g0(e,o){o||(o=I0(e));for(var n=o.substring(o.indexOf("-")+1),t=e.rev_tree.map(Oa),i;i=t.pop();){if(i[0]===n)return!!i[1].deleted;t=t.concat(i[2])}}function L0(e){return typeof e=="string"&&e.startsWith("_local/")}function Da(e,o){for(var n=o.rev_tree.slice(),t;t=n.pop();){var i=t.pos,a=t.ids,s=a[0],c=a[1],d=a[2],x=d.length===0,k=t.history?t.history.slice():[];if(k.push({id:s,pos:i,opts:c}),x)for(var l=0,M=k.length;l<M;l++){var _=k[l],B=_.pos+"-"+_.id;if(B===e)return i+"-"+s}for(var A=0,z=d.length;A<z;A++)n.push({pos:i+1,ids:d[A],history:k})}throw new Error("Unable to resolve latest revision for id "+o.id+", rev "+e)}function Ea(e,o,n,t){try{e.emit("change",o,n,t)}catch(i){G1("error",'Error in .on("change", function):',i)}}function Pa(e,o,n){var t=[{rev:e._rev}];n.style==="all_docs"&&(t=W4(o.rev_tree).map(function(a){return{rev:a.rev}}));var i={id:o.id,changes:t,doc:e};return g0(o,e._rev)&&(i.deleted=!0),n.conflicts&&(i.doc._conflicts=Z4(o),i.doc._conflicts.length||delete i.doc._conflicts),i}var x4=class extends r0.default{constructor(o,n,t){super(),this.db=o,n=n?O1(n):{};var i=n.complete=c6((c,d)=>{c?ua(this,"error")>0&&this.emit("error",c):this.emit("complete",d),this.removeAllListeners(),o.removeListener("destroyed",a)});t&&(this.on("complete",function(c){t(null,c)}),this.on("error",t));let a=()=>{this.cancel()};o.once("destroyed",a),n.onChange=(c,d,x)=>{this.isCancelled||Ea(this,c,d,x)};var s=new Promise(function(c,d){n.complete=function(x,k){x?d(x):c(k)}});this.once("cancel",function(){o.removeListener("destroyed",a),n.complete(null,{status:"cancelled"})}),this.then=s.then.bind(s),this.catch=s.catch.bind(s),this.then(function(c){i(null,c)},i),o.taskqueue.isReady?this.validateChanges(n):o.taskqueue.addTask(c=>{c?n.complete(c):this.isCancelled?this.emit("cancel"):this.validateChanges(n)})}cancel(){this.isCancelled=!0,this.db.taskqueue.isReady&&this.emit("cancel")}validateChanges(o){var n=o.complete;l1._changesFilterPlugin?l1._changesFilterPlugin.validate(o,t=>{if(t)return n(t);this.doChanges(o)}):this.doChanges(o)}doChanges(o){var n=o.complete;if(o=O1(o),"live"in o&&!("continuous"in o)&&(o.continuous=o.live),o.processChange=Pa,o.since==="latest"&&(o.since="now"),o.since||(o.since=0),o.since==="now"){this.db.info().then(i=>{if(this.isCancelled){n(null,{status:"cancelled"});return}o.since=i.update_seq,this.doChanges(o)},n);return}if(l1._changesFilterPlugin){if(l1._changesFilterPlugin.normalize(o),l1._changesFilterPlugin.shouldFilter(this,o))return l1._changesFilterPlugin.filter(this,o)}else["doc_ids","filter","selector","view"].forEach(function(i){i in o&&G1("warn",'The "'+i+'" option was passed in to changes/replicate, but pouchdb-changes-filter plugin is not installed, so it was ignored. Please install the plugin to enable filtering.')});"descending"in o||(o.descending=!1),o.limit=o.limit===0?1:o.limit,o.complete=n;var t=this.db._changes(o);if(t&&typeof t.cancel=="function"){let i=this.cancel;this.cancel=(...a)=>{t.cancel(),i.apply(this,a)}}}};function o4(e,o){return function(n,t){n||t[0]&&t[0].error?(n=n||t[0],n.docId=o,e(n)):e(null,t.length?t[0]:t)}}function Ta(e){for(var o=0;o<e.length;o++){var n=e[o];if(n._deleted)delete n._attachments;else if(n._attachments)for(var t=Object.keys(n._attachments),i=0;i<t.length;i++){var a=t[i];n._attachments[a]=s2(n._attachments[a],["data","digest","content_type","length","revpos","stub"])}}}function Ra(e,o){if(e._id===o._id){let n=e._revisions?e._revisions.start:0,t=o._revisions?o._revisions.start:0;return n-t}return e._id<o._id?-1:1}function Fa(e){var o={},n=[];return j0(e,function(t,i,a,s){var c=i+"-"+a;return t&&(o[c]=0),s!==void 0&&n.push({from:s,to:c}),c}),n.reverse(),n.forEach(function(t){o[t.from]===void 0?o[t.from]=1+o[t.to]:o[t.from]=Math.min(o[t.from],1+o[t.to])}),o}function qa(e){var o="limit"in e?e.keys.slice(e.skip,e.limit+e.skip):e.skip>0?e.keys.slice(e.skip):e.keys;e.keys=o,e.skip=0,delete e.limit,e.descending&&(o.reverse(),e.descending=!1)}function y6(e){var o=e._compactionQueue[0],n=o.opts,t=o.callback;e.get("_local/compaction").catch(function(){return!1}).then(function(i){i&&i.last_seq&&(n.last_seq=i.last_seq),e._compact(n,function(a,s){a?t(a):t(null,s),c0(function(){e._compactionQueue.shift(),e._compactionQueue.length&&y6(e)})})})}function Na(e,o,n){return e.get("_local/purges").then(function(t){let i=t.purgeSeq+1;return t.purges.push({docId:o,rev:n,purgeSeq:i}),t.purges.length>self.purged_infos_limit&&t.purges.splice(0,t.purges.length-self.purged_infos_limit),t.purgeSeq=i,t}).catch(function(t){if(t.status!==404)throw t;return{_id:"_local/purges",purges:[{docId:o,rev:n,purgeSeq:0}],purgeSeq:0}}).then(function(t){return e.put(t)})}function $a(e){return e.charAt(0)==="_"?e+" is not a valid attachment name, attachment names cannot start with '_'":!1}function i4(e){return e===null||typeof e!="object"||Array.isArray(e)}var Ua=/^\d+-[^-]*$/;function a4(e){return typeof e=="string"&&Ua.test(e)}var V2=class extends r0.default{_setup(){this.post=L1("post",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),i4(o))return t(r1(n4));this.bulkDocs({docs:[o]},n,o4(t,o._id))}).bind(this),this.put=L1("put",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),i4(o))return t(r1(n4));if(h6(o._id),"_rev"in o&&!a4(o._rev))return t(r1(S2));if(L0(o._id)&&typeof this._putLocal=="function")return o._deleted?this._removeLocal(o,t):this._putLocal(o,t);let i=s=>{typeof this._put=="function"&&n.new_edits!==!1?this._put(o,n,s):this.bulkDocs({docs:[o]},n,o4(s,o._id))};n.force&&o._rev?(a(),i(function(s){var c=s?null:{ok:!0,id:o._id,rev:o._rev};t(s,c)})):i(t);function a(){var s=o._rev.split("-"),c=s[1],d=parseInt(s[0],10),x=d+1,k=f6();o._revisions={start:x,ids:[k,c]},o._rev=x+"-"+k,n.new_edits=!1}}).bind(this),this.putAttachment=L1("putAttachment",function(o,n,t,i,a){var s=this;typeof a=="function"&&(a=i,i=t,t=null),typeof a>"u"&&(a=i,i=t,t=null),a||G1("warn","Attachment",n,"on document",o,"is missing content_type");function c(d){var x="_rev"in d?parseInt(d._rev,10):0;return d._attachments=d._attachments||{},d._attachments[n]={content_type:a,data:i,revpos:++x},s.put(d)}return s.get(o).then(function(d){if(d._rev!==t)throw r1(q0);return c(d)},function(d){if(d.reason===$1.message)return c({_id:o});throw d})}).bind(this),this.removeAttachment=L1("removeAttachment",function(o,n,t,i){this.get(o,(a,s)=>{if(a){i(a);return}if(s._rev!==t){i(r1(q0));return}if(!s._attachments)return i();delete s._attachments[n],Object.keys(s._attachments).length===0&&delete s._attachments,this.put(s,i)})}).bind(this),this.remove=L1("remove",function(o,n,t,i){var a;typeof n=="string"?(a={_id:o,_rev:n},typeof t=="function"&&(i=t,t={})):(a=o,typeof n=="function"?(i=n,t={}):(i=t,t=n)),t=t||{},t.was_delete=!0;var s={_id:a._id,_rev:a._rev||t.rev};if(s._deleted=!0,L0(s._id)&&typeof this._removeLocal=="function")return this._removeLocal(a,i);this.bulkDocs({docs:[s]},t,o4(i,s._id))}).bind(this),this.revsDiff=L1("revsDiff",function(o,n,t){typeof n=="function"&&(t=n,n={});var i=Object.keys(o);if(!i.length)return t(null,{});var a=0,s=new Map;function c(x,k){s.has(x)||s.set(x,{missing:[]}),s.get(x).missing.push(k)}function d(x,k){var l=o[x].slice(0);j0(k,function(M,_,B,A,z){var u=_+"-"+B,h=l.indexOf(u);h!==-1&&(l.splice(h,1),z.status!=="available"&&c(x,u))}),l.forEach(function(M){c(x,M)})}i.forEach(function(x){this._getRevisionTree(x,function(k,l){if(k&&k.status===404&&k.message==="missing")s.set(x,{missing:o[x]});else{if(k)return t(k);d(x,l)}if(++a===i.length){var M={};return s.forEach(function(_,B){M[B]=_}),t(null,M)}})},this)}).bind(this),this.bulkGet=L1("bulkGet",function(o,n){d6(this,o,n)}).bind(this),this.compactDocument=L1("compactDocument",function(o,n,t){this._getRevisionTree(o,(i,a)=>{if(i)return t(i);var s=Fa(a),c=[],d=[];Object.keys(s).forEach(function(x){s[x]>n&&c.push(x)}),j0(a,function(x,k,l,M,_){var B=k+"-"+l;_.status==="available"&&c.indexOf(B)!==-1&&d.push(B)}),this._doCompaction(o,d,t)})}).bind(this),this.compact=L1("compact",function(o,n){typeof o=="function"&&(n=o,o={}),o=o||{},this._compactionQueue=this._compactionQueue||[],this._compactionQueue.push({opts:o,callback:n}),this._compactionQueue.length===1&&y6(this)}).bind(this),this.get=L1("get",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),n=n||{},typeof o!="string")return t(r1(v6));if(L0(o)&&typeof this._getLocal=="function")return this._getLocal(o,t);var i=[];let a=()=>{var d=[],x=i.length;if(!x)return t(null,d);i.forEach(k=>{this.get(o,{rev:k,revs:n.revs,latest:n.latest,attachments:n.attachments,binary:n.binary},function(l,M){if(l)d.push({missing:k});else{for(var _,B=0,A=d.length;B<A;B++)if(d[B].ok&&d[B].ok._rev===M._rev){_=!0;break}_||d.push({ok:M})}x--,x||t(null,d)})})};if(n.open_revs){if(n.open_revs==="all")this._getRevisionTree(o,function(d,x){if(d)return t(d);i=W4(x).map(function(k){return k.rev}),a()});else if(Array.isArray(n.open_revs)){i=n.open_revs;for(var s=0;s<i.length;s++){var c=i[s];if(!a4(c))return t(r1(S2))}a()}else return t(r1(P4,"function_clause"));return}return this._get(o,n,(d,x)=>{if(d)return d.docId=o,t(d);var k=x.doc,l=x.metadata,M=x.ctx;if(n.conflicts){var _=Z4(l);_.length&&(k._conflicts=_)}if(g0(l,k._rev)&&(k._deleted=!0),n.revs||n.revs_info){for(var B=k._rev.split("-"),A=parseInt(B[0],10),z=B[1],u=x6(l.rev_tree),h=null,r=0;r<u.length;r++){var v=u[r];let e1=v.ids.findIndex(i1=>i1.id===z);var w=e1===A-1;(w||!h&&e1!==-1)&&(h=v)}if(!h)return d=new Error("invalid rev tree"),d.docId=o,t(d);let Y=k._rev.split("-")[1],s1=h.ids.findIndex(e1=>e1.id===Y)+1;var f=h.ids.length-s1;if(h.ids.splice(s1,f),h.ids.reverse(),n.revs&&(k._revisions={start:h.pos+h.ids.length-1,ids:h.ids.map(function(e1){return e1.id})}),n.revs_info){var V=h.pos+h.ids.length;k._revs_info=h.ids.map(function(e1){return V--,{rev:V+"-"+e1.id,status:e1.opts.status}})}}if(n.attachments&&k._attachments){var D=k._attachments,j=Object.keys(D).length;if(j===0)return t(null,k);Object.keys(D).forEach(Y=>{this._getAttachment(k._id,Y,D[Y],{binary:n.binary,metadata:l,ctx:M},function(s1,e1){var i1=k._attachments[Y];i1.data=e1,delete i1.stub,delete i1.length,--j||t(null,k)})})}else{if(k._attachments)for(var n1 in k._attachments)Object.prototype.hasOwnProperty.call(k._attachments,n1)&&(k._attachments[n1].stub=!0);t(null,k)}})}).bind(this),this.getAttachment=L1("getAttachment",function(o,n,t,i){t instanceof Function&&(i=t,t={}),this._get(o,t,(a,s)=>{if(a)return i(a);if(s.doc._attachments&&s.doc._attachments[n])t.ctx=s.ctx,t.binary=!0,t.metadata=s.metadata,this._getAttachment(o,n,s.doc._attachments[n],t,i);else return i(r1($1))})}).bind(this),this.allDocs=L1("allDocs",function(o,n){if(typeof o=="function"&&(n=o,o={}),o.skip=typeof o.skip<"u"?o.skip:0,o.start_key&&(o.startkey=o.start_key),o.end_key&&(o.endkey=o.end_key),"keys"in o){if(!Array.isArray(o.keys))return n(new TypeError("options.keys must be an array"));var t=["startkey","endkey","key"].filter(function(i){return i in o})[0];if(t){n(r1(va,"Query parameter `"+t+"` is not compatible with multi-get"));return}if(!s0(this)&&(qa(o),o.keys.length===0))return this._allDocs({limit:0},n)}return this._allDocs(o,n)}).bind(this),this.close=L1("close",function(o){return this._closed=!0,this.emit("closed"),this._close(o)}).bind(this),this.info=L1("info",function(o){this._info((n,t)=>{if(n)return o(n);t.db_name=t.db_name||this.name,t.auto_compaction=!!(this.auto_compaction&&!s0(this)),t.adapter=this.adapter,o(null,t)})}).bind(this),this.id=L1("id",function(o){return this._id(o)}).bind(this),this.bulkDocs=L1("bulkDocs",function(o,n,t){if(typeof n=="function"&&(t=n,n={}),n=n||{},Array.isArray(o)&&(o={docs:o}),!o||!o.docs||!Array.isArray(o.docs))return t(r1(ca));for(var i=0;i<o.docs.length;++i){let d=o.docs[i];if(i4(d))return t(r1(n4));if("_rev"in d&&!a4(d._rev))return t(r1(S2))}var a;if(o.docs.forEach(function(d){d._attachments&&Object.keys(d._attachments).forEach(function(x){a=a||$a(x),d._attachments[x].content_type||G1("warn","Attachment",x,"on document",d._id,"is missing content_type")})}),a)return t(r1(F2,a));"new_edits"in n||("new_edits"in o?n.new_edits=o.new_edits:n.new_edits=!0);var s=this;!n.new_edits&&!s0(s)&&o.docs.sort(Ra),Ta(o.docs);var c=o.docs.map(function(d){return d._id});this._bulkDocs(o,n,function(d,x){if(d)return t(d);if(n.new_edits||(x=x.filter(function(M){return M.error})),!s0(s))for(var k=0,l=x.length;k<l;k++)x[k].id=x[k].id||c[k];t(null,x)})}).bind(this),this.registerDependentDatabase=L1("registerDependentDatabase",function(o,n){var t=O1(this.__opts);this.__opts.view_adapter&&(t.adapter=this.__opts.view_adapter);var i=new this.constructor(o,t);function a(s){return s.dependentDbs=s.dependentDbs||{},s.dependentDbs[o]?!1:(s.dependentDbs[o]=!0,s)}j2(this,"_local/_pouch_dependentDbs",a).then(function(){n(null,{db:i})}).catch(n)}).bind(this),this.destroy=L1("destroy",function(o,n){typeof o=="function"&&(n=o,o={});var t="use_prefix"in this?this.use_prefix:!0;let i=()=>{this._destroy(o,(a,s)=>{if(a)return n(a);this._destroyed=!0,this.emit("destroyed"),n(null,s||{ok:!0})})};if(s0(this))return i();this.get("_local/_pouch_dependentDbs",(a,s)=>{if(a)return a.status!==404?n(a):i();var c=s.dependentDbs,d=this.constructor,x=Object.keys(c).map(k=>{var l=t?k.replace(new RegExp("^"+d.prefix),""):k;return new d(l,this.__opts).destroy()});Promise.all(x).then(i,n)})}).bind(this)}_compact(o,n){var t={return_docs:!1,last_seq:o.last_seq||0,since:o.last_seq||0},i=[],a,s=0;let c=k=>{this.activeTasks.update(a,{completed_items:++s}),i.push(this.compactDocument(k.id,0))},d=k=>{this.activeTasks.remove(a,k),n(k)},x=k=>{var l=k.last_seq;Promise.all(i).then(()=>j2(this,"_local/compaction",M=>!M.last_seq||M.last_seq<l?(M.last_seq=l,M):!1)).then(()=>{this.activeTasks.remove(a),n(null,{ok:!0})}).catch(d)};this.info().then(k=>{a=this.activeTasks.add({name:"database_compaction",total_items:k.update_seq-t.last_seq}),this.changes(t).on("change",c).on("complete",x).on("error",d)})}changes(o,n){return typeof o=="function"&&(n=o,o={}),o=o||{},o.return_docs="return_docs"in o?o.return_docs:!o.live,new x4(this,o,n)}type(){return typeof this._type=="function"?this._type():this.adapter}};V2.prototype.purge=L1("_purge",function(e,o,n){if(typeof this._purge>"u")return n(r1(P4,"Purge is not implemented in the "+this.adapter+" adapter."));var t=this;t._getRevisionTree(e,(i,a)=>{if(i)return n(i);if(!a)return n(r1($1));let s;try{s=Sa(a,o)}catch(c){return n(c.message||c)}t._purge(e,s,(c,d)=>{if(c)return n(c);Na(t,e,o).then(function(){return n(null,d)})})})});var M4=class{constructor(){this.isReady=!1,this.failed=!1,this.queue=[]}execute(){var o;if(this.failed)for(;o=this.queue.shift();)o(this.failed);else for(;o=this.queue.shift();)o()}fail(o){this.failed=o,this.execute()}ready(o){this.isReady=!0,this.db=o,this.execute()}addTask(o){this.queue.push(o),this.failed&&this.execute()}};function Ga(e,o){var n=e.match(/([a-z-]*):\/\/(.*)/);if(n)return{name:/https?/.test(n[1])?n[1]+"://"+n[2]:n[2],adapter:n[1]};var t=l1.adapters,i=l1.preferredAdapters,a=l1.prefix,s=o.adapter;if(!s)for(var c=0;c<i.length;++c){if(s=i[c],s==="idb"&&"websql"in t&&I2()&&localStorage["_pouch__websqldb_"+a+e]){G1("log",'PouchDB is downgrading "'+e+'" to WebSQL to avoid data loss, because it was already opened with WebSQL.');continue}break}var d=t[s],x=d&&"use_prefix"in d?d.use_prefix:!0;return{name:x?a+e:e,adapter:s}}function Ka(e,o){e.prototype=Object.create(o.prototype,{constructor:{value:e}})}function z6(e,o){let n=function(...t){if(!(this instanceof n))return new n(...t);o.apply(this,t)};return Ka(n,e),n}function Wa(e){function o(t){e.removeListener("closed",n),t||e.constructor.emit("destroyed",e.name)}function n(){e.removeListener("destroyed",o),e.constructor.emit("unref",e)}e.once("destroyed",o),e.once("closed",n),e.constructor.emit("ref",e)}var H2=class extends V2{constructor(o,n){super(),this._setup(o,n)}_setup(o,n){if(super._setup(),n=n||{},o&&typeof o=="object"&&(n=o,o=n.name,delete n.name),n.deterministic_revs===void 0&&(n.deterministic_revs=!0),this.__opts=n=O1(n),this.auto_compaction=n.auto_compaction,this.purged_infos_limit=n.purged_infos_limit||1e3,this.prefix=l1.prefix,typeof o!="string")throw new Error("Missing/invalid DB name");var t=(n.prefix||"")+o,i=Ga(t,n);if(n.name=i.name,n.adapter=n.adapter||i.adapter,this.name=o,this._adapter=n.adapter,l1.emit("debug",["adapter","Picked adapter: ",n.adapter]),!l1.adapters[n.adapter]||!l1.adapters[n.adapter].valid())throw new Error("Invalid Adapter: "+n.adapter);if(n.view_adapter&&(!l1.adapters[n.view_adapter]||!l1.adapters[n.view_adapter].valid()))throw new Error("Invalid View Adapter: "+n.view_adapter);this.taskqueue=new M4,this.adapter=n.adapter,l1.adapters[n.adapter].call(this,n,a=>{if(a)return this.taskqueue.fail(a);Wa(this),this.emit("created",this),l1.emit("created",this.name),this.taskqueue.ready(this)})}},l1=z6(H2,function(e,o){H2.prototype._setup.call(this,e,o)}),C6=fetch,R0=Headers,k4=class{constructor(){this.tasks={}}list(){return Object.values(this.tasks)}add(o){let n=t2();return this.tasks[n]={id:n,name:o.name,total_items:o.total_items,created_at:new Date().toJSON()},n}get(o){return this.tasks[o]}remove(o,n){return delete this.tasks[o],this.tasks}update(o,n){let t=this.tasks[o];if(typeof t<"u"){let i={id:t.id,name:t.name,created_at:t.created_at,total_items:n.total_items||t.total_items,completed_items:n.completed_items||t.completed_items,updated_at:new Date().toJSON()};this.tasks[o]=i}return this.tasks}};l1.adapters={};l1.preferredAdapters=[];l1.prefix="_pouch_";var F3=new r0.default;function Za(e){Object.keys(r0.default.prototype).forEach(function(n){typeof r0.default.prototype[n]=="function"&&(e[n]=F3[n].bind(F3))});var o=e._destructionListeners=new Map;e.on("ref",function(t){o.has(t.name)||o.set(t.name,[]),o.get(t.name).push(t)}),e.on("unref",function(t){if(o.has(t.name)){var i=o.get(t.name),a=i.indexOf(t);a<0||(i.splice(a,1),i.length>1?o.set(t.name,i):o.delete(t.name))}}),e.on("destroyed",function(t){if(o.has(t)){var i=o.get(t);o.delete(t),i.forEach(function(a){a.emit("destroyed",!0)})}})}Za(l1);l1.adapter=function(e,o,n){o.valid()&&(l1.adapters[e]=o,n&&l1.preferredAdapters.push(e))};l1.plugin=function(e){if(typeof e=="function")e(l1);else{if(typeof e!="object"||Object.keys(e).length===0)throw new Error('Invalid plugin: got "'+e+'", expected an object or a function');Object.keys(e).forEach(function(o){l1.prototype[o]=e[o]})}return this.__defaults&&(l1.__defaults=Object.assign({},this.__defaults)),l1};l1.defaults=function(e){let o=z6(l1,function(n,t){t=t||{},n&&typeof n=="object"&&(t=n,n=t.name,delete t.name),t=Object.assign({},o.__defaults,t),l1.call(this,n,t)});return o.preferredAdapters=l1.preferredAdapters.slice(),Object.keys(l1).forEach(function(n){n in o||(o[n]=l1[n])}),o.__defaults=Object.assign({},this.__defaults,e),o};l1.fetch=function(e,o){return C6(e,o)};l1.prototype.activeTasks=l1.activeTasks=new k4;var Qa="9.0.0";function Q4(e,o){for(var n=e,t=0,i=o.length;t<i;t++){var a=o[t];if(n=n[a],!n)break}return n}function Xa(e,o){return e<o?-1:e>o?1:0}function X4(e){for(var o=[],n="",t=0,i=e.length;t<i;t++){var a=e[t];t>0&&e[t-1]==="\\"&&(a==="$"||a===".")?n=n.substring(0,n.length-1)+a:a==="."?(o.push(n),n=""):n+=a}return o.push(n),o}var Ja=["$or","$nor","$not"];function _6(e){return Ja.indexOf(e)>-1}function b6(e){return Object.keys(e)[0]}function Ya(e){return e[b6(e)]}function i2(e){var o={},n={$or:!0,$nor:!0};return e.forEach(function(t){Object.keys(t).forEach(function(i){var a=t[i];if(typeof a!="object"&&(a={$eq:a}),_6(i))if(a instanceof Array){if(n[i]){n[i]=!1,o[i]=a;return}var s=[];o[i].forEach(function(d){Object.keys(a).forEach(function(x){var k=a[x],l=Math.max(Object.keys(d).length,Object.keys(k).length),M=i2([d,k]);Object.keys(M).length<=l||s.push(M)})}),o[i]=s}else o[i]=i2([a]);else{var c=o[i]=o[i]||{};Object.keys(a).forEach(function(d){var x=a[d];if(d==="$gt"||d==="$gte")return es(d,x,c);if(d==="$lt"||d==="$lte")return ts(d,x,c);if(d==="$ne")return ns(x,c);if(d==="$eq")return os(x,c);if(d==="$regex")return is(x,c);c[d]=x})}})}),o}function es(e,o,n){typeof n.$eq<"u"||(typeof n.$gte<"u"?e==="$gte"?o>n.$gte&&(n.$gte=o):o>=n.$gte&&(delete n.$gte,n.$gt=o):typeof n.$gt<"u"?e==="$gte"?o>n.$gt&&(delete n.$gt,n.$gte=o):o>n.$gt&&(n.$gt=o):n[e]=o)}function ts(e,o,n){typeof n.$eq<"u"||(typeof n.$lte<"u"?e==="$lte"?o<n.$lte&&(n.$lte=o):o<=n.$lte&&(delete n.$lte,n.$lt=o):typeof n.$lt<"u"?e==="$lte"?o<n.$lt&&(delete n.$lt,n.$lte=o):o<n.$lt&&(n.$lt=o):n[e]=o)}function ns(e,o){"$ne"in o?o.$ne.push(e):o.$ne=[e]}function os(e,o){delete o.$gt,delete o.$gte,delete o.$lt,delete o.$lte,delete o.$ne,o.$eq=e}function is(e,o){"$regex"in o?o.$regex.push(e):o.$regex=[e]}function B6(e){for(var o in e){if(Array.isArray(e))for(var n in e)e[n].$and&&(e[n]=i2(e[n].$and));var t=e[o];typeof t=="object"&&B6(t)}return e}function S6(e,o){for(var n in e){n==="$and"&&(o=!0);var t=e[n];typeof t=="object"&&(o=S6(t,o))}return o}function as(e){var o=O1(e);S6(o,!1)&&(o=B6(o),"$and"in o&&(o=i2(o.$and))),["$or","$nor"].forEach(function(s){s in o&&o[s].forEach(function(c){for(var d=Object.keys(c),x=0;x<d.length;x++){var k=d[x],l=c[k];(typeof l!="object"||l===null)&&(c[k]={$eq:l})}})}),"$not"in o&&(o.$not=i2([o.$not]));for(var n=Object.keys(o),t=0;t<n.length;t++){var i=n[t],a=o[i];(typeof a!="object"||a===null)&&(a={$eq:a}),o[i]=a}return y4(o),o}function y4(e){Object.keys(e).forEach(function(o){var n=e[o];Array.isArray(n)?n.forEach(function(t){t&&typeof t=="object"&&y4(t)}):o==="$ne"?e.$ne=[n]:o==="$regex"?e.$regex=[n]:n&&typeof n=="object"&&y4(n)})}function ss(e,o,n){for(var t="",i=n-e.length;t.length<i;)t+=o;return t}function rs(e,o,n){var t=ss(e,o,n);return t+e}var A6=-324,z4=3,C4="";function I1(e,o){if(e===o)return 0;e=V0(e),o=V0(o);var n=_4(e),t=_4(o);if(n-t!==0)return n-t;switch(typeof e){case"number":return e-o;case"boolean":return e<o?-1:1;case"string":return hs(e,o)}return Array.isArray(e)?gs(e,o):us(e,o)}function V0(e){switch(typeof e){case"undefined":return null;case"number":return e===1/0||e===-1/0||isNaN(e)?null:e;case"object":var o=e;if(Array.isArray(e)){var n=e.length;e=new Array(n);for(var t=0;t<n;t++)e[t]=V0(o[t])}else{if(e instanceof Date)return e.toJSON();if(e!==null){e={};for(var i in o)if(Object.prototype.hasOwnProperty.call(o,i)){var a=o[i];typeof a<"u"&&(e[i]=V0(a))}}}}return e}function cs(e){if(e!==null)switch(typeof e){case"boolean":return e?1:0;case"number":return ps(e);case"string":return e.replace(/\u0002/g,"").replace(/\u0001/g,"").replace(/\u0000/g,"");case"object":var o=Array.isArray(e),n=o?e:Object.keys(e),t=-1,i=n.length,a="";if(o)for(;++t<i;)a+=Q1(n[t]);else for(;++t<i;){var s=n[t];a+=Q1(s)+Q1(e[s])}return a}return""}function Q1(e){var o="\0";return e=V0(e),_4(e)+C4+cs(e)+o}function ls(e,o){var n=o,t,i=e[o]==="1";if(i)t=0,o++;else{var a=e[o]==="0";o++;var s="",c=e.substring(o,o+z4),d=parseInt(c,10)+A6;for(a&&(d=-d),o+=z4;;){var x=e[o];if(x==="\0")break;s+=x,o++}s=s.split("."),s.length===1?t=parseInt(s,10):t=parseFloat(s[0]+"."+s[1]),a&&(t=t-10),d!==0&&(t=parseFloat(t+"e"+d))}return{num:t,length:o-n}}function ds(e,o){var n=e.pop();if(o.length){var t=o[o.length-1];n===t.element&&(o.pop(),t=o[o.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(n);else if(a===e.length-2){var s=e.pop();i[s]=n}else e.push(n)}}function vs(e){for(var o=[],n=[],t=0;;){var i=e[t++];if(i==="\0"){if(o.length===1)return o.pop();ds(o,n);continue}switch(i){case"1":o.push(null);break;case"2":o.push(e[t]==="1"),t++;break;case"3":var a=ls(e,t);o.push(a.num),t+=a.length;break;case"4":for(var s="";;){var c=e[t];if(c==="\0")break;s+=c,t++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"").replace(/\u0002\u0002/g,""),o.push(s);break;case"5":var d={element:[],index:o.length};o.push(d.element),n.push(d);break;case"6":var x={element:{},index:o.length};o.push(x.element),n.push(x);break;default:throw new Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}function gs(e,o){for(var n=Math.min(e.length,o.length),t=0;t<n;t++){var i=I1(e[t],o[t]);if(i!==0)return i}return e.length===o.length?0:e.length>o.length?1:-1}function hs(e,o){return e===o?0:e>o?1:-1}function us(e,o){for(var n=Object.keys(e),t=Object.keys(o),i=Math.min(n.length,t.length),a=0;a<i;a++){var s=I1(n[a],t[a]);if(s!==0||(s=I1(e[n[a]],o[t[a]]),s!==0))return s}return n.length===t.length?0:n.length>t.length?1:-1}function _4(e){var o=["boolean","number","string","object"],n=o.indexOf(typeof e);if(~n)return e===null?1:Array.isArray(e)?5:n<3?n+2:n+3;if(Array.isArray(e))return 5}function ps(e){if(e===0)return"1";var o=e.toExponential().split(/e\+?/),n=parseInt(o[1],10),t=e<0,i=t?"0":"2",a=(t?-n:n)-A6,s=rs(a.toString(),"0",z4);i+=C4+s;var c=Math.abs(parseFloat(o[0]));t&&(c=10-c);var d=c.toFixed(20);return d=d.replace(/\.?0+$/,""),i+=C4+d,i}function ms(e){function o(n){return e.map(function(t){var i=b6(t),a=X4(i),s=Q4(n,a);return s})}return function(n,t){var i=o(n.doc),a=o(t.doc),s=I1(i,a);return s!==0?s:Xa(n.doc._id,t.doc._id)}}function ws(e,o,n){if(e=e.filter(function(s){return F0(s.doc,o.selector,n)}),o.sort){var t=ms(o.sort);e=e.sort(t),typeof o.sort[0]!="string"&&Ya(o.sort[0])==="desc"&&(e=e.reverse())}if("limit"in o||"skip"in o){var i=o.skip||0,a=("limit"in o?o.limit:e.length)+i;e=e.slice(i,a)}return e}function F0(e,o,n){return n.every(function(t){var i=o[t],a=X4(t),s=Q4(e,a);return _6(t)?fs(t,i,e):O2(i,e,a,s)})}function O2(e,o,n,t){return e?typeof e=="object"?Object.keys(e).every(function(i){var a=e[i];if(i.indexOf("$")===0)return q3(i,o,a,n,t);var s=X4(i);if(t===void 0&&typeof a!="object"&&s.length>0)return!1;var c=Q4(t,s);return typeof a=="object"?O2(a,o,n,c):q3("$eq",o,a,s,c)}):e===t:!0}function fs(e,o,n){return e==="$or"?o.some(function(t){return F0(n,t,Object.keys(t))}):e==="$not"?!F0(n,o,Object.keys(o)):!o.find(function(t){return F0(n,t,Object.keys(t))})}function q3(e,o,n,t,i){if(!$3[e])throw new Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return $3[e](o,n,t,i)}function n2(e){return typeof e<"u"&&e!==null}function _0(e){return typeof e<"u"}function xs(e,o){if(typeof e!="number"||parseInt(e,10)!==e)return!1;var n=o[0],t=o[1];return e%n===t}function N3(e,o){return o.some(function(n){return e instanceof Array?e.some(function(t){return I1(n,t)===0}):I1(n,e)===0})}function Ms(e,o){return o.every(function(n){return e.some(function(t){return I1(n,t)===0})})}function ks(e,o){return e.length===o}function ys(e,o){var n=new RegExp(o);return n.test(e)}function zs(e,o){switch(o){case"null":return e===null;case"boolean":return typeof e=="boolean";case"number":return typeof e=="number";case"string":return typeof e=="string";case"array":return e instanceof Array;case"object":return{}.toString.call(e)==="[object Object]"}}var $3={$elemMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.some(function(i){return F0(i,o,Object.keys(o))}):t.some(function(i){return O2(o,e,n,i)})},$allMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.every(function(i){return F0(i,o,Object.keys(o))}):t.every(function(i){return O2(o,e,n,i)})},$eq:function(e,o,n,t){return _0(t)&&I1(t,o)===0},$gte:function(e,o,n,t){return _0(t)&&I1(t,o)>=0},$gt:function(e,o,n,t){return _0(t)&&I1(t,o)>0},$lte:function(e,o,n,t){return _0(t)&&I1(t,o)<=0},$lt:function(e,o,n,t){return _0(t)&&I1(t,o)<0},$exists:function(e,o,n,t){return o?_0(t):!_0(t)},$mod:function(e,o,n,t){return n2(t)&&xs(t,o)},$ne:function(e,o,n,t){return o.every(function(i){return I1(t,i)!==0})},$in:function(e,o,n,t){return n2(t)&&N3(t,o)},$nin:function(e,o,n,t){return n2(t)&&!N3(t,o)},$size:function(e,o,n,t){return n2(t)&&Array.isArray(t)&&ks(t,o)},$all:function(e,o,n,t){return Array.isArray(t)&&Ms(t,o)},$regex:function(e,o,n,t){return n2(t)&&typeof t=="string"&&o.every(function(i){return ys(t,i)})},$type:function(e,o,n,t){return zs(t,o)}};function Cs(e,o){if(typeof o!="object")throw new Error("Selector error: expected a JSON object");o=as(o);var n={doc:e},t=ws([n],{selector:o},Object.keys(o));return t&&t.length===1}function _s(e){return F4(`"use strict";
return `+e+";",{})}function bs(e){var o=["return function(doc) {",'  "use strict";',"  var emitted = false;","  var emit = function (a, b) {","    emitted = true;","  };","  var view = "+e+";","  view(doc);","  if (emitted) {","    return true;","  }","};"].join(`
`);return F4(o,{})}function Bs(e,o){if(e.selector&&e.filter&&e.filter!=="_selector"){var n=typeof e.filter=="string"?e.filter:"function";return o(new Error('selector invalid for filter "'+n+'"'))}o()}function Ss(e){e.view&&!e.filter&&(e.filter="_view"),e.selector&&!e.filter&&(e.filter="_selector"),e.filter&&typeof e.filter=="string"&&(e.filter==="_view"?e.view=D3(e.view):e.filter=D3(e.filter))}function As(e,o){return o.filter&&typeof o.filter=="string"&&!o.doc_ids&&!s0(e.db)}function Ls(e,o){var n=o.complete;if(o.filter==="_view"){if(!o.view||typeof o.view!="string"){var t=r1(F2,"`view` filter parameter not found or invalid.");return n(t)}var i=f4(o.view);e.db.get("_design/"+i[0],function(s,c){if(e.isCancelled)return n(null,{status:"cancelled"});if(s)return n(N0(s));var d=c&&c.views&&c.views[i[1]]&&c.views[i[1]].map;if(!d)return n(r1($1,c.views?"missing json key: "+i[1]:"missing json key: views"));o.filter=bs(d),e.doChanges(o)})}else if(o.selector)o.filter=function(s){return Cs(s,o.selector)},e.doChanges(o);else{var a=f4(o.filter);e.db.get("_design/"+a[0],function(s,c){if(e.isCancelled)return n(null,{status:"cancelled"});if(s)return n(N0(s));var d=c&&c.filters&&c.filters[a[1]];if(!d)return n(r1($1,c&&c.filters?"missing json key: "+a[1]:"missing json key: filters"));o.filter=_s(d),e.doChanges(o)})}}function Is(e){e._changesFilterPlugin={validate:Bs,normalize:Ss,shouldFilter:As,filter:Ls}}l1.plugin(Is);l1.version=Qa;function js(e,o,n){return new Promise(function(t){var i=N4([""]);let a;if(typeof n=="function"){let c=n(i);a=e.objectStore(o).put(c)}else{let s=n;a=e.objectStore(o).put(i,s)}a.onsuccess=function(){var s=navigator.userAgent.match(/Chrome\/(\d+)/),c=navigator.userAgent.match(/Edge\//);t(c||!s||parseInt(s[1],10)>=43)},a.onerror=e.onabort=function(s){s.preventDefault(),s.stopPropagation(),t(!1)}}).catch(function(){return!1})}function L6(e){return e.reduce(function(o,n){return o[n]=!0,o},{})}var Vs=L6(["_id","_rev","_access","_attachments","_deleted","_revisions","_revs_info","_conflicts","_deleted_conflicts","_local_seq","_rev_tree","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats","_removed"]),Hs=L6(["_access","_attachments","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats"]);function U3(e){if(!/^\d+-/.test(e))return r1(S2);var o=e.indexOf("-"),n=e.substring(0,o),t=e.substring(o+1);return{prefix:parseInt(n,10),id:t}}function Os(e,o){for(var n=e.start-e.ids.length+1,t=e.ids,i=[t[0],o,[]],a=1,s=t.length;a<s;a++)i=[t[a],{status:"missing"},[i]];return[{pos:n,ids:i}]}function I6(e,o,n){n||(n={deterministic_revs:!0});var t,i,a,s={status:"available"};if(e._deleted&&(s.deleted=!0),o)if(e._id||(e._id=q2()),i=f6(e,n.deterministic_revs),e._rev){if(a=U3(e._rev),a.error)return a;e._rev_tree=[{pos:a.prefix,ids:[a.id,{status:"missing"},[[i,s,[]]]]}],t=a.prefix+1}else e._rev_tree=[{pos:1,ids:[i,s,[]]}],t=1;else if(e._revisions&&(e._rev_tree=Os(e._revisions,s),t=e._revisions.start,i=e._revisions.ids[0]),!e._rev_tree){if(a=U3(e._rev),a.error)return a;t=a.prefix,i=a.id,e._rev_tree=[{pos:t,ids:[i,s,[]]}]}h6(e._id),e._rev=t+"-"+i;var c={metadata:{},data:{}};for(var d in e)if(Object.prototype.hasOwnProperty.call(e,d)){var x=d[0]==="_";if(x&&!Vs[d]){var k=r1(O3,d);throw k.message=O3.message+": "+d,k}else x&&!Hs[d]?c.metadata[d.slice(1)]=e[d]:c.data[d]=e[d]}return c}function Ds(e){try{return q4(e)}catch{var o=r1(g6,"Attachment is not a valid base64 string");return{error:o}}}function Es(e,o,n){var t=Ds(e.data);if(t.error)return n(t.error);e.length=t.length,o==="blob"?e.data=$4(t,e.content_type):o==="base64"?e.data=r2(t):e.data=t,K4(t,function(i){e.digest="md5-"+i,n()})}function Ps(e,o,n){K4(e.data,function(t){e.digest="md5-"+t,e.length=e.data.size||e.data.length||0,o==="binary"?m6(e.data,function(i){e.data=i,n()}):o==="base64"?G4(e.data,function(i){e.data=i,n()}):n()})}function Ts(e,o,n){if(e.stub)return n();typeof e.data=="string"?Es(e,o,n):Ps(e,o,n)}function Rs(e,o,n){if(!e.length)return n();var t=0,i;e.forEach(function(s){var c=s.data&&s.data._attachments?Object.keys(s.data._attachments):[],d=0;if(!c.length)return a();function x(l){i=l,d++,d===c.length&&a()}for(var k in s.data._attachments)Object.prototype.hasOwnProperty.call(s.data._attachments,k)&&Ts(s.data._attachments[k],o,x)});function a(){t++,e.length===t&&(i?n(i):n())}}function Fs(e,o,n,t,i,a,s,c){if(Ha(o.rev_tree,n.metadata.rev)&&!c)return t[i]=n,a();var d=o.winningRev||I0(o),x="deleted"in o?o.deleted:g0(o,d),k="deleted"in n.metadata?n.metadata.deleted:g0(n.metadata),l=/^1-/.test(n.metadata.rev);if(x&&!k&&c&&l){var M=n.data;M._rev=d,M._id=n.metadata.id,n=I6(M,c)}var _=k6(o.rev_tree,n.metadata.rev_tree[0],e),B=c&&(x&&k&&_.conflicts!=="new_leaf"||!x&&_.conflicts!=="new_leaf"||x&&!k&&_.conflicts==="new_branch");if(B){var A=r1(q0);return t[i]=A,a()}var z=n.metadata.rev;n.metadata.rev_tree=_.tree,n.stemmedRevs=_.stemmedRevs||[],o.rev_map&&(n.metadata.rev_map=o.rev_map);var u=I0(n.metadata),h=g0(n.metadata,u),r=x===h?0:x<h?-1:1,v;z===u?v=h:v=g0(n.metadata,z),s(n,u,h,v,!0,r,i,a)}function qs(e){return e.metadata.rev_tree[0].ids[1].status==="missing"}function Ns(e,o,n,t,i,a,s,c,d){e=e||1e3;function x(A,z,u){var h=I0(A.metadata),r=g0(A.metadata,h);if("was_delete"in c&&r)return a[z]=r1($1,"deleted"),u();var v=k&&qs(A);if(v){var w=r1(q0);return a[z]=w,u()}var f=r?0:1;s(A,h,r,r,!1,f,z,u)}var k=c.new_edits,l=new Map,M=0,_=o.length;function B(){++M===_&&d&&d()}o.forEach(function(A,z){if(A._id&&L0(A._id)){var u=A._deleted?"_removeLocal":"_putLocal";n[u](A,{ctx:i},function(r,v){a[z]=r||v,B()});return}var h=A.metadata.id;l.has(h)?(_--,l.get(h).push([A,z])):l.set(h,[[A,z]])}),l.forEach(function(A,z){var u=0;function h(){++u<A.length?r():B()}function r(){var v=A[u],w=v[0],f=v[1];if(t.has(z))Fs(e,t.get(z),w,a,f,h,s,k);else{var V=k6([],w.metadata.rev_tree[0],e);w.metadata.rev_tree=V.tree,w.stemmedRevs=V.stemmedRevs||[],x(w,f,h)}}r()})}var $s=5,j1="document-store",E1="by-sequence",U1="attach-store",S0="attach-seq-store",q1="meta-store",i0="local-store",s4="detect-blob-support";function Us(e){try{return JSON.parse(e)}catch{return E4.default.parse(e)}}function Gs(e){try{return JSON.stringify(e)}catch{return E4.default.stringify(e)}}function a0(e){return function(o){var n="unknown_error";o.target&&o.target.error&&(n=o.target.error.name||o.target.error.message),e(r1(T4,n,o.type))}}function b4(e,o,n){return{data:Gs(e),winningRev:o,deletedOrLocal:n?"1":"0",seq:e.seq,id:e.id}}function A0(e){if(!e)return null;var o=Us(e.data);return o.winningRev=e.winningRev,o.deleted=e.deletedOrLocal==="1",o.seq=e.seq,o}function D2(e){if(!e)return e;var o=e._doc_id_rev.lastIndexOf(":");return e._id=e._doc_id_rev.substring(0,o-1),e._rev=e._doc_id_rev.substring(o+1),delete e._doc_id_rev,e}function j6(e,o,n,t){n?t(e?typeof e!="string"?e:U4(e,o):N4([""],{type:o})):e?typeof e!="string"?p6(e,function(i){t(r2(i))}):t(e):t("")}function V6(e,o,n,t){var i=Object.keys(e._attachments||{});if(!i.length)return t&&t();var a=0;function s(){++a===i.length&&t&&t()}function c(d,x){var k=d._attachments[x],l=k.digest,M=n.objectStore(U1).get(l);M.onsuccess=function(_){k.body=_.target.result.body,s()}}i.forEach(function(d){o.attachments&&o.include_docs?c(e,d):(e._attachments[d].stub=!0,s())})}function B4(e,o){return Promise.all(e.map(function(n){if(n.doc&&n.doc._attachments){var t=Object.keys(n.doc._attachments);return Promise.all(t.map(function(i){var a=n.doc._attachments[i];if("body"in a){var s=a.body,c=a.content_type;return new Promise(function(d){j6(s,c,o,function(x){n.doc._attachments[i]=Object.assign(s2(a,["digest","content_type"]),{data:x}),d()})})}}))}}))}function H6(e,o,n){var t=[],i=n.objectStore(E1),a=n.objectStore(U1),s=n.objectStore(S0),c=e.length;function d(){c--,c||x()}function x(){t.length&&t.forEach(function(k){var l=s.index("digestSeq").count(IDBKeyRange.bound(k+"::",k+"::\uFFFF",!1,!1));l.onsuccess=function(M){var _=M.target.result;_||a.delete(k)}})}e.forEach(function(k){var l=i.index("_doc_id_rev"),M=o+"::"+k;l.getKey(M).onsuccess=function(_){var B=_.target.result;if(typeof B!="number")return d();i.delete(B);var A=s.index("seq").openCursor(IDBKeyRange.only(B));A.onsuccess=function(z){var u=z.target.result;if(u){var h=u.value.digestSeq.split("::")[0];t.push(h),s.delete(u.primaryKey),u.continue()}else d()}}})}function X1(e,o,n){try{return{txn:e.transaction(o,n)}}catch(t){return{error:t}}}var o2=new m4;function Ks(e,o,n,t,i,a){for(var s=o.docs,c,d,x,k,l,M,_,B,A=0,z=s.length;A<z;A++){var u=s[A];u._id&&L0(u._id)||(u=s[A]=I6(u,n.new_edits,e),u.error&&!_&&(_=u))}if(_)return a(_);var h=!1,r=0,v=new Array(s.length),w=new Map,f=!1,V=t._meta.blobSupport?"blob":"base64";Rs(s,V,function(C){if(C)return a(C);D()});function D(){var C=[j1,E1,U1,i0,S0,q1],L=X1(i,C,"readwrite");if(L.error)return a(L.error);c=L.txn,c.onabort=a0(a),c.ontimeout=a0(a),c.oncomplete=e1,d=c.objectStore(j1),x=c.objectStore(E1),k=c.objectStore(U1),l=c.objectStore(S0),M=c.objectStore(q1),M.get(q1).onsuccess=function(E){B=E.target.result,Y()},d1(function(E){if(E)return f=!0,a(E);s1()})}function j(){h=!0,Y()}function n1(){Ns(e.revs_limit,s,t,w,c,v,v1,n,j)}function Y(){!B||!h||(B.docCount+=r,M.put(B))}function s1(){if(!s.length)return;var C=0;function L(){++C===s.length&&n1()}function E(H){var $=A0(H.target.result);$&&w.set($.id,$),L()}for(var X=0,I=s.length;X<I;X++){var b=s[X];if(b._id&&L0(b._id)){L();continue}var S=d.get(b.metadata.id);S.onsuccess=E}}function e1(){f||(o2.notify(t._meta.name),a(null,v))}function i1(C,L){var E=k.get(C);E.onsuccess=function(X){if(X.target.result)L();else{var I=r1(ga,"unknown stub attachment with digest "+C);I.status=412,L(I)}}}function d1(C){var L=[];if(s.forEach(function(b){b.data&&b.data._attachments&&Object.keys(b.data._attachments).forEach(function(S){var H=b.data._attachments[S];H.stub&&L.push(H.digest)})}),!L.length)return C();var E=0,X;function I(){++E===L.length&&C(X)}L.forEach(function(b){i1(b,function(S){S&&!X&&(X=S),I()})})}function v1(C,L,E,X,I,b,S,H){C.metadata.winningRev=L,C.metadata.deleted=E;var $=C.data;$._id=C.metadata.id,$._rev=C.metadata.rev,X&&($._deleted=!0);var Z=$._attachments&&Object.keys($._attachments).length;if(Z)return g(C,L,E,I,S,H);r+=b,Y(),p1(C,L,E,I,S,H)}function p1(C,L,E,X,I,b){var S=C.data,H=C.metadata;S._doc_id_rev=H.id+"::"+H.rev,delete S._id,delete S._rev;function $(C1){var V1=C.stemmedRevs||[];X&&t.auto_compaction&&(V1=V1.concat(Ba(C.metadata))),V1&&V1.length&&H6(V1,C.metadata.id,c),H.seq=C1.target.result;var T1=b4(H,L,E),o1=d.put(T1);o1.onsuccess=c1}function Z(C1){C1.preventDefault(),C1.stopPropagation();var V1=x.index("_doc_id_rev"),T1=V1.getKey(S._doc_id_rev);T1.onsuccess=function(o1){var t1=x.put(S,o1.target.result);t1.onsuccess=$}}function c1(){v[I]={ok:!0,id:H.id,rev:H.rev},w.set(C.metadata.id,C.metadata),p(C,H.seq,b)}var w1=x.put(S);w1.onsuccess=$,w1.onerror=Z}function g(C,L,E,X,I,b){var S=C.data,H=0,$=Object.keys(S._attachments);function Z(){H===$.length&&p1(C,L,E,X,I,b)}function c1(){H++,Z()}$.forEach(function(w1){var C1=C.data._attachments[w1];if(C1.stub)H++,Z();else{var V1=C1.data;delete C1.data,C1.revpos=parseInt(L,10);var T1=C1.digest;y(T1,V1,c1)}})}function p(C,L,E){var X=0,I=Object.keys(C.data._attachments||{});if(!I.length)return E();function b(){++X===I.length&&E()}function S($){var Z=C.data._attachments[$].digest,c1=l.put({seq:L,digestSeq:Z+"::"+L});c1.onsuccess=b,c1.onerror=function(w1){w1.preventDefault(),w1.stopPropagation(),b()}}for(var H=0;H<I.length;H++)S(I[H])}function y(C,L,E){var X=k.count(C);X.onsuccess=function(I){var b=I.target.result;if(b)return E();var S={digest:C,body:L},H=k.put(S);H.onsuccess=E}}}function O6(e,o,n,t,i){t===-1&&(t=1e3);var a=typeof e.getAll=="function"&&typeof e.getAllKeys=="function"&&t>1&&!n,s,c,d;function x(_){c=_.target.result,s&&i(s,c,d)}function k(_){s=_.target.result,c&&i(s,c,d)}function l(){if(!s.length)return i();var _=s[s.length-1],B;if(o&&o.upper)try{B=IDBKeyRange.bound(_,o.upper,!0,o.upperOpen)}catch(A){if(A.name==="DataError"&&A.code===0)return i()}else B=IDBKeyRange.lowerBound(_,!0);o=B,s=null,c=null,e.getAll(o,t).onsuccess=x,e.getAllKeys(o,t).onsuccess=k}function M(_){var B=_.target.result;if(!B)return i();i([B.key],[B.value],B)}a?(d={continue:l},e.getAll(o,t).onsuccess=x,e.getAllKeys(o,t).onsuccess=k):n?e.openCursor(o,"prev").onsuccess=M:e.openCursor(o).onsuccess=M}function Ws(e,o,n){if(typeof e.getAll=="function"){e.getAll(o).onsuccess=n;return}var t=[];function i(a){var s=a.target.result;s?(t.push(s.value),s.continue()):n({target:{result:t}})}e.openCursor(o).onsuccess=i}function Zs(e,o,n){var t=new Array(e.length),i=0;e.forEach(function(a,s){o.get(a).onsuccess=function(c){c.target.result?t[s]=c.target.result:t[s]={key:a,error:"not_found"},i++,i===e.length&&n(e,t,{})}})}function Qs(e,o,n,t,i){try{if(e&&o)return i?IDBKeyRange.bound(o,e,!n,!1):IDBKeyRange.bound(e,o,!1,!n);if(e)return i?IDBKeyRange.upperBound(e):IDBKeyRange.lowerBound(e);if(o)return i?IDBKeyRange.lowerBound(o,!n):IDBKeyRange.upperBound(o,!n);if(t)return IDBKeyRange.only(t)}catch(a){return{error:a}}return null}function Xs(e,o,n){var t="startkey"in e?e.startkey:!1,i="endkey"in e?e.endkey:!1,a="key"in e?e.key:!1,s="keys"in e?e.keys:!1,c=e.skip||0,d=typeof e.limit=="number"?e.limit:-1,x=e.inclusive_end!==!1,k,l;if(!s&&(k=Qs(t,i,x,a,e.descending),l=k&&k.error,l&&!(l.name==="DataError"&&l.code===0)))return n(r1(T4,l.name,l.message));var M=[j1,E1,q1];e.attachments&&M.push(U1);var _=X1(o,M,"readonly");if(_.error)return n(_.error);var B=_.txn;B.oncomplete=s1,B.onabort=a0(n);var A=B.objectStore(j1),z=B.objectStore(E1),u=B.objectStore(q1),h=z.index("_doc_id_rev"),r=[],v,w;u.get(q1).onsuccess=function(e1){v=e1.target.result.docCount},e.update_seq&&(z.openKeyCursor(null,"prev").onsuccess=e1=>{var i1=e1.target.result;i1&&i1.key&&(w=i1.key)});function f(e1,i1,d1){var v1=e1.id+"::"+d1;h.get(v1).onsuccess=function(g){if(i1.doc=D2(g.target.result)||{},e.conflicts){var p=Z4(e1);p.length&&(i1.doc._conflicts=p)}V6(i1.doc,e,B)}}function V(e1,i1){var d1={id:i1.id,key:i1.id,value:{rev:e1}},v1=i1.deleted;v1?s&&(r.push(d1),d1.value.deleted=!0,d1.doc=null):c--<=0&&(r.push(d1),e.include_docs&&f(i1,d1,e1))}function D(e1){for(var i1=0,d1=e1.length;i1<d1&&r.length!==d;i1++){var v1=e1[i1];if(v1.error&&s){r.push(v1);continue}var p1=A0(v1),g=p1.winningRev;V(g,p1)}}function j(e1,i1,d1){d1&&(D(i1),r.length<d&&d1.continue())}function n1(e1){var i1=e1.target.result;e.descending&&(i1=i1.reverse()),D(i1)}function Y(){var e1={total_rows:v,offset:e.skip,rows:r};e.update_seq&&w!==void 0&&(e1.update_seq=w),n(null,e1)}function s1(){e.attachments?B4(r,e.binary).then(Y):Y()}if(!(l||d===0)){if(s)return Zs(s,A,j);if(d===-1)return Ws(A,k,n1);O6(A,k,e.descending,d+c,j)}}function Js(e,o){var n=e.objectStore(j1).index("deletedOrLocal");n.count(IDBKeyRange.only("0")).onsuccess=function(t){o(t.target.result)}}var S4=!1,A4=[];function Ys(e,o,n,t){try{e(o,n)}catch(i){t.emit("error",i)}}function G3(){S4||!A4.length||(S4=!0,A4.shift()())}function e5(e,o,n){A4.push(function(){e(function(a,s){Ys(o,a,s,n),S4=!1,c0(function(){G3(n)})})}),G3()}function t5(e,o,n,t){if(e=O1(e),e.continuous){var i=n+":"+q2();return o2.addListener(n,i,o,e),o2.notify(n),{cancel:function(){o2.removeListener(n,i)}}}var a=e.doc_ids&&new Set(e.doc_ids);e.since=e.since||0;var s=e.since,c="limit"in e?e.limit:-1;c===0&&(c=1);var d=[],x=0,k=R4(e),l=new Map,M,_,B,A;function z(D,j,n1){if(!n1||!D.length)return;var Y=new Array(D.length),s1=new Array(D.length);function e1(v1,p1){var g=e.processChange(p1,v1,e);s=g.seq=v1.seq;var p=k(g);return typeof p=="object"?Promise.reject(p):p?(x++,e.return_docs&&d.push(g),e.attachments&&e.include_docs?new Promise(function(y){V6(p1,e,M,function(){B4([g],e.binary).then(function(){y(g)})})}):Promise.resolve(g)):Promise.resolve()}function i1(){for(var v1=[],p1=0,g=Y.length;p1<g&&x!==c;p1++){var p=Y[p1];if(p){var y=s1[p1];v1.push(e1(y,p))}}Promise.all(v1).then(function(C){for(var L=0,E=C.length;L<E;L++)C[L]&&e.onChange(C[L])}).catch(e.complete),x!==c&&n1.continue()}var d1=0;j.forEach(function(v1,p1){var g=D2(v1),p=D[p1];h(g,p,function(y,C){s1[p1]=y,Y[p1]=C,++d1===D.length&&i1()})})}function u(D,j,n1,Y){if(n1.seq!==j)return Y();if(n1.winningRev===D._rev)return Y(n1,D);var s1=D._id+"::"+n1.winningRev,e1=A.get(s1);e1.onsuccess=function(i1){Y(n1,D2(i1.target.result))}}function h(D,j,n1){if(a&&!a.has(D._id))return n1();var Y=l.get(D._id);if(Y)return u(D,j,Y,n1);B.get(D._id).onsuccess=function(s1){Y=A0(s1.target.result),l.set(D._id,Y),u(D,j,Y,n1)}}function r(){e.complete(null,{results:d,last_seq:s})}function v(){!e.continuous&&e.attachments?B4(d).then(r):r()}var w=[j1,E1];e.attachments&&w.push(U1);var f=X1(t,w,"readonly");if(f.error)return e.complete(f.error);M=f.txn,M.onabort=a0(e.complete),M.oncomplete=v,_=M.objectStore(E1),B=M.objectStore(j1),A=_.index("_doc_id_rev");var V=e.since&&!e.descending?IDBKeyRange.lowerBound(e.since,!0):null;O6(_,V,e.descending,c,z)}var T0=new Map,r4,c4=new Map;function D6(e,o){var n=this;e5(function(t){n5(n,e,t)},o,n.constructor)}function n5(e,o,n){var t=o.name,i=null,a=null;e._meta=null;function s(z){return function(u,h){u&&u instanceof Error&&!u.reason&&a&&(u.reason=a),z(u,h)}}function c(z){var u=z.createObjectStore(j1,{keyPath:"id"});z.createObjectStore(E1,{autoIncrement:!0}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0}),z.createObjectStore(U1,{keyPath:"digest"}),z.createObjectStore(q1,{keyPath:"id",autoIncrement:!1}),z.createObjectStore(s4),u.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),z.createObjectStore(i0,{keyPath:"_id"});var h=z.createObjectStore(S0,{autoIncrement:!0});h.createIndex("seq","seq"),h.createIndex("digestSeq","digestSeq",{unique:!0})}function d(z,u){var h=z.objectStore(j1);h.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),h.openCursor().onsuccess=function(r){var v=r.target.result;if(v){var w=v.value,f=g0(w);w.deletedOrLocal=f?"1":"0",h.put(w),v.continue()}else u()}}function x(z){z.createObjectStore(i0,{keyPath:"_id"}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0})}function k(z,u){var h=z.objectStore(i0),r=z.objectStore(j1),v=z.objectStore(E1),w=r.openCursor();w.onsuccess=function(f){var V=f.target.result;if(V){var D=V.value,j=D.id,n1=L0(j),Y=I0(D);if(n1){var s1=j+"::"+Y,e1=j+"::",i1=j+"::~",d1=v.index("_doc_id_rev"),v1=IDBKeyRange.bound(e1,i1,!1,!1),p1=d1.openCursor(v1);p1.onsuccess=function(g){if(p1=g.target.result,!p1)r.delete(V.primaryKey),V.continue();else{var p=p1.value;p._doc_id_rev===s1&&h.put(p),v.delete(p1.primaryKey),p1.continue()}}}else V.continue()}else u&&u()}}function l(z){var u=z.createObjectStore(S0,{autoIncrement:!0});u.createIndex("seq","seq"),u.createIndex("digestSeq","digestSeq",{unique:!0})}function M(z,u){var h=z.objectStore(E1),r=z.objectStore(U1),v=z.objectStore(S0),w=r.count();w.onsuccess=function(f){var V=f.target.result;if(!V)return u();h.openCursor().onsuccess=function(D){var j=D.target.result;if(!j)return u();for(var n1=j.value,Y=j.primaryKey,s1=Object.keys(n1._attachments||{}),e1={},i1=0;i1<s1.length;i1++){var d1=n1._attachments[s1[i1]];e1[d1.digest]=!0}var v1=Object.keys(e1);for(i1=0;i1<v1.length;i1++){var p1=v1[i1];v.put({seq:Y,digestSeq:p1+"::"+Y})}j.continue()}}}function _(z){function u(w){return w.data?A0(w):(w.deleted=w.deletedOrLocal==="1",w)}var h=z.objectStore(E1),r=z.objectStore(j1),v=r.openCursor();v.onsuccess=function(w){var f=w.target.result;if(!f)return;var V=u(f.value);V.winningRev=V.winningRev||I0(V);function D(){var n1=V.id+"::",Y=V.id+"::\uFFFF",s1=h.index("_doc_id_rev").openCursor(IDBKeyRange.bound(n1,Y)),e1=0;s1.onsuccess=function(i1){var d1=i1.target.result;if(!d1)return V.seq=e1,j();var v1=d1.primaryKey;v1>e1&&(e1=v1),d1.continue()}}function j(){var n1=b4(V,V.winningRev,V.deleted),Y=r.put(n1);Y.onsuccess=function(){f.continue()}}if(V.seq)return j();D()}}e._remote=!1,e.type=function(){return"idb"},e._id=l6(function(z){z(null,e._meta.instanceId)}),e._bulkDocs=function(u,h,r){Ks(o,u,h,e,i,s(r))},e._get=function(u,h,r){var v,w,f,V=h.ctx;if(!V){var D=X1(i,[j1,E1,U1],"readonly");if(D.error)return r(D.error);V=D.txn}function j(){r(f,{doc:v,metadata:w,ctx:V})}V.objectStore(j1).get(u).onsuccess=function(n1){if(w=A0(n1.target.result),!w)return f=r1($1,"missing"),j();var Y;if(h.rev)Y=h.latest?Da(h.rev,w):h.rev;else{Y=w.winningRev;var s1=g0(w);if(s1)return f=r1($1,"deleted"),j()}var e1=V.objectStore(E1),i1=w.id+"::"+Y;e1.index("_doc_id_rev").get(i1).onsuccess=function(d1){if(v=d1.target.result,v&&(v=D2(v)),!v)return f=r1($1,"missing"),j();j()}}},e._getAttachment=function(z,u,h,r,v){var w;if(r.ctx)w=r.ctx;else{var f=X1(i,[j1,E1,U1],"readonly");if(f.error)return v(f.error);w=f.txn}var V=h.digest,D=h.content_type;w.objectStore(U1).get(V).onsuccess=function(j){var n1=j.target.result.body;j6(n1,D,r.binary,function(Y){v(null,Y)})}},e._info=function(u){var h,r,v=X1(i,[q1,E1],"readonly");if(v.error)return u(v.error);var w=v.txn;w.objectStore(q1).get(q1).onsuccess=function(f){r=f.target.result.docCount},w.objectStore(E1).openKeyCursor(null,"prev").onsuccess=function(f){var V=f.target.result;h=V?V.key:0},w.oncomplete=function(){u(null,{doc_count:r,update_seq:h,idb_attachment_format:e._meta.blobSupport?"binary":"base64"})}},e._allDocs=function(u,h){Xs(u,i,s(h))},e._changes=function(u){return t5(u,e,t,i)},e._close=function(z){i.close(),T0.delete(t),z()},e._getRevisionTree=function(z,u){var h=X1(i,[j1],"readonly");if(h.error)return u(h.error);var r=h.txn,v=r.objectStore(j1).get(z);v.onsuccess=function(w){var f=A0(w.target.result);f?u(null,f.rev_tree):u(r1($1))}},e._doCompaction=function(z,u,h){var r=[j1,E1,U1,S0],v=X1(i,r,"readwrite");if(v.error)return h(v.error);var w=v.txn,f=w.objectStore(j1);f.get(z).onsuccess=function(V){var D=A0(V.target.result);j0(D.rev_tree,function(Y,s1,e1,i1,d1){var v1=s1+"-"+e1;u.indexOf(v1)!==-1&&(d1.status="missing")}),H6(u,z,w);var j=D.winningRev,n1=D.deleted;w.objectStore(j1).put(b4(D,j,n1))},w.onabort=a0(h),w.oncomplete=function(){h()}},e._getLocal=function(z,u){var h=X1(i,[i0],"readonly");if(h.error)return u(h.error);var r=h.txn,v=r.objectStore(i0).get(z);v.onerror=a0(u),v.onsuccess=function(w){var f=w.target.result;f?(delete f._doc_id_rev,u(null,f)):u(r1($1))}},e._putLocal=function(z,u,h){typeof u=="function"&&(h=u,u={}),delete z._revisions;var r=z._rev,v=z._id;r?z._rev="0-"+(parseInt(r.split("-")[1],10)+1):z._rev="0-1";var w=u.ctx,f;if(!w){var V=X1(i,[i0],"readwrite");if(V.error)return h(V.error);w=V.txn,w.onerror=a0(h),w.oncomplete=function(){f&&h(null,f)}}var D=w.objectStore(i0),j;r?(j=D.get(v),j.onsuccess=function(n1){var Y=n1.target.result;if(!Y||Y._rev!==r)h(r1(q0));else{var s1=D.put(z);s1.onsuccess=function(){f={ok:!0,id:z._id,rev:z._rev},u.ctx&&h(null,f)}}}):(j=D.add(z),j.onerror=function(n1){h(r1(q0)),n1.preventDefault(),n1.stopPropagation()},j.onsuccess=function(){f={ok:!0,id:z._id,rev:z._rev},u.ctx&&h(null,f)})},e._removeLocal=function(z,u,h){typeof u=="function"&&(h=u,u={});var r=u.ctx;if(!r){var v=X1(i,[i0],"readwrite");if(v.error)return h(v.error);r=v.txn,r.oncomplete=function(){w&&h(null,w)}}var w,f=z._id,V=r.objectStore(i0),D=V.get(f);D.onerror=a0(h),D.onsuccess=function(j){var n1=j.target.result;!n1||n1._rev!==z._rev?h(r1($1)):(V.delete(f),w={ok:!0,id:f,rev:"0-0"},u.ctx&&h(null,w))}},e._destroy=function(z,u){o2.removeAllListeners(t);var h=c4.get(t);h&&h.result&&(h.result.close(),T0.delete(t));var r=indexedDB.deleteDatabase(t);r.onsuccess=function(){c4.delete(t),I2()&&t in localStorage&&delete localStorage[t],u(null,{ok:!0})},r.onerror=a0(u)};var B=T0.get(t);if(B)return i=B.idb,e._meta=B.global,c0(function(){n(null,e)});var A=indexedDB.open(t,$s);c4.set(t,A),A.onupgradeneeded=function(z){var u=z.target.result;if(z.oldVersion<1)return c(u);var h=z.currentTarget.transaction;z.oldVersion<3&&x(u),z.oldVersion<4&&l(u);var r=[d,k,M,_],v=z.oldVersion;function w(){var f=r[v-1];v++,f&&f(h,w)}w()},A.onsuccess=function(z){i=z.target.result,i.onversionchange=function(){i.close(),T0.delete(t)},i.onabort=function(j){G1("error","Database has a global failure",j.target.error),a=j.target.error,i.close(),T0.delete(t)};var u=i.transaction([q1,s4,j1],"readwrite"),h=!1,r,v,w,f;function V(){typeof w>"u"||!h||(e._meta={name:t,instanceId:f,blobSupport:w},T0.set(t,{idb:i,global:e._meta}),n(null,e))}function D(){if(!(typeof v>"u"||typeof r>"u")){var j=t+"_id";j in r?f=r[j]:r[j]=f=q2(),r.docCount=v,u.objectStore(q1).put(r)}}u.objectStore(q1).get(q1).onsuccess=function(j){r=j.target.result||{id:q1},D()},Js(u,function(j){v=j,D()}),r4||(r4=js(u,s4,"key")),r4.then(function(j){w=j,V()}),u.oncomplete=function(){h=!0,V()},u.onabort=a0(n)},A.onerror=function(z){var u=z.target.error&&z.target.error.message;u?u.indexOf("stored database is a higher version")!==-1&&(u=new Error('This DB was created with the newer "indexeddb" adapter, but you are trying to open it with the older "idb" adapter')):u="Failed to open indexedDB, are you in private browsing mode?",G1("error",u),n(r1(T4,u))}}D6.valid=function(){try{return typeof indexedDB<"u"&&typeof IDBKeyRange<"u"}catch{return!1}};function o5(e){e.adapter("idb",D6,!0)}function i5(e,o){return new Promise(function(n,t){var i=0,a=0,s=0,c=e.length,d;function x(){i++,e[a++]().then(l,M)}function k(){++s===c?d?t(d):n():_()}function l(){i--,k()}function M(B){i--,d=d||B,k()}function _(){for(;i<o&&a<c;)x()}_()})}var a5=25,s5=50,b2=5e3,r5=1e4,l4={};function d4(e){let o=e.doc||e.ok,n=o&&o._attachments;n&&Object.keys(n).forEach(function(t){let i=n[t];i.data=U4(i.data,i.content_type)})}function b0(e){return/^_design/.test(e)?"_design/"+encodeURIComponent(e.slice(8)):e.startsWith("_local/")?"_local/"+encodeURIComponent(e.slice(7)):encodeURIComponent(e)}function K3(e){return!e._attachments||!Object.keys(e._attachments)?Promise.resolve():Promise.all(Object.keys(e._attachments).map(function(o){let n=e._attachments[o];if(n.data&&typeof n.data!="string")return new Promise(function(t){G4(n.data,t)}).then(function(t){n.data=t})}))}function c5(e){if(!e.prefix)return!1;let o=u6(e.prefix).protocol;return o==="http"||o==="https"}function l5(e,o){if(c5(o)){let i=o.name.substr(o.prefix.length);e=o.prefix.replace(/\/?$/,"/")+encodeURIComponent(i)}let n=u6(e);(n.user||n.password)&&(n.auth={username:n.user,password:n.password});let t=n.path.replace(/(^\/|\/$)/g,"").split("/");return n.db=t.pop(),n.db.indexOf("%")===-1&&(n.db=encodeURIComponent(n.db)),n.path=t.join("/"),n}function H1(e,o){return A2(e,e.db+"/"+o)}function A2(e,o){let n=e.path?"/":"";return e.protocol+"://"+e.host+(e.port?":"+e.port:"")+"/"+e.path+n+o}function B2(e){let o=Object.keys(e);return o.length===0?"":"?"+o.map(n=>n+"="+encodeURIComponent(e[n])).join("&")}function d5(e){let o=typeof navigator<"u"&&navigator.userAgent?navigator.userAgent.toLowerCase():"",n=o.indexOf("msie")!==-1,t=o.indexOf("trident")!==-1,i=o.indexOf("edge")!==-1,a=!("method"in e)||e.method==="GET";return(n||t||i)&&a}function L4(e,o){let n=this,t=l5(e.name,e),i=H1(t,"");e=O1(e);let a=function(l,M){return Q(this,null,function*(){if(M=M||{},M.headers=M.headers||new R0,M.credentials="include",e.auth||t.auth){let A=e.auth||t.auth,z=A.username+":"+A.password,u=r2(unescape(encodeURIComponent(z)));M.headers.set("Authorization","Basic "+u)}let _=e.headers||{};return Object.keys(_).forEach(function(A){M.headers.append(A,_[A])}),d5(M)&&(l+=(l.indexOf("?")===-1?"?":"&")+"_nonce="+Date.now()),yield(e.fetch||C6)(l,M)})};function s(l,M){return L1(l,function(..._){x().then(function(){return M.apply(this,_)}).catch(function(B){_.pop()(B)})}).bind(n)}function c(l,M){return Q(this,null,function*(){let _={};M=M||{},M.headers=M.headers||new R0,M.headers.get("Content-Type")||M.headers.set("Content-Type","application/json"),M.headers.get("Accept")||M.headers.set("Accept","application/json");let B=yield a(l,M);_.ok=B.ok,_.status=B.status;let A=yield B.json();if(_.data=A,!_.ok)throw _.data.status=_.status,N0(_.data);return Array.isArray(_.data)&&(_.data=_.data.map(function(z){return z.error||z.missing?N0(z):z})),_})}let d;function x(){return Q(this,null,function*(){return e.skip_setup?Promise.resolve():d||(d=c(i).catch(function(l){return l&&l.status&&l.status===404?(w4(404,"PouchDB is just detecting if the remote exists."),c(i,{method:"PUT"})):Promise.reject(l)}).catch(function(l){return l&&l.status&&l.status===412?!0:Promise.reject(l)}),d.catch(function(){d=null}),d)})}c0(function(){o(null,n)}),n._remote=!0,n.type=function(){return"http"},n.id=s("id",function(l){return Q(this,null,function*(){let M;try{M=yield(yield a(A2(t,""))).json()}catch{M={}}let _=M&&M.uuid?M.uuid+t.db:H1(t,"");l(null,_)})}),n.compact=s("compact",function(l,M){return Q(this,null,function*(){typeof l=="function"&&(M=l,l={}),l=O1(l),yield c(H1(t,"_compact"),{method:"POST"});function _(){n.info(function(B,A){A&&!A.compact_running?M(null,{ok:!0}):setTimeout(_,l.interval||200)})}_()})}),n.bulkGet=L1("bulkGet",function(l,M){let _=this;function B(h){return Q(this,null,function*(){let r={};l.revs&&(r.revs=!0),l.attachments&&(r.attachments=!0),l.latest&&(r.latest=!0);try{let v=yield c(H1(t,"_bulk_get"+B2(r)),{method:"POST",body:JSON.stringify({docs:l.docs})});l.attachments&&l.binary&&v.data.results.forEach(function(w){w.docs.forEach(d4)}),h(null,v.data)}catch(v){h(v)}})}function A(){let h=s5,r=Math.ceil(l.docs.length/h),v=0,w=new Array(r);function f(V){return function(D,j){w[V]=j.results,++v===r&&M(null,{results:w.flat()})}}for(let V=0;V<r;V++){let D=s2(l,["revs","attachments","binary","latest"]);D.docs=l.docs.slice(V*h,Math.min(l.docs.length,(V+1)*h)),d6(_,D,f(V))}}let z=A2(t,""),u=l4[z];typeof u!="boolean"?B(function(h,r){h?(l4[z]=!1,w4(h.status,"PouchDB is just detecting if the remote supports the _bulk_get API."),A()):(l4[z]=!0,M(null,r))}):u?B(M):A()}),n._info=function(l){return Q(this,null,function*(){try{yield x();let _=yield(yield a(H1(t,""))).json();_.host=H1(t,""),l(null,_)}catch(M){l(M)}})},n.fetch=function(l,M){return Q(this,null,function*(){yield x();let _=l.substring(0,1)==="/"?A2(t,l.substring(1)):H1(t,l);return a(_,M)})},n.get=s("get",function(l,M,_){return Q(this,null,function*(){typeof M=="function"&&(_=M,M={}),M=O1(M);let B={};M.revs&&(B.revs=!0),M.revs_info&&(B.revs_info=!0),M.latest&&(B.latest=!0),M.open_revs&&(M.open_revs!=="all"&&(M.open_revs=JSON.stringify(M.open_revs)),B.open_revs=M.open_revs),M.rev&&(B.rev=M.rev),M.conflicts&&(B.conflicts=M.conflicts),M.update_seq&&(B.update_seq=M.update_seq),l=b0(l);function A(h){let r=h._attachments,v=r&&Object.keys(r);if(!r||!v.length)return;function w(V){return Q(this,null,function*(){let D=r[V],j=b0(h._id)+"/"+k(V)+"?rev="+h._rev,n1=yield a(H1(t,j)),Y;"buffer"in n1?Y=yield n1.buffer():Y=yield n1.blob();let s1;if(M.binary){let e1=Object.getOwnPropertyDescriptor(Y.__proto__,"type");(!e1||e1.set)&&(Y.type=D.content_type),s1=Y}else s1=yield new Promise(function(e1){G4(Y,e1)});delete D.stub,delete D.length,D.data=s1})}let f=v.map(function(V){return function(){return w(V)}});return i5(f,5)}function z(h){return Array.isArray(h)?Promise.all(h.map(function(r){if(r.ok)return A(r.ok)})):A(h)}let u=H1(t,l+B2(B));try{let h=yield c(u);M.attachments&&(yield z(h.data)),_(null,h.data)}catch(h){h.docId=l,_(h)}})}),n.remove=s("remove",function(l,M,_,B){return Q(this,null,function*(){let A;typeof M=="string"?(A={_id:l,_rev:M},typeof _=="function"&&(B=_,_={})):(A=l,typeof M=="function"?(B=M,_={}):(B=_,_=M));let z=A._rev||_.rev,u=H1(t,b0(A._id))+"?rev="+z;try{let h=yield c(u,{method:"DELETE"});B(null,h.data)}catch(h){B(h)}})});function k(l){return l.split("/").map(encodeURIComponent).join("/")}n.getAttachment=s("getAttachment",function(l,M,_,B){return Q(this,null,function*(){typeof _=="function"&&(B=_,_={});let A=_.rev?"?rev="+_.rev:"",z=H1(t,b0(l))+"/"+k(M)+A,u;try{let h=yield a(z,{method:"GET"});if(!h.ok)throw h;u=h.headers.get("content-type");let r;if(typeof process<"u"&&!process.browser&&typeof h.buffer=="function"?r=yield h.buffer():r=yield h.blob(),typeof process<"u"&&!process.browser){let v=Object.getOwnPropertyDescriptor(r.__proto__,"type");(!v||v.set)&&(r.type=u)}B(null,r)}catch(h){B(h)}})}),n.removeAttachment=s("removeAttachment",function(l,M,_,B){return Q(this,null,function*(){let A=H1(t,b0(l)+"/"+k(M))+"?rev="+_;try{let z=yield c(A,{method:"DELETE"});B(null,z.data)}catch(z){B(z)}})}),n.putAttachment=s("putAttachment",function(l,M,_,B,A,z){return Q(this,null,function*(){typeof A=="function"&&(z=A,A=B,B=_,_=null);let u=b0(l)+"/"+k(M),h=H1(t,u);if(_&&(h+="?rev="+_),typeof B=="string"){let r;try{r=q4(B)}catch{return z(r1(g6,"Attachment is not a valid base64 string"))}B=r?$4(r,A):""}try{let r=yield c(h,{headers:new R0({"Content-Type":A}),method:"PUT",body:B});z(null,r.data)}catch(r){z(r)}})}),n._bulkDocs=function(l,M,_){return Q(this,null,function*(){l.new_edits=M.new_edits;try{yield x(),yield Promise.all(l.docs.map(K3));let B=yield c(H1(t,"_bulk_docs"),{method:"POST",body:JSON.stringify(l)});_(null,B.data)}catch(B){_(B)}})},n._put=function(l,M,_){return Q(this,null,function*(){try{yield x(),yield K3(l);let B=yield c(H1(t,b0(l._id)),{method:"PUT",body:JSON.stringify(l)});_(null,B.data)}catch(B){B.docId=l&&l._id,_(B)}})},n.allDocs=s("allDocs",function(l,M){return Q(this,null,function*(){typeof l=="function"&&(M=l,l={}),l=O1(l);let _={},B,A="GET";l.conflicts&&(_.conflicts=!0),l.update_seq&&(_.update_seq=!0),l.descending&&(_.descending=!0),l.include_docs&&(_.include_docs=!0),l.attachments&&(_.attachments=!0),l.key&&(_.key=JSON.stringify(l.key)),l.start_key&&(l.startkey=l.start_key),l.startkey&&(_.startkey=JSON.stringify(l.startkey)),l.end_key&&(l.endkey=l.end_key),l.endkey&&(_.endkey=JSON.stringify(l.endkey)),typeof l.inclusive_end<"u"&&(_.inclusive_end=!!l.inclusive_end),typeof l.limit<"u"&&(_.limit=l.limit),typeof l.skip<"u"&&(_.skip=l.skip);let z=B2(_);typeof l.keys<"u"&&(A="POST",B={keys:l.keys});try{let u=yield c(H1(t,"_all_docs"+z),{method:A,body:JSON.stringify(B)});l.include_docs&&l.attachments&&l.binary&&u.data.rows.forEach(d4),M(null,u.data)}catch(u){M(u)}})}),n._changes=function(l){let M="batch_size"in l?l.batch_size:a5;l=O1(l),l.continuous&&!("heartbeat"in l)&&(l.heartbeat=r5);let _="timeout"in l?l.timeout:30*1e3;"timeout"in l&&l.timeout&&_-l.timeout<b2&&(_=l.timeout+b2),"heartbeat"in l&&l.heartbeat&&_-l.heartbeat<b2&&(_=l.heartbeat+b2);let B={};"timeout"in l&&l.timeout&&(B.timeout=l.timeout);let A=typeof l.limit<"u"?l.limit:!1,z=A;if(l.style&&(B.style=l.style),(l.include_docs||l.filter&&typeof l.filter=="function")&&(B.include_docs=!0),l.attachments&&(B.attachments=!0),l.continuous&&(B.feed="longpoll"),l.seq_interval&&(B.seq_interval=l.seq_interval),l.conflicts&&(B.conflicts=!0),l.descending&&(B.descending=!0),l.update_seq&&(B.update_seq=!0),"heartbeat"in l&&l.heartbeat&&(B.heartbeat=l.heartbeat),l.filter&&typeof l.filter=="string"&&(B.filter=l.filter),l.view&&typeof l.view=="string"&&(B.filter="_view",B.view=l.view),l.query_params&&typeof l.query_params=="object")for(let D in l.query_params)Object.prototype.hasOwnProperty.call(l.query_params,D)&&(B[D]=l.query_params[D]);let u="GET",h;l.doc_ids?(B.filter="_doc_ids",u="POST",h={doc_ids:l.doc_ids}):l.selector&&(B.filter="_selector",u="POST",h={selector:l.selector});let r=new AbortController,v,w=function(D,j){return Q(this,null,function*(){if(l.aborted)return;B.since=D,typeof B.since=="object"&&(B.since=JSON.stringify(B.since)),l.descending?A&&(B.limit=z):B.limit=!A||z>M?M:z;let n1=H1(t,"_changes"+B2(B)),Y={signal:r.signal,method:u,body:JSON.stringify(h)};if(v=D,!l.aborted)try{yield x();let s1=yield c(n1,Y);j(null,s1.data)}catch(s1){j(s1)}})},f={results:[]},V=function(D,j){if(l.aborted)return;let n1=0;if(j&&j.results){n1=j.results.length,f.last_seq=j.last_seq;let s1=null,e1=null;typeof j.pending=="number"&&(s1=j.pending),(typeof f.last_seq=="string"||typeof f.last_seq=="number")&&(e1=f.last_seq);let i1={};i1.query=l.query_params,j.results=j.results.filter(function(d1){z--;let v1=R4(l)(d1);return v1&&(l.include_docs&&l.attachments&&l.binary&&d4(d1),l.return_docs&&f.results.push(d1),l.onChange(d1,s1,e1)),v1})}else if(D){l.aborted=!0,l.complete(D);return}j&&j.last_seq&&(v=j.last_seq);let Y=A&&z<=0||j&&n1<M||l.descending;l.continuous&&!(A&&z<=0)||!Y?c0(function(){w(v,V)}):l.complete(null,f)};return w(l.since||0,V),{cancel:function(){l.aborted=!0,r.abort()}}},n.revsDiff=s("revsDiff",function(l,M,_){return Q(this,null,function*(){typeof M=="function"&&(_=M,M={});try{let B=yield c(H1(t,"_revs_diff"),{method:"POST",body:JSON.stringify(l)});_(null,B.data)}catch(B){_(B)}})}),n._close=function(l){l()},n._destroy=function(l,M){return Q(this,null,function*(){try{let _=yield c(H1(t,""),{method:"DELETE"});M(null,_)}catch(_){_.status===404?M(null,{ok:!0}):M(_)}})}}L4.valid=function(){return!0};function v5(e){e.adapter("http",L4,!1),e.adapter("https",L4,!1)}var B0=class e extends Error{constructor(o){super(),this.status=400,this.name="query_parse_error",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},E2=class e extends Error{constructor(o){super(),this.status=404,this.name="not_found",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},P2=class e extends Error{constructor(o){super(),this.status=500,this.name="invalid_value",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}};function E6(e,o){return o&&e.then(function(n){c0(function(){o(null,n)})},function(n){c0(function(){o(n)})}),e}function g5(e){return function(...o){var n=o.pop(),t=e.apply(this,o);return typeof n=="function"&&E6(t,n),t}}function h5(e,o){return e.then(function(n){return o().then(function(){return n})},function(n){return o().then(function(){throw n})})}function v4(e,o){return function(){var n=arguments,t=this;return e.add(function(){return o.apply(t,n)})}}function W3(e){var o=new Set(e),n=new Array(o.size),t=-1;return o.forEach(function(i){n[++t]=i}),n}function g4(e){var o=new Array(e.size),n=-1;return e.forEach(function(t,i){o[++n]=i}),o}function Z3(e){var o="builtin "+e+" function requires map values to be numbers or number arrays";return new P2(o)}function I4(e){for(var o=0,n=0,t=e.length;n<t;n++){var i=e[n];if(typeof i!="number")if(Array.isArray(i)){o=typeof o=="number"?[o]:o;for(var a=0,s=i.length;a<s;a++){var c=i[a];if(typeof c!="number")throw Z3("_sum");typeof o[a]>"u"?o.push(c):o[a]+=c}}else throw Z3("_sum");else typeof o=="number"?o+=i:o[0]+=i}return o}var u5=G1.bind(null,"log"),p5=Array.isArray,m5=JSON.parse;function P6(e,o){return F4("return ("+e.replace(/;\s*$/,"")+");",{emit:o,sum:I4,log:u5,isArray:p5,toJSON:m5})}var a2=class{constructor(){this.promise=Promise.resolve()}add(o){return this.promise=this.promise.catch(()=>{}).then(()=>o()),this.promise}finish(){return this.promise}};function Q3(e){if(!e)return"undefined";switch(typeof e){case"function":return e.toString();case"string":return e.toString();default:return JSON.stringify(e)}}function w5(e,o){return Q3(e)+Q3(o)+"undefined"}function X3(e,o,n,t,i,a){return Q(this,null,function*(){let s=w5(n,t),c;if(!i&&(c=e._cachedViews=e._cachedViews||{},c[s]))return c[s];let d=e.info().then(function(x){return Q(this,null,function*(){let k=x.db_name+"-mrview-"+(i?"temp":w6(s));function l(z){z.views=z.views||{};let u=o;u.indexOf("/")===-1&&(u=o+"/"+o);let h=z.views[u]=z.views[u]||{};if(!h[k])return h[k]=!0,z}yield j2(e,"_local/"+a,l);let _=(yield e.registerDependentDatabase(k)).db;_.auto_compaction=!0;let B={name:k,db:_,sourceDB:e,adapter:e.adapter,mapFun:n,reduceFun:t},A;try{A=yield B.db.get("_local/lastSeq")}catch(z){if(z.status!==404)throw z}return B.seq=A?A.seq:0,c&&B.db.once("destroyed",function(){delete c[s]}),B})});return c&&(c[s]=d),d})}var J3={},Y3=new a2,f5=50;function h4(e){return e.indexOf("/")===-1?[e,e]:e.split("/")}function x5(e){return e.length===1&&/^1-/.test(e[0].rev)}function e6(e,o,n){try{e.emit("error",o)}catch{G1("error",`The user's map/reduce function threw an uncaught error.
You can debug this error by doing:
myDatabase.on('error', function (err) { debugger; });
Please double-check your map/reduce function.`),G1("error",o,n)}}function M5(e,o,n,t){function i(g,p,y){try{p(y)}catch(C){e6(g,C,{fun:p,doc:y})}}function a(g,p,y,C,L){try{return{output:p(y,C,L)}}catch(E){return e6(g,E,{fun:p,keys:y,values:C,rereduce:L}),{error:E}}}function s(g,p){let y=I1(g.key,p.key);return y!==0?y:I1(g.value,p.value)}function c(g,p,y){return y=y||0,typeof p=="number"?g.slice(y,p+y):y>0?g.slice(y):g}function d(g){let p=g.value;return p&&typeof p=="object"&&p._id||g.id}function x(g){for(let p of g.rows){let y=p.doc&&p.doc._attachments;if(y)for(let C of Object.keys(y)){let L=y[C];y[C].data=U4(L.data,L.content_type)}}}function k(g){return function(p){return g.include_docs&&g.attachments&&g.binary&&x(p),p}}function l(g,p,y,C){let L=p[g];typeof L<"u"&&(C&&(L=encodeURIComponent(JSON.stringify(L))),y.push(g+"="+L))}function M(g){if(typeof g<"u"){let p=Number(g);return!isNaN(p)&&p===parseInt(g,10)?p:g}}function _(g){return g.group_level=M(g.group_level),g.limit=M(g.limit),g.skip=M(g.skip),g}function B(g){if(g){if(typeof g!="number")return new B0(`Invalid value for integer: "${g}"`);if(g<0)return new B0(`Invalid value for positive integer: "${g}"`)}}function A(g,p){let y=g.descending?"endkey":"startkey",C=g.descending?"startkey":"endkey";if(typeof g[y]<"u"&&typeof g[C]<"u"&&I1(g[y],g[C])>0)throw new B0("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(p.reduce&&g.reduce!==!1){if(g.include_docs)throw new B0("{include_docs:true} is invalid for reduce");if(g.keys&&g.keys.length>1&&!g.group&&!g.group_level)throw new B0("Multi-key fetches for reduce views must use {group: true}")}for(let L of["group_level","limit","skip"]){let E=B(g[L]);if(E)throw E}}function z(g,p,y){return Q(this,null,function*(){let C=[],L,E="GET",X;if(l("reduce",y,C),l("include_docs",y,C),l("attachments",y,C),l("limit",y,C),l("descending",y,C),l("group",y,C),l("group_level",y,C),l("skip",y,C),l("stale",y,C),l("conflicts",y,C),l("startkey",y,C,!0),l("start_key",y,C,!0),l("endkey",y,C,!0),l("end_key",y,C,!0),l("inclusive_end",y,C),l("key",y,C,!0),l("update_seq",y,C),C=C.join("&"),C=C===""?"":"?"+C,typeof y.keys<"u"){let H=`keys=${encodeURIComponent(JSON.stringify(y.keys))}`;H.length+C.length+1<=2e3?C+=(C[0]==="?"?"&":"?")+H:(E="POST",typeof p=="string"?L={keys:y.keys}:p.keys=y.keys)}if(typeof p=="string"){let S=h4(p),H=yield g.fetch("_design/"+S[0]+"/_view/"+S[1]+C,{headers:new R0({"Content-Type":"application/json"}),method:E,body:JSON.stringify(L)});X=H.ok;let $=yield H.json();if(!X)throw $.status=H.status,N0($);for(let Z of $.rows)if(Z.value&&Z.value.error&&Z.value.error==="builtin_reduce_error")throw new Error(Z.reason);return new Promise(function(Z){Z($)}).then(k(y))}L=L||{};for(let S of Object.keys(p))Array.isArray(p[S])?L[S]=p[S]:L[S]=p[S].toString();let I=yield g.fetch("_temp_view"+C,{headers:new R0({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(L)});X=I.ok;let b=yield I.json();if(!X)throw b.status=I.status,N0(b);return new Promise(function(S){S(b)}).then(k(y))})}function u(g,p,y){return new Promise(function(C,L){g._query(p,y,function(E,X){if(E)return L(E);C(X)})})}function h(g){return new Promise(function(p,y){g._viewCleanup(function(C,L){if(C)return y(C);p(L)})})}function r(g){return function(p){if(p.status===404)return g;throw p}}function v(g,p,y){return Q(this,null,function*(){let C="_local/doc_"+g,L={_id:C,keys:[]},E=y.get(g),X=E[0],I=E[1];function b(){return x5(I)?Promise.resolve(L):p.db.get(C).catch(r(L))}function S(c1){return c1.keys.length?p.db.allDocs({keys:c1.keys,include_docs:!0}):Promise.resolve({rows:[]})}function H(c1,w1){let C1=[],V1=new Set;for(let o1 of w1.rows){let t1=o1.doc;if(t1&&(C1.push(t1),V1.add(t1._id),t1._deleted=!X.has(t1._id),!t1._deleted)){let a1=X.get(t1._id);"value"in a1&&(t1.value=a1.value)}}let T1=g4(X);for(let o1 of T1)if(!V1.has(o1)){let t1={_id:o1},a1=X.get(o1);"value"in a1&&(t1.value=a1.value),C1.push(t1)}return c1.keys=W3(T1.concat(c1.keys)),C1.push(c1),C1}let $=yield b(),Z=yield S($);return H($,Z)})}function w(g){return g.sourceDB.get("_local/purges").then(function(p){let y=p.purgeSeq;return g.db.get("_local/purgeSeq").then(function(C){return C._rev}).catch(r(void 0)).then(function(C){return g.db.put({_id:"_local/purgeSeq",_rev:C,purgeSeq:y})})}).catch(function(p){if(p.status!==404)throw p})}function f(g,p,y){var C="_local/lastSeq";return g.db.get(C).catch(r({_id:C,seq:0})).then(function(L){var E=g4(p);return Promise.all(E.map(function(X){return v(X,g,p)})).then(function(X){var I=X.flat();return L.seq=y,I.push(L),g.db.bulkDocs({docs:I})}).then(()=>w(g))})}function V(g){let p=typeof g=="string"?g:g.name,y=J3[p];return y||(y=J3[p]=new a2),y}function D(g,p){return Q(this,null,function*(){return v4(V(g),function(){return j(g,p)})()})}function j(g,p){return Q(this,null,function*(){let y,C,L;function E(o1,t1){let a1={id:C._id,key:V0(o1)};typeof t1<"u"&&t1!==null&&(a1.value=V0(t1)),y.push(a1)}let X=o(g.mapFun,E),I=g.seq||0;function b(){return g.sourceDB.info().then(function(o1){L=g.sourceDB.activeTasks.add({name:"view_indexing",total_items:o1.update_seq-I})})}function S(o1,t1){return function(){return f(g,o1,t1)}}let H=0,$={view:g.name,indexed_docs:H};g.sourceDB.emit("indexing",$);let Z=new a2;function c1(){return Q(this,null,function*(){let o1=yield g.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:I,limit:p.changes_batch_size}),t1=yield w1();return C1(o1,t1)})}function w1(){return g.db.get("_local/purgeSeq").then(function(o1){return o1.purgeSeq}).catch(r(-1)).then(function(o1){return g.sourceDB.get("_local/purges").then(function(t1){let a1=t1.purges.filter(function(B1,u1){return u1>o1}).map(B1=>B1.docId),S1=a1.filter(function(B1,u1){return a1.indexOf(B1)===u1});return Promise.all(S1.map(function(B1){return g.sourceDB.get(B1).then(function(u1){return{docId:B1,doc:u1}}).catch(r({docId:B1}))}))}).catch(r([]))})}function C1(o1,t1){let a1=o1.results;if(!a1.length&&!t1.length)return;for(let u1 of t1)if(a1.findIndex(function(W1){return W1.id===u1.docId})<0){let W1={_id:u1.docId,doc:{_id:u1.docId,_deleted:1},changes:[]};u1.doc&&(W1.doc=u1.doc,W1.changes.push({rev:u1.doc._rev})),a1.push(W1)}let S1=V1(a1);Z.add(S(S1,I)),H=H+a1.length;let B1={view:g.name,last_seq:o1.last_seq,results_count:a1.length,indexed_docs:H};if(g.sourceDB.emit("indexing",B1),g.sourceDB.activeTasks.update(L,{completed_items:H}),!(a1.length<p.changes_batch_size))return c1()}function V1(o1){let t1=new Map;for(let a1 of o1){if(a1.doc._id[0]!=="_"){y=[],C=a1.doc,C._deleted||i(g.sourceDB,X,C),y.sort(s);let S1=T1(y);t1.set(a1.doc._id,[S1,a1.changes])}I=a1.seq}return t1}function T1(o1){let t1=new Map,a1;for(let S1=0,B1=o1.length;S1<B1;S1++){let u1=o1[S1],M0=[u1.key,u1.id];S1>0&&I1(u1.key,a1)===0&&M0.push(S1),t1.set(Q1(M0),u1),a1=u1.key}return t1}try{yield b(),yield c1(),yield Z.finish(),g.seq=I,g.sourceDB.activeTasks.remove(L)}catch(o1){g.sourceDB.activeTasks.remove(L,o1)}})}function n1(g,p,y){y.group_level===0&&delete y.group_level;let C=y.group||y.group_level,L=n(g.reduceFun),E=[],X=isNaN(y.group_level)?Number.POSITIVE_INFINITY:y.group_level;for(let I of p){let b=E[E.length-1],S=C?I.key:null;if(C&&Array.isArray(S)&&(S=S.slice(0,X)),b&&I1(b.groupKey,S)===0){b.keys.push([I.key,I.id]),b.values.push(I.value);continue}E.push({keys:[[I.key,I.id]],values:[I.value],groupKey:S})}p=[];for(let I of E){let b=a(g.sourceDB,L,I.keys,I.values,!1);if(b.error&&b.error instanceof P2)throw b.error;p.push({value:b.error?null:b.output,key:I.groupKey})}return{rows:c(p,y.limit,y.skip)}}function Y(g,p){return v4(V(g),function(){return s1(g,p)})()}function s1(g,p){return Q(this,null,function*(){let y,C=g.reduceFun&&p.reduce!==!1,L=p.skip||0;typeof p.keys<"u"&&!p.keys.length&&(p.limit=0,delete p.keys);function E(I){return Q(this,null,function*(){I.include_docs=!0;let b=yield g.db.allDocs(I);return y=b.total_rows,b.rows.map(function(S){if("value"in S.doc&&typeof S.doc.value=="object"&&S.doc.value!==null){let $=Object.keys(S.doc.value).sort(),Z=["id","key","value"];if(!($<Z||$>Z))return S.doc.value}let H=vs(S.doc._id);return{key:H[0],id:H[1],value:"value"in S.doc?S.doc.value:null}})})}function X(I){return Q(this,null,function*(){let b;if(C?b=n1(g,I,p):typeof p.keys>"u"?b={total_rows:y,offset:L,rows:I}:b={total_rows:y,offset:L,rows:c(I,p.limit,p.skip)},p.update_seq&&(b.update_seq=g.seq),p.include_docs){let S=W3(I.map(d)),H=yield g.sourceDB.allDocs({keys:S,include_docs:!0,conflicts:p.conflicts,attachments:p.attachments,binary:p.binary}),$=new Map;for(let Z of H.rows)$.set(Z.id,Z.doc);for(let Z of I){let c1=d(Z),w1=$.get(c1);w1&&(Z.doc=w1)}}return b})}if(typeof p.keys<"u"){let b=p.keys.map(function($){let Z={startkey:Q1([$]),endkey:Q1([$,{}])};return p.update_seq&&(Z.update_seq=!0),E(Z)}),H=(yield Promise.all(b)).flat();return X(H)}else{let I={descending:p.descending};p.update_seq&&(I.update_seq=!0);let b,S;if("start_key"in p&&(b=p.start_key),"startkey"in p&&(b=p.startkey),"end_key"in p&&(S=p.end_key),"endkey"in p&&(S=p.endkey),typeof b<"u"&&(I.startkey=p.descending?Q1([b,{}]):Q1([b])),typeof S<"u"){let $=p.inclusive_end!==!1;p.descending&&($=!$),I.endkey=Q1($?[S,{}]:[S])}if(typeof p.key<"u"){let $=Q1([p.key]),Z=Q1([p.key,{}]);I.descending?(I.endkey=$,I.startkey=Z):(I.startkey=$,I.endkey=Z)}C||(typeof p.limit=="number"&&(I.limit=p.limit),I.skip=L);let H=yield E(I);return X(H)}})}function e1(g){return Q(this,null,function*(){return(yield g.fetch("_view_cleanup",{headers:new R0({"Content-Type":"application/json"}),method:"POST"})).json()})}function i1(g){return Q(this,null,function*(){try{let p=yield g.get("_local/"+e),y=new Map;for(let b of Object.keys(p.views)){let S=h4(b),H="_design/"+S[0],$=S[1],Z=y.get(H);Z||(Z=new Set,y.set(H,Z)),Z.add($)}let C={keys:g4(y),include_docs:!0},L=yield g.allDocs(C),E={};for(let b of L.rows){let S=b.key.substring(8);for(let H of y.get(b.key)){let $=S+"/"+H;p.views[$]||($=H);let Z=Object.keys(p.views[$]),c1=b.doc&&b.doc.views&&b.doc.views[H];for(let w1 of Z)E[w1]=E[w1]||c1}}let I=Object.keys(E).filter(function(b){return!E[b]}).map(function(b){return v4(V(b),function(){return new g.constructor(b,g.__opts).destroy()})()});return Promise.all(I).then(function(){return{ok:!0}})}catch(p){if(p.status===404)return{ok:!0};throw p}})}function d1(g,p,y){return Q(this,null,function*(){if(typeof g._query=="function")return u(g,p,y);if(s0(g))return z(g,p,y);let C={changes_batch_size:g.__opts.view_update_changes_batch_size||f5};if(typeof p!="string")return A(y,p),Y3.add(function(){return Q(this,null,function*(){let L=yield X3(g,"temp_view/temp_view",p.map,p.reduce,!0,e);return h5(D(L,C).then(function(){return Y(L,y)}),function(){return L.db.destroy()})})}),Y3.finish();{let L=p,E=h4(L),X=E[0],I=E[1],b=yield g.get("_design/"+X);if(p=b.views&&b.views[I],!p)throw new E2(`ddoc ${b._id} has no view named ${I}`);t(b,I),A(y,p);let S=yield X3(g,L,p.map,p.reduce,!1,e);return y.stale==="ok"||y.stale==="update_after"?(y.stale==="update_after"&&c0(function(){D(S,C)}),Y(S,y)):(yield D(S,C),Y(S,y))}})}function v1(g,p,y){let C=this;typeof p=="function"&&(y=p,p={}),p=p?_(p):{},typeof g=="function"&&(g={map:g});let L=Promise.resolve().then(function(){return d1(C,g,p)});return E6(L,y),L}let p1=g5(function(){let g=this;return typeof g._viewCleanup=="function"?h(g):s0(g)?e1(g):i1(g)});return{query:v1,viewCleanup:p1}}var u4={_sum:function(e,o){return I4(o)},_count:function(e,o){return o.length},_stats:function(e,o){function n(t){for(var i=0,a=0,s=t.length;a<s;a++){var c=t[a];i+=c*c}return i}return{sum:I4(o),min:Math.min.apply(null,o),max:Math.max.apply(null,o),count:o.length,sumsqr:n(o)}}};function k5(e){if(/^_sum/.test(e))return u4._sum;if(/^_count/.test(e))return u4._count;if(/^_stats/.test(e))return u4._stats;if(/^_/.test(e))throw new Error(e+" is not a supported reduce function.")}function y5(e,o){if(typeof e=="function"&&e.length===2){var n=e;return function(t){return n(t,o)}}else return P6(e.toString(),o)}function z5(e){var o=e.toString(),n=k5(o);return n||P6(o)}function C5(e,o){var n=e.views&&e.views[o];if(typeof n.map!="string")throw new E2("ddoc "+e._id+" has no string view named "+o+", instead found object of type: "+typeof n.map)}var _5="mrviews",T6=M5(_5,y5,z5,C5);function b5(e,o,n){return T6.query.call(this,e,o,n)}function B5(e){return T6.viewCleanup.call(this,e)}var S5={query:b5,viewCleanup:B5};function A5(e,o,n){return!e._attachments||!e._attachments[n]||e._attachments[n].digest!==o._attachments[n].digest}function t6(e,o){var n=Object.keys(o._attachments);return Promise.all(n.map(function(t){return e.getAttachment(o._id,t,{rev:o._rev})}))}function L5(e,o,n){var t=s0(o)&&!s0(e),i=Object.keys(n._attachments);return t?e.get(n._id).then(function(a){return Promise.all(i.map(function(s){return A5(a,n,s)?o.getAttachment(n._id,s):e.getAttachment(a._id,s)}))}).catch(function(a){if(a.status!==404)throw a;return t6(o,n)}):t6(o,n)}function I5(e){var o=[];return Object.keys(e).forEach(function(n){var t=e[n].missing;t.forEach(function(i){o.push({id:n,rev:i})})}),{docs:o,revs:!0,latest:!0}}function j5(e,o,n,t){n=O1(n);var i=[],a=!0;function s(){var d=I5(n);if(d.docs.length)return e.bulkGet(d).then(function(x){if(t.cancelled)throw new Error("cancelled");return Promise.all(x.results.map(function(k){return Promise.all(k.docs.map(function(l){var M=l.ok;return l.error&&(a=!1),!M||!M._attachments?M:L5(o,e,M).then(_=>{var B=Object.keys(M._attachments);return _.forEach(function(A,z){var u=M._attachments[B[z]];delete u.stub,delete u.length,u.data=A}),M})}))})).then(function(k){i=i.concat(k.flat().filter(Boolean))})})}function c(){return{ok:a,docs:i}}return Promise.resolve().then(s).then(c)}var n6=1,o6="pouchdb",V5=5,Z1=0;function j4(e,o,n,t,i){return e.get(o).catch(function(a){if(a.status===404)return(e.adapter==="http"||e.adapter==="https")&&w4(404,"PouchDB is just checking if a remote checkpoint exists."),{session_id:t,_id:o,history:[],replicator:o6,version:n6};throw a}).then(function(a){if(!i.cancelled&&a.last_seq!==n)return a.history=(a.history||[]).filter(function(s){return s.session_id!==t}),a.history.unshift({last_seq:n,session_id:t}),a.history=a.history.slice(0,V5),a.version=n6,a.replicator=o6,a.session_id=t,a.last_seq=n,e.put(a).catch(function(s){if(s.status===409)return j4(e,o,n,t,i);throw s})})}var T2=class{constructor(o,n,t,i,a={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0}){this.src=o,this.target=n,this.id=t,this.returnValue=i,this.opts=a,typeof a.writeSourceCheckpoint>"u"&&(a.writeSourceCheckpoint=!0),typeof a.writeTargetCheckpoint>"u"&&(a.writeTargetCheckpoint=!0)}writeCheckpoint(o,n){var t=this;return this.updateTarget(o,n).then(function(){return t.updateSource(o,n)})}updateTarget(o,n){return this.opts.writeTargetCheckpoint?j4(this.target,this.id,o,n,this.returnValue):Promise.resolve(!0)}updateSource(o,n){if(this.opts.writeSourceCheckpoint){var t=this;return j4(this.src,this.id,o,n,this.returnValue).catch(function(i){if(a6(i))return t.opts.writeSourceCheckpoint=!1,!0;throw i})}else return Promise.resolve(!0)}getCheckpoint(){var o=this;return!o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?Promise.resolve(Z1):o.opts&&o.opts.writeSourceCheckpoint&&!o.opts.writeTargetCheckpoint?o.src.get(o.id).then(function(n){return n.last_seq||Z1}).catch(function(n){if(n.status!==404)throw n;return Z1}):o.target.get(o.id).then(function(n){return o.opts&&o.opts.writeTargetCheckpoint&&!o.opts.writeSourceCheckpoint?n.last_seq||Z1:o.src.get(o.id).then(function(t){if(n.version!==t.version)return Z1;var i;return n.version?i=n.version.toString():i="undefined",i in i6?i6[i](n,t):Z1},function(t){if(t.status===404&&n.last_seq)return o.src.put({_id:o.id,last_seq:Z1}).then(function(){return Z1},function(i){return a6(i)?(o.opts.writeSourceCheckpoint=!1,n.last_seq):Z1});throw t})}).catch(function(n){if(n.status!==404)throw n;return Z1})}},i6={undefined:function(e,o){return I1(e.last_seq,o.last_seq)===0?o.last_seq:0},1:function(e,o){return H5(o,e).last_seq}};function H5(e,o){return e.session_id===o.session_id?{last_seq:e.last_seq,history:e.history}:R6(e.history,o.history)}function R6(e,o){var n=e[0],t=e.slice(1),i=o[0],a=o.slice(1);if(!n||o.length===0)return{last_seq:Z1,history:[]};var s=n.session_id;if(V4(s,o))return{last_seq:n.last_seq,history:e};var c=i.session_id;return V4(c,t)?{last_seq:i.last_seq,history:a}:R6(t,a)}function V4(e,o){var n=o[0],t=o.slice(1);return!e||o.length===0?!1:e===n.session_id?!0:V4(e,t)}function a6(e){return typeof e.status=="number"&&Math.floor(e.status/100)===4}function F6(e,o,n,t,i){return this instanceof T2?F6:new T2(e,o,n,t,i)}var s6=0;function O5(e,o,n,t){if(e.retry===!1){o.emit("error",n),o.removeAllListeners();return}if(typeof e.back_off_function!="function"&&(e.back_off_function=ra),o.emit("requestError",n),o.state==="active"||o.state==="pending"){o.emit("paused",n),o.state="stopped";var i=function(){e.current_back_off=s6},a=function(){o.removeListener("active",i)};o.once("paused",a),o.once("active",i)}e.current_back_off=e.current_back_off||s6,e.current_back_off=e.back_off_function(e.current_back_off),setTimeout(t,e.current_back_off)}function D5(e){return Object.keys(e).sort(I1).reduce(function(o,n){return o[n]=e[n],o},{})}function E5(e,o,n){var t=n.doc_ids?n.doc_ids.sort(I1):"",i=n.filter?n.filter.toString():"",a="",s="",c="";return n.selector&&(c=JSON.stringify(n.selector)),n.filter&&n.query_params&&(a=JSON.stringify(D5(n.query_params))),n.filter&&n.filter==="_view"&&(s=n.view.toString()),Promise.all([e.id(),o.id()]).then(function(d){var x=d[0]+d[1]+i+s+a+t+c;return new Promise(function(k){K4(x,k)})}).then(function(d){return d=d.replace(/\//g,".").replace(/\+/g,"_"),"_local/"+d})}function q6(e,o,n,t,i){var a=[],s,c={seq:0,changes:[],docs:[]},d=!1,x=!1,k=!1,l=0,M=0,_=n.continuous||n.live||!1,B=n.batch_size||100,A=n.batches_limit||10,z=n.style||"all_docs",u=!1,h=n.doc_ids,r=n.selector,v,w,f=[],V=q2(),D;i=i||{ok:!0,start_time:new Date().toISOString(),docs_read:0,docs_written:0,doc_write_failures:0,errors:[]};var j={};t.ready(e,o);function n1(){return w?Promise.resolve():E5(e,o,n).then(function(b){v=b;var S={};n.checkpoint===!1?S={writeSourceCheckpoint:!1,writeTargetCheckpoint:!1}:n.checkpoint==="source"?S={writeSourceCheckpoint:!0,writeTargetCheckpoint:!1}:n.checkpoint==="target"?S={writeSourceCheckpoint:!1,writeTargetCheckpoint:!0}:S={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0},w=new F6(e,o,v,t,S)})}function Y(){if(f=[],s.docs.length!==0){var b=s.docs,S={timeout:n.timeout};return o.bulkDocs({docs:b,new_edits:!1},S).then(function(H){if(t.cancelled)throw g(),new Error("cancelled");var $=Object.create(null);H.forEach(function(c1){c1.error&&($[c1.id]=c1)});var Z=Object.keys($).length;i.doc_write_failures+=Z,i.docs_written+=b.length-Z,b.forEach(function(c1){var w1=$[c1._id];if(w1){i.errors.push(w1);var C1=(w1.name||"").toLowerCase();if(C1==="unauthorized"||C1==="forbidden")t.emit("denied",O1(w1));else throw w1}else f.push(c1)})},function(H){throw i.doc_write_failures+=b.length,H})}}function s1(){if(s.error)throw new Error("There was a problem getting docs.");i.last_seq=M=s.seq;var b=O1(i);return f.length&&(b.docs=f,typeof s.pending=="number"&&(b.pending=s.pending,delete s.pending),t.emit("change",b)),d=!0,e.info().then(function(S){var H=e.activeTasks.get(D);if(!(!s||!H)){var $=H.completed_items||0,Z=parseInt(S.update_seq,10)-parseInt(l,10);e.activeTasks.update(D,{completed_items:$+s.changes.length,total_items:Z})}}),w.writeCheckpoint(s.seq,V).then(function(){if(t.emit("checkpoint",{checkpoint:s.seq}),d=!1,t.cancelled)throw g(),new Error("cancelled");s=void 0,L()}).catch(function(S){throw I(S),S})}function e1(){var b={};return s.changes.forEach(function(S){t.emit("checkpoint",{revs_diff:S}),S.id!=="_user/"&&(b[S.id]=S.changes.map(function(H){return H.rev}))}),o.revsDiff(b).then(function(S){if(t.cancelled)throw g(),new Error("cancelled");s.diffs=S})}function i1(){return j5(e,o,s.diffs,t).then(function(b){s.error=!b.ok,b.docs.forEach(function(S){delete s.diffs[S._id],i.docs_read++,s.docs.push(S)})})}function d1(){if(!(t.cancelled||s)){if(a.length===0){v1(!0);return}s=a.shift(),t.emit("checkpoint",{start_next_batch:s.seq}),e1().then(i1).then(Y).then(s1).then(d1).catch(function(b){p1("batch processing terminated with error",b)})}}function v1(b){if(c.changes.length===0){a.length===0&&!s&&((_&&j.live||x)&&(t.state="pending",t.emit("paused")),x&&g());return}(b||x||c.changes.length>=B)&&(a.push(c),c={seq:0,changes:[],docs:[]},(t.state==="pending"||t.state==="stopped")&&(t.state="active",t.emit("active")),d1())}function p1(b,S){k||(S.message||(S.message=b),i.ok=!1,i.status="aborting",a=[],c={seq:0,changes:[],docs:[]},g(S))}function g(b){if(!k&&!(t.cancelled&&(i.status="cancelled",d)))if(i.status=i.status||"complete",i.end_time=new Date().toISOString(),i.last_seq=M,k=!0,e.activeTasks.remove(D,b),b){b=r1(b),b.result=i;var S=(b.name||"").toLowerCase();S==="unauthorized"||S==="forbidden"?(t.emit("error",b),t.removeAllListeners()):O5(n,t,b,function(){q6(e,o,n,t)})}else t.emit("complete",i),t.removeAllListeners()}function p(b,S,H){if(t.cancelled)return g();typeof S=="number"&&(c.pending=S);var $=R4(n)(b);if(!$){var Z=e.activeTasks.get(D);if(Z){var c1=Z.completed_items||0;e.activeTasks.update(D,{completed_items:++c1})}return}c.seq=b.seq||H,c.changes.push(b),t.emit("checkpoint",{pending_batch:c.seq}),c0(function(){v1(a.length===0&&j.live)})}function y(b){if(u=!1,t.cancelled)return g();if(b.results.length>0)j.since=b.results[b.results.length-1].seq,L(),v1(!0);else{var S=function(){_?(j.live=!0,L()):x=!0,v1(!0)};!s&&b.results.length===0?(d=!0,w.writeCheckpoint(b.last_seq,V).then(function(){if(d=!1,i.last_seq=M=b.last_seq,t.cancelled)throw g(),new Error("cancelled");S()}).catch(I)):S()}}function C(b){if(u=!1,t.cancelled)return g();p1("changes rejected",b)}function L(){if(!(!u&&!x&&a.length<A))return;u=!0;function b(){H.cancel()}function S(){t.removeListener("cancel",b)}t._changes&&(t.removeListener("cancel",t._abortChanges),t._changes.cancel()),t.once("cancel",b);var H=e.changes(j).on("change",p);H.then(S,S),H.then(y).catch(C),n.retry&&(t._changes=H,t._abortChanges=b)}function E(b){return e.info().then(function(S){var H=typeof n.since>"u"?parseInt(S.update_seq,10)-parseInt(b,10):parseInt(S.update_seq,10);return D=e.activeTasks.add({name:`${_?"continuous ":""}replication from ${S.db_name}`,total_items:H}),b})}function X(){n1().then(function(){if(t.cancelled){g();return}return w.getCheckpoint().then(E).then(function(b){M=b,l=b,j={since:M,limit:B,batch_size:B,style:z,doc_ids:h,selector:r,return_docs:!0},n.filter&&(typeof n.filter!="string"?j.include_docs=!0:j.filter=n.filter),"heartbeat"in n&&(j.heartbeat=n.heartbeat),"timeout"in n&&(j.timeout=n.timeout),n.query_params&&(j.query_params=n.query_params),n.view&&(j.view=n.view),L()})}).catch(function(b){p1("getCheckpoint rejected with ",b)})}function I(b){d=!1,p1("writeCheckpoint completed with error",b)}if(t.cancelled){g();return}t._addedListeners||(t.once("cancel",g),typeof n.complete=="function"&&(t.once("error",n.complete),t.once("complete",function(b){n.complete(null,b)})),t._addedListeners=!0),typeof n.since>"u"?X():n1().then(function(){return d=!0,w.writeCheckpoint(n.since,V)}).then(function(){if(d=!1,t.cancelled){g();return}M=n.since,X()}).catch(I)}var H4=class extends r0.default{constructor(){super(),this.cancelled=!1,this.state="pending";let o=new Promise((n,t)=>{this.once("complete",n),this.once("error",t)});this.then=function(n,t){return o.then(n,t)},this.catch=function(n){return o.catch(n)},this.catch(function(){})}cancel(){this.cancelled=!0,this.state="cancelled",this.emit("cancel")}ready(o,n){if(this._readyCalled)return;this._readyCalled=!0;let t=()=>{this.cancel()};o.once("destroyed",t),n.once("destroyed",t);function i(){o.removeListener("destroyed",t),n.removeListener("destroyed",t)}this.once("complete",i),this.once("error",i)}};function R2(e,o){var n=o.PouchConstructor;return typeof e=="string"?new n(e,o):e}function O4(e,o,n,t){if(typeof n=="function"&&(t=n,n={}),typeof n>"u"&&(n={}),n.doc_ids&&!Array.isArray(n.doc_ids))throw r1(F2,"`doc_ids` filter parameter is not a list.");n.complete=t,n=O1(n),n.continuous=n.continuous||n.live,n.retry="retry"in n?n.retry:!1,n.PouchConstructor=n.PouchConstructor||this;var i=new H4(n),a=R2(e,n),s=R2(o,n);return q6(a,s,n,i),i}function P5(e,o,n,t){return typeof n=="function"&&(t=n,n={}),typeof n>"u"&&(n={}),n=O1(n),n.PouchConstructor=n.PouchConstructor||this,e=R2(e,n),o=R2(o,n),new D4(e,o,n,t)}var D4=class extends r0.default{constructor(o,n,t,i){super(),this.canceled=!1;let a=t.push?Object.assign({},t,t.push):t,s=t.pull?Object.assign({},t,t.pull):t;this.push=O4(o,n,a),this.pull=O4(n,o,s),this.pushPaused=!0,this.pullPaused=!0;let c=r=>{this.emit("change",{direction:"pull",change:r})},d=r=>{this.emit("change",{direction:"push",change:r})},x=r=>{this.emit("denied",{direction:"push",doc:r})},k=r=>{this.emit("denied",{direction:"pull",doc:r})},l=()=>{this.pushPaused=!0,this.pullPaused&&this.emit("paused")},M=()=>{this.pullPaused=!0,this.pushPaused&&this.emit("paused")},_=()=>{this.pushPaused=!1,this.pullPaused&&this.emit("active",{direction:"push"})},B=()=>{this.pullPaused=!1,this.pushPaused&&this.emit("active",{direction:"pull"})},A={},z=r=>(v,w)=>{(v==="change"&&(w===c||w===d)||v==="denied"&&(w===k||w===x)||v==="paused"&&(w===M||w===l)||v==="active"&&(w===B||w===_))&&(v in A||(A[v]={}),A[v][r]=!0,Object.keys(A[v]).length===2&&this.removeAllListeners(v))};t.live&&(this.push.on("complete",this.pull.cancel.bind(this.pull)),this.pull.on("complete",this.push.cancel.bind(this.push)));function u(r,v,w){r.listeners(v).indexOf(w)==-1&&r.on(v,w)}this.on("newListener",function(r){r==="change"?(u(this.pull,"change",c),u(this.push,"change",d)):r==="denied"?(u(this.pull,"denied",k),u(this.push,"denied",x)):r==="active"?(u(this.pull,"active",B),u(this.push,"active",_)):r==="paused"&&(u(this.pull,"paused",M),u(this.push,"paused",l))}),this.on("removeListener",function(r){r==="change"?(this.pull.removeListener("change",c),this.push.removeListener("change",d)):r==="denied"?(this.pull.removeListener("denied",k),this.push.removeListener("denied",x)):r==="active"?(this.pull.removeListener("active",B),this.push.removeListener("active",_)):r==="paused"&&(this.pull.removeListener("paused",M),this.push.removeListener("paused",l))}),this.pull.on("removeListener",z("pull")),this.push.on("removeListener",z("push"));let h=Promise.all([this.push,this.pull]).then(r=>{let v={push:r[0],pull:r[1]};return this.emit("complete",v),i&&i(null,v),this.removeAllListeners(),v},r=>{if(this.cancel(),i?i(r):this.emit("error",r),this.removeAllListeners(),i)throw r});this.then=function(r,v){return h.then(r,v)},this.catch=function(r){return h.catch(r)}}cancel(){this.canceled||(this.canceled=!0,this.push.cancel(),this.pull.cancel())}};function T5(e){e.replicate=O4,e.sync=P5,Object.defineProperty(e.prototype,"replicate",{get:function(){var o=this;return typeof this.replicateMethods>"u"&&(this.replicateMethods={from:function(n,t,i){return o.constructor.replicate(n,o,t,i)},to:function(n,t,i){return o.constructor.replicate(o,n,t,i)}}),this.replicateMethods}}),e.prototype.sync=function(o,n,t){return this.constructor.sync(this,o,n,t)}}l1.plugin(o5).plugin(v5).plugin(S5).plugin(T5);var J4=l1;var b1=class extends Error{constructor(o,n,t){super(),this.status=o,this.name=n,this.message=t,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}},F7=new b1(401,"unauthorized","Name or password is incorrect."),q7=new b1(400,"bad_request","Missing JSON list of 'docs'"),N7=new b1(404,"not_found","missing"),$7=new b1(409,"conflict","Document update conflict"),R5=new b1(400,"bad_request","_id field must contain a string"),F5=new b1(412,"missing_id","_id is required for puts"),q5=new b1(400,"bad_request","Only reserved document ids may start with underscore."),U7=new b1(412,"precondition_failed","Database not open"),N5=new b1(500,"unknown_error","Database encountered an unknown error"),G7=new b1(500,"badarg","Some query argument is invalid"),K7=new b1(400,"invalid_request","Request was invalid"),W7=new b1(400,"query_parse_error","Some query parameter is invalid"),Z7=new b1(500,"doc_validation","Bad special document member"),$5=new b1(400,"bad_request","Something wrong with the request"),Q7=new b1(400,"bad_request","Document must be a JSON object"),X7=new b1(404,"not_found","Database not found"),J7=new b1(500,"indexed_db_went_bad","unknown"),Y7=new b1(500,"web_sql_went_bad","unknown"),e9=new b1(500,"levelDB_went_went_bad","unknown"),t9=new b1(403,"forbidden","Forbidden by design doc validate_doc_update function"),n9=new b1(400,"bad_request","Invalid rev format"),o9=new b1(412,"file_exists","The database could not be created, the file already exists."),i9=new b1(412,"missing_stub","A pre-existing attachment stub wasn't found"),a9=new b1(413,"invalid_url","Provided URL is invalid");function Y4(e,o){function n(t){for(var i=Object.getOwnPropertyNames(e),a=0,s=i.length;a<s;a++)typeof e[i[a]]!="function"&&(this[i[a]]=e[i[a]]);this.stack===void 0&&(this.stack=new Error().stack),t!==void 0&&(this.reason=t)}return n.prototype=b1.prototype,new n(o)}function c2(e){if(typeof e!="object"){var o=e;e=N5,e.data=o}return"error"in e&&e.error==="conflict"&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=new Error().stack),e}var $0=Headers;var U5=function(e){return atob(e)};function G5(e,o){e=e||[],o=o||{};try{return new Blob(e,o)}catch(a){if(a.name!=="TypeError")throw a;for(var n=typeof BlobBuilder<"u"?BlobBuilder:typeof MSBlobBuilder<"u"?MSBlobBuilder:typeof MozBlobBuilder<"u"?MozBlobBuilder:WebKitBlobBuilder,t=new n,i=0;i<e.length;i+=1)t.append(e[i]);return t.getBlob(o.type)}}function K5(e){for(var o=e.length,n=new ArrayBuffer(o),t=new Uint8Array(n),i=0;i<o;i++)t[i]=e.charCodeAt(i);return n}function W5(e,o){return G5([K5(e)],{type:o})}function N6(e,o){return W5(U5(e),o)}function Z5(e,o,n){for(var t="",i=n-e.length;t.length<i;)t+=o;return t}function Q5(e,o,n){var t=Z5(e,o,n);return t+e}var $6=-324,en=3,tn="";function A1(e,o){if(e===o)return 0;e=w0(e),o=w0(o);var n=nn(e),t=nn(o);if(n-t!==0)return n-t;switch(typeof e){case"number":return e-o;case"boolean":return e<o?-1:1;case"string":return tr(e,o)}return Array.isArray(e)?er(e,o):nr(e,o)}function w0(e){switch(typeof e){case"undefined":return null;case"number":return e===1/0||e===-1/0||isNaN(e)?null:e;case"object":var o=e;if(Array.isArray(e)){var n=e.length;e=new Array(n);for(var t=0;t<n;t++)e[t]=w0(o[t])}else{if(e instanceof Date)return e.toJSON();if(e!==null){e={};for(var i in o)if(Object.prototype.hasOwnProperty.call(o,i)){var a=o[i];typeof a<"u"&&(e[i]=w0(a))}}}}return e}function X5(e){if(e!==null)switch(typeof e){case"boolean":return e?1:0;case"number":return or(e);case"string":return e.replace(/\u0002/g,"").replace(/\u0001/g,"").replace(/\u0000/g,"");case"object":var o=Array.isArray(e),n=o?e:Object.keys(e),t=-1,i=n.length,a="";if(o)for(;++t<i;)a+=K1(n[t]);else for(;++t<i;){var s=n[t];a+=K1(s)+K1(e[s])}return a}return""}function K1(e){var o="\0";return e=w0(e),nn(e)+tn+X5(e)+o}function J5(e,o){var n=o,t,i=e[o]==="1";if(i)t=0,o++;else{var a=e[o]==="0";o++;var s="",c=e.substring(o,o+en),d=parseInt(c,10)+$6;for(a&&(d=-d),o+=en;;){var x=e[o];if(x==="\0")break;s+=x,o++}s=s.split("."),s.length===1?t=parseInt(s,10):t=parseFloat(s[0]+"."+s[1]),a&&(t=t-10),d!==0&&(t=parseFloat(t+"e"+d))}return{num:t,length:o-n}}function Y5(e,o){var n=e.pop();if(o.length){var t=o[o.length-1];n===t.element&&(o.pop(),t=o[o.length-1]);var i=t.element,a=t.index;if(Array.isArray(i))i.push(n);else if(a===e.length-2){var s=e.pop();i[s]=n}else e.push(n)}}function U6(e){for(var o=[],n=[],t=0;;){var i=e[t++];if(i==="\0"){if(o.length===1)return o.pop();Y5(o,n);continue}switch(i){case"1":o.push(null);break;case"2":o.push(e[t]==="1"),t++;break;case"3":var a=J5(e,t);o.push(a.num),t+=a.length;break;case"4":for(var s="";;){var c=e[t];if(c==="\0")break;s+=c,t++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"").replace(/\u0002\u0002/g,""),o.push(s);break;case"5":var d={element:[],index:o.length};o.push(d.element),n.push(d);break;case"6":var x={element:{},index:o.length};o.push(x.element),n.push(x);break;default:throw new Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}function er(e,o){for(var n=Math.min(e.length,o.length),t=0;t<n;t++){var i=A1(e[t],o[t]);if(i!==0)return i}return e.length===o.length?0:e.length>o.length?1:-1}function tr(e,o){return e===o?0:e>o?1:-1}function nr(e,o){for(var n=Object.keys(e),t=Object.keys(o),i=Math.min(n.length,t.length),a=0;a<i;a++){var s=A1(n[a],t[a]);if(s!==0||(s=A1(e[n[a]],o[t[a]]),s!==0))return s}return n.length===t.length?0:n.length>t.length?1:-1}function nn(e){var o=["boolean","number","string","object"],n=o.indexOf(typeof e);if(~n)return e===null?1:Array.isArray(e)?5:n<3?n+2:n+3;if(Array.isArray(e))return 5}function or(e){if(e===0)return"1";var o=e.toExponential().split(/e\+?/),n=parseInt(o[1],10),t=e<0,i=t?"0":"2",a=(t?-n:n)-$6,s=Q5(a.toString(),"0",en);i+=tn+s;var c=Math.abs(parseFloat(o[0]));t&&(c=10-c);var d=c.toFixed(20);return d=d.replace(/\.?0+$/,""),i+=tn+d,i}var ir=Z0(t4());var G6=Z0(Xt()),h9=self.setImmediate||self.setTimeout;function l2(e){return G6.default.hash(e)}function ar(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer||typeof Blob<"u"&&e instanceof Blob}function sr(e){return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type)}var Z6=Function.prototype.toString,rr=Z6.call(Object);function cr(e){var o=Object.getPrototypeOf(e);if(o===null)return!0;var n=o.constructor;return typeof n=="function"&&n instanceof n&&Z6.call(n)==rr}function h0(e){var o,n,t;if(!e||typeof e!="object")return e;if(Array.isArray(e)){for(o=[],n=0,t=e.length;n<t;n++)o[n]=h0(e[n]);return o}if(e instanceof Date&&isFinite(e))return e.toISOString();if(ar(e))return sr(e);if(!cr(e))return e;o={};for(n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var i=h0(e[n]);typeof i<"u"&&(o[n]=i)}return o}var K6;try{localStorage.setItem("_pouch_check_localstorage",1),K6=!!localStorage.getItem("_pouch_check_localstorage")}catch{K6=!1}var d2=typeof queueMicrotask=="function"?queueMicrotask:function(o){Promise.resolve().then(o)};function N2(e){if(typeof console<"u"&&typeof console[e]=="function"){var o=Array.prototype.slice.call(arguments,1);console[e].apply(console,o)}}function lr(){}var dr=lr.name,W6;dr?W6=function(e){return e.name}:W6=function(e){var o=e.toString().match(/^\s*function\s*(?:(\S+)\s*)?\(/);return o&&o[1]?o[1]:""};function u0(e){return typeof e._remote=="boolean"?e._remote:typeof e.type=="function"?(N2("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),e.type()==="http"):!1}function U0(e,o,n){return e.get(o).catch(function(t){if(t.status!==404)throw t;return{}}).then(function(t){var i=t._rev,a=n(t);return a?(a._id=o,a._rev=i,vr(e,a,n)):{updated:!1,rev:i}})}function vr(e,o,n){return e.put(o).then(function(t){return{updated:!0,rev:t.rev}},function(t){if(t.status!==409)throw t;return U0(e,o._id,n)})}var f0=class e extends Error{constructor(o){super(),this.status=400,this.name="query_parse_error",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},$2=class e extends Error{constructor(o){super(),this.status=404,this.name="not_found",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}},U2=class e extends Error{constructor(o){super(),this.status=500,this.name="invalid_value",this.message=o,this.error=!0;try{Error.captureStackTrace(this,e)}catch{}}};function on(e,o){return o&&e.then(function(n){d2(function(){o(null,n)})},function(n){d2(function(){o(n)})}),e}function Q6(e){return function(...o){var n=o.pop(),t=e.apply(this,o);return typeof n=="function"&&on(t,n),t}}function X6(e,o){return e.then(function(n){return o().then(function(){return n})},function(n){return o().then(function(){throw n})})}function G2(e,o){return function(){var n=arguments,t=this;return e.add(function(){return o.apply(t,n)})}}function an(e){var o=new Set(e),n=new Array(o.size),t=-1;return o.forEach(function(i){n[++t]=i}),n}function K2(e){var o=new Array(e.size),n=-1;return e.forEach(function(t,i){o[++n]=i}),o}var v2=class{constructor(){this.promise=Promise.resolve()}add(o){return this.promise=this.promise.catch(()=>{}).then(()=>o()),this.promise}finish(){return this.promise}};function J6(e){if(!e)return"undefined";switch(typeof e){case"function":return e.toString();case"string":return e.toString();default:return JSON.stringify(e)}}function gr(e,o){return J6(e)+J6(o)+"undefined"}function Y6(e,o,n,t,i,a){return Q(this,null,function*(){let s=gr(n,t),c;if(!i&&(c=e._cachedViews=e._cachedViews||{},c[s]))return c[s];let d=e.info().then(function(x){return Q(this,null,function*(){let k=x.db_name+"-mrview-"+(i?"temp":l2(s));function l(z){z.views=z.views||{};let u=o;u.indexOf("/")===-1&&(u=o+"/"+o);let h=z.views[u]=z.views[u]||{};if(!h[k])return h[k]=!0,z}yield U0(e,"_local/"+a,l);let _=(yield e.registerDependentDatabase(k)).db;_.auto_compaction=!0;let B={name:k,db:_,sourceDB:e,adapter:e.adapter,mapFun:n,reduceFun:t},A;try{A=yield B.db.get("_local/lastSeq")}catch(z){if(z.status!==404)throw z}return B.seq=A?A.seq:0,c&&B.db.once("destroyed",function(){delete c[s]}),B})});return c&&(c[s]=d),d})}var eo={},to=new v2,hr=50;function sn(e){return e.indexOf("/")===-1?[e,e]:e.split("/")}function ur(e){return e.length===1&&/^1-/.test(e[0].rev)}function no(e,o,n){try{e.emit("error",o)}catch{N2("error",`The user's map/reduce function threw an uncaught error.
You can debug this error by doing:
myDatabase.on('error', function (err) { debugger; });
Please double-check your map/reduce function.`),N2("error",o,n)}}function pr(e,o,n,t){function i(g,p,y){try{p(y)}catch(C){no(g,C,{fun:p,doc:y})}}function a(g,p,y,C,L){try{return{output:p(y,C,L)}}catch(E){return no(g,E,{fun:p,keys:y,values:C,rereduce:L}),{error:E}}}function s(g,p){let y=A1(g.key,p.key);return y!==0?y:A1(g.value,p.value)}function c(g,p,y){return y=y||0,typeof p=="number"?g.slice(y,p+y):y>0?g.slice(y):g}function d(g){let p=g.value;return p&&typeof p=="object"&&p._id||g.id}function x(g){for(let p of g.rows){let y=p.doc&&p.doc._attachments;if(y)for(let C of Object.keys(y)){let L=y[C];y[C].data=N6(L.data,L.content_type)}}}function k(g){return function(p){return g.include_docs&&g.attachments&&g.binary&&x(p),p}}function l(g,p,y,C){let L=p[g];typeof L<"u"&&(C&&(L=encodeURIComponent(JSON.stringify(L))),y.push(g+"="+L))}function M(g){if(typeof g<"u"){let p=Number(g);return!isNaN(p)&&p===parseInt(g,10)?p:g}}function _(g){return g.group_level=M(g.group_level),g.limit=M(g.limit),g.skip=M(g.skip),g}function B(g){if(g){if(typeof g!="number")return new f0(`Invalid value for integer: "${g}"`);if(g<0)return new f0(`Invalid value for positive integer: "${g}"`)}}function A(g,p){let y=g.descending?"endkey":"startkey",C=g.descending?"startkey":"endkey";if(typeof g[y]<"u"&&typeof g[C]<"u"&&A1(g[y],g[C])>0)throw new f0("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(p.reduce&&g.reduce!==!1){if(g.include_docs)throw new f0("{include_docs:true} is invalid for reduce");if(g.keys&&g.keys.length>1&&!g.group&&!g.group_level)throw new f0("Multi-key fetches for reduce views must use {group: true}")}for(let L of["group_level","limit","skip"]){let E=B(g[L]);if(E)throw E}}function z(g,p,y){return Q(this,null,function*(){let C=[],L,E="GET",X;if(l("reduce",y,C),l("include_docs",y,C),l("attachments",y,C),l("limit",y,C),l("descending",y,C),l("group",y,C),l("group_level",y,C),l("skip",y,C),l("stale",y,C),l("conflicts",y,C),l("startkey",y,C,!0),l("start_key",y,C,!0),l("endkey",y,C,!0),l("end_key",y,C,!0),l("inclusive_end",y,C),l("key",y,C,!0),l("update_seq",y,C),C=C.join("&"),C=C===""?"":"?"+C,typeof y.keys<"u"){let H=`keys=${encodeURIComponent(JSON.stringify(y.keys))}`;H.length+C.length+1<=2e3?C+=(C[0]==="?"?"&":"?")+H:(E="POST",typeof p=="string"?L={keys:y.keys}:p.keys=y.keys)}if(typeof p=="string"){let S=sn(p),H=yield g.fetch("_design/"+S[0]+"/_view/"+S[1]+C,{headers:new $0({"Content-Type":"application/json"}),method:E,body:JSON.stringify(L)});X=H.ok;let $=yield H.json();if(!X)throw $.status=H.status,c2($);for(let Z of $.rows)if(Z.value&&Z.value.error&&Z.value.error==="builtin_reduce_error")throw new Error(Z.reason);return new Promise(function(Z){Z($)}).then(k(y))}L=L||{};for(let S of Object.keys(p))Array.isArray(p[S])?L[S]=p[S]:L[S]=p[S].toString();let I=yield g.fetch("_temp_view"+C,{headers:new $0({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(L)});X=I.ok;let b=yield I.json();if(!X)throw b.status=I.status,c2(b);return new Promise(function(S){S(b)}).then(k(y))})}function u(g,p,y){return new Promise(function(C,L){g._query(p,y,function(E,X){if(E)return L(E);C(X)})})}function h(g){return new Promise(function(p,y){g._viewCleanup(function(C,L){if(C)return y(C);p(L)})})}function r(g){return function(p){if(p.status===404)return g;throw p}}function v(g,p,y){return Q(this,null,function*(){let C="_local/doc_"+g,L={_id:C,keys:[]},E=y.get(g),X=E[0],I=E[1];function b(){return ur(I)?Promise.resolve(L):p.db.get(C).catch(r(L))}function S(c1){return c1.keys.length?p.db.allDocs({keys:c1.keys,include_docs:!0}):Promise.resolve({rows:[]})}function H(c1,w1){let C1=[],V1=new Set;for(let o1 of w1.rows){let t1=o1.doc;if(t1&&(C1.push(t1),V1.add(t1._id),t1._deleted=!X.has(t1._id),!t1._deleted)){let a1=X.get(t1._id);"value"in a1&&(t1.value=a1.value)}}let T1=K2(X);for(let o1 of T1)if(!V1.has(o1)){let t1={_id:o1},a1=X.get(o1);"value"in a1&&(t1.value=a1.value),C1.push(t1)}return c1.keys=an(T1.concat(c1.keys)),C1.push(c1),C1}let $=yield b(),Z=yield S($);return H($,Z)})}function w(g){return g.sourceDB.get("_local/purges").then(function(p){let y=p.purgeSeq;return g.db.get("_local/purgeSeq").then(function(C){return C._rev}).catch(r(void 0)).then(function(C){return g.db.put({_id:"_local/purgeSeq",_rev:C,purgeSeq:y})})}).catch(function(p){if(p.status!==404)throw p})}function f(g,p,y){var C="_local/lastSeq";return g.db.get(C).catch(r({_id:C,seq:0})).then(function(L){var E=K2(p);return Promise.all(E.map(function(X){return v(X,g,p)})).then(function(X){var I=X.flat();return L.seq=y,I.push(L),g.db.bulkDocs({docs:I})}).then(()=>w(g))})}function V(g){let p=typeof g=="string"?g:g.name,y=eo[p];return y||(y=eo[p]=new v2),y}function D(g,p){return Q(this,null,function*(){return G2(V(g),function(){return j(g,p)})()})}function j(g,p){return Q(this,null,function*(){let y,C,L;function E(o1,t1){let a1={id:C._id,key:w0(o1)};typeof t1<"u"&&t1!==null&&(a1.value=w0(t1)),y.push(a1)}let X=o(g.mapFun,E),I=g.seq||0;function b(){return g.sourceDB.info().then(function(o1){L=g.sourceDB.activeTasks.add({name:"view_indexing",total_items:o1.update_seq-I})})}function S(o1,t1){return function(){return f(g,o1,t1)}}let H=0,$={view:g.name,indexed_docs:H};g.sourceDB.emit("indexing",$);let Z=new v2;function c1(){return Q(this,null,function*(){let o1=yield g.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:I,limit:p.changes_batch_size}),t1=yield w1();return C1(o1,t1)})}function w1(){return g.db.get("_local/purgeSeq").then(function(o1){return o1.purgeSeq}).catch(r(-1)).then(function(o1){return g.sourceDB.get("_local/purges").then(function(t1){let a1=t1.purges.filter(function(B1,u1){return u1>o1}).map(B1=>B1.docId),S1=a1.filter(function(B1,u1){return a1.indexOf(B1)===u1});return Promise.all(S1.map(function(B1){return g.sourceDB.get(B1).then(function(u1){return{docId:B1,doc:u1}}).catch(r({docId:B1}))}))}).catch(r([]))})}function C1(o1,t1){let a1=o1.results;if(!a1.length&&!t1.length)return;for(let u1 of t1)if(a1.findIndex(function(W1){return W1.id===u1.docId})<0){let W1={_id:u1.docId,doc:{_id:u1.docId,_deleted:1},changes:[]};u1.doc&&(W1.doc=u1.doc,W1.changes.push({rev:u1.doc._rev})),a1.push(W1)}let S1=V1(a1);Z.add(S(S1,I)),H=H+a1.length;let B1={view:g.name,last_seq:o1.last_seq,results_count:a1.length,indexed_docs:H};if(g.sourceDB.emit("indexing",B1),g.sourceDB.activeTasks.update(L,{completed_items:H}),!(a1.length<p.changes_batch_size))return c1()}function V1(o1){let t1=new Map;for(let a1 of o1){if(a1.doc._id[0]!=="_"){y=[],C=a1.doc,C._deleted||i(g.sourceDB,X,C),y.sort(s);let S1=T1(y);t1.set(a1.doc._id,[S1,a1.changes])}I=a1.seq}return t1}function T1(o1){let t1=new Map,a1;for(let S1=0,B1=o1.length;S1<B1;S1++){let u1=o1[S1],M0=[u1.key,u1.id];S1>0&&A1(u1.key,a1)===0&&M0.push(S1),t1.set(K1(M0),u1),a1=u1.key}return t1}try{yield b(),yield c1(),yield Z.finish(),g.seq=I,g.sourceDB.activeTasks.remove(L)}catch(o1){g.sourceDB.activeTasks.remove(L,o1)}})}function n1(g,p,y){y.group_level===0&&delete y.group_level;let C=y.group||y.group_level,L=n(g.reduceFun),E=[],X=isNaN(y.group_level)?Number.POSITIVE_INFINITY:y.group_level;for(let I of p){let b=E[E.length-1],S=C?I.key:null;if(C&&Array.isArray(S)&&(S=S.slice(0,X)),b&&A1(b.groupKey,S)===0){b.keys.push([I.key,I.id]),b.values.push(I.value);continue}E.push({keys:[[I.key,I.id]],values:[I.value],groupKey:S})}p=[];for(let I of E){let b=a(g.sourceDB,L,I.keys,I.values,!1);if(b.error&&b.error instanceof U2)throw b.error;p.push({value:b.error?null:b.output,key:I.groupKey})}return{rows:c(p,y.limit,y.skip)}}function Y(g,p){return G2(V(g),function(){return s1(g,p)})()}function s1(g,p){return Q(this,null,function*(){let y,C=g.reduceFun&&p.reduce!==!1,L=p.skip||0;typeof p.keys<"u"&&!p.keys.length&&(p.limit=0,delete p.keys);function E(I){return Q(this,null,function*(){I.include_docs=!0;let b=yield g.db.allDocs(I);return y=b.total_rows,b.rows.map(function(S){if("value"in S.doc&&typeof S.doc.value=="object"&&S.doc.value!==null){let $=Object.keys(S.doc.value).sort(),Z=["id","key","value"];if(!($<Z||$>Z))return S.doc.value}let H=U6(S.doc._id);return{key:H[0],id:H[1],value:"value"in S.doc?S.doc.value:null}})})}function X(I){return Q(this,null,function*(){let b;if(C?b=n1(g,I,p):typeof p.keys>"u"?b={total_rows:y,offset:L,rows:I}:b={total_rows:y,offset:L,rows:c(I,p.limit,p.skip)},p.update_seq&&(b.update_seq=g.seq),p.include_docs){let S=an(I.map(d)),H=yield g.sourceDB.allDocs({keys:S,include_docs:!0,conflicts:p.conflicts,attachments:p.attachments,binary:p.binary}),$=new Map;for(let Z of H.rows)$.set(Z.id,Z.doc);for(let Z of I){let c1=d(Z),w1=$.get(c1);w1&&(Z.doc=w1)}}return b})}if(typeof p.keys<"u"){let b=p.keys.map(function($){let Z={startkey:K1([$]),endkey:K1([$,{}])};return p.update_seq&&(Z.update_seq=!0),E(Z)}),H=(yield Promise.all(b)).flat();return X(H)}else{let I={descending:p.descending};p.update_seq&&(I.update_seq=!0);let b,S;if("start_key"in p&&(b=p.start_key),"startkey"in p&&(b=p.startkey),"end_key"in p&&(S=p.end_key),"endkey"in p&&(S=p.endkey),typeof b<"u"&&(I.startkey=p.descending?K1([b,{}]):K1([b])),typeof S<"u"){let $=p.inclusive_end!==!1;p.descending&&($=!$),I.endkey=K1($?[S,{}]:[S])}if(typeof p.key<"u"){let $=K1([p.key]),Z=K1([p.key,{}]);I.descending?(I.endkey=$,I.startkey=Z):(I.startkey=$,I.endkey=Z)}C||(typeof p.limit=="number"&&(I.limit=p.limit),I.skip=L);let H=yield E(I);return X(H)}})}function e1(g){return Q(this,null,function*(){return(yield g.fetch("_view_cleanup",{headers:new $0({"Content-Type":"application/json"}),method:"POST"})).json()})}function i1(g){return Q(this,null,function*(){try{let p=yield g.get("_local/"+e),y=new Map;for(let b of Object.keys(p.views)){let S=sn(b),H="_design/"+S[0],$=S[1],Z=y.get(H);Z||(Z=new Set,y.set(H,Z)),Z.add($)}let C={keys:K2(y),include_docs:!0},L=yield g.allDocs(C),E={};for(let b of L.rows){let S=b.key.substring(8);for(let H of y.get(b.key)){let $=S+"/"+H;p.views[$]||($=H);let Z=Object.keys(p.views[$]),c1=b.doc&&b.doc.views&&b.doc.views[H];for(let w1 of Z)E[w1]=E[w1]||c1}}let I=Object.keys(E).filter(function(b){return!E[b]}).map(function(b){return G2(V(b),function(){return new g.constructor(b,g.__opts).destroy()})()});return Promise.all(I).then(function(){return{ok:!0}})}catch(p){if(p.status===404)return{ok:!0};throw p}})}function d1(g,p,y){return Q(this,null,function*(){if(typeof g._query=="function")return u(g,p,y);if(u0(g))return z(g,p,y);let C={changes_batch_size:g.__opts.view_update_changes_batch_size||hr};if(typeof p!="string")return A(y,p),to.add(function(){return Q(this,null,function*(){let L=yield Y6(g,"temp_view/temp_view",p.map,p.reduce,!0,e);return X6(D(L,C).then(function(){return Y(L,y)}),function(){return L.db.destroy()})})}),to.finish();{let L=p,E=sn(L),X=E[0],I=E[1],b=yield g.get("_design/"+X);if(p=b.views&&b.views[I],!p)throw new $2(`ddoc ${b._id} has no view named ${I}`);t(b,I),A(y,p);let S=yield Y6(g,L,p.map,p.reduce,!1,e);return y.stale==="ok"||y.stale==="update_after"?(y.stale==="update_after"&&d2(function(){D(S,C)}),Y(S,y)):(yield D(S,C),Y(S,y))}})}function v1(g,p,y){let C=this;typeof p=="function"&&(y=p,p={}),p=p?_(p):{},typeof g=="function"&&(g={map:g});let L=Promise.resolve().then(function(){return d1(C,g,p)});return on(L,y),L}let p1=Q6(function(){let g=this;return typeof g._viewCleanup=="function"?h(g):u0(g)?e1(g):i1(g)});return{query:v1,viewCleanup:p1}}var oo=pr;function K0(e,o){for(var n=e,t=0,i=o.length;t<i;t++){var a=o[t];if(n=n[a],!n)break}return n}function ro(e,o,n){for(var t=0,i=o.length;t<i-1;t++){var a=o[t];e=e[a]=e[a]||{}}e[o[i-1]]=n}function Z2(e,o){return e<o?-1:e>o?1:0}function x0(e){for(var o=[],n="",t=0,i=e.length;t<i;t++){var a=e[t];t>0&&e[t-1]==="\\"&&(a==="$"||a===".")?n=n.substring(0,n.length-1)+a:a==="."?(o.push(n),n=""):n+=a}return o.push(n),o}var mr=["$or","$nor","$not"];function co(e){return mr.indexOf(e)>-1}function N1(e){return Object.keys(e)[0]}function Q2(e){return e[N1(e)]}function h2(e){var o={},n={$or:!0,$nor:!0};return e.forEach(function(t){Object.keys(t).forEach(function(i){var a=t[i];if(typeof a!="object"&&(a={$eq:a}),co(i))if(a instanceof Array){if(n[i]){n[i]=!1,o[i]=a;return}var s=[];o[i].forEach(function(d){Object.keys(a).forEach(function(x){var k=a[x],l=Math.max(Object.keys(d).length,Object.keys(k).length),M=h2([d,k]);Object.keys(M).length<=l||s.push(M)})}),o[i]=s}else o[i]=h2([a]);else{var c=o[i]=o[i]||{};Object.keys(a).forEach(function(d){var x=a[d];if(d==="$gt"||d==="$gte")return wr(d,x,c);if(d==="$lt"||d==="$lte")return fr(d,x,c);if(d==="$ne")return xr(x,c);if(d==="$eq")return Mr(x,c);if(d==="$regex")return kr(x,c);c[d]=x})}})}),o}function wr(e,o,n){typeof n.$eq<"u"||(typeof n.$gte<"u"?e==="$gte"?o>n.$gte&&(n.$gte=o):o>=n.$gte&&(delete n.$gte,n.$gt=o):typeof n.$gt<"u"?e==="$gte"?o>n.$gt&&(delete n.$gt,n.$gte=o):o>n.$gt&&(n.$gt=o):n[e]=o)}function fr(e,o,n){typeof n.$eq<"u"||(typeof n.$lte<"u"?e==="$lte"?o<n.$lte&&(n.$lte=o):o<=n.$lte&&(delete n.$lte,n.$lt=o):typeof n.$lt<"u"?e==="$lte"?o<n.$lt&&(delete n.$lt,n.$lte=o):o<n.$lt&&(n.$lt=o):n[e]=o)}function xr(e,o){"$ne"in o?o.$ne.push(e):o.$ne=[e]}function Mr(e,o){delete o.$gt,delete o.$gte,delete o.$lt,delete o.$lte,delete o.$ne,o.$eq=e}function kr(e,o){"$regex"in o?o.$regex.push(e):o.$regex=[e]}function lo(e){for(var o in e){if(Array.isArray(e))for(var n in e)e[n].$and&&(e[n]=h2(e[n].$and));var t=e[o];typeof t=="object"&&lo(t)}return e}function vo(e,o){for(var n in e){n==="$and"&&(o=!0);var t=e[n];typeof t=="object"&&(o=vo(t,o))}return o}function X2(e){var o=h0(e);vo(o,!1)&&(o=lo(o),"$and"in o&&(o=h2(o.$and))),["$or","$nor"].forEach(function(s){s in o&&o[s].forEach(function(c){for(var d=Object.keys(c),x=0;x<d.length;x++){var k=d[x],l=c[k];(typeof l!="object"||l===null)&&(c[k]={$eq:l})}})}),"$not"in o&&(o.$not=h2([o.$not]));for(var n=Object.keys(o),t=0;t<n.length;t++){var i=n[t],a=o[i];(typeof a!="object"||a===null)&&(a={$eq:a}),o[i]=a}return rn(o),o}function rn(e){Object.keys(e).forEach(function(o){var n=e[o];Array.isArray(n)?n.forEach(function(t){t&&typeof t=="object"&&rn(t)}):o==="$ne"?e.$ne=[n]:o==="$regex"?e.$regex=[n]:n&&typeof n=="object"&&rn(n)})}function yr(e){function o(n){return e.map(function(t){var i=N1(t),a=x0(i),s=K0(n,a);return s})}return function(n,t){var i=o(n.doc),a=o(t.doc),s=A1(i,a);return s!==0?s:Z2(n.doc._id,t.doc._id)}}function cn(e,o,n){if(e=e.filter(function(s){return G0(s.doc,o.selector,n)}),o.sort){var t=yr(o.sort);e=e.sort(t),typeof o.sort[0]!="string"&&Q2(o.sort[0])==="desc"&&(e=e.reverse())}if("limit"in o||"skip"in o){var i=o.skip||0,a=("limit"in o?o.limit:e.length)+i;e=e.slice(i,a)}return e}function G0(e,o,n){return n.every(function(t){var i=o[t],a=x0(t),s=K0(e,a);return co(t)?zr(t,i,e):W2(i,e,a,s)})}function W2(e,o,n,t){return e?typeof e=="object"?Object.keys(e).every(function(i){var a=e[i];if(i.indexOf("$")===0)return io(i,o,a,n,t);var s=x0(i);if(t===void 0&&typeof a!="object"&&s.length>0)return!1;var c=K0(t,s);return typeof a=="object"?W2(a,o,n,c):io("$eq",o,a,s,c)}):e===t:!0}function zr(e,o,n){return e==="$or"?o.some(function(t){return G0(n,t,Object.keys(t))}):e==="$not"?!G0(n,o,Object.keys(o)):!o.find(function(t){return G0(n,t,Object.keys(t))})}function io(e,o,n,t,i){if(!so[e])throw new Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return so[e](o,n,t,i)}function g2(e){return typeof e<"u"&&e!==null}function H0(e){return typeof e<"u"}function Cr(e,o){if(typeof e!="number"||parseInt(e,10)!==e)return!1;var n=o[0],t=o[1];return e%n===t}function ao(e,o){return o.some(function(n){return e instanceof Array?e.some(function(t){return A1(n,t)===0}):A1(n,e)===0})}function _r(e,o){return o.every(function(n){return e.some(function(t){return A1(n,t)===0})})}function br(e,o){return e.length===o}function Br(e,o){var n=new RegExp(o);return n.test(e)}function Sr(e,o){switch(o){case"null":return e===null;case"boolean":return typeof e=="boolean";case"number":return typeof e=="number";case"string":return typeof e=="string";case"array":return e instanceof Array;case"object":return{}.toString.call(e)==="[object Object]"}}var so={$elemMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.some(function(i){return G0(i,o,Object.keys(o))}):t.some(function(i){return W2(o,e,n,i)})},$allMatch:function(e,o,n,t){return!Array.isArray(t)||t.length===0?!1:typeof t[0]=="object"&&t[0]!==null?t.every(function(i){return G0(i,o,Object.keys(o))}):t.every(function(i){return W2(o,e,n,i)})},$eq:function(e,o,n,t){return H0(t)&&A1(t,o)===0},$gte:function(e,o,n,t){return H0(t)&&A1(t,o)>=0},$gt:function(e,o,n,t){return H0(t)&&A1(t,o)>0},$lte:function(e,o,n,t){return H0(t)&&A1(t,o)<=0},$lt:function(e,o,n,t){return H0(t)&&A1(t,o)<0},$exists:function(e,o,n,t){return o?H0(t):!H0(t)},$mod:function(e,o,n,t){return g2(t)&&Cr(t,o)},$ne:function(e,o,n,t){return o.every(function(i){return A1(t,i)!==0})},$in:function(e,o,n,t){return g2(t)&&ao(t,o)},$nin:function(e,o,n,t){return g2(t)&&!ao(t,o)},$size:function(e,o,n,t){return g2(t)&&Array.isArray(t)&&br(t,o)},$all:function(e,o,n,t){return Array.isArray(t)&&_r(t,o)},$regex:function(e,o,n,t){return g2(t)&&typeof t=="string"&&o.every(function(i){return Br(t,i)})},$type:function(e,o,n,t){return Sr(t,o)}};function u2(e,o){if(typeof o!="object")throw new Error("Selector error: expected a JSON object");o=X2(o);var n={doc:e},t=cn([n],{selector:o},Object.keys(o));return t&&t.length===1}var Ar=(...e)=>e.flat(1/0),ho=(...e)=>{let o=[];for(let n of e)Array.isArray(n)?o=o.concat(ho(...n)):o.push(n);return o},uo=typeof Array.prototype.flat=="function"?Ar:ho;function vn(e){let o={};for(let n of e)Object.assign(o,n);return o}function Lr(e,o){let n={};for(let t of o){let i=x0(t),a=K0(e,i);typeof a<"u"&&ro(n,i,a)}return n}function po(e,o){for(let n=0,t=Math.min(e.length,o.length);n<t;n++)if(e[n]!==o[n])return!1;return!0}function Ir(e,o){return e.length>o.length?!1:po(e,o)}function jr(e,o){e=e.slice();for(let n of o){if(!e.length)break;let t=e.indexOf(n);if(t===-1)return!1;e.splice(t,1)}return!0}function Vr(e){let o={};for(let n of e)o[n]=!0;return o}function Hr(e,o){let n=null,t=-1;for(let i of e){let a=o(i);a>t&&(t=a,n=i)}return n}function go(e,o){if(e.length!==o.length)return!1;for(let n=0,t=e.length;n<t;n++)if(e[n]!==o[n])return!1;return!0}function Or(e){return Array.from(new Set(e))}function p2(e){return function(...o){let n=o[o.length-1];if(typeof n=="function"){let t=n.bind(null,null),i=n.bind(null);e.apply(this,o.slice(0,-1)).then(t,i)}else return e.apply(this,o)}}function mo(e){e=h0(e),e.index||(e.index={});for(let o of["type","name","ddoc"])e.index[o]&&(e[o]=e.index[o],delete e.index[o]);return e.fields&&(e.index.fields=e.fields,delete e.fields),e.type||(e.type="json"),e}function J2(e){return typeof e=="object"&&e!==null}function Dr(e,o,n){let t="",i=o,a=!0;if(["$in","$nin","$or","$and","$mod","$nor","$all"].indexOf(e)!==-1&&(Array.isArray(o)||(t="Query operator "+e+" must be an array.")),["$not","$elemMatch","$allMatch"].indexOf(e)!==-1&&(!Array.isArray(o)&&J2(o)||(t="Query operator "+e+" must be an object.")),e==="$mod"&&Array.isArray(o))if(o.length!==2)t="Query operator $mod must be in the format [divisor, remainder], where divisor and remainder are both integers.";else{let s=o[0],c=o[1];s===0&&(t="Query operator $mod's divisor cannot be 0, cannot divide by zero.",a=!1),(typeof s!="number"||parseInt(s,10)!==s)&&(t="Query operator $mod's divisor is not an integer.",i=s),parseInt(c,10)!==c&&(t="Query operator $mod's remainder is not an integer.",i=c)}if(e==="$exists"&&typeof o!="boolean"&&(t="Query operator $exists must be a boolean."),e==="$type"){let s=["null","boolean","number","string","array","object"],c='"'+s.slice(0,s.length-1).join('", "')+'", or "'+s[s.length-1]+'"';(typeof o!="string"||s.indexOf(o)==-1)&&(t="Query operator $type must be a string. Supported values: "+c+".")}if(e==="$size"&&parseInt(o,10)!==o&&(t="Query operator $size must be a integer."),e==="$regex"&&typeof o!="string"&&(n?t="Query operator $regex must be a string.":o instanceof RegExp||(t="Query operator $regex must be a string or an instance of a javascript regular expression.")),t){if(a){let s=i===null?" ":Array.isArray(i)?" array":" "+typeof i,c=J2(i)?JSON.stringify(i,null,"	"):i;t+=" Received"+s+": "+c}throw new Error(t)}}var Er=["$all","$allMatch","$and","$elemMatch","$exists","$in","$mod","$nin","$nor","$not","$or","$regex","$size","$type"],Pr=["$in","$nin","$mod","$all"],Tr=["$eq","$gt","$gte","$lt","$lte"];function Y2(e,o){if(Array.isArray(e))for(let n of e)J2(n)&&Y2(n,o);else for(let[n,t]of Object.entries(e))Er.indexOf(n)!==-1&&Dr(n,t,o),Tr.indexOf(n)===-1&&Pr.indexOf(n)===-1&&J2(t)&&Y2(t,o)}function m2(e,o,n){return Q(this,null,function*(){n.body&&(n.body=JSON.stringify(n.body),n.headers=new $0({"Content-type":"application/json"}));let t=yield e.fetch(o,n),i=yield t.json();if(!t.ok){i.status=t.status;let a=Y4(i);throw c2(a)}return i})}function Rr(e,o){return Q(this,null,function*(){return yield m2(e,"_index",{method:"POST",body:mo(o)})})}function Fr(e,o){return Q(this,null,function*(){return Y2(o.selector,!0),yield m2(e,"_find",{method:"POST",body:o})})}function qr(e,o){return Q(this,null,function*(){return yield m2(e,"_explain",{method:"POST",body:o})})}function Nr(e){return Q(this,null,function*(){return yield m2(e,"_index",{method:"GET"})})}function $r(e,o){return Q(this,null,function*(){let n=o.ddoc,t=o.type||"json",i=o.name;if(!n)throw new Error("you must provide an index's ddoc");if(!i)throw new Error("you must provide an index's name");let a="_index/"+[n,t,i].map(encodeURIComponent).join("/");return yield m2(e,a,{method:"DELETE"})})}function wo(e,o){for(let n of o)if(e=e[n],e===void 0)return;return e}function Ur(e,o,n){return function(t){if(n&&!u2(t,n))return;let i=[];for(let a of e){let s=wo(t,x0(a));if(s===void 0)return;i.push(s)}o(i)}}function Gr(e,o,n){let t=x0(e);return function(i){if(n&&!u2(i,n))return;let a=wo(i,t);a!==void 0&&o(a)}}function Kr(e,o,n){return function(t){n&&!u2(t,n)||o(t[e])}}function Wr(e,o,n){return function(t){if(n&&!u2(t,n))return;let i=e.map(a=>t[a]);o(i)}}function Zr(e){return e.every(o=>o.indexOf(".")===-1)}function Qr(e,o,n){let t=Zr(e),i=e.length===1;return t?i?Kr(e[0],o,n):Wr(e,o,n):i?Gr(e[0],o,n):Ur(e,o,n)}function Xr(e,o){let n=Object.keys(e.fields),t=e.partial_filter_selector;return Qr(n,o,t)}function Jr(){throw new Error("reduce not supported")}function Yr(e,o){let n=e.views[o];if(!n.map||!n.map.fields)throw new Error("ddoc "+e._id+" with view "+o+" doesn't have map.fields defined. maybe it wasn't created by this plugin?")}var ln=oo("indexes",Xr,Jr,Yr);function gn(e){return e._customFindAbstractMapper?{query:function(n,t){let i=ln.query.bind(this);return e._customFindAbstractMapper.query.call(this,n,t,i)},viewCleanup:function(){let n=ln.viewCleanup.bind(this);return e._customFindAbstractMapper.viewCleanup.call(this,n)}}:ln}function ec(e){if(!Array.isArray(e))throw new Error("invalid sort json - should be an array");return e.map(function(o){if(typeof o=="string"){let n={};return n[o]="asc",n}else return o})}var tc=/^_design\//;function nc(e){let o=[];return typeof e=="string"?o.push(e):o=e,o.map(function(n){return n.replace(tc,"")})}function fo(e){return e.fields=e.fields.map(function(o){if(typeof o=="string"){let n={};return n[o]="asc",n}return o}),e.partial_filter_selector&&(e.partial_filter_selector=X2(e.partial_filter_selector)),e}function oc(e,o){return o.def.fields.map(n=>{let t=N1(n);return K0(e,x0(t))})}function ic(e,o,n){let t=n.def.fields,i=0;for(let a of e){let s=oc(a.doc,n);if(t.length===1)s=s[0];else for(;s.length>o.length;)s.pop();if(Math.abs(A1(s,o))>0)break;++i}return i>0?e.slice(i):e}function ac(e){let o=h0(e);return delete o.startkey,delete o.endkey,delete o.inclusive_start,delete o.inclusive_end,"endkey"in e&&(o.startkey=e.endkey),"startkey"in e&&(o.endkey=e.startkey),"inclusive_start"in e&&(o.inclusive_end=e.inclusive_start),"inclusive_end"in e&&(o.inclusive_start=e.inclusive_end),o}function sc(e){let o=e.fields.filter(function(n){return Q2(n)==="asc"});if(o.length!==0&&o.length!==e.fields.length)throw new Error("unsupported mixed sorting")}function rc(e,o){if(o.defaultUsed&&e.sort){let n=e.sort.filter(function(t){return Object.keys(t)[0]!=="_id"}).map(function(t){return Object.keys(t)[0]});if(n.length>0)throw new Error('Cannot sort on field(s) "'+n.join(",")+'" when using the default index')}o.defaultUsed}function cc(e){if(typeof e.selector!="object")throw new Error("you must provide a selector when you find()")}function lc(e,o){let n=Object.keys(e),t=o?o.map(N1):[],i;return n.length>=t.length?i=n:i=t,t.length===0?{fields:i}:(i=i.sort(function(a,s){let c=t.indexOf(a);c===-1&&(c=Number.MAX_VALUE);let d=t.indexOf(s);return d===-1&&(d=Number.MAX_VALUE),c<d?-1:c>d?1:0}),{fields:i,sortOrder:o.map(N1)})}function dc(e,o){return Q(this,null,function*(){o=mo(o);let n=h0(o.index);o.index=fo(o.index),sc(o.index);let t;function i(){return t||(t=l2(JSON.stringify(o)))}let a=o.name||"idx-"+i(),s=o.ddoc||"idx-"+i(),c="_design/"+s,d=!1,x=!1;function k(M){return M._rev&&M.language!=="query"&&(d=!0),M.language="query",M.views=M.views||{},x=!!M.views[a],x?!1:(M.views[a]={map:{fields:vn(o.index.fields),partial_filter_selector:o.index.partial_filter_selector},reduce:"_count",options:{def:n}},M)}if(e.constructor.emit("debug",["find","creating index",c]),yield U0(e,c,k),d)throw new Error('invalid language for ddoc with id "'+c+'" (should be "query")');let l=s+"/"+a;return yield gn(e).query.call(e,l,{limit:0,reduce:!1}),{id:c,name:a,result:x?"exists":"created"}})}function xo(e){return Q(this,null,function*(){let o=yield e.allDocs({startkey:"_design/",endkey:"_design/\uFFFF",include_docs:!0}),n={indexes:[{ddoc:null,name:"_all_docs",type:"special",def:{fields:[{_id:"asc"}]}}]};return n.indexes=uo(n.indexes,o.rows.filter(function(t){return t.doc.language==="query"}).map(function(t){return(t.doc.views!==void 0?Object.keys(t.doc.views):[]).map(function(a){let s=t.doc.views[a];return{ddoc:t.id,name:a,type:"json",def:fo(s.options.def)}})})),n.indexes.sort(function(t,i){return Z2(t.name,i.name)}),n.total_rows=n.indexes.length,n})}var ee=null,dn={"\uFFFF":{}},vc={queryOpts:{limit:0,startkey:dn,endkey:ee},inMemoryFields:[]};function gc(e,o){return e.def.fields.some(n=>N1(n)===o)}function hc(e,o){let n=e[o];return N1(n)!=="$eq"}function Mo(e,o){let n=o.def.fields.map(N1);return e.slice().sort(function(t,i){let a=n.indexOf(t),s=n.indexOf(i);return a===-1&&(a=Number.MAX_VALUE),s===-1&&(s=Number.MAX_VALUE),Z2(a,s)})}function uc(e,o,n){n=Mo(n,e);let t=!1;for(let i=0,a=n.length;i<a;i++){let s=n[i];if(t||!gc(e,s))return n.slice(i);i<a-1&&hc(o,s)&&(t=!0)}return[]}function pc(e){let o=[];for(let[n,t]of Object.entries(e))for(let i of Object.keys(t))i==="$ne"&&o.push(n);return o}function mc(e,o,n,t){let i=uo(e,uc(o,n,t),pc(n));return Mo(Or(i),o)}function wc(e,o,n){if(o){let t=Ir(o,e),i=po(n,e);return t&&i}return jr(n,e)}var fc=["$eq","$gt","$gte","$lt","$lte"];function ko(e){return fc.indexOf(e)===-1}function xc(e,o){let n=e[0],t=o[n];return typeof t>"u"?!0:!(Object.keys(t).length===1&&N1(t)==="$ne")}function Mc(e,o,n,t){let i=e.def.fields.map(N1);return wc(i,o,n)?xc(i,t):!1}function kc(e,o,n,t){return t.filter(function(i){return Mc(i,n,o,e)})}function yc(e,o,n,t,i){let a=kc(e,o,n,t);if(a.length===0){if(i)throw{error:"no_usable_index",message:"There is no index available for this selector."};let d=t[0];return d.defaultUsed=!0,d}if(a.length===1&&!i)return a[0];let s=Vr(o);function c(d){let x=d.def.fields.map(N1),k=0;for(let l of x)s[l]&&k++;return k}if(i){let d="_design/"+i[0],x=i.length===2?i[1]:!1,k=a.find(function(l){return!!(x&&l.ddoc===d&&x===l.name||l.ddoc===d)});if(!k)throw{error:"unknown_error",message:"Could not find that index or could not use that index for the query"};return k}return Hr(a,c)}function zc(e,o){switch(e){case"$eq":return{key:o};case"$lte":return{endkey:o};case"$gte":return{startkey:o};case"$lt":return{endkey:o,inclusive_end:!1};case"$gt":return{startkey:o,inclusive_start:!1}}return{startkey:ee}}function Cc(e,o){let n=N1(o.def.fields[0]),t=e[n]||{},i=[],a=Object.keys(t),s;for(let c of a){ko(c)&&i.push(n);let d=t[c],x=zc(c,d);s?s=vn([s,x]):s=x}return{queryOpts:s,inMemoryFields:i}}function _c(e,o){switch(e){case"$eq":return{startkey:o,endkey:o};case"$lte":return{endkey:o};case"$gte":return{startkey:o};case"$lt":return{endkey:o,inclusive_end:!1};case"$gt":return{startkey:o,inclusive_start:!1}}}function bc(e,o){let n=o.def.fields.map(N1),t=[],i=[],a=[],s,c;function d(k){s!==!1&&i.push(ee),c!==!1&&a.push(dn),t=n.slice(k)}for(let k=0,l=n.length;k<l;k++){let M=n[k],_=e[M];if(!_||!Object.keys(_).length){d(k);break}else if(Object.keys(_).some(ko)){d(k);break}else if(k>0){let z="$gt"in _||"$gte"in _||"$lt"in _||"$lte"in _,u=Object.keys(e[n[k-1]]),h=go(u,["$eq"]),r=go(u,Object.keys(_));if(z&&!h&&!r){d(k);break}}let B=Object.keys(_),A=null;for(let z of B){let u=_[z],h=_c(z,u);A?A=vn([A,h]):A=h}i.push("startkey"in A?A.startkey:ee),a.push("endkey"in A?A.endkey:dn),"inclusive_start"in A&&(s=A.inclusive_start),"inclusive_end"in A&&(c=A.inclusive_end)}let x={startkey:i,endkey:a};return typeof s<"u"&&(x.inclusive_start=s),typeof c<"u"&&(x.inclusive_end=c),{queryOpts:x,inMemoryFields:t}}function Bc(e){return Object.keys(e).map(function(n){return e[n]}).some(function(n){return typeof n=="object"&&Object.keys(n).length===0})}function Sc(e){return{queryOpts:{startkey:null},inMemoryFields:[Object.keys(e)]}}function Ac(e,o){return o.defaultUsed?Sc(e,o):o.def.fields.length===1?Cc(e,o):bc(e,o)}function Lc(e,o){let n=e.selector,t=e.sort;if(Bc(n))return Object.assign({},vc,{index:o[0]});let i=lc(n,t),a=i.fields,s=i.sortOrder,c=yc(n,a,s,o,e.use_index),d=Ac(n,c),x=d.queryOpts,k=d.inMemoryFields,l=mc(k,c,n,a);return{queryOpts:x,index:c,inMemoryFields:l}}function Ic(e){return e.ddoc.substring(8)+"/"+e.name}function jc(e,o){return Q(this,null,function*(){let n=h0(o);n.descending?("endkey"in n&&typeof n.endkey!="string"&&(n.endkey=""),"startkey"in n&&typeof n.startkey!="string"&&(n.limit=0)):("startkey"in n&&typeof n.startkey!="string"&&(n.startkey=""),"endkey"in n&&typeof n.endkey!="string"&&(n.limit=0)),"key"in n&&typeof n.key!="string"&&(n.limit=0),n.limit>0&&n.indexes_count&&(n.original_limit=n.limit,n.limit+=n.indexes_count);let t=yield e.allDocs(n);return t.rows=t.rows.filter(function(i){return!/^_design\//.test(i.id)}),n.original_limit&&(n.limit=n.original_limit),t.rows=t.rows.slice(0,n.limit),t})}function Vc(e,o,n){return Q(this,null,function*(){return n.name==="_all_docs"?jc(e,o):gn(e).query.call(e,Ic(n),o)})}function yo(e,o,n){return Q(this,null,function*(){o.selector&&(Y2(o.selector,!1),o.selector=X2(o.selector)),o.sort&&(o.sort=ec(o.sort)),o.use_index&&(o.use_index=nc(o.use_index)),"limit"in o||(o.limit=25),cc(o);let t=yield xo(e);e.constructor.emit("debug",["find","planning query",o]);let i=Lc(o,t.indexes);e.constructor.emit("debug",["find","query plan",i]);let a=i.index;rc(o,a);let s=Object.assign({include_docs:!0,reduce:!1,indexes_count:t.total_rows},i.queryOpts);if("startkey"in s&&"endkey"in s&&A1(s.startkey,s.endkey)>0)return{docs:[]};if(o.sort&&typeof o.sort[0]!="string"&&Q2(o.sort[0])==="desc"&&(s.descending=!0,s=ac(s)),i.inMemoryFields.length||(s.limit=o.limit,"skip"in o&&(s.skip=o.skip)),n)return Promise.resolve(i,s);let d=yield Vc(e,s,a);s.inclusive_start===!1&&(d.rows=ic(d.rows,s.startkey,a)),i.inMemoryFields.length&&(d.rows=cn(d.rows,o,i.inMemoryFields));let x={docs:d.rows.map(function(k){let l=k.doc;return o.fields?Lr(l,o.fields):l})};return a.defaultUsed&&(x.warning="No matching index found, create an index to optimize query time."),x})}function Hc(e,o){return Q(this,null,function*(){let n=yield yo(e,o,!0);return{dbname:e.name,index:n.index,selector:o.selector,range:{start_key:n.queryOpts.startkey,end_key:n.queryOpts.endkey},opts:{use_index:o.use_index||[],bookmark:"nil",limit:o.limit,skip:o.skip,sort:o.sort||{},fields:o.fields,conflicts:!1,r:[49]},limit:o.limit,skip:o.skip||0,fields:o.fields}})}function Oc(e,o){return Q(this,null,function*(){if(!o.ddoc)throw new Error("you must supply an index.ddoc when deleting");if(!o.name)throw new Error("you must supply an index.name when deleting");let n=o.ddoc,t=o.name;function i(a){return Object.keys(a.views).length===1&&a.views[t]?{_id:n,_deleted:!0}:(delete a.views[t],a)}return yield U0(e,n,i),yield gn(e).viewCleanup.apply(e),{ok:!0}})}var W0={};W0.createIndex=p2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide an index to create");return(u0(this)?Rr:dc)(this,e)})});W0.find=p2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide search parameters to find()");return(u0(this)?Fr:yo)(this,e)})});W0.explain=p2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide search parameters to explain()");return(u0(this)?qr:Hc)(this,e)})});W0.getIndexes=p2(function(){return Q(this,null,function*(){return(u0(this)?Nr:xo)(this)})});W0.deleteIndex=p2(function(e){return Q(this,null,function*(){if(typeof e!="object")throw new Error("you must provide an index to delete");return(u0(this)?$r:Oc)(this,e)})});var zo=W0;function Co(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let o=Math.random()*16|0;return(e==="x"?o:o&3|8).toString(16)})}var _o=(()=>{let o=class o{constructor(){J4.plugin(zo),this.initDB("register_patient")}initDB(t){this.db=new J4(t,{adapter:"idb"}),console.log(` PouchDB Initialized: ${t}`)}addRecord(t){return t._id=Co(),Y1(this.db.put(t)).pipe(e0(i=>i),t0(this.handleError))}getAllRecords(){return Y1(this.db.allDocs({include_docs:!0})).pipe(e0(t=>t.rows.map(i=>i.doc)),t0(this.handleError))}getRecordById(t){return Y1(this.db.get(t)).pipe(e0(i=>i),t0(this.handleError))}updateRecord(t){return!t._id||!t._rev?w2(()=>new Error(" Record must have _id and _rev to update")):Y1(this.db.put(t)).pipe(e0(i=>i),t0(this.handleError))}deleteRecord(t){return!t._id||!t._rev?w2(()=>new Error(" Record must have _id and _rev to delete")):Y1(this.db.remove(t._id,t._rev)).pipe(e0(i=>i),t0(this.handleError))}addOrUpdateMasterData(t){let i="master_data";return Y1(this.db.get(i).then(a=>this.db.put(p0(J1(J1({},a),t),{_id:i,_rev:a._rev}))).catch(()=>this.db.put(J1({_id:i},t)))).pipe(e0(a=>a),t0(this.handleError))}getMasterData(){return Y1(this.db.get("master_data")).pipe(e0(t=>t),t0(this.handleError))}getMasterTable(t){return Y1(this.db.get("master_data")).pipe(e0(i=>i[t]||[]),t0(this.handleError))}getRecordsByType(t){return Y1(this.db.allDocs({include_docs:!0})).pipe(e0(i=>i.rows.map(a=>a.doc).filter(a=>a.type===t)),t0(this.handleError))}syncWithServer(){return new hn(t=>{if(!this.remoteDB){t.error(" Remote DB not configured!");return}let i=this.db.sync(this.remoteDB,{live:!0,retry:!0}).on("change",a=>t.next(a)).on("paused",a=>console.log(" Sync paused:",a)).on("active",()=>console.log(" Sync active")).on("error",a=>t.error(a));return()=>i.cancel()})}handleError(t){return console.error(" PouchDB Error:",t),w2(()=>t)}};o.\u0275fac=function(i){return new(i||o)},o.\u0275prov=X0({token:o,factory:o.\u0275fac,providedIn:"root"});let e=o;return e})();var Ec=["video"],Pc=["canvas"],Tc=["uploadVideo"];function Rc(e,o){if(e&1){let n=F1();P(0,"div",17)(1,"h3"),J(2,"Basic Information"),O(),P(3,"div",18)(4,"div",19)(5,"label"),J(6,"First Name"),O(),x1(7,"input",20),O(),P(8,"div",19)(9,"label"),J(10,"Last Name"),O(),x1(11,"input",21),O(),P(12,"div",19)(13,"label"),J(14,"Date of Birth"),O(),x1(15,"input",22),O()(),P(16,"div",18)(17,"div",19)(18,"label"),J(19,"Gender"),O(),P(20,"select",23)(21,"option",24),J(22,"Select Gender"),O(),P(23,"option",25),J(24,"Male"),O(),P(25,"option",26),J(26,"Female"),O(),P(27,"option",27),J(28,"Other"),O()()(),P(29,"div",19)(30,"label"),J(31,"Contact Number"),O(),x1(32,"input",28),O(),P(33,"div",19)(34,"label"),J(35,"Email ID"),O(),x1(36,"input",29),O()(),P(37,"div",18)(38,"div",30)(39,"label"),J(40,"Address"),O(),x1(41,"textarea",31),O()(),P(42,"div",32)(43,"button",33),M1("click",function(){k1(n);let i=h1();return y1(i.nextStep())}),J(44,"Next "),x1(45,"img",34),O()()()}}function Fc(e,o){if(e&1&&(P(0,"option",50),J(1),O()),e&2){let n=o.$implicit;f1("value",n.CountryId),g1(),v0(n.Country)}}function qc(e,o){if(e&1&&(P(0,"option",50),J(1),O()),e&2){let n=o.$implicit;f1("value",n.StateId),g1(),v0(n.State)}}function Nc(e,o){if(e&1&&(P(0,"option",50),J(1),O()),e&2){let n=o.$implicit;f1("value",n),g1(),v0(n)}}function $c(e,o){if(e&1){let n=F1();P(0,"div",17)(1,"h3"),J(2,"Additional Information"),O(),P(3,"div",18)(4,"div",19)(5,"label"),J(6,"Relationship Status"),O(),P(7,"select",35)(8,"option",36),J(9,"Select a Relationship Status *"),O(),P(10,"option",37),J(11,"Single"),O(),P(12,"option",38),J(13,"Married"),O()()(),P(14,"div",19)(15,"label"),J(16,"Height"),O(),x1(17,"input",39),O(),P(18,"div",19)(19,"label"),J(20,"Weight"),O(),x1(21,"input",40),O()(),P(22,"div",18)(23,"div",19)(24,"label"),J(25,"Country"),O(),P(26,"select",41),M1("change",function(i){k1(n);let a=h1();return y1(a.onCountryChange(i))}),P(27,"option",36),J(28,"Select Country *"),O(),P1(29,Fc,2,2,"option",42),O()(),P(30,"div",19)(31,"label"),J(32,"State"),O(),P(33,"select",43),M1("change",function(i){k1(n);let a=h1();return y1(a.onStateChange(i))}),P(34,"option",36),J(35,"Select State *"),O(),P1(36,qc,2,2,"option",42),O()(),P(37,"div",19)(38,"label"),J(39,"District"),O(),P(40,"select",44)(41,"option",36),J(42,"Select District *"),O(),P1(43,Nc,2,2,"option",42),O()()(),P(44,"div",18)(45,"div",19)(46,"label"),J(47,"City"),O(),x1(48,"input",45),O(),P(49,"div",19)(50,"label"),J(51,"Taluk"),O(),x1(52,"input",46),O(),P(53,"div",19)(54,"label"),J(55,"UID"),O(),x1(56,"input",47),O()(),P(57,"div",32)(58,"button",48),M1("click",function(){k1(n);let i=h1();return y1(i.prevStep())}),x1(59,"img",49),J(60," Back"),O(),P(61,"button",33),M1("click",function(){k1(n);let i=h1();return y1(i.nextStep())}),J(62,"Next "),x1(63,"img",34),O()()()}if(e&2){let n=h1();g1(29),f1("ngForOf",n.countryList),g1(7),f1("ngForOf",n.stateList),g1(7),f1("ngForOf",n.districtList)}}function Uc(e,o){e&1&&x1(0,"video",62,1)}function Gc(e,o){if(e&1&&x1(0,"img",63),e&2){let n=h1(2);f1("src",n.photoPreviewUrl,ie)}}function Kc(e,o){if(e&1){let n=F1();P(0,"div",64)(1,"button",65),M1("click",function(){k1(n);let i=h1(2);return y1(i.openCamera())}),x1(2,"img",66),J(3," Open Camera "),O()()}}function Wc(e,o){if(e&1){let n=F1();P(0,"button",67),M1("click",function(){k1(n);let i=h1(2);return y1(i.retakeImage())}),J(1," \u{1F504} Retake Image "),O()}}function Zc(e,o){e&1&&(P(0,"span",78),J(1,"\u2713"),O())}function Qc(e,o){if(e&1){let n=F1();P(0,"div",76),M1("click",function(){let i=k1(n).$implicit,a=h1(4);return y1(a.selectCamera(i.deviceId))}),P(1,"span"),J(2),O(),P1(3,Zc,2,0,"span",77),O()}if(e&2){let n=o.$implicit,t=o.index,i=h1(4);z0("selected",i.selectedCameraId===n.deviceId),g1(2),v0(n.label||"Camera "+(t+1)),g1(),f1("ngIf",i.selectedCameraId===n.deviceId)}}function Xc(e,o){if(e&1&&(P(0,"div",73)(1,"div",74),J(2,"Select Camera"),O(),P1(3,Qc,4,4,"div",75),O()),e&2){let n=h1(3);g1(3),f1("ngForOf",n.videoDevices)}}function Jc(e,o){if(e&1){let n=F1();P(0,"div",68)(1,"div",69),M1("click",function(){k1(n);let i=h1(2);return y1(i.captureImage())}),x1(2,"img",66),O(),P(3,"div",70),M1("click",function(){k1(n);let i=h1(2);return y1(i.toggleCameraDropdown())}),x1(4,"img",71),O(),P1(5,Xc,4,1,"div",72),O()}if(e&2){let n=h1(2);g1(5),f1("ngIf",n.showCameraDropdown)}}function Yc(e,o){if(e&1){let n=F1();P(0,"button",79),M1("click",function(){k1(n);let i=h1(2);return y1(i.captureImage())}),J(1," Save "),O()}}function el(e,o){if(e&1){let n=F1();P(0,"div",17)(1,"h3"),J(2,"Capture Patient\u2019s Image"),O(),P(3,"div",51)(4,"div",52)(5,"div",53),P1(6,Uc,2,0,"video",54)(7,Gc,1,1,"img",55)(8,Kc,4,0,"div",56),x1(9,"canvas",57,0),O(),P(11,"div",58),P1(12,Wc,2,0,"button",59)(13,Jc,6,1,"div",60)(14,Yc,2,0,"button",61),O()()(),P(15,"div",32)(16,"button",48),M1("click",function(){k1(n);let i=h1();return y1(i.prevStep())}),x1(17,"img",49),J(18," Back"),O(),P(19,"button",33),M1("click",function(){k1(n);let i=h1();return y1(i.nextStep())}),J(20,"Next "),x1(21,"img",34),O()()()}if(e&2){let n=h1();g1(5),z0("captured",n.photoPreviewUrl),g1(),f1("ngIf",!n.photoPreviewUrl&&n.isCameraActive),g1(),f1("ngIf",n.photoPreviewUrl),g1(),f1("ngIf",!n.photoPreviewUrl&&!n.isCameraActive),g1(4),f1("ngIf",n.photoPreviewUrl),g1(),f1("ngIf",n.isCameraActive&&!n.photoPreviewUrl),g1(),f1("ngIf",n.isCameraActive&&!n.photoPreviewUrl)}}function tl(e,o){if(e&1){let n=F1();P(0,"div",93)(1,"div"),x1(2,"img",94),O(),P(3,"div",95)(4,"label",96),J(5,"Document Type"),O(),P(6,"select",97)(7,"option",36),J(8,"Select Document Type"),O(),P(9,"option",98),J(10,"Aadhaar"),O(),P(11,"option",99),J(12,"PAN"),O(),P(13,"option",100),J(14,"Passport"),O(),P(15,"option",27),J(16,"Other"),O()()(),P(17,"div",101)(18,"button",102),M1("click",function(){let i=k1(n).index,a=h1(3);return y1(a.deleteDoc(i))}),x1(19,"img",103),O(),P(20,"button",102),M1("click",function(){let i=k1(n).index,a=h1(3);return y1(a.editDoc(i))}),x1(21,"img",104),O()()()}if(e&2){let n=o.$implicit;g1(2),f1("src",n.preview,ie)}}function nl(e,o){if(e&1){let n=F1();P(0,"div",83)(1,"div",84),P1(2,tl,22,1,"div",85),O(),P(3,"div",86),M1("drop",function(i){k1(n);let a=h1(2);return y1(a.onFileDrop(i))})("dragover",function(i){k1(n);let a=h1(2);return y1(a.onDragOver(i))}),P(4,"div",87),J(5,"\u2B06\uFE0F"),O(),P(6,"p",88),J(7,"Upload Documents"),O(),P(8,"p",89),J(9," Drag & Drop / "),P(10,"span",90),M1("click",function(){k1(n);let i=re(16);return y1(i.click())}),J(11,"Choose File"),O(),J(12," / "),P(13,"span",91),M1("click",function(){k1(n);let i=h1(2);return y1(i.startCapture())}),J(14,"Capture Image"),O()(),P(15,"input",92,2),M1("change",function(i){k1(n);let a=h1(2);return y1(a.onFileSelected(i))}),O()()()}if(e&2){let n=h1(2);g1(2),f1("ngForOf",n.uploadedDocs)}}function ol(e,o){if(e&1){let n=F1();P(0,"div",105),M1("drop",function(i){k1(n);let a=h1(2);return y1(a.onFileDrop(i))})("dragover",function(i){k1(n);let a=h1(2);return y1(a.onDragOver(i))}),P(1,"div",87),x1(2,"img",106),O(),P(3,"p",88),J(4,"Upload Documents"),O(),P(5,"div",107)(6,"p",89),J(7," Drag & Drop / "),P(8,"span",90),M1("click",function(){k1(n);let i=re(15);return y1(i.click())}),J(9,"Choose File"),O(),J(10," / "),P(11,"span",91),M1("click",function(){k1(n);let i=h1(2);return y1(i.startCapture())}),J(12,"Capture Image"),O(),J(13," to upload "),O()(),P(14,"input",92,2),M1("change",function(i){k1(n);let a=h1(2);return y1(a.onFileSelected(i))}),O()()}}function il(e,o){if(e&1){let n=F1();P(0,"div",17)(1,"h3"),J(2,"Upload Documents"),O(),P1(3,nl,17,1,"div",80)(4,ol,16,0,"div",81),P(5,"div",32)(6,"button",48),M1("click",function(){k1(n);let i=h1();return y1(i.prevStep())}),x1(7,"img",49),J(8," Back"),O(),P(9,"button",82),J(10,"Register \u2714"),O()()()}if(e&2){let n=h1();g1(3),f1("ngIf",n.uploadedDocs.length>0),g1(),f1("ngIf",n.uploadedDocs.length===0)}}var od=(()=>{let o=class o{nextStep(){this.currentStep<4&&this.currentStep++,this.currentStep===3&&this.openCamera()}prevStep(){this.currentStep>1&&this.currentStep--,this.currentStep===3?this.openCamera():(this.stopCamera(),this.isCameraActive=!1,this.showCameraDropdown=!1)}onDragOver(t){t.preventDefault()}onFileDrop(t){t.preventDefault(),t.dataTransfer?.files&&console.log("Files dropped:",t.dataTransfer.files)}addFiles(t){Array.from(t).forEach(i=>{let a=new FileReader;a.onload=()=>{let s=new Date;this.uploadedDocs.push({preview:a.result,type:"",date:s,time:s.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}),this.documents.push(this.fb.group({fileName:i.name,fileType:i.type,data:a.result,type:this.patientForm.value.imageType||"Document"}))},a.readAsDataURL(i)})}onFileSelected(t){let i=t.target;i.files&&this.addFiles(i.files)}editDoc(t){this.uploadedDocs[t].type="Image"}deleteDoc(t){this.uploadedDocs.splice(t,1),t<this.documents.length&&this.documents.removeAt(t)}startCapture(){console.log("Start capture clicked")}openCamera(){return Q(this,null,function*(){this.isCameraActive=!0,this.showCameraDropdown=!1,yield this.startCamera(this.selectedCameraId)})}retakeImage(){this.photoPreviewUrl=null,this.isCameraActive=!0,this.showCameraDropdown=!1,this.startCamera(this.selectedCameraId)}toggleCameraDropdown(){this.showCameraDropdown=!this.showCameraDropdown}selectCamera(t){this.selectedCameraId=t,this.showCameraDropdown=!1,this.startCamera(t)}constructor(t,i,a,s){this.objPouchdbService=t,this.fb=i,this.toastController=a,this.http=s,this.activeSection="section0",this.videoDevices=[],this.selectedCameraId="",this.submittedPatients=[],this.photoPreviewUrl=null,this.editIndex=null,this.isCameraActive=!1,this.showCameraDropdown=!1,this.countryList=[],this.stateList=[],this.districtList=[],this.masterData={},this.currentStep=1,this.uploadedDocs=[],this.capturing=!1,Qt({"create-outline":m3,"trash-outline":w3})}ngOnInit(){return Q(this,null,function*(){if(this.patientForm=this.fb.group({_id:[""],_rev:[null],domainwisepid:[0],patientid:[0],first_name:[""],last_name:[""],date_of_birth:[""],age:[""],ageYears:[""],gender:[""],maritalstatus:[""],height:[""],weight:[""],mobile:[""],email:[""],head_of_household_fname:[""],head_of_household_lname:[""],country:[""],state:[""],district:[""],block:[""],village:[""],address:[""],projid:[""],head_of_household_mobile:[""],isAbhaPatient:[!1],profile:this.fb.group({patientid:[0],imagepath:[""],S3URL:[""]}),pastrecord:[null],createdat:[""],createdby:[""],domain:[0],uid:[""],prefix:[null],EhealthId:[""],MRN:[""],password:[""],consentformcheckstatus:[0],fingerPrintTemplate:[""],health_number:[""],health_address:[""],unique_id:[null],nationalId:[null],ethnicity:[null],subscriptionDetails:this.fb.group({subscribedId:[0],familycardid:[null],freeSubcriptionAllocated:[0],completedFreeSubcrition:[0],remainingSubcription:[0],isActive:[null],subcriptionName:[null],subscriptionPlanActivatedOn:[null],subscriptionExpiredOn:[null],isExpaired:[0]}),localId:[""],patient_status:[null],patient_title:[null],postCode:[null],centerName:[null],status:[null],isSync:[!1],imageType:["select"],documents:this.fb.array([]),patientImage:[""],type:["patient"]}),yield this.loadPatients(),yield this.loadMasterData(),navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices)try{let t=yield navigator.mediaDevices.enumerateDevices();this.videoDevices=t.filter(i=>i.kind==="videoinput"),this.videoDevices.length>0&&(this.selectedCameraId=this.videoDevices[0].deviceId)}catch(t){console.error("Error enumerating devices:",t)}else console.warn("Media Devices API not supported.")})}loadMasterData(){return Q(this,null,function*(){try{this.objPouchdbService.getMasterData().subscribe({next:t=>{console.log(" Master Data already exists in PouchDB"),this.processMasterData(t)},error:()=>{console.log("\u26A0 Master Data not found \u2192 Loading from assets..."),this.http.get("assets/RemediNovaAPI.json").subscribe(t=>{this.objPouchdbService.addOrUpdateMasterData(t).subscribe(()=>{console.log(" Master Data saved in PouchDB"),this.processMasterData(t)})})}})}catch(t){console.error(" Error loading master data:",t)}})}processMasterData(t){try{console.log(" Processing master data:",t),t.data&&Array.isArray(t.data)?(console.log(" Found data array with",t.data.length,"items"),t.data.forEach((i,a)=>{i.tblcountry&&(this.masterData.tblcountry=i.tblcountry,console.log(" Found tblcountry at index",a,"with",i.tblcountry.length,"countries")),i.tblstate&&(this.masterData.tblstate=i.tblstate,console.log(" Found tblstate at index",a,"with",i.tblstate.length,"states")),i.tbldistrict&&(this.masterData.tbldistrict=i.tbldistrict,console.log(" Found tbldistrict at index",a,"with",i.tbldistrict.length,"districts"))})):(console.log(" Using direct data structure"),this.masterData=t),this.masterData.tblcountry?(this.countryList=this.masterData.tblcountry.filter(i=>i.IsDeleted==="0"),console.log(" Countries loaded:",this.countryList.length),console.log(" Sample countries:",this.countryList.slice(0,3)),this.countryList.length>0&&console.log(" SUCCESS: Countries dropdown should now work!")):(console.log(" No tblcountry found in master data"),console.log(" Available keys in masterData:",Object.keys(this.masterData))),console.log(" Master Data processed successfully"),console.log(" Master data structure:",Object.keys(this.masterData))}catch(i){console.error(" Error processing master data:",i)}}loadPatients(){return Q(this,null,function*(){try{this.objPouchdbService.getRecordsByType("patient").subscribe({next:t=>this.submittedPatients=t,error:t=>console.error("Failed to load patients:",t)})}catch(t){console.error("Failed to load patients:",t)}})}get documents(){return this.patientForm.get("documents")}toggleSection(t){this.activeSection=this.activeSection===t?"":t}onCountryChange(t){let i=t.target.value;this.patientForm.patchValue({state:"",district:""}),this.masterData.tblstate?(this.stateList=this.masterData.tblstate.filter(a=>a.CountryId===i&&a.IsDeleted==="0"),console.log(" States loaded for country:",i,this.stateList.length)):(this.stateList=[],console.log(" No states data available")),this.districtList=[]}onStateChange(t){let i=t.target.value;this.patientForm.patchValue({district:""}),this.masterData.tbldistrict?(this.districtList=this.masterData.tbldistrict.filter(a=>a.StateId===i&&a.IsDeleted==="0").map(a=>a.District),console.log(" Districts loaded for state:",i,this.districtList.length)):(this.districtList=[],console.log(" No districts data available"))}startCamera(t){return Q(this,null,function*(){if(!navigator.mediaDevices||!navigator.mediaDevices.getUserMedia){alert("Camera API not supported");return}try{let i=yield navigator.mediaDevices.getUserMedia({video:t?{deviceId:{exact:t}}:!0});this.mediaStream=i,this.video.nativeElement.srcObject=i,yield this.video.nativeElement.play()}catch(i){console.error("Error starting camera:",i)}})}switchCamera(t){this.selectedCameraId=t.target.value,this.selectedCameraId&&this.startCamera(this.selectedCameraId)}captureImage(){let t=this.video.nativeElement,i=this.canvas.nativeElement;i.width=t.videoWidth,i.height=t.videoHeight;let a=i.getContext("2d");if(!a){console.error("Failed to get canvas context.");return}a.drawImage(t,0,0,i.width,i.height);let s=i.toDataURL("image/png");this.documents.push(this.fb.group({fileName:`PatientImage-${Date.now()}.png`,fileType:"image/png",data:s,type:"Patient Image"})),this.patientForm.patchValue({patientImage:s}),this.photoPreviewUrl=s,this.stopCamera(),this.isCameraActive=!1,this.showCameraDropdown=!1,this.video&&this.video.nativeElement&&(this.video.nativeElement.srcObject=null),console.log("Image captured and stored in PouchDB format")}stopCamera(){this.mediaStream&&(this.mediaStream.getTracks().forEach(t=>t.stop()),this.mediaStream=null),this.isCameraActive=!1,this.showCameraDropdown=!1}removeDocument(t){this.documents.removeAt(t)}savePatients(){return Q(this,null,function*(){try{this.patientForm.patchValue({patientImage:this.photoPreviewUrl,documents:this.documents.value});let t=this.patientForm.value;if(t.type="patient",this.editIndex===null)t._rev||delete t._rev,(!t._id||t._id.trim()==="")&&delete t._id,yield Q0(this.objPouchdbService.addRecord(t)),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient saved successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present();else{if(!t._id)throw new Error("Invalid _id for update");let i=yield Q0(this.objPouchdbService.getRecordById(t._id));t._rev=i._rev,yield Q0(this.objPouchdbService.updateRecord(t)),this.editIndex=null,yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient edited successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}this.patientForm.reset(),this.photoPreviewUrl=null}catch(t){console.error("Failed to save patient:",t),yield(yield this.toastController.create({message:"Failed to save patient.",duration:3e3,color:"danger"})).present()}})}editPatients(t,i){this.editIndex=i,this.patientForm.patchValue(p0(J1({},t),{_id:t._id||"",_rev:t._rev||""})),this.photoPreviewUrl=t.profile?.imagepath||null}delete(t){return Q(this,null,function*(){try{yield Q0(this.objPouchdbService.deleteRecord(t)),yield this.loadPatients(),yield(yield this.toastController.create({message:`Patient deleted successfully. Total entries: ${this.submittedPatients.length}`,duration:3e3,color:"success"})).present()}catch(i){console.error("Failed to delete patient:",i),yield(yield this.toastController.create({message:"Failed to delete patient.",duration:3e3,color:"danger"})).present()}})}};o.\u0275fac=function(i){return new(i||o)(m(_o),m(Fn),m(h3),m(Cn))},o.\u0275cmp=T({type:o,selectors:[["app-patient-entry"]],viewQuery:function(i,a){if(i&1&&(y0(Ec,5),y0(Pc,5),y0(Tc,5)),i&2){let s;n0(s=o0())&&(a.video=s.first),n0(s=o0())&&(a.canvas=s.first),n0(s=o0())&&(a.uploadVideo=s.first)}},decls:41,vars:23,consts:[["canvas",""],["video",""],["fileInput",""],[3,"ngSubmit","formGroup"],[1,"popup-overlay"],[1,"container"],[1,"form-wrapper"],[1,"header-container"],[1,"title"],["src","assets/icon/cross.png","alt","",1,"close-btn"],[1,"progress-steps"],[1,"step"],[1,"dot"],[1,"label"],[1,"step-title"],[1,"step-status"],["class","form-section",4,"ngIf"],[1,"form-section"],[1,"form-row"],[1,"form-group"],["type","text","formControlName","first_name","placeholder","Enter Patient\u2019s First Name *","required",""],["type","text","formControlName","last_name","placeholder","Enter Patient\u2019s Last Name *","required",""],["type","date","formControlName","date_of_birth","required",""],["formControlName","gender"],["selected","","disabled",""],["value","Male"],["value","Female"],["value","Other"],["type","text","formControlName","mobile","placeholder","Enter Contact Number *","required",""],["type","email","formControlName","email","placeholder","Enter Email ID"],[1,"form-group","full-width"],["formControlName","address","placeholder","Enter Patient\u2019s Address Here"],[1,"form-actions"],["type","button",1,"btn-next",3,"click"],["src","assets/icon/next.png"],["formControlName","maritalstatus"],["value",""],["value","Single"],["value","Married"],["type","number","formControlName","height","placeholder","Enter Patient\u2019s Height (Cms) *"],["type","number","formControlName","weight","placeholder","Enter Patient\u2019s Weight (Kgs) *"],["formControlName","country",3,"change"],[3,"value",4,"ngFor","ngForOf"],["formControlName","state",3,"change"],["formControlName","district"],["type","text","formControlName","village","placeholder","Enter City *"],["type","text","formControlName","block","placeholder","Enter Taluk *"],["type","text","formControlName","uid","placeholder","Enter UID *"],["type","button",1,"btn-back",3,"click"],["src","assets/icon/left-arraw.png"],[3,"value"],[1,"image-container"],[1,"img-2"],[1,"image-box"],["autoplay","","muted","","playsinline","",4,"ngIf"],["class","captured-image",3,"src",4,"ngIf"],["class","open-camera-container",4,"ngIf"],["hidden",""],[1,"actions"],["type","button","class","btn-retake",3,"click",4,"ngIf"],["class","capture-controls",4,"ngIf"],["type","button","class","btn-save",3,"click",4,"ngIf"],["autoplay","","muted","","playsinline",""],[1,"captured-image",3,"src"],[1,"open-camera-container"],["type","button",1,"btn-open-camera",3,"click"],["src","assets/icon/camera.png"],["type","button",1,"btn-retake",3,"click"],[1,"capture-controls"],[1,"btn-camera",3,"click"],[1,"btn-dropdown",3,"click"],["src","assets/icon/down-arraw.png"],["class","camera-dropdown",4,"ngIf"],[1,"camera-dropdown"],[1,"dropdown-header"],["class","dropdown-item",3,"selected","click",4,"ngFor","ngForOf"],[1,"dropdown-item",3,"click"],["class","check-icon",4,"ngIf"],[1,"check-icon"],["type","button",1,"btn-save",3,"click"],["class","upload-docs-wrapper",4,"ngIf"],["class","upload-box-full",3,"drop","dragover",4,"ngIf"],["type","submit",1,"btn-register"],[1,"upload-docs-wrapper"],[1,"uploaded-docs"],["class","uploaded-item",4,"ngFor","ngForOf"],[1,"upload-box",3,"drop","dragover"],[1,"upload-icon"],[1,"upload-text"],[1,"upload-subtext"],[1,"choose-file",3,"click"],[1,"capture-image",3,"click"],["type","file","hidden","","multiple","",3,"change"],[1,"uploaded-item"],[1,"doc-preview",3,"src"],[1,"doc-info"],["for","select"],["formControlName","type","id","select",1,"doc-type"],["value","Aadhaar"],["value","PAN"],["value","Passport"],[1,"doc-actions"],[1,"icon-btn",3,"click"],["src","assets/icon/cross.png","alt",""],["src","assets/icon/right.png","alt",""],[1,"upload-box-full",3,"drop","dragover"],["src","assets/icon/upload-img.png"],[2,"display","flex","justify-content","center"]],template:function(i,a){i&1&&(P(0,"form",3),M1("ngSubmit",function(){return a.savePatients()}),P(1,"div",4)(2,"div",5)(3,"div",6)(4,"div",7)(5,"h2",8),J(6,"Register New Patient"),O(),x1(7,"img",9),O(),P(8,"div",10)(9,"div",11),x1(10,"span",12),P(11,"div",13)(12,"p",14),J(13,"Basic Information"),O(),P(14,"p",15),J(15),O()()(),P(16,"div",11),x1(17,"span",12),P(18,"div",13)(19,"p",14),J(20,"Additional Information"),O(),P(21,"p",15),J(22),O()()(),P(23,"div",11),x1(24,"span",12),P(25,"div",13)(26,"p",14),J(27,"Patient\u2019s Image"),O(),P(28,"p",15),J(29),O()()(),P(30,"div",11),x1(31,"span",12),P(32,"div",13)(33,"p",14),J(34,"Upload Documents"),O(),P(35,"p",15),J(36),O()()()(),P1(37,Rc,46,0,"div",16)(38,$c,64,3,"div",16)(39,el,22,8,"div",16)(40,il,11,2,"div",16),O()()()()),i&2&&(f1("formGroup",a.patientForm),g1(9),z0("active",a.currentStep===1)("completed",a.currentStep>1),g1(6),v0(a.currentStep===1?"In Progress":a.currentStep>1?"Completed":"Pending"),g1(),z0("active",a.currentStep===2)("completed",a.currentStep>2),g1(6),v0(a.currentStep===2?"In Progress":a.currentStep>2?"Completed":"Pending"),g1(),z0("active",a.currentStep===3)("completed",a.currentStep>3),g1(6),v0(a.currentStep===3?"In Progress":a.currentStep>3?"Completed":"Pending"),g1(),z0("active",a.currentStep===4),g1(6),v0(a.currentStep===4?"In Progress":"Pending"),g1(),f1("ngIf",a.currentStep===1),g1(),f1("ngIf",a.currentStep===2),g1(),f1("ngIf",a.currentStep===3),g1(),f1("ngIf",a.currentStep===4))},dependencies:[Nn,In,Dn,En,Sn,jn,On,An,Ln,Rn,Vn,Hn,qn,u3,x2,zn,O0,_n],styles:['@font-face{font-family:DM Sans;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu6-K6h9Q.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:DM Sans;font-style:normal;font-weight:400;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K4.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:DM Sans;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu6-K6h9Q.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:DM Sans;font-style:normal;font-weight:500;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K4.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}@font-face{font-family:DM Sans;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu6-K6h9Q.woff2) format("woff2");unicode-range:U+0100-02BA,U+02BD-02C5,U+02C7-02CC,U+02CE-02D7,U+02DD-02FF,U+0304,U+0308,U+0329,U+1D00-1DBF,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20C0,U+2113,U+2C60-2C7F,U+A720-A7FF}@font-face{font-family:DM Sans;font-style:normal;font-weight:700;font-display:swap;src:url(https://fonts.gstatic.com/s/dmsans/v16/rP2Yp2ywxg089UriI5-g4vlH9VoD8Cmcqbu0-K4.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}.popup-overlay[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100%;height:100%;background:#0006;display:flex;justify-content:center;align-items:center;z-index:1000}.container[_ngcontent-%COMP%]{width:94%;height:90vh;background:#fff;border-radius:8px;box-shadow:0 2px 6px #00000014;display:flex;flex-direction:column}.header-container[_ngcontent-%COMP%]{display:flex;justify-content:space-between;padding:16px 32px;border-bottom:1px solid #E5E7EB;background:#fff;position:sticky;top:0;z-index:100}.title[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:500;font-size:20px;line-height:130%;letter-spacing:0;vertical-align:middle;color:#111827;margin:0}.close-btn[_ngcontent-%COMP%]{height:15px;width:15px}.form-wrapper[_ngcontent-%COMP%]{width:100%;display:flex;flex-direction:column;height:100%}.progress-steps[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:24px;padding:16px 32px 32px;border-bottom:1px solid #E5E7EB;background:#fff;position:sticky;top:0;z-index:100;position:relative;margin-top:19px}.step[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:10px;opacity:.5;position:relative;z-index:2;min-width:0;margin-right:20px}.step[_ngcontent-%COMP%]:not(:last-child):after{content:"";position:absolute;top:6px;left:50%;right:-50%;height:1px;left:calc(50% + 22px);right:calc(-50% + 12px);background:#d3d3d3;z-index:1;width:calc(25vw - 40px);max-width:135px;min-width:60px}.step.active[_ngcontent-%COMP%]{opacity:1}.step.active[_ngcontent-%COMP%]:not(:last-child):after{background:#f59e0b}.step.completed[_ngcontent-%COMP%]{opacity:1}.step.completed[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{background:#10b981}.step.completed[_ngcontent-%COMP%]:not(:last-child):after{background:#10b981}.step.completed[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{color:#10b981}.step.active[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{background:#f59e0b}.step[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{width:14px;height:14px;border-radius:50%;background:#d3d3d3;position:relative;z-index:3}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;vertical-align:middle;text-align:center;margin:0}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:400;font-size:11px;line-height:140%;letter-spacing:.2px;vertical-align:middle;color:#92400e;text-align:center;margin:0}input[type=date][_ngcontent-%COMP%]{font-size:14px;color:#374151}input[_ngcontent-%COMP%]::placeholder{font-size:14px;color:#374151}select[_ngcontent-%COMP%]   option[disabled][_ngcontent-%COMP%]{color:#374151;font-size:14px}select[_ngcontent-%COMP%]{font-size:14px;color:#374151}.form-section[_ngcontent-%COMP%]{margin-top:10px;padding:0 32px 32px;overflow-y:auto;flex:1;-ms-overflow-style:none;scrollbar-width:none}.form-section[_ngcontent-%COMP%]::-webkit-scrollbar{display:none}.form-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:400;font-size:16px;line-height:150%;letter-spacing:0;vertical-align:middle;color:#4a4a48;margin-bottom:24px;margin-top:22px}.form-row[_ngcontent-%COMP%]{display:flex;flex-wrap:wrap;gap:16px}.form-group[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:6px;min-width:200px}.form-group.full-width[_ngcontent-%COMP%]{flex:1 1 100%}label[_ngcontent-%COMP%]{font-family:DM Sans,sans-serif;font-weight:500;font-size:13px;line-height:140%;letter-spacing:.2px;color:#374151;margin-top:29px;margin-bottom:11px;margin-left:6px}input[_ngcontent-%COMP%], select[_ngcontent-%COMP%]{width:387px;height:48px;border-radius:8px;gap:10px;opacity:1;padding:12px 16px;border-width:1px;border-style:solid;border-color:#d1d5db}textarea[_ngcontent-%COMP%]{width:1193px;min-width:320px;height:96px;min-height:96px;max-height:160px;border-radius:8px;gap:10px;opacity:1;padding:12px 16px;border-width:1px;border-style:solid;border-color:#d1d5db}input[_ngcontent-%COMP%]:focus, select[_ngcontent-%COMP%]:focus{border-color:#999}.form-actions[_ngcontent-%COMP%]{display:flex;justify-content:space-between;align-items:center;margin-top:33px;border-top:1px solid #E5E7EB;padding:20px;gap:16px}.btn-back[_ngcontent-%COMP%]{background:none;border:none;color:#007aff;cursor:pointer;min-width:80px;height:48px;display:flex;align-items:center;justify-content:center;gap:8px;font-family:DM Sans,system-ui,-apple-system,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;text-align:center;padding:12px 16px;border-radius:8px;transition:all .3s ease}.btn-back[_ngcontent-%COMP%]:hover{background:#f0f8ff}.btn-back[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:14px;height:14px}.btn-next[_ngcontent-%COMP%]{min-width:120px;height:48px;border-radius:8px;display:inline-flex;align-items:center;justify-content:center;gap:8px;opacity:1;padding:12px 24px;border:1px solid #007AFF;color:#fff;box-sizing:border-box;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;cursor:pointer;transition:all .3s ease}.btn-register[_ngcontent-%COMP%]{background:#007aff;color:#fff;padding:12px 24px;font-size:14px;border:none;border-radius:8px;cursor:pointer;min-width:120px;height:48px;font-family:DM Sans,sans-serif;font-weight:600;transition:all .3s ease}.btn-register[_ngcontent-%COMP%]:hover{background:#0056cc}.image-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:16px;margin-top:20px}.img-2[_ngcontent-%COMP%]{width:402.67px;height:366px;display:flex;flex-direction:column;gap:16px;opacity:1}.image-box[_ngcontent-%COMP%]{width:402.67px;height:302px;border-radius:8px;opacity:1;background-color:#d1d5db;display:flex;align-items:center;justify-content:center;position:relative;overflow:hidden}.image-box[_ngcontent-%COMP%]   video[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:8px}.image-box[_ngcontent-%COMP%]   .captured-image[_ngcontent-%COMP%]{width:100%;height:100%;object-fit:cover;border-radius:8px}.open-camera-container[_ngcontent-%COMP%]{display:flex;flex-direction:column;align-items:center;gap:12px}.btn-open-camera[_ngcontent-%COMP%]{width:160px;height:48px;border-radius:8px;border:1px solid #007AFF;background:transparent;color:#007aff;display:flex;align-items:center;justify-content:center;gap:8px;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;cursor:pointer;transition:all .3s ease}.btn-open-camera[_ngcontent-%COMP%]:hover{background:#007aff;color:#fff}.btn-open-camera[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{width:20px;height:20px}.actions[_ngcontent-%COMP%]{display:flex;gap:20px;align-items:center;justify-content:space-between}.btn-retake[_ngcontent-%COMP%]{width:140px;min-width:140px;height:48px;min-height:48px;border-radius:8px;display:inline-flex;align-items:center;justify-content:center;gap:8px;padding:12px 24px;box-sizing:border-box;border:1px solid #007AFF;opacity:1;background:transparent;color:#007aff;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;cursor:pointer;transition:all .3s ease}.btn-retake[_ngcontent-%COMP%]:hover{background:#007aff;color:#fff}.btn-save[_ngcontent-%COMP%]{width:120px;min-width:120px;height:48px;min-height:48px;border-radius:8px;display:inline-flex;align-items:center;justify-content:center;gap:8px;padding:12px 24px;box-sizing:border-box;border:1px solid #007AFF;opacity:1;background:#007aff;color:#fff;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;cursor:pointer;transition:all .3s ease}.btn-save[_ngcontent-%COMP%]:hover{background:#0056cc}.capture-controls[_ngcontent-%COMP%]{display:flex;gap:0;width:96px;height:48px;position:relative}.btn-camera[_ngcontent-%COMP%]{width:48px;height:48px;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;border-top-left-radius:8px;border-bottom-left-radius:8px;opacity:1;cursor:pointer;background-color:#007aff;border:1px solid #007AFF;transition:all .3s ease}.btn-camera[_ngcontent-%COMP%]:hover{background-color:#0056cc}.btn-dropdown[_ngcontent-%COMP%]{width:48px;height:48px;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;border-top-right-radius:8px;border-bottom-right-radius:8px;border:1px solid #007AFF;opacity:1;background:transparent;cursor:pointer;transition:all .3s ease}.btn-dropdown[_ngcontent-%COMP%]:hover{background:#f0f8ff}.btn-camera[_ngcontent-%COMP%]   img[_ngcontent-%COMP%], .btn-dropdown[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{max-width:24px;max-height:24px}.camera-dropdown[_ngcontent-%COMP%]{position:absolute;top:52px;right:0;width:250px;background:#fff;border:1px solid #E5E7EB;border-radius:8px;box-shadow:0 4px 12px #00000026;z-index:1000;overflow:hidden}.dropdown-header[_ngcontent-%COMP%]{padding:12px 16px;background:#f9fafb;border-bottom:1px solid #E5E7EB;font-family:DM Sans,sans-serif;font-weight:600;font-size:14px;color:#374151}.dropdown-item[_ngcontent-%COMP%]{padding:12px 16px;display:flex;align-items:center;justify-content:space-between;cursor:pointer;font-family:DM Sans,sans-serif;font-size:14px;color:#374151;transition:background .2s ease}.dropdown-item[_ngcontent-%COMP%]:hover{background:#f3f4f6}.dropdown-item.selected[_ngcontent-%COMP%]{background:#ebf8ff;color:#007aff}.check-icon[_ngcontent-%COMP%]{color:#007aff;font-weight:700;font-size:16px}.upload-box[_ngcontent-%COMP%], .upload-box-full[_ngcontent-%COMP%]{border:1px dashed #c7c7c7;border-radius:6px;padding:40px;text-align:center;cursor:pointer;background:#fff;transition:background .3s ease;margin-top:20px}.upload-box[_ngcontent-%COMP%]:hover, .upload-box-full[_ngcontent-%COMP%]:hover{background:#f9f9f9}.upload-icon[_ngcontent-%COMP%]{font-size:40px;color:#1976d2}.upload-text[_ngcontent-%COMP%]{font-family:DM Sans,system-ui,-apple-system,sans-serif;font-weight:600;font-size:14px;line-height:140%;letter-spacing:.4px;text-align:center;vertical-align:middle;color:#374151}.upload-subtext[_ngcontent-%COMP%]{margin-top:4px;font-size:14px;color:#555;width:392px;height:21px;display:flex;gap:8px;align-items:center;opacity:1;transform:rotate(0)}.doc-info[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:8px;margin-left:16px}.choose-file[_ngcontent-%COMP%], .capture-image[_ngcontent-%COMP%]{color:#1976d2;cursor:pointer;text-decoration:underline}.upload-docs-wrapper[_ngcontent-%COMP%]{display:flex;gap:24px;margin-top:20px;height:366px;background:transparent}.uploaded-docs[_ngcontent-%COMP%]{flex:1;display:flex;flex-direction:column;gap:12px}.uploaded-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:12px;background:#f8f9fb;border-radius:6px;padding:8px 12px}.doc-preview[_ngcontent-%COMP%]{width:40px;height:40px;border-radius:4px;object-fit:cover}.doc-type[_ngcontent-%COMP%]{flex:1;padding:6px;font-size:14px;border:1px solid #ccc;border-radius:4px}.doc-actions[_ngcontent-%COMP%]{display:flex;align-items:center;gap:8px;position:relative;bottom:34px}.upload-box[_ngcontent-%COMP%]{flex:1}@media (max-width: 768px){.upload-docs-wrapper[_ngcontent-%COMP%]{flex-direction:column}.upload-box[_ngcontent-%COMP%]{width:100%}}.uploaded-item[_ngcontent-%COMP%]{display:flex;align-items:center;gap:16px;background:#f8f9fb;border-radius:8px;padding:16px;margin-bottom:10px;width:545px;height:122px;background:#f9fafb}.doc-preview[_ngcontent-%COMP%]{width:80px;height:80px;border-radius:8px;object-fit:cover}.doc-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{margin:0}.doc-type[_ngcontent-%COMP%]{width:340px;height:48px;display:flex;justify-content:space-between;align-items:center;padding:12px 16px;border-radius:8px;border:1px solid #ccc;box-sizing:border-box;opacity:1;transform:rotate(0)}.doc-name[_ngcontent-%COMP%]{font-weight:600}.doc-meta[_ngcontent-%COMP%]{font-size:12px;color:gray}.icon-btn[_ngcontent-%COMP%]{background:none;border:none;font-size:16px;cursor:pointer}.camera-box[_ngcontent-%COMP%]{margin-top:20px;text-align:center}.preview-box[_ngcontent-%COMP%]{width:300px;height:200px;background:#d5d8de;margin:0 auto 12px;border-radius:6px}.capture-actions[_ngcontent-%COMP%]{display:flex;justify-content:center;gap:12px}.btn-outline[_ngcontent-%COMP%]{background:none;border:1px solid #ccc;border-radius:4px;padding:8px 16px;cursor:pointer}@media (max-width: 1024px){.form-row[_ngcontent-%COMP%]{flex-direction:column}}@media (max-width: 768px){.progress-steps[_ngcontent-%COMP%]{flex-direction:row;gap:8px;padding:12px 16px 24px;justify-content:space-between}.step[_ngcontent-%COMP%]{flex:1;min-width:0;max-width:120px}.step[_ngcontent-%COMP%]:not(:last-child):after{width:calc(100% - 20px);left:60%;right:-40%}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:12px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-size:10px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.form-actions[_ngcontent-%COMP%]{flex-direction:row;justify-content:space-between;align-items:center;gap:16px;padding:16px 20px}.btn-back[_ngcontent-%COMP%], .btn-next[_ngcontent-%COMP%], .btn-register[_ngcontent-%COMP%]{flex:1;max-width:150px;min-width:100px}}@media (max-width: 480px){.container[_ngcontent-%COMP%]{width:100%;height:100vh;border-radius:0}.header-container[_ngcontent-%COMP%]{padding:12px 16px}.title[_ngcontent-%COMP%]{font-size:18px}.progress-steps[_ngcontent-%COMP%]{padding:8px 12px 16px;gap:4px}.step[_ngcontent-%COMP%]{flex:1;min-width:0}.step[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{width:12px;height:12px}.step[_ngcontent-%COMP%]:not(:last-child):after{height:2px;top:5px;width:calc(100% - 10px);left:55%;right:-45%}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:10px;line-height:1.2}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-size:8px;line-height:1.2}.form-section[_ngcontent-%COMP%]{padding:0 12px 12px}.form-actions[_ngcontent-%COMP%]{padding:12px 16px;gap:12px}.btn-back[_ngcontent-%COMP%]{min-width:70px;padding:10px 12px;font-size:13px}.btn-next[_ngcontent-%COMP%], .btn-register[_ngcontent-%COMP%]{min-width:90px;padding:10px 16px;font-size:13px}.image-box[_ngcontent-%COMP%]{width:90%;height:200px}.img-2[_ngcontent-%COMP%]{width:100%;height:auto}.preview-box[_ngcontent-%COMP%]{width:90%;height:160px}.actions[_ngcontent-%COMP%]{flex-direction:column;gap:12px;align-items:stretch}.btn-retake[_ngcontent-%COMP%], .btn-save[_ngcontent-%COMP%], .btn-open-camera[_ngcontent-%COMP%]{width:100%}.capture-controls[_ngcontent-%COMP%]{width:100%;justify-content:center}.camera-dropdown[_ngcontent-%COMP%]{width:100%;right:auto;left:0}}@media (max-width: 768px) and (min-width: 481px){.container[_ngcontent-%COMP%]{width:100%;height:100vh;border-radius:0}.header-container[_ngcontent-%COMP%]{padding:16px 20px}.progress-steps[_ngcontent-%COMP%]{padding:12px 20px 20px;gap:12px}.step[_ngcontent-%COMP%]:not(:last-child):after{width:calc(100% - 30px);left:65%;right:-35%}.form-actions[_ngcontent-%COMP%]{padding:16px 20px}.image-container[_ngcontent-%COMP%]{margin-top:10px}.img-2[_ngcontent-%COMP%]{width:100%;max-width:350px}.image-box[_ngcontent-%COMP%]{width:100%;height:250px}.btn-open-camera[_ngcontent-%COMP%]{width:180px}.actions[_ngcontent-%COMP%]{justify-content:center;gap:16px}.camera-dropdown[_ngcontent-%COMP%]{width:280px}}@media (max-width: 360px){.progress-steps[_ngcontent-%COMP%]{padding:6px 8px 12px;gap:2px}.step[_ngcontent-%COMP%]   .dot[_ngcontent-%COMP%]{width:10px;height:10px}.step[_ngcontent-%COMP%]:not(:last-child):after{height:1px;top:4px}.label[_ngcontent-%COMP%]   .step-title[_ngcontent-%COMP%]{font-size:9px}.label[_ngcontent-%COMP%]   .step-status[_ngcontent-%COMP%]{font-size:7px}.form-actions[_ngcontent-%COMP%]{padding:10px 12px}.btn-back[_ngcontent-%COMP%]{min-width:60px;font-size:12px}.btn-next[_ngcontent-%COMP%], .btn-register[_ngcontent-%COMP%]{min-width:80px;font-size:12px}}']});let e=o;return e})();export{xi as a,u3 as b,od as c};
