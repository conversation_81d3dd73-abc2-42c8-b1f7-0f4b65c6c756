import { Component, OnInit } from '@angular/core'
import { CommonModule } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { IonContent, IonHeader, IonTitle, IonToolbar } from '@ionic/angular/standalone'

// ✅ Import shared interface
import { UploadedDoc } from '../interface'

@Component({
  selector: 'app-new-petient-register',
  templateUrl: './new-petient-register.page.html',
  styleUrls: ['./new-petient-register.page.scss'],
  standalone: true,
  imports: [ CommonModule, FormsModule]
})
export class NewPetientRegisterPage implements OnInit {
  currentStep = 1
  uploadedDocs: UploadedDoc[] = []
  capturing = false

  nextStep() {
    if (this.currentStep < 4) this.currentStep++
  }

  prevStep() {
    if (this.currentStep > 1) this.currentStep--
  }

  onDragOver(event: DragEvent) {
    event.preventDefault()
  }

  onFileDrop(event: DragEvent) {
    event.preventDefault()
    if (event.dataTransfer?.files) this.addFiles(event.dataTransfer.files)
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement
    if (input.files) this.addFiles(input.files)
  }


  private addFiles(files: FileList) {
    Array.from(files).forEach(file => {
      const reader = new FileReader()
      reader.onload = () => {
        const now = new Date()
        this.uploadedDocs.push({
          preview: reader.result as string,
          type: '',
          date: now,
          time: now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
        })
      }
      reader.readAsDataURL(file)
    })
  }

  editDoc(index: number) {
    this.uploadedDocs[index].type = 'Image'
  }


  deleteDoc(index: number) {
    this.uploadedDocs.splice(index, 1)
  }

  startCapture() {
    this.capturing = true
  }

  retake() {
    this.capturing = true
  }

  save() {
    this.capturing = false
  }

  registerPatient() {
    const finalData = {
      step1: 'Basic Info Data',
      step2: 'Additional Info Data',
      uploadedDocs: this.uploadedDocs
    }
    console.log('Final Registration Data:', finalData)
    alert('Patient Registered Successfully!')
  }

  constructor() {}
  ngOnInit() {}
}
