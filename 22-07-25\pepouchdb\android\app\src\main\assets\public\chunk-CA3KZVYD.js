import{b as Cc,c as wc,d as Ic,e as bc,f as _c}from"./chunk-B7SFH74S.js";import{g as jf,h as Vf}from"./chunk-UOV5QIVR.js";import{a as qf,d as Zf}from"./chunk-GIIU5PV3.js";import{a as Tc}from"./chunk-M2X7KQLB.js";import{a as jt,d as zf,e as Gf,f as Wf,h as Ec}from"./chunk-XTVTS2NW.js";import{a as Re,b as Bf,c as Uf,d as Hf,e as wi,f as $f}from"./chunk-C5RQ2IC2.js";import{b as Lt}from"./chunk-42C7ZIID.js";import{a as g,b as N,d as Dc,g as oe}from"./chunk-2R6CW7ES.js";function Hn(e){let t=e(r=>{Error.call(r),r.stack=new Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}var qe=Hn(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function PD(e,n){let t=typeof n=="object";return new Promise((r,o)=>{let i=!1,s;e.subscribe({next:a=>{s=a,i=!0},error:o,complete:()=>{i?r(s):t?r(n.defaultValue):o(new qe)}})})}function T(e){return typeof e=="function"}var Ii=Hn(e=>function(t){e(this),this.message=t?`${t.length} errors occurred during unsubscription:
${t.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=t});function eo(e,n){if(e){let t=e.indexOf(n);0<=t&&e.splice(t,1)}}var X=class e{constructor(n){this.initialTeardown=n,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let n;if(!this.closed){this.closed=!0;let{_parentage:t}=this;if(t)if(this._parentage=null,Array.isArray(t))for(let i of t)i.remove(this);else t.remove(this);let{initialTeardown:r}=this;if(T(r))try{r()}catch(i){n=i instanceof Ii?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{Yf(i)}catch(s){n=n??[],s instanceof Ii?n=[...n,...s.errors]:n.push(s)}}if(n)throw new Ii(n)}}add(n){var t;if(n&&n!==this)if(this.closed)Yf(n);else{if(n instanceof e){if(n.closed||n._hasParent(this))return;n._addParent(this)}(this._finalizers=(t=this._finalizers)!==null&&t!==void 0?t:[]).push(n)}}_hasParent(n){let{_parentage:t}=this;return t===n||Array.isArray(t)&&t.includes(n)}_addParent(n){let{_parentage:t}=this;this._parentage=Array.isArray(t)?(t.push(n),t):t?[t,n]:n}_removeParent(n){let{_parentage:t}=this;t===n?this._parentage=null:Array.isArray(t)&&eo(t,n)}remove(n){let{_finalizers:t}=this;t&&eo(t,n),n instanceof e&&n._removeParent(this)}};X.EMPTY=(()=>{let e=new X;return e.closed=!0,e})();var Sc=X.EMPTY;function bi(e){return e instanceof X||e&&"closed"in e&&T(e.remove)&&T(e.add)&&T(e.unsubscribe)}function Yf(e){T(e)?e():e.unsubscribe()}var Ze={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var $n={setTimeout(e,n,...t){let{delegate:r}=$n;return r?.setTimeout?r.setTimeout(e,n,...t):setTimeout(e,n,...t)},clearTimeout(e){let{delegate:n}=$n;return(n?.clearTimeout||clearTimeout)(e)},delegate:void 0};function _i(e){$n.setTimeout(()=>{let{onUnhandledError:n}=Ze;if(n)n(e);else throw e})}function to(){}var Qf=Mc("C",void 0,void 0);function Kf(e){return Mc("E",void 0,e)}function Jf(e){return Mc("N",e,void 0)}function Mc(e,n,t){return{kind:e,value:n,error:t}}var cn=null;function zn(e){if(Ze.useDeprecatedSynchronousErrorHandling){let n=!cn;if(n&&(cn={errorThrown:!1,error:null}),e(),n){let{errorThrown:t,error:r}=cn;if(cn=null,t)throw r}}else e()}function Xf(e){Ze.useDeprecatedSynchronousErrorHandling&&cn&&(cn.errorThrown=!0,cn.error=e)}var un=class extends X{constructor(n){super(),this.isStopped=!1,n?(this.destination=n,bi(n)&&n.add(this)):this.destination=jD}static create(n,t,r){return new Gn(n,t,r)}next(n){this.isStopped?Nc(Jf(n),this):this._next(n)}error(n){this.isStopped?Nc(Kf(n),this):(this.isStopped=!0,this._error(n))}complete(){this.isStopped?Nc(Qf,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(n){this.destination.next(n)}_error(n){try{this.destination.error(n)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},FD=Function.prototype.bind;function Ac(e,n){return FD.call(e,n)}var Rc=class{constructor(n){this.partialObserver=n}next(n){let{partialObserver:t}=this;if(t.next)try{t.next(n)}catch(r){Ti(r)}}error(n){let{partialObserver:t}=this;if(t.error)try{t.error(n)}catch(r){Ti(r)}else Ti(n)}complete(){let{partialObserver:n}=this;if(n.complete)try{n.complete()}catch(t){Ti(t)}}},Gn=class extends un{constructor(n,t,r){super();let o;if(T(n)||!n)o={next:n??void 0,error:t??void 0,complete:r??void 0};else{let i;this&&Ze.useDeprecatedNextContext?(i=Object.create(n),i.unsubscribe=()=>this.unsubscribe(),o={next:n.next&&Ac(n.next,i),error:n.error&&Ac(n.error,i),complete:n.complete&&Ac(n.complete,i)}):o=n}this.destination=new Rc(o)}};function Ti(e){Ze.useDeprecatedSynchronousErrorHandling?Xf(e):_i(e)}function LD(e){throw e}function Nc(e,n){let{onStoppedNotification:t}=Ze;t&&$n.setTimeout(()=>t(e,n))}var jD={closed:!0,next:to,error:LD,complete:to};var Wn=typeof Symbol=="function"&&Symbol.observable||"@@observable";function ve(e){return e}function xc(...e){return Oc(e)}function Oc(e){return e.length===0?ve:e.length===1?e[0]:function(t){return e.reduce((r,o)=>o(r),t)}}var O=(()=>{class e{constructor(t){t&&(this._subscribe=t)}lift(t){let r=new e;return r.source=this,r.operator=t,r}subscribe(t,r,o){let i=BD(t)?t:new Gn(t,r,o);return zn(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(t){try{return this._subscribe(t)}catch(r){t.error(r)}}forEach(t,r){return r=eh(r),new r((o,i)=>{let s=new Gn({next:a=>{try{t(a)}catch(c){i(c),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(t){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(t)}[Wn](){return this}pipe(...t){return Oc(t)(this)}toPromise(t){return t=eh(t),new t((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=n=>new e(n),e})();function eh(e){var n;return(n=e??Ze.Promise)!==null&&n!==void 0?n:Promise}function VD(e){return e&&T(e.next)&&T(e.error)&&T(e.complete)}function BD(e){return e&&e instanceof un||VD(e)&&bi(e)}function kc(e){return T(e?.lift)}function L(e){return n=>{if(kc(n))return n.lift(function(t){try{return e(t,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function k(e,n,t,r,o){return new Pc(e,n,t,r,o)}var Pc=class extends un{constructor(n,t,r,o,i,s){super(n),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=t?function(a){try{t(a)}catch(c){n.error(c)}}:super._next,this._error=o?function(a){try{o(a)}catch(c){n.error(c)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){n.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var n;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:t}=this;super.unsubscribe(),!t&&((n=this.onFinalize)===null||n===void 0||n.call(this))}}};function qn(){return L((e,n)=>{let t=null;e._refCount++;let r=k(n,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){t=null;return}let o=e._connection,i=t;t=null,o&&(!i||o===i)&&o.unsubscribe(),n.unsubscribe()});e.subscribe(r),r.closed||(t=e.connect())})}var Zn=class extends O{constructor(n,t){super(),this.source=n,this.subjectFactory=t,this._subject=null,this._refCount=0,this._connection=null,kc(n)&&(this.lift=n.lift)}_subscribe(n){return this.getSubject().subscribe(n)}getSubject(){let n=this._subject;return(!n||n.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:n}=this;this._subject=this._connection=null,n?.unsubscribe()}connect(){let n=this._connection;if(!n){n=this._connection=new X;let t=this.getSubject();n.add(this.source.subscribe(k(t,void 0,()=>{this._teardown(),t.complete()},r=>{this._teardown(),t.error(r)},()=>this._teardown()))),n.closed&&(this._connection=null,n=X.EMPTY)}return n}refCount(){return qn()(this)}};var th=Hn(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var $=(()=>{class e extends O{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(t){let r=new Si(this,this);return r.operator=t,r}_throwIfClosed(){if(this.closed)throw new th}next(t){zn(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(t)}})}error(t){zn(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=t;let{observers:r}=this;for(;r.length;)r.shift().error(t)}})}complete(){zn(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:t}=this;for(;t.length;)t.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var t;return((t=this.observers)===null||t===void 0?void 0:t.length)>0}_trySubscribe(t){return this._throwIfClosed(),super._trySubscribe(t)}_subscribe(t){return this._throwIfClosed(),this._checkFinalizedStatuses(t),this._innerSubscribe(t)}_innerSubscribe(t){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Sc:(this.currentObservers=null,i.push(t),new X(()=>{this.currentObservers=null,eo(i,t)}))}_checkFinalizedStatuses(t){let{hasError:r,thrownError:o,isStopped:i}=this;r?t.error(o):i&&t.complete()}asObservable(){let t=new O;return t.source=this,t}}return e.create=(n,t)=>new Si(n,t),e})(),Si=class extends ${constructor(n,t){super(),this.destination=n,this.source=t}next(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.next)===null||r===void 0||r.call(t,n)}error(n){var t,r;(r=(t=this.destination)===null||t===void 0?void 0:t.error)===null||r===void 0||r.call(t,n)}complete(){var n,t;(t=(n=this.destination)===null||n===void 0?void 0:n.complete)===null||t===void 0||t.call(n)}_subscribe(n){var t,r;return(r=(t=this.source)===null||t===void 0?void 0:t.subscribe(n))!==null&&r!==void 0?r:Sc}};var ee=class extends ${constructor(n){super(),this._value=n}get value(){return this.getValue()}_subscribe(n){let t=super._subscribe(n);return!t.closed&&n.next(this._value),t}getValue(){let{hasError:n,thrownError:t,_value:r}=this;if(n)throw t;return this._throwIfClosed(),r}next(n){super.next(this._value=n)}};var be=new O(e=>e.complete());function nh(e){return e&&T(e.schedule)}function rh(e){return e[e.length-1]}function Mi(e){return T(rh(e))?e.pop():void 0}function Vt(e){return nh(rh(e))?e.pop():void 0}function no(e,n,t,r){var o=arguments.length,i=o<3?n:r===null?r=Object.getOwnPropertyDescriptor(n,t):r,s;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")i=Reflect.decorate(e,n,t,r);else for(var a=e.length-1;a>=0;a--)(s=e[a])&&(i=(o<3?s(i):o>3?s(n,t,i):s(n,t))||i);return o>3&&i&&Object.defineProperty(n,t,i),i}function ih(e,n,t,r){function o(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function a(l){try{u(r.next(l))}catch(d){s(d)}}function c(l){try{u(r.throw(l))}catch(d){s(d)}}function u(l){l.done?i(l.value):o(l.value).then(a,c)}u((r=r.apply(e,n||[])).next())})}function oh(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")}function ln(e){return this instanceof ln?(this.v=e,this):new ln(e)}function sh(e,n,t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=t.apply(e,n||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(m){return Promise.resolve(m).then(f,d)}}function a(f,m){r[f]&&(o[f]=function(b){return new Promise(function(y,C){i.push([f,b,y,C])>1||c(f,b)})},m&&(o[f]=m(o[f])))}function c(f,m){try{u(r[f](m))}catch(b){h(i[0][3],b)}}function u(f){f.value instanceof ln?Promise.resolve(f.value.v).then(l,d):h(i[0][2],f)}function l(f){c("next",f)}function d(f){c("throw",f)}function h(f,m){f(m),i.shift(),i.length&&c(i[0][0],i[0][1])}}function ah(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n=e[Symbol.asyncIterator],t;return n?n.call(e):(e=typeof oh=="function"?oh(e):e[Symbol.iterator](),t={},r("next"),r("throw"),r("return"),t[Symbol.asyncIterator]=function(){return this},t);function r(i){t[i]=e[i]&&function(s){return new Promise(function(a,c){s=e[i](s),o(a,c,s.done,s.value)})}}function o(i,s,a,c){Promise.resolve(c).then(function(u){i({value:u,done:a})},s)}}var Yn=e=>e&&typeof e.length=="number"&&typeof e!="function";function Ai(e){return T(e?.then)}function Ni(e){return T(e[Wn])}function Ri(e){return Symbol.asyncIterator&&T(e?.[Symbol.asyncIterator])}function xi(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function UD(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var Oi=UD();function ki(e){return T(e?.[Oi])}function Pi(e){return sh(this,arguments,function*(){let t=e.getReader();try{for(;;){let{value:r,done:o}=yield ln(t.read());if(o)return yield ln(void 0);yield yield ln(r)}}finally{t.releaseLock()}})}function Fi(e){return T(e?.getReader)}function Q(e){if(e instanceof O)return e;if(e!=null){if(Ni(e))return HD(e);if(Yn(e))return $D(e);if(Ai(e))return zD(e);if(Ri(e))return ch(e);if(ki(e))return GD(e);if(Fi(e))return WD(e)}throw xi(e)}function HD(e){return new O(n=>{let t=e[Wn]();if(T(t.subscribe))return t.subscribe(n);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function $D(e){return new O(n=>{for(let t=0;t<e.length&&!n.closed;t++)n.next(e[t]);n.complete()})}function zD(e){return new O(n=>{e.then(t=>{n.closed||(n.next(t),n.complete())},t=>n.error(t)).then(null,_i)})}function GD(e){return new O(n=>{for(let t of e)if(n.next(t),n.closed)return;n.complete()})}function ch(e){return new O(n=>{qD(e,n).catch(t=>n.error(t))})}function WD(e){return ch(Pi(e))}function qD(e,n){var t,r,o,i;return ih(this,void 0,void 0,function*(){try{for(t=ah(e);r=yield t.next(),!r.done;){let s=r.value;if(n.next(s),n.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=t.return)&&(yield i.call(t))}finally{if(o)throw o.error}}n.complete()})}function _e(e,n,t,r=0,o=!1){let i=n.schedule(function(){t(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function Li(e,n=0){return L((t,r)=>{t.subscribe(k(r,o=>_e(r,e,()=>r.next(o),n),()=>_e(r,e,()=>r.complete(),n),o=>_e(r,e,()=>r.error(o),n)))})}function ji(e,n=0){return L((t,r)=>{r.add(e.schedule(()=>t.subscribe(r),n))})}function uh(e,n){return Q(e).pipe(ji(n),Li(n))}function lh(e,n){return Q(e).pipe(ji(n),Li(n))}function dh(e,n){return new O(t=>{let r=0;return n.schedule(function(){r===e.length?t.complete():(t.next(e[r++]),t.closed||this.schedule())})})}function fh(e,n){return new O(t=>{let r;return _e(t,n,()=>{r=e[Oi](),_e(t,n,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){t.error(s);return}i?t.complete():t.next(o)},0,!0)}),()=>T(r?.return)&&r.return()})}function Vi(e,n){if(!e)throw new Error("Iterable cannot be null");return new O(t=>{_e(t,n,()=>{let r=e[Symbol.asyncIterator]();_e(t,n,()=>{r.next().then(o=>{o.done?t.complete():t.next(o.value)})},0,!0)})})}function hh(e,n){return Vi(Pi(e),n)}function ph(e,n){if(e!=null){if(Ni(e))return uh(e,n);if(Yn(e))return dh(e,n);if(Ai(e))return lh(e,n);if(Ri(e))return Vi(e,n);if(ki(e))return fh(e,n);if(Fi(e))return hh(e,n)}throw xi(e)}function W(e,n){return n?ph(e,n):Q(e)}function _(...e){let n=Vt(e);return W(e,n)}function Qn(e,n){let t=T(e)?e:()=>e,r=o=>o.error(t());return new O(n?o=>n.schedule(r,0,o):r)}function Fc(e){return!!e&&(e instanceof O||T(e.lift)&&T(e.subscribe))}function R(e,n){return L((t,r)=>{let o=0;t.subscribe(k(r,i=>{r.next(e.call(n,i,o++))}))})}var{isArray:ZD}=Array;function YD(e,n){return ZD(n)?e(...n):e(n)}function Kn(e){return R(n=>YD(e,n))}var{isArray:QD}=Array,{getPrototypeOf:KD,prototype:JD,keys:XD}=Object;function Bi(e){if(e.length===1){let n=e[0];if(QD(n))return{args:n,keys:null};if(eE(n)){let t=XD(n);return{args:t.map(r=>n[r]),keys:t}}}return{args:e,keys:null}}function eE(e){return e&&typeof e=="object"&&KD(e)===JD}function Ui(e,n){return e.reduce((t,r,o)=>(t[r]=n[o],t),{})}function Jn(...e){let n=Vt(e),t=Mi(e),{args:r,keys:o}=Bi(e);if(r.length===0)return W([],n);let i=new O(tE(r,n,o?s=>Ui(o,s):ve));return t?i.pipe(Kn(t)):i}function tE(e,n,t=ve){return r=>{gh(n,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let c=0;c<o;c++)gh(n,()=>{let u=W(e[c],n),l=!1;u.subscribe(k(r,d=>{i[c]=d,l||(l=!0,a--),a||r.next(t(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function gh(e,n,t){e?_e(t,e,n):n()}function mh(e,n,t,r,o,i,s,a){let c=[],u=0,l=0,d=!1,h=()=>{d&&!c.length&&!u&&n.complete()},f=b=>u<r?m(b):c.push(b),m=b=>{i&&n.next(b),u++;let y=!1;Q(t(b,l++)).subscribe(k(n,C=>{o?.(C),i?f(C):n.next(C)},()=>{y=!0},void 0,()=>{if(y)try{for(u--;c.length&&u<r;){let C=c.shift();s?_e(n,s,()=>m(C)):m(C)}h()}catch(C){n.error(C)}}))};return e.subscribe(k(n,f,()=>{d=!0,h()})),()=>{a?.()}}function Y(e,n,t=1/0){return T(n)?Y((r,o)=>R((i,s)=>n(r,i,o,s))(Q(e(r,o))),t):(typeof n=="number"&&(t=n),L((r,o)=>mh(r,o,e,t)))}function Xn(e=1/0){return Y(ve,e)}function vh(){return Xn(1)}function er(...e){return vh()(W(e,Vt(e)))}function ro(e){return new O(n=>{Q(e()).subscribe(n)})}function Lc(...e){let n=Mi(e),{args:t,keys:r}=Bi(e),o=new O(i=>{let{length:s}=t;if(!s){i.complete();return}let a=new Array(s),c=s,u=s;for(let l=0;l<s;l++){let d=!1;Q(t[l]).subscribe(k(i,h=>{d||(d=!0,u--),a[l]=h},()=>c--,void 0,()=>{(!c||!d)&&(u||i.next(r?Ui(r,a):a),i.complete())}))}});return n?o.pipe(Kn(n)):o}var nE=["addListener","removeListener"],rE=["addEventListener","removeEventListener"],oE=["on","off"];function oo(e,n,t,r){if(T(t)&&(r=t,t=void 0),r)return oo(e,n,t).pipe(Kn(r));let[o,i]=aE(e)?rE.map(s=>a=>e[s](n,a,t)):iE(e)?nE.map(yh(e,n)):sE(e)?oE.map(yh(e,n)):[];if(!o&&Yn(e))return Y(s=>oo(s,n,t))(Q(e));if(!o)throw new TypeError("Invalid event target");return new O(s=>{let a=(...c)=>s.next(1<c.length?c:c[0]);return o(a),()=>i(a)})}function yh(e,n){return t=>r=>e[t](n,r)}function iE(e){return T(e.addListener)&&T(e.removeListener)}function sE(e){return T(e.on)&&T(e.off)}function aE(e){return T(e.addEventListener)&&T(e.removeEventListener)}function te(e,n){return L((t,r)=>{let o=0;t.subscribe(k(r,i=>e.call(n,i,o++)&&r.next(i)))})}function at(e){return L((n,t)=>{let r=null,o=!1,i;r=n.subscribe(k(t,void 0,void 0,s=>{i=Q(e(s,at(e)(n))),r?(r.unsubscribe(),r=null,i.subscribe(t)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(t))})}function Dh(e,n,t,r,o){return(i,s)=>{let a=t,c=n,u=0;i.subscribe(k(s,l=>{let d=u++;c=a?e(c,l,d):(a=!0,l),r&&s.next(c)},o&&(()=>{a&&s.next(c),s.complete()})))}}function ct(e,n){return T(n)?Y(e,n,1):Y(e,1)}function Bt(e){return L((n,t)=>{let r=!1;n.subscribe(k(t,o=>{r=!0,t.next(o)},()=>{r||t.next(e),t.complete()}))})}function wt(e){return e<=0?()=>be:L((n,t)=>{let r=0;n.subscribe(k(t,o=>{++r<=e&&(t.next(o),e<=r&&t.complete())}))})}function jc(e,n=ve){return e=e??cE,L((t,r)=>{let o,i=!0;t.subscribe(k(r,s=>{let a=n(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function cE(e,n){return e===n}function Hi(e=uE){return L((n,t)=>{let r=!1;n.subscribe(k(t,o=>{r=!0,t.next(o)},()=>r?t.complete():t.error(e())))})}function uE(){return new qe}function Ut(e){return L((n,t)=>{try{n.subscribe(t)}finally{t.add(e)}})}function It(e,n){let t=arguments.length>=2;return r=>r.pipe(e?te((o,i)=>e(o,i,r)):ve,wt(1),t?Bt(n):Hi(()=>new qe))}function tr(e){return e<=0?()=>be:L((n,t)=>{let r=[];n.subscribe(k(t,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)t.next(o);t.complete()},void 0,()=>{r=null}))})}function Vc(e,n){let t=arguments.length>=2;return r=>r.pipe(e?te((o,i)=>e(o,i,r)):ve,tr(1),t?Bt(n):Hi(()=>new qe))}function Bc(e,n){return L(Dh(e,n,arguments.length>=2,!0))}function Uc(...e){let n=Vt(e);return L((t,r)=>{(n?er(e,t,n):er(e,t)).subscribe(r)})}function ie(e,n){return L((t,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();t.subscribe(k(r,c=>{o?.unsubscribe();let u=0,l=i++;Q(e(c,l)).subscribe(o=k(r,d=>r.next(n?n(c,d,l,u++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function $i(e){return L((n,t)=>{Q(e).subscribe(k(t,()=>t.complete(),to)),!t.closed&&n.subscribe(t)})}function se(e,n,t){let r=T(e)||n||t?{next:e,error:n,complete:t}:e;return r?L((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(k(i,c=>{var u;(u=r.next)===null||u===void 0||u.call(r,c),i.next(c)},()=>{var c;a=!1,(c=r.complete)===null||c===void 0||c.call(r),i.complete()},c=>{var u;a=!1,(u=r.error)===null||u===void 0||u.call(r,c),i.error(c)},()=>{var c,u;a&&((c=r.unsubscribe)===null||c===void 0||c.call(r)),(u=r.finalize)===null||u===void 0||u.call(r)}))}):ve}var Hc;function zi(){return Hc}function ut(e){let n=Hc;return Hc=e,n}var Eh=Symbol("NotFound");function nr(e){return e===Eh||e?.name==="\u0275NotFound"}function Yi(e,n){return Object.is(e,n)}var ne=null,Gi=!1,$c=1,lE=null,ye=Symbol("SIGNAL");function x(e){let n=ne;return ne=e,n}function Qi(){return ne}var dn={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,kind:"unknown",producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function rr(e){if(Gi)throw new Error("");if(ne===null)return;ne.consumerOnSignalRead(e);let n=ne.nextProducerIndex++;if(es(ne),n<ne.producerNode.length&&ne.producerNode[n]!==e&&so(ne)){let t=ne.producerNode[n];Xi(t,ne.producerIndexOfThis[n])}ne.producerNode[n]!==e&&(ne.producerNode[n]=e,ne.producerIndexOfThis[n]=so(ne)?wh(e,ne,n):0),ne.producerLastReadVersion[n]=e.version}function Ch(){$c++}function Ki(e){if(!(so(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===$c)){if(!e.producerMustRecompute(e)&&!ao(e)){Zi(e);return}e.producerRecomputeValue(e),Zi(e)}}function zc(e){if(e.liveConsumerNode===void 0)return;let n=Gi;Gi=!0;try{for(let t of e.liveConsumerNode)t.dirty||dE(t)}finally{Gi=n}}function Gc(){return ne?.consumerAllowSignalWrites!==!1}function dE(e){e.dirty=!0,zc(e),e.consumerMarkedDirty?.(e)}function Zi(e){e.dirty=!1,e.lastCleanEpoch=$c}function fn(e){return e&&(e.nextProducerIndex=0),x(e)}function or(e,n){if(x(n),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(so(e))for(let t=e.nextProducerIndex;t<e.producerNode.length;t++)Xi(e.producerNode[t],e.producerIndexOfThis[t]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function ao(e){es(e);for(let n=0;n<e.producerNode.length;n++){let t=e.producerNode[n],r=e.producerLastReadVersion[n];if(r!==t.version||(Ki(t),r!==t.version))return!0}return!1}function Ji(e){if(es(e),so(e))for(let n=0;n<e.producerNode.length;n++)Xi(e.producerNode[n],e.producerIndexOfThis[n]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function wh(e,n,t){if(Ih(e),e.liveConsumerNode.length===0&&bh(e))for(let r=0;r<e.producerNode.length;r++)e.producerIndexOfThis[r]=wh(e.producerNode[r],e,r);return e.liveConsumerIndexOfThis.push(t),e.liveConsumerNode.push(n)-1}function Xi(e,n){if(Ih(e),e.liveConsumerNode.length===1&&bh(e))for(let r=0;r<e.producerNode.length;r++)Xi(e.producerNode[r],e.producerIndexOfThis[r]);let t=e.liveConsumerNode.length-1;if(e.liveConsumerNode[n]=e.liveConsumerNode[t],e.liveConsumerIndexOfThis[n]=e.liveConsumerIndexOfThis[t],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,n<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[n],o=e.liveConsumerNode[n];es(o),o.producerIndexOfThis[r]=n}}function so(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function es(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function Ih(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function bh(e){return e.producerNode!==void 0}function ts(e){lE?.(e)}function ns(e,n){let t=Object.create(fE);t.computation=e,n!==void 0&&(t.equal=n);let r=()=>{if(Ki(t),rr(t),t.value===io)throw t.error;return t.value};return r[ye]=t,ts(t),r}var Wi=Symbol("UNSET"),qi=Symbol("COMPUTING"),io=Symbol("ERRORED"),fE=N(g({},dn),{value:Wi,dirty:!0,error:null,equal:Yi,kind:"computed",producerMustRecompute(e){return e.value===Wi||e.value===qi},producerRecomputeValue(e){if(e.value===qi)throw new Error("");let n=e.value;e.value=qi;let t=fn(e),r,o=!1;try{r=e.computation(),x(null),o=n!==Wi&&n!==io&&r!==io&&e.equal(n,r)}catch(i){r=io,e.error=i}finally{or(e,t)}if(o){e.value=n;return}e.value=r,e.version++}});function hE(){throw new Error}var _h=hE;function Th(e){_h(e)}function Wc(e){_h=e}var pE=null;function qc(e,n){let t=Object.create(rs);t.value=e,n!==void 0&&(t.equal=n);let r=()=>Sh(t);return r[ye]=t,ts(t),[r,s=>ir(t,s),s=>Zc(t,s)]}function Sh(e){return rr(e),e.value}function ir(e,n){Gc()||Th(e),e.equal(e.value,n)||(e.value=n,gE(e))}function Zc(e,n){Gc()||Th(e),ir(e,n(e.value))}var rs=N(g({},dn),{equal:Yi,value:void 0,kind:"signal"});function gE(e){e.version++,Ch(),zc(e),pE?.(e)}function Mh(e){let n=x(null);try{return e()}finally{x(n)}}var cs="https://angular.dev/best-practices/security#preventing-cross-site-scripting-xss",D=class extends Error{code;constructor(n,t){super(ar(n,t)),this.code=n}};function mE(e){return`NG0${Math.abs(e)}`}function ar(e,n){return`${mE(e)}${n?": "+n:""}`}var ho=globalThis;function B(e){for(let n in e)if(e[n]===B)return n;throw Error("")}function Rh(e,n){for(let t in n)n.hasOwnProperty(t)&&!e.hasOwnProperty(t)&&(e[t]=n[t])}function _t(e){if(typeof e=="string")return e;if(Array.isArray(e))return`[${e.map(_t).join(", ")}]`;if(e==null)return""+e;let n=e.overriddenName||e.name;if(n)return`${n}`;let t=e.toString();if(t==null)return""+t;let r=t.indexOf(`
`);return r>=0?t.slice(0,r):t}function au(e,n){return e?n?`${e} ${n}`:e:n||""}var vE=B({__forward_ref__:B});function Te(e){return e.__forward_ref__=Te,e.toString=function(){return _t(this())},e}function de(e){return cu(e)?e():e}function cu(e){return typeof e=="function"&&e.hasOwnProperty(vE)&&e.__forward_ref__===Te}function xh(e,n){e==null&&uu(n,e,null,"!=")}function uu(e,n,t,r){throw new Error(`ASSERTION ERROR: ${e}`+(r==null?"":` [Expected=> ${t} ${r} ${n} <=Actual]`))}function w(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function ke(e){return{providers:e.providers||[],imports:e.imports||[]}}function po(e){return yE(e,us)}function lu(e){return po(e)!==null}function yE(e,n){return e.hasOwnProperty(n)&&e[n]||null}function DE(e){let n=e?.[us]??null;return n||null}function Qc(e){return e&&e.hasOwnProperty(is)?e[is]:null}var us=B({\u0275prov:B}),is=B({\u0275inj:B}),E=class{_desc;ngMetadataName="InjectionToken";\u0275prov;constructor(n,t){this._desc=n,this.\u0275prov=void 0,typeof t=="number"?this.__NG_ELEMENT_ID__=t:t!==void 0&&(this.\u0275prov=w({token:this,providedIn:t.providedIn||"root",factory:t.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function du(e){return e&&!!e.\u0275providers}var fu=B({\u0275cmp:B}),hu=B({\u0275dir:B}),pu=B({\u0275pipe:B}),gu=B({\u0275mod:B}),lo=B({\u0275fac:B}),yn=B({__NG_ELEMENT_ID__:B}),Ah=B({__NG_ENV_ID__:B});function go(e){return typeof e=="string"?e:e==null?"":String(e)}function ss(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():go(e)}var mu=B({ngErrorCode:B}),Oh=B({ngErrorMessage:B}),uo=B({ngTokenPath:B});function vu(e,n){return kh("",-200,n)}function ls(e,n){throw new D(-201,!1)}function EE(e,n){e[uo]??=[];let t=e[uo],r;typeof n=="object"&&"multi"in n&&n?.multi===!0?(xh(n.provide,"Token with multi: true should have a provide property"),r=ss(n.provide)):r=ss(n),t[0]!==r&&e[uo].unshift(r)}function CE(e,n){let t=e[uo],r=e[mu],o=e[Oh]||e.message;return e.message=IE(o,r,t,n),e}function kh(e,n,t){let r=new D(n,e);return r[mu]=n,r[Oh]=e,t&&(r[uo]=t),r}function wE(e){return e[mu]}function IE(e,n,t=[],r=null){let o="";t&&t.length>1&&(o=` Path: ${t.join(" -> ")}.`);let i=r?` Source: ${r}.`:"";return ar(n,`${e}${i}${o}`)}var Kc;function Ph(){return Kc}function xe(e){let n=Kc;return Kc=e,n}function yu(e,n,t){let r=po(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(t&8)return null;if(n!==void 0)return n;ls(e,"Injector")}var bE={},hn=bE,Jc="__NG_DI_FLAG__",Xc=class{injector;constructor(n){this.injector=n}retrieve(n,t){let r=pn(t)||0;try{return this.injector.get(n,r&8?null:hn,r)}catch(o){if(nr(o))return o;throw o}}};function _E(e,n=0){let t=zi();if(t===void 0)throw new D(-203,!1);if(t===null)return yu(e,void 0,n);{let r=TE(n),o=t.retrieve(e,r);if(nr(o)){if(r.optional)return null;throw o}return o}}function I(e,n=0){return(Ph()||_E)(de(e),n)}function p(e,n){return I(e,pn(n))}function pn(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function TE(e){return{optional:!!(e&8),host:!!(e&1),self:!!(e&2),skipSelf:!!(e&4)}}function eu(e){let n=[];for(let t=0;t<e.length;t++){let r=de(e[t]);if(Array.isArray(r)){if(r.length===0)throw new D(900,!1);let o,i=0;for(let s=0;s<r.length;s++){let a=r[s],c=SE(a);typeof c=="number"?c===-1?o=a.token:i|=c:o=a}n.push(I(o,i))}else n.push(I(r))}return n}function Du(e,n){return e[Jc]=n,e.prototype[Jc]=n,e}function SE(e){return e[Jc]}function gn(e,n){let t=e.hasOwnProperty(lo);return t?e[lo]:null}function Fh(e,n,t){if(e.length!==n.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=n[r];if(t&&(o=t(o),i=t(i)),i!==o)return!1}return!0}function Lh(e){return e.flat(Number.POSITIVE_INFINITY)}function ds(e,n){e.forEach(t=>Array.isArray(t)?ds(t,n):n(t))}function Eu(e,n,t){n>=e.length?e.push(t):e.splice(n,0,t)}function mo(e,n){return n>=e.length-1?e.pop():e.splice(n,1)[0]}function jh(e,n){let t=[];for(let r=0;r<e;r++)t.push(n);return t}function Vh(e,n,t,r){let o=e.length;if(o==n)e.push(t,r);else if(o===1)e.push(r,e[0]),e[0]=t;else{for(o--,e.push(e[o-1],e[o]);o>n;){let i=o-2;e[o]=e[i],o--}e[n]=t,e[n+1]=r}}function Bh(e,n,t){let r=cr(e,n);return r>=0?e[r|1]=t:(r=~r,Vh(e,r,n,t)),r}function fs(e,n){let t=cr(e,n);if(t>=0)return e[t|1]}function cr(e,n){return ME(e,n,1)}function ME(e,n,t){let r=0,o=e.length>>t;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<t];if(n===s)return i<<t;s>n?o=i:r=i+1}return~(o<<t)}var $t={},Oe=[],zt=new E(""),Cu=new E("",-1),wu=new E(""),fo=class{get(n,t=hn){if(t===hn){let o=kh("",-201);throw o.name="\u0275NotFound",o}return t}};function Iu(e){return e[gu]||null}function lt(e){return e[fu]||null}function bu(e){return e[hu]||null}function Uh(e){return e[pu]||null}function ur(e){return{\u0275providers:e}}function Hh(...e){return{\u0275providers:_u(!0,e),\u0275fromNgModule:!0}}function _u(e,...n){let t=[],r=new Set,o,i=s=>{t.push(s)};return ds(n,s=>{let a=s;as(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&$h(o,i),t}function $h(e,n){for(let t=0;t<e.length;t++){let{ngModule:r,providers:o}=e[t];Tu(o,i=>{n(i,r)})}}function as(e,n,t,r){if(e=de(e),!e)return!1;let o=null,i=Qc(e),s=!i&&lt(e);if(!i&&!s){let c=e.ngModule;if(i=Qc(c),i)o=c;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let c=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let u of c)as(u,n,t,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let u;try{ds(i.imports,l=>{as(l,n,t,r)&&(u||=[],u.push(l))})}finally{}u!==void 0&&$h(u,n)}if(!a){let u=gn(o)||(()=>new o);n({provide:o,useFactory:u,deps:Oe},o),n({provide:wu,useValue:o,multi:!0},o),n({provide:zt,useValue:()=>I(o),multi:!0},o)}let c=i.providers;if(c!=null&&!a){let u=e;Tu(c,l=>{n(l,u)})}}else return!1;return o!==e&&e.providers!==void 0}function Tu(e,n){for(let t of e)du(t)&&(t=t.\u0275providers),Array.isArray(t)?Tu(t,n):n(t)}var AE=B({provide:String,useValue:B});function zh(e){return e!==null&&typeof e=="object"&&AE in e}function NE(e){return!!(e&&e.useExisting)}function RE(e){return!!(e&&e.useFactory)}function mn(e){return typeof e=="function"}function Gh(e){return!!e.useClass}var vo=new E(""),os={},Nh={},Yc;function lr(){return Yc===void 0&&(Yc=new fo),Yc}var G=class{},vn=class extends G{parent;source;scopes;records=new Map;_ngOnDestroyHooks=new Set;_onDestroyHooks=[];get destroyed(){return this._destroyed}_destroyed=!1;injectorDefTypes;constructor(n,t,r,o){super(),this.parent=t,this.source=r,this.scopes=o,nu(n,s=>this.processProvider(s)),this.records.set(Cu,sr(void 0,this)),o.has("environment")&&this.records.set(G,sr(void 0,this));let i=this.records.get(vo);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(wu,Oe,{self:!0}))}retrieve(n,t){let r=pn(t)||0;try{return this.get(n,hn,r)}catch(o){if(nr(o))return o;throw o}}destroy(){co(this),this._destroyed=!0;let n=x(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let t=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of t)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),x(n)}}onDestroy(n){return co(this),this._onDestroyHooks.push(n),()=>this.removeOnDestroy(n)}runInContext(n){co(this);let t=ut(this),r=xe(void 0),o;try{return n()}finally{ut(t),xe(r)}}get(n,t=hn,r){if(co(this),n.hasOwnProperty(Ah))return n[Ah](this);let o=pn(r),i,s=ut(this),a=xe(void 0);try{if(!(o&4)){let u=this.records.get(n);if(u===void 0){let l=FE(n)&&po(n);l&&this.injectableDefInScope(l)?u=sr(tu(n),os):u=null,this.records.set(n,u)}if(u!=null)return this.hydrate(n,u,o)}let c=o&2?lr():this.parent;return t=o&8&&t===hn?null:t,c.get(n,t)}catch(c){let u=wE(c);throw u===-200||u===-201?new D(u,null):c}finally{xe(a),ut(s)}}resolveInjectorInitializers(){let n=x(null),t=ut(this),r=xe(void 0),o;try{let i=this.get(zt,Oe,{self:!0});for(let s of i)s()}finally{ut(t),xe(r),x(n)}}toString(){let n=[],t=this.records;for(let r of t.keys())n.push(_t(r));return`R3Injector[${n.join(", ")}]`}processProvider(n){n=de(n);let t=mn(n)?n:de(n&&n.provide),r=OE(n);if(!mn(n)&&n.multi===!0){let o=this.records.get(t);o||(o=sr(void 0,os,!0),o.factory=()=>eu(o.multi),this.records.set(t,o)),t=n,o.multi.push(n)}this.records.set(t,r)}hydrate(n,t,r){let o=x(null);try{if(t.value===Nh)throw vu(_t(n));return t.value===os&&(t.value=Nh,t.value=t.factory(void 0,r)),typeof t.value=="object"&&t.value&&PE(t.value)&&this._ngOnDestroyHooks.add(t.value),t.value}finally{x(o)}}injectableDefInScope(n){if(!n.providedIn)return!1;let t=de(n.providedIn);return typeof t=="string"?t==="any"||this.scopes.has(t):this.injectorDefTypes.has(t)}removeOnDestroy(n){let t=this._onDestroyHooks.indexOf(n);t!==-1&&this._onDestroyHooks.splice(t,1)}};function tu(e){let n=po(e),t=n!==null?n.factory:gn(e);if(t!==null)return t;if(e instanceof E)throw new D(204,!1);if(e instanceof Function)return xE(e);throw new D(204,!1)}function xE(e){if(e.length>0)throw new D(204,!1);let t=DE(e);return t!==null?()=>t.factory(e):()=>new e}function OE(e){if(zh(e))return sr(void 0,e.useValue);{let n=Su(e);return sr(n,os)}}function Su(e,n,t){let r;if(mn(e)){let o=de(e);return gn(o)||tu(o)}else if(zh(e))r=()=>de(e.useValue);else if(RE(e))r=()=>e.useFactory(...eu(e.deps||[]));else if(NE(e))r=(o,i)=>I(de(e.useExisting),i!==void 0&&i&8?8:void 0);else{let o=de(e&&(e.useClass||e.provide));if(kE(e))r=()=>new o(...eu(e.deps));else return gn(o)||tu(o)}return r}function co(e){if(e.destroyed)throw new D(205,!1)}function sr(e,n,t=!1){return{factory:e,value:n,multi:t?[]:void 0}}function kE(e){return!!e.deps}function PE(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function FE(e){return typeof e=="function"||typeof e=="object"&&e.ngMetadataName==="InjectionToken"}function nu(e,n){for(let t of e)Array.isArray(t)?nu(t,n):t&&du(t)?nu(t.\u0275providers,n):n(t)}function he(e,n){let t;e instanceof vn?(co(e),t=e):t=new Xc(e);let r,o=ut(t),i=xe(void 0);try{return n()}finally{ut(o),xe(i)}}function Wh(){return Ph()!==void 0||zi()!=null}var Qe=0,S=1,M=2,ae=3,Ve=4,De=5,dr=6,fr=7,pe=8,Dn=9,dt=10,K=11,hr=12,Mu=13,En=14,Se=15,Gt=16,Cn=17,ft=18,yo=19,Au=20,bt=21,hs=22,Do=23,Pe=24,wn=25,re=26,qh=1;var Wt=7,Eo=8,In=9,Ee=10;function ht(e){return Array.isArray(e)&&typeof e[qh]=="object"}function Ke(e){return Array.isArray(e)&&e[qh]===!0}function Nu(e){return(e.flags&4)!==0}function qt(e){return e.componentOffset>-1}function pr(e){return(e.flags&1)===1}function pt(e){return!!e.template}function gr(e){return(e[M]&512)!==0}function bn(e){return(e[M]&256)===256}var Zh="svg",Yh="math";function Be(e){for(;Array.isArray(e);)e=e[Qe];return e}function Ru(e,n){return Be(n[e])}function Je(e,n){return Be(n[e.index])}function ps(e,n){return e.data[n]}function Qh(e,n){return e[n]}function Ue(e,n){let t=n[e];return ht(t)?t:t[Qe]}function Kh(e){return(e[M]&4)===4}function gs(e){return(e[M]&128)===128}function Jh(e){return Ke(e[ae])}function Zt(e,n){return n==null?null:e[n]}function xu(e){e[Cn]=0}function Ou(e){e[M]&1024||(e[M]|=1024,gs(e)&&mr(e))}function Xh(e,n){for(;e>0;)n=n[En],e--;return n}function Co(e){return!!(e[M]&9216||e[Pe]?.dirty)}function ms(e){e[dt].changeDetectionScheduler?.notify(8),e[M]&64&&(e[M]|=1024),Co(e)&&mr(e)}function mr(e){e[dt].changeDetectionScheduler?.notify(0);let n=Ht(e);for(;n!==null&&!(n[M]&8192||(n[M]|=8192,!gs(n)));)n=Ht(n)}function ku(e,n){if(bn(e))throw new D(911,!1);e[bt]===null&&(e[bt]=[]),e[bt].push(n)}function ep(e,n){if(e[bt]===null)return;let t=e[bt].indexOf(n);t!==-1&&e[bt].splice(t,1)}function Ht(e){let n=e[ae];return Ke(n)?n[ae]:n}function Pu(e){return e[fr]??=[]}function Fu(e){return e.cleanup??=[]}function tp(e,n,t,r){let o=Pu(n);o.push(t),e.firstCreatePass&&Fu(e).push(r,o.length-1)}var P={lFrame:yp(null),bindingsEnabled:!0,skipHydrationRootTNode:null},wo=function(e){return e[e.Off=0]="Off",e[e.Exhaustive=1]="Exhaustive",e[e.OnlyDirtyViews=2]="OnlyDirtyViews",e}(wo||{}),LE=0,ru=!1;function np(){return P.lFrame.elementDepthCount}function rp(){P.lFrame.elementDepthCount++}function op(){P.lFrame.elementDepthCount--}function vs(){return P.bindingsEnabled}function Lu(){return P.skipHydrationRootTNode!==null}function ip(e){return P.skipHydrationRootTNode===e}function sp(){P.skipHydrationRootTNode=null}function j(){return P.lFrame.lView}function ge(){return P.lFrame.tView}function ap(e){return P.lFrame.contextLView=e,e[pe]}function cp(e){return P.lFrame.contextLView=null,e}function ce(){let e=ju();for(;e!==null&&e.type===64;)e=e.parent;return e}function ju(){return P.lFrame.currentTNode}function up(){let e=P.lFrame,n=e.currentTNode;return e.isParent?n:n.parent}function vr(e,n){let t=P.lFrame;t.currentTNode=e,t.isParent=n}function Vu(){return P.lFrame.isParent}function Bu(){P.lFrame.isParent=!1}function lp(){return P.lFrame.contextLView}function Uu(e){uu("Must never be called in production mode"),LE=e}function Hu(){return ru}function $u(e){let n=ru;return ru=e,n}function dp(e){return P.lFrame.bindingIndex=e}function ys(){return P.lFrame.bindingIndex++}function fp(e){let n=P.lFrame,t=n.bindingIndex;return n.bindingIndex=n.bindingIndex+e,t}function hp(){return P.lFrame.inI18n}function pp(e,n){let t=P.lFrame;t.bindingIndex=t.bindingRootIndex=e,Ds(n)}function gp(){return P.lFrame.currentDirectiveIndex}function Ds(e){P.lFrame.currentDirectiveIndex=e}function mp(e){let n=P.lFrame.currentDirectiveIndex;return n===-1?null:e[n]}function zu(){return P.lFrame.currentQueryIndex}function Es(e){P.lFrame.currentQueryIndex=e}function jE(e){let n=e[S];return n.type===2?n.declTNode:n.type===1?e[De]:null}function Gu(e,n,t){if(t&4){let o=n,i=e;for(;o=o.parent,o===null&&!(t&1);)if(o=jE(i),o===null||(i=i[En],o.type&10))break;if(o===null)return!1;n=o,e=i}let r=P.lFrame=vp();return r.currentTNode=n,r.lView=e,!0}function Cs(e){let n=vp(),t=e[S];P.lFrame=n,n.currentTNode=t.firstChild,n.lView=e,n.tView=t,n.contextLView=e,n.bindingIndex=t.bindingStartIndex,n.inI18n=!1}function vp(){let e=P.lFrame,n=e===null?null:e.child;return n===null?yp(e):n}function yp(e){let n={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=n),n}function Dp(){let e=P.lFrame;return P.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var Wu=Dp;function ws(){let e=Dp();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Ep(e){return(P.lFrame.contextLView=Xh(e,P.lFrame.contextLView))[pe]}function _n(){return P.lFrame.selectedIndex}function Yt(e){P.lFrame.selectedIndex=e}function qu(){let e=P.lFrame;return ps(e.tView,e.selectedIndex)}function Cp(){return P.lFrame.currentNamespace}var wp=!0;function Is(){return wp}function Io(e){wp=e}function ou(e,n=null,t=null,r){let o=Zu(e,n,t,r);return o.resolveInjectorInitializers(),o}function Zu(e,n=null,t=null,r,o=new Set){let i=[t||Oe,Hh(e)];return r=r||(typeof e=="object"?void 0:_t(e)),new vn(i,n||lr(),r||null,o)}var fe=class e{static THROW_IF_NOT_FOUND=hn;static NULL=new fo;static create(n,t){if(Array.isArray(n))return ou({name:""},t,n,"");{let r=n.name??"";return ou({name:r},n.parent,n.providers,r)}}static \u0275prov=w({token:e,providedIn:"any",factory:()=>I(Cu)});static __NG_ELEMENT_ID__=-1},J=new E(""),Xe=(()=>{class e{static __NG_ELEMENT_ID__=VE;static __NG_ENV_ID__=t=>t}return e})(),iu=class extends Xe{_lView;constructor(n){super(),this._lView=n}get destroyed(){return bn(this._lView)}onDestroy(n){let t=this._lView;return ku(t,n),()=>ep(t,n)}};function VE(){return new iu(j())}var Ye=class{_console=console;handleError(n){this._console.error("ERROR",n)}},Me=new E("",{providedIn:"root",factory:()=>{let e=p(G),n;return t=>{e.destroyed&&!n?setTimeout(()=>{throw t}):(n??=e.get(Ye),n.handleError(t))}}}),Ip={provide:zt,useValue:()=>void p(Ye),multi:!0};function St(e,n){let[t,r,o]=qc(e,n?.equal),i=t,s=i[ye];return i.set=r,i.update=o,i.asReadonly=bp.bind(i),i}function bp(){let e=this[ye];if(e.readonlyFn===void 0){let n=()=>this();n[ye]=e,e.readonlyFn=n}return e.readonlyFn}var Tt=class{},bs=new E("",{providedIn:"root",factory:()=>!1});var Yu=new E(""),Qu=new E("");var _s=(()=>{class e{view;node;constructor(t,r){this.view=t,this.node=r}static __NG_ELEMENT_ID__=BE}return e})();function BE(){return new _s(j(),ce())}var gt=(()=>{class e{taskId=0;pendingTasks=new Set;destroyed=!1;pendingTask=new ee(!1);get hasPendingTasks(){return this.destroyed?!1:this.pendingTask.value}get hasPendingTasksObservable(){return this.destroyed?new O(t=>{t.next(!1),t.complete()}):this.pendingTask}add(){!this.hasPendingTasks&&!this.destroyed&&this.pendingTask.next(!0);let t=this.taskId++;return this.pendingTasks.add(t),t}has(t){return this.pendingTasks.has(t)}remove(t){this.pendingTasks.delete(t),this.pendingTasks.size===0&&this.hasPendingTasks&&this.pendingTask.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this.hasPendingTasks&&this.pendingTask.next(!1),this.destroyed=!0,this.pendingTask.unsubscribe()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),bo=(()=>{class e{internalPendingTasks=p(gt);scheduler=p(Tt);errorHandler=p(Me);add(){let t=this.internalPendingTasks.add();return()=>{this.internalPendingTasks.has(t)&&(this.scheduler.notify(11),this.internalPendingTasks.remove(t))}}run(t){let r=this.add();t().catch(this.errorHandler).finally(r)}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})();function _o(...e){}var Ku=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>new su})}return e})(),su=class{dirtyEffectCount=0;queues=new Map;add(n){this.enqueue(n),this.schedule(n)}schedule(n){n.dirty&&this.dirtyEffectCount++}remove(n){let t=n.zone,r=this.queues.get(t);r.has(n)&&(r.delete(n),n.dirty&&this.dirtyEffectCount--)}enqueue(n){let t=n.zone;this.queues.has(t)||this.queues.set(t,new Set);let r=this.queues.get(t);r.has(n)||r.add(n)}flush(){for(;this.dirtyEffectCount>0;){let n=!1;for(let[t,r]of this.queues)t===null?n||=this.flushQueue(r):n||=t.run(()=>this.flushQueue(r));n||(this.dirtyEffectCount=0)}}flushQueue(n){let t=!1;for(let r of n)r.dirty&&(this.dirtyEffectCount--,t=!0,r.run());return t}};function Ir(e){return{toString:e}.toString()}var Ts="__parameters__";function QE(e){return function(...t){if(e){let r=e(...t);for(let o in r)this[o]=r[o]}}}function Xp(e,n,t){return Ir(()=>{let r=QE(n);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(c,u,l){let d=c.hasOwnProperty(Ts)?c[Ts]:Object.defineProperty(c,Ts,{value:[]})[Ts];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),c}}return o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var eg=Du(Xp("Optional"),8);var tg=Du(Xp("SkipSelf"),4);function KE(e){return typeof e=="function"}var Os=class{previousValue;currentValue;firstChange;constructor(n,t,r){this.previousValue=n,this.currentValue=t,this.firstChange=r}isFirstChange(){return this.firstChange}};function ng(e,n,t,r){n!==null?n.applyValueToInputSignal(n,r):e[t]=r}var Ae=(()=>{let e=()=>rg;return e.ngInherit=!0,e})();function rg(e){return e.type.prototype.ngOnChanges&&(e.setInput=XE),JE}function JE(){let e=ig(this),n=e?.current;if(n){let t=e.previous;if(t===$t)e.previous=n;else for(let r in n)t[r]=n[r];e.current=null,this.ngOnChanges(n)}}function XE(e,n,t,r,o){let i=this.declaredInputs[r],s=ig(e)||eC(e,{previous:$t,current:null}),a=s.current||(s.current={}),c=s.previous,u=c[i];a[i]=new Os(u&&u.currentValue,t,c===$t),ng(e,n,o,t)}var og="__ngSimpleChanges__";function ig(e){return e[og]||null}function eC(e,n){return e[og]=n}var _p=[];var U=function(e,n=null,t){for(let r=0;r<_p.length;r++){let o=_p[r];o(e,n,t)}};function tC(e,n,t){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=n.type.prototype;if(r){let s=rg(n);(t.preOrderHooks??=[]).push(e,s),(t.preOrderCheckHooks??=[]).push(e,s)}o&&(t.preOrderHooks??=[]).push(0-e,o),i&&((t.preOrderHooks??=[]).push(e,i),(t.preOrderCheckHooks??=[]).push(e,i))}function sg(e,n){for(let t=n.directiveStart,r=n.directiveEnd;t<r;t++){let i=e.data[t].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:c,ngAfterViewChecked:u,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-t,s),a&&((e.contentHooks??=[]).push(t,a),(e.contentCheckHooks??=[]).push(t,a)),c&&(e.viewHooks??=[]).push(-t,c),u&&((e.viewHooks??=[]).push(t,u),(e.viewCheckHooks??=[]).push(t,u)),l!=null&&(e.destroyHooks??=[]).push(t,l)}}function As(e,n,t){ag(e,n,3,t)}function Ns(e,n,t,r){(e[M]&3)===t&&ag(e,n,t,r)}function Ju(e,n){let t=e[M];(t&3)===n&&(t&=16383,t+=1,e[M]=t)}function ag(e,n,t,r){let o=r!==void 0?e[Cn]&65535:0,i=r??-1,s=n.length-1,a=0;for(let c=o;c<s;c++)if(typeof n[c+1]=="number"){if(a=n[c],r!=null&&a>=r)break}else n[c]<0&&(e[Cn]+=65536),(a<i||i==-1)&&(nC(e,t,n,c),e[Cn]=(e[Cn]&**********)+c+2),c++}function Tp(e,n){U(4,e,n);let t=x(null);try{n.call(e)}finally{x(t),U(5,e,n)}}function nC(e,n,t,r){let o=t[r]<0,i=t[r+1],s=o?-t[r]:t[r],a=e[s];o?e[M]>>14<e[Cn]>>16&&(e[M]&3)===n&&(e[M]+=16384,Tp(a,i)):Tp(a,i)}var Dr=-1,Sn=class{factory;name;injectImpl;resolving=!1;canSeeViewProviders;multi;componentProviders;index;providerFactory;constructor(n,t,r,o){this.factory=n,this.name=o,this.canSeeViewProviders=t,this.injectImpl=r}};function rC(e){return(e.flags&8)!==0}function oC(e){return(e.flags&16)!==0}function iC(e,n,t){let r=0;for(;r<t.length;){let o=t[r];if(typeof o=="number"){if(o!==0)break;r++;let i=t[r++],s=t[r++],a=t[r++];e.setAttribute(n,s,a,i)}else{let i=o,s=t[++r];sC(i)?e.setProperty(n,i,s):e.setAttribute(n,i,s),r++}}return r}function cg(e){return e===3||e===4||e===6}function sC(e){return e.charCodeAt(0)===64}function Er(e,n){if(!(n===null||n.length===0))if(e===null||e.length===0)e=n.slice();else{let t=-1;for(let r=0;r<n.length;r++){let o=n[r];typeof o=="number"?t=o:t===0||(t===-1||t===2?Sp(e,t,o,null,n[++r]):Sp(e,t,o,null,null))}}return e}function Sp(e,n,t,r,o){let i=0,s=e.length;if(n===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===n){s=-1;break}else if(a>n){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===t){o!==null&&(e[i+1]=o);return}i++,o!==null&&i++}s!==-1&&(e.splice(s,0,n),i=s+1),e.splice(i++,0,t),o!==null&&e.splice(i++,0,o)}function ug(e){return e!==Dr}function ks(e){return e&32767}function aC(e){return e>>16}function Ps(e,n){let t=aC(e),r=n;for(;t>0;)r=r[En],t--;return r}var cl=!0;function Mp(e){let n=cl;return cl=e,n}var cC=256,lg=cC-1,dg=5,uC=0,mt={};function lC(e,n,t){let r;typeof t=="string"?r=t.charCodeAt(0)||0:t.hasOwnProperty(yn)&&(r=t[yn]),r==null&&(r=t[yn]=uC++);let o=r&lg,i=1<<o;n.data[e+(o>>dg)]|=i}function Fs(e,n){let t=fg(e,n);if(t!==-1)return t;let r=n[S];r.firstCreatePass&&(e.injectorIndex=n.length,Xu(r.data,e),Xu(n,null),Xu(r.blueprint,null));let o=kl(e,n),i=e.injectorIndex;if(ug(o)){let s=ks(o),a=Ps(o,n),c=a[S].data;for(let u=0;u<8;u++)n[i+u]=a[s+u]|c[s+u]}return n[i+8]=o,i}function Xu(e,n){e.push(0,0,0,0,0,0,0,0,n)}function fg(e,n){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||n[e.injectorIndex+8]===null?-1:e.injectorIndex}function kl(e,n){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let t=0,r=null,o=n;for(;o!==null;){if(r=vg(o),r===null)return Dr;if(t++,o=o[En],r.injectorIndex!==-1)return r.injectorIndex|t<<16}return Dr}function ul(e,n,t){lC(e,n,t)}function dC(e,n){if(n==="class")return e.classes;if(n==="style")return e.styles;let t=e.attrs;if(t){let r=t.length,o=0;for(;o<r;){let i=t[o];if(cg(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof t[o]=="string";)o++;else{if(i===n)return t[o+1];o=o+2}}}return null}function hg(e,n,t){if(t&8||e!==void 0)return e;ls(n,"NodeInjector")}function pg(e,n,t,r){if(t&8&&r===void 0&&(r=null),(t&3)===0){let o=e[Dn],i=xe(void 0);try{return o?o.get(n,r,t&8):yu(n,r,t&8)}finally{xe(i)}}return hg(r,n,t)}function gg(e,n,t,r=0,o){if(e!==null){if(n[M]&2048&&!(r&2)){let s=gC(e,n,t,r,mt);if(s!==mt)return s}let i=mg(e,n,t,r,mt);if(i!==mt)return i}return pg(n,t,r,o)}function mg(e,n,t,r,o){let i=hC(t);if(typeof i=="function"){if(!Gu(n,e,r))return r&1?hg(o,t,r):pg(n,t,r,o);try{let s;if(s=i(r),s==null&&!(r&8))ls(t);else return s}finally{Wu()}}else if(typeof i=="number"){let s=null,a=fg(e,n),c=Dr,u=r&1?n[Se][De]:null;for((a===-1||r&4)&&(c=a===-1?kl(e,n):n[a+8],c===Dr||!Np(r,!1)?a=-1:(s=n[S],a=ks(c),n=Ps(c,n)));a!==-1;){let l=n[S];if(Ap(i,a,l.data)){let d=fC(a,n,t,s,r,u);if(d!==mt)return d}c=n[a+8],c!==Dr&&Np(r,n[S].data[a+8]===u)&&Ap(i,a,n)?(s=l,a=ks(c),n=Ps(c,n)):a=-1}}return o}function fC(e,n,t,r,o,i){let s=n[S],a=s.data[e+8],c=r==null?qt(a)&&cl:r!=s&&(a.type&3)!==0,u=o&1&&i===a,l=Rs(a,s,t,c,u);return l!==null?Mo(n,s,l,a,o):mt}function Rs(e,n,t,r,o){let i=e.providerIndexes,s=n.data,a=i&1048575,c=e.directiveStart,u=e.directiveEnd,l=i>>20,d=r?a:a+l,h=o?a+l:u;for(let f=d;f<h;f++){let m=s[f];if(f<c&&t===m||f>=c&&m.type===t)return f}if(o){let f=s[c];if(f&&pt(f)&&f.type===t)return c}return null}function Mo(e,n,t,r,o){let i=e[t],s=n.data;if(i instanceof Sn){let a=i;if(a.resolving){let f=ss(s[t]);throw vu(f)}let c=Mp(a.canSeeViewProviders);a.resolving=!0;let u=s[t].type||s[t],l,d=a.injectImpl?xe(a.injectImpl):null,h=Gu(e,r,0);try{i=e[t]=a.factory(void 0,o,s,e,r),n.firstCreatePass&&t>=r.directiveStart&&tC(t,s[t],n)}finally{d!==null&&xe(d),Mp(c),a.resolving=!1,Wu()}}return i}function hC(e){if(typeof e=="string")return e.charCodeAt(0)||0;let n=e.hasOwnProperty(yn)?e[yn]:void 0;return typeof n=="number"?n>=0?n&lg:pC:n}function Ap(e,n,t){let r=1<<e;return!!(t[n+(e>>dg)]&r)}function Np(e,n){return!(e&2)&&!(e&1&&n)}var Tn=class{_tNode;_lView;constructor(n,t){this._tNode=n,this._lView=t}get(n,t,r){return gg(this._tNode,this._lView,n,pn(r),t)}};function pC(){return new Tn(ce(),j())}function He(e){return Ir(()=>{let n=e.prototype.constructor,t=n[lo]||ll(n),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[lo]||ll(o);if(i&&i!==t)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function ll(e){return cu(e)?()=>{let n=ll(de(e));return n&&n()}:gn(e)}function gC(e,n,t,r,o){let i=e,s=n;for(;i!==null&&s!==null&&s[M]&2048&&!gr(s);){let a=mg(i,s,t,r|2,mt);if(a!==mt)return a;let c=i.parent;if(!c){let u=s[Au];if(u){let l=u.get(t,mt,r);if(l!==mt)return l}c=vg(s),s=s[En]}i=c}return o}function vg(e){let n=e[S],t=n.type;return t===2?n.declTNode:t===1?e[De]:null}function Jt(e){return dC(ce(),e)}function mC(){return br(ce(),j())}function br(e,n){return new q(Je(e,n))}var q=(()=>{class e{nativeElement;constructor(t){this.nativeElement=t}static __NG_ELEMENT_ID__=mC}return e})();function vC(e){return e instanceof q?e.nativeElement:e}function yC(){return this._results[Symbol.iterator]()}var Ls=class{_emitDistinctChangesOnly;dirty=!0;_onDirty=void 0;_results=[];_changesDetected=!1;_changes=void 0;length=0;first=void 0;last=void 0;get changes(){return this._changes??=new $}constructor(n=!1){this._emitDistinctChangesOnly=n}get(n){return this._results[n]}map(n){return this._results.map(n)}filter(n){return this._results.filter(n)}find(n){return this._results.find(n)}reduce(n,t){return this._results.reduce(n,t)}forEach(n){this._results.forEach(n)}some(n){return this._results.some(n)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(n,t){this.dirty=!1;let r=Lh(n);(this._changesDetected=!Fh(this._results,r,t))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.next(this)}onDirty(n){this._onDirty=n}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}[Symbol.iterator]=yC};function yg(e){return(e.flags&128)===128}var Pl=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Pl||{}),Dg=new Map,DC=0;function EC(){return DC++}function CC(e){Dg.set(e[yo],e)}function dl(e){Dg.delete(e[yo])}var Rp="__ngContext__";function Cr(e,n){ht(n)?(e[Rp]=n[yo],CC(n)):e[Rp]=n}function Eg(e){return wg(e[hr])}function Cg(e){return wg(e[Ve])}function wg(e){for(;e!==null&&!Ke(e);)e=e[Ve];return e}var fl;function Fl(e){fl=e}function Ig(){if(fl!==void 0)return fl;if(typeof document<"u")return document;throw new D(210,!1)}var Ks=new E("",{providedIn:"root",factory:()=>wC}),wC="ng",Js=new E(""),_r=new E("",{providedIn:"platform",factory:()=>"unknown"});var Xs=new E("",{providedIn:"root",factory:()=>Ig().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var IC="h",bC="b";var bg=!1,_g=new E("",{providedIn:"root",factory:()=>bg});var _C=(e,n,t,r)=>{};function TC(e,n,t,r){_C(e,n,t,r)}function ea(e){return(e.flags&32)===32}var SC=()=>null;function Tg(e,n,t=!1){return SC(e,n,t)}function Sg(e,n){let t=e.contentQueries;if(t!==null){let r=x(null);try{for(let o=0;o<t.length;o+=2){let i=t[o],s=t[o+1];if(s!==-1){let a=e.data[s];Es(i),a.contentQueries(2,n[s],s)}}}finally{x(r)}}}function hl(e,n,t){Es(0);let r=x(null);try{n(e,t)}finally{x(r)}}function Ll(e,n,t){if(Nu(n)){let r=x(null);try{let o=n.directiveStart,i=n.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let c=t[s];a.contentQueries(1,c,s)}}}finally{x(r)}}}var Mt=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(Mt||{});var Ss;function MC(){if(Ss===void 0&&(Ss=null,ho.trustedTypes))try{Ss=ho.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Ss}function xp(e){return MC()?.createScriptURL(e)||e}var js=class{changingThisBreaksApplicationSecurity;constructor(n){this.changingThisBreaksApplicationSecurity=n}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${cs})`}};function ta(e){return e instanceof js?e.changingThisBreaksApplicationSecurity:e}function jl(e,n){let t=Mg(e);if(t!=null&&t!==n){if(t==="ResourceURL"&&n==="URL")return!0;throw new Error(`Required a safe ${n}, got a ${t} (see ${cs})`)}return t===n}function Mg(e){return e instanceof js&&e.getTypeName()||null}var AC=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Ag(e){return e=String(e),e.match(AC)?e:"unsafe:"+e}var na=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(na||{});function Ng(e){let n=xg();return n?n.sanitize(na.URL,e)||"":jl(e,"URL")?ta(e):Ag(go(e))}function Rg(e){let n=xg();if(n)return xp(n.sanitize(na.RESOURCE_URL,e)||"");if(jl(e,"ResourceURL"))return xp(ta(e));throw new D(904,!1)}function NC(e,n){return n==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||n==="href"&&(e==="base"||e==="link")?Rg:Ng}function Vl(e,n,t){return NC(n,t)(e)}function xg(){let e=j();return e&&e[dt].sanitizer}var RC=/^>|^->|<!--|-->|--!>|<!-$/g,xC=/(<|>)/g,OC="\u200B$1\u200B";function kC(e){return e.replace(RC,n=>n.replace(xC,OC))}function Og(e){return e instanceof Function?e():e}function PC(e,n,t){let r=e.length;for(;;){let o=e.indexOf(n,t);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=n.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}t=o+1}}var kg="ng-template";function FC(e,n,t,r){let o=0;if(r){for(;o<n.length&&typeof n[o]=="string";o+=2)if(n[o]==="class"&&PC(n[o+1].toLowerCase(),t,0)!==-1)return!0}else if(Bl(e))return!1;if(o=n.indexOf(1,o),o>-1){let i;for(;++o<n.length&&typeof(i=n[o])=="string";)if(i.toLowerCase()===t)return!0}return!1}function Bl(e){return e.type===4&&e.value!==kg}function LC(e,n,t){let r=e.type===4&&!t?kg:e.value;return n===r}function jC(e,n,t){let r=4,o=e.attrs,i=o!==null?UC(o):0,s=!1;for(let a=0;a<n.length;a++){let c=n[a];if(typeof c=="number"){if(!s&&!et(r)&&!et(c))return!1;if(s&&et(c))continue;s=!1,r=c|r&1;continue}if(!s)if(r&4){if(r=2|r&1,c!==""&&!LC(e,c,t)||c===""&&n.length===1){if(et(r))return!1;s=!0}}else if(r&8){if(o===null||!FC(e,o,c,t)){if(et(r))return!1;s=!0}}else{let u=n[++a],l=VC(c,o,Bl(e),t);if(l===-1){if(et(r))return!1;s=!0;continue}if(u!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&u!==d){if(et(r))return!1;s=!0}}}}return et(r)||s}function et(e){return(e&1)===0}function VC(e,n,t,r){if(n===null)return-1;let o=0;if(r||!t){let i=!1;for(;o<n.length;){let s=n[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=n[++o];for(;typeof a=="string";)a=n[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return HC(n,e)}function Pg(e,n,t=!1){for(let r=0;r<n.length;r++)if(jC(e,n[r],t))return!0;return!1}function BC(e){let n=e.attrs;if(n!=null){let t=n.indexOf(5);if((t&1)===0)return n[t+1]}return null}function UC(e){for(let n=0;n<e.length;n++){let t=e[n];if(cg(t))return n}return e.length}function HC(e,n){let t=e.indexOf(4);if(t>-1)for(t++;t<e.length;){let r=e[t];if(typeof r=="number")return-1;if(r===n)return t;t++}return-1}function $C(e,n){e:for(let t=0;t<n.length;t++){let r=n[t];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function Op(e,n){return e?":not("+n.trim()+")":n}function zC(e){let n=e[0],t=1,r=2,o="",i=!1;for(;t<e.length;){let s=e[t];if(typeof s=="string")if(r&2){let a=e[++t];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!et(s)&&(n+=Op(i,o),o=""),r=s,i=i||!et(r);t++}return o!==""&&(n+=Op(i,o)),n}function GC(e){return e.map(zC).join(",")}function WC(e){let n=[],t=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&n.push(i,e[++r]):o===8&&t.push(i);else{if(!et(o))break;o=i}r++}return t.length&&n.push(1,...t),n}var Xt={};function qC(e,n){return e.createText(n)}function ZC(e,n,t){e.setValue(n,t)}function YC(e,n){return e.createComment(kC(n))}function Fg(e,n,t){return e.createElement(n,t)}function Vs(e,n,t,r,o){e.insertBefore(n,t,r,o)}function Lg(e,n,t){e.appendChild(n,t)}function kp(e,n,t,r,o){r!==null?Vs(e,n,t,r,o):Lg(e,n,t)}function QC(e,n,t){e.removeChild(null,n,t)}function KC(e,n,t){e.setAttribute(n,"style",t)}function JC(e,n,t){t===""?e.removeAttribute(n,"class"):e.setAttribute(n,"class",t)}function jg(e,n,t){let{mergedAttrs:r,classes:o,styles:i}=t;r!==null&&iC(e,n,r),o!==null&&JC(e,n,o),i!==null&&KC(e,n,i)}function Ul(e,n,t,r,o,i,s,a,c,u,l){let d=re+r,h=d+o,f=XC(d,h),m=typeof u=="function"?u():u;return f[S]={type:e,blueprint:f,template:t,queries:null,viewQuery:a,declTNode:n,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:h,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:c,consts:m,incompleteFirstPass:!1,ssrId:l}}function XC(e,n){let t=[];for(let r=0;r<n;r++)t.push(r<e?null:Xt);return t}function ew(e){let n=e.tView;return n===null||n.incompleteFirstPass?e.tView=Ul(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):n}function Hl(e,n,t,r,o,i,s,a,c,u,l){let d=n.blueprint.slice();return d[Qe]=o,d[M]=r|4|128|8|64|1024,(u!==null||e&&e[M]&2048)&&(d[M]|=2048),xu(d),d[ae]=d[En]=e,d[pe]=t,d[dt]=s||e&&e[dt],d[K]=a||e&&e[K],d[Dn]=c||e&&e[Dn]||null,d[De]=i,d[yo]=EC(),d[dr]=l,d[Au]=u,d[Se]=n.type==2?e[Se]:d,d}function tw(e,n,t){let r=Je(n,e),o=ew(t),i=e[dt].rendererFactory,s=$l(e,Hl(e,o,null,Vg(t),r,n,null,i.createRenderer(r,t),null,null,null));return e[n.index]=s}function Vg(e){let n=16;return e.signals?n=4096:e.onPush&&(n=64),n}function Bg(e,n,t,r){if(t===0)return-1;let o=n.length;for(let i=0;i<t;i++)n.push(r),e.blueprint.push(r),e.data.push(null);return o}function $l(e,n){return e[hr]?e[Mu][Ve]=n:e[hr]=n,e[Mu]=n,n}function nw(e=1){Ug(ge(),j(),_n()+e,!1)}function Ug(e,n,t,r){if(!r)if((n[M]&3)===3){let i=e.preOrderCheckHooks;i!==null&&As(n,i,t)}else{let i=e.preOrderHooks;i!==null&&Ns(n,i,0,t)}Yt(t)}var ra=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(ra||{});function pl(e,n,t,r){let o=x(null);try{let[i,s,a]=e.inputs[t],c=null;(s&ra.SignalBased)!==0&&(c=n[i][ye]),c!==null&&c.transformFn!==void 0?r=c.transformFn(r):a!==null&&(r=a.call(n,r)),e.setInput!==null?e.setInput(n,c,r,t,i):ng(n,c,i,r)}finally{x(o)}}var vt=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(vt||{}),rw;function zl(e,n){return rw(e,n)}function yr(e,n,t,r,o){if(r!=null){let i,s=!1;Ke(r)?i=r:ht(r)&&(s=!0,r=r[Qe]);let a=Be(r);e===0&&t!==null?o==null?Lg(n,t,a):Vs(n,t,a,o||null,!0):e===1&&t!==null?Vs(n,t,a,o||null,!0):e===2?QC(n,a,s):e===3&&n.destroyNode(a),i!=null&&hw(n,e,i,t,o)}}function ow(e,n){Hg(e,n),n[Qe]=null,n[De]=null}function iw(e,n,t,r,o,i){r[Qe]=o,r[De]=n,oa(e,r,t,1,o,i)}function Hg(e,n){n[dt].changeDetectionScheduler?.notify(9),oa(e,n,n[K],2,null,null)}function sw(e){let n=e[hr];if(!n)return el(e[S],e);for(;n;){let t=null;if(ht(n))t=n[hr];else{let r=n[Ee];r&&(t=r)}if(!t){for(;n&&!n[Ve]&&n!==e;)ht(n)&&el(n[S],n),n=n[ae];n===null&&(n=e),ht(n)&&el(n[S],n),t=n&&n[Ve]}n=t}}function Gl(e,n){let t=e[In],r=t.indexOf(n);t.splice(r,1)}function $g(e,n){if(bn(n))return;let t=n[K];t.destroyNode&&oa(e,n,t,3,null,null),sw(n)}function el(e,n){if(bn(n))return;let t=x(null);try{n[M]&=-129,n[M]|=256,n[Pe]&&Ji(n[Pe]),cw(e,n),aw(e,n),n[S].type===1&&n[K].destroy();let r=n[Gt];if(r!==null&&Ke(n[ae])){r!==n[ae]&&Gl(r,n);let o=n[ft];o!==null&&o.detachView(e)}dl(n)}finally{x(t)}}function aw(e,n){let t=e.cleanup,r=n[fr];if(t!==null)for(let s=0;s<t.length-1;s+=2)if(typeof t[s]=="string"){let a=t[s+3];a>=0?r[a]():r[-a].unsubscribe(),s+=2}else{let a=r[t[s+1]];t[s].call(a)}r!==null&&(n[fr]=null);let o=n[bt];if(o!==null){n[bt]=null;for(let s=0;s<o.length;s++){let a=o[s];a()}}let i=n[Do];if(i!==null){n[Do]=null;for(let s of i)s.destroy()}}function cw(e,n){let t;if(e!=null&&(t=e.destroyHooks)!=null)for(let r=0;r<t.length;r+=2){let o=n[t[r]];if(!(o instanceof Sn)){let i=t[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],c=i[s+1];U(4,a,c);try{c.call(a)}finally{U(5,a,c)}}else{U(4,o,i);try{i.call(o)}finally{U(5,o,i)}}}}}function zg(e,n,t){return uw(e,n.parent,t)}function uw(e,n,t){let r=n;for(;r!==null&&r.type&168;)n=r,r=n.parent;if(r===null)return t[Qe];if(qt(r)){let{encapsulation:o}=e.data[r.directiveStart+r.componentOffset];if(o===Mt.None||o===Mt.Emulated)return null}return Je(r,t)}function Gg(e,n,t){return dw(e,n,t)}function lw(e,n,t){return e.type&40?Je(e,t):null}var dw=lw,Pp;function Wl(e,n,t,r){let o=zg(e,r,n),i=n[K],s=r.parent||n[De],a=Gg(s,r,n);if(o!=null)if(Array.isArray(t))for(let c=0;c<t.length;c++)kp(i,o,t[c],a,!1);else kp(i,o,t,a,!1);Pp!==void 0&&Pp(i,r,n,t,o)}function To(e,n){if(n!==null){let t=n.type;if(t&3)return Je(n,e);if(t&4)return gl(-1,e[n.index]);if(t&8){let r=n.child;if(r!==null)return To(e,r);{let o=e[n.index];return Ke(o)?gl(-1,o):Be(o)}}else{if(t&128)return To(e,n.next);if(t&32)return zl(n,e)()||Be(e[n.index]);{let r=Wg(e,n);if(r!==null){if(Array.isArray(r))return r[0];let o=Ht(e[Se]);return To(o,r)}else return To(e,n.next)}}}return null}function Wg(e,n){if(n!==null){let r=e[Se][De],o=n.projection;return r.projection[o]}return null}function gl(e,n){let t=Ee+e+1;if(t<n.length){let r=n[t],o=r[S].firstChild;if(o!==null)return To(r,o)}return n[Wt]}function ql(e,n,t,r,o,i,s){for(;t!=null;){if(t.type===128){t=t.next;continue}let a=r[t.index],c=t.type;if(s&&n===0&&(a&&Cr(Be(a),r),t.flags|=2),!ea(t))if(c&8)ql(e,n,t.child,r,o,i,!1),yr(n,e,o,a,i);else if(c&32){let u=zl(t,r),l;for(;l=u();)yr(n,e,o,l,i);yr(n,e,o,a,i)}else c&16?qg(e,n,r,t,o,i):yr(n,e,o,a,i);t=s?t.projectionNext:t.next}}function oa(e,n,t,r,o,i){ql(t,r,e.firstChild,n,o,i,!1)}function fw(e,n,t){let r=n[K],o=zg(e,t,n),i=t.parent||n[De],s=Gg(i,t,n);qg(r,0,n,t,o,s)}function qg(e,n,t,r,o,i){let s=t[Se],c=s[De].projection[r.projection];if(Array.isArray(c))for(let u=0;u<c.length;u++){let l=c[u];yr(n,e,o,l,i)}else{let u=c,l=s[ae];yg(r)&&(u.flags|=128),ql(e,n,u,l,o,i,!0)}}function hw(e,n,t,r,o){let i=t[Wt],s=Be(t);i!==s&&yr(n,e,r,i,o);for(let a=Ee;a<t.length;a++){let c=t[a];oa(c[S],c,e,n,r,i)}}function pw(e,n,t,r,o){if(n)o?e.addClass(t,r):e.removeClass(t,r);else{let i=r.indexOf("-")===-1?void 0:vt.DashCase;o==null?e.removeStyle(t,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=vt.Important),e.setStyle(t,r,o,i))}}function Zg(e,n,t,r,o){let i=_n(),s=r&2;try{Yt(-1),s&&n.length>re&&Ug(e,n,re,!1),U(s?2:0,o,t),t(r,o)}finally{Yt(i),U(s?3:1,o,t)}}function ia(e,n,t){ww(e,n,t),(t.flags&64)===64&&Iw(e,n,t)}function xo(e,n,t=Je){let r=n.localNames;if(r!==null){let o=n.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?t(n,e):e[s];e[o++]=a}}}function gw(e,n,t,r){let i=r.get(_g,bg)||t===Mt.ShadowDom,s=e.selectRootElement(n,i);return mw(s),s}function mw(e){vw(e)}var vw=()=>null;function yw(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function Dw(e,n,t,r,o,i){let s=n[S];if(Kl(e,s,n,t,r)){qt(e)&&Cw(n,e.index);return}e.type&3&&(t=yw(t)),Ew(e,n,t,r,o,i)}function Ew(e,n,t,r,o,i){if(e.type&3){let s=Je(e,n);r=i!=null?i(r,e.value||"",t):r,o.setProperty(s,t,r)}else e.type&12}function Cw(e,n){let t=Ue(n,e);t[M]&16||(t[M]|=64)}function ww(e,n,t){let r=t.directiveStart,o=t.directiveEnd;qt(t)&&tw(n,t,e.data[r+t.componentOffset]),e.firstCreatePass||Fs(t,n);let i=t.initialInputs;for(let s=r;s<o;s++){let a=e.data[s],c=Mo(n,e,s,t);if(Cr(c,n),i!==null&&Sw(n,s-r,c,a,t,i),pt(a)){let u=Ue(t.index,n);u[pe]=Mo(n,e,s,t)}}}function Iw(e,n,t){let r=t.directiveStart,o=t.directiveEnd,i=t.index,s=gp();try{Yt(i);for(let a=r;a<o;a++){let c=e.data[a],u=n[a];Ds(a),(c.hostBindings!==null||c.hostVars!==0||c.hostAttrs!==null)&&bw(c,u)}}finally{Yt(-1),Ds(s)}}function bw(e,n){e.hostBindings!==null&&e.hostBindings(1,n)}function Zl(e,n){let t=e.directiveRegistry,r=null;if(t)for(let o=0;o<t.length;o++){let i=t[o];Pg(n,i.selectors,!1)&&(r??=[],pt(i)?r.unshift(i):r.push(i))}return r}function _w(e,n,t,r,o,i){let s=Je(e,n);Tw(n[K],s,i,e.value,t,r,o)}function Tw(e,n,t,r,o,i,s){if(i==null)e.removeAttribute(n,o,t);else{let a=s==null?go(i):s(i,r||"",o);e.setAttribute(n,o,a,t)}}function Sw(e,n,t,r,o,i){let s=i[n];if(s!==null)for(let a=0;a<s.length;a+=2){let c=s[a],u=s[a+1];pl(r,t,c,u)}}function Yl(e,n,t,r,o){let i=re+t,s=n[S],a=o(s,n,e,r,t);n[i]=a,vr(e,!0);let c=e.type===2;return c?(jg(n[K],a,e),(np()===0||pr(e))&&Cr(a,n),rp()):Cr(a,n),Is()&&(!c||!ea(e))&&Wl(s,n,a,e),e}function Ql(e){let n=e;return Vu()?Bu():(n=n.parent,vr(n,!1)),n}function Mw(e,n){let t=e[Dn];if(!t)return;t.get(Me,null)?.(n)}function Kl(e,n,t,r,o){let i=e.inputs?.[r],s=e.hostDirectiveInputs?.[r],a=!1;if(s)for(let c=0;c<s.length;c+=2){let u=s[c],l=s[c+1],d=n.data[u];pl(d,t[u],l,o),a=!0}if(i)for(let c of i){let u=t[c],l=n.data[c];pl(l,u,r,o),a=!0}return a}function Aw(e,n){let t=Ue(n,e),r=t[S];Nw(r,t);let o=t[Qe];o!==null&&t[dr]===null&&(t[dr]=Tg(o,t[Dn])),U(18),Jl(r,t,t[pe]),U(19,t[pe])}function Nw(e,n){for(let t=n.length;t<e.blueprint.length;t++)n.push(e.blueprint[t])}function Jl(e,n,t){Cs(n);try{let r=e.viewQuery;r!==null&&hl(1,r,t);let o=e.template;o!==null&&Zg(e,n,o,1,t),e.firstCreatePass&&(e.firstCreatePass=!1),n[ft]?.finishViewCreation(e),e.staticContentQueries&&Sg(e,n),e.staticViewQueries&&hl(2,e.viewQuery,t);let i=e.components;i!==null&&Rw(n,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{n[M]&=-5,ws()}}function Rw(e,n){for(let t=0;t<n.length;t++)Aw(e,n[t])}function Yg(e,n,t,r){let o=x(null);try{let i=n.tView,a=e[M]&4096?4096:16,c=Hl(e,i,t,a,null,n,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),u=e[n.index];c[Gt]=u;let l=e[ft];return l!==null&&(c[ft]=l.createEmbeddedView(i)),Jl(i,c,t),c}finally{x(o)}}function ml(e,n){return!n||n.firstChild===null||yg(e)}var Fp=!1,xw=new E("");function Ao(e,n,t,r,o=!1){for(;t!==null;){if(t.type===128){t=o?t.projectionNext:t.next;continue}let i=n[t.index];i!==null&&r.push(Be(i)),Ke(i)&&Qg(i,r);let s=t.type;if(s&8)Ao(e,n,t.child,r);else if(s&32){let a=zl(t,n),c;for(;c=a();)r.push(c)}else if(s&16){let a=Wg(n,t);if(Array.isArray(a))r.push(...a);else{let c=Ht(n[Se]);Ao(c[S],c,a,r,!0)}}t=o?t.projectionNext:t.next}return r}function Qg(e,n){for(let t=Ee;t<e.length;t++){let r=e[t],o=r[S].firstChild;o!==null&&Ao(r[S],r,o,n)}e[Wt]!==e[Qe]&&n.push(e[Wt])}function Kg(e){if(e[wn]!==null){for(let n of e[wn])n.impl.addSequence(n);e[wn].length=0}}var Jg=[];function Ow(e){return e[Pe]??kw(e)}function kw(e){let n=Jg.pop()??Object.create(Fw);return n.lView=e,n}function Pw(e){e.lView[Pe]!==e&&(e.lView=null,Jg.push(e))}var Fw=N(g({},dn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{mr(e.lView)},consumerOnSignalRead(){this.lView[Pe]=this}});function Lw(e){let n=e[Pe]??Object.create(jw);return n.lView=e,n}var jw=N(g({},dn),{consumerIsAlwaysLive:!0,kind:"template",consumerMarkedDirty:e=>{let n=Ht(e.lView);for(;n&&!Xg(n[S]);)n=Ht(n);n&&Ou(n)},consumerOnSignalRead(){this.lView[Pe]=this}});function Xg(e){return e.type!==2}function em(e){if(e[Do]===null)return;let n=!0;for(;n;){let t=!1;for(let r of e[Do])r.dirty&&(t=!0,r.zone===null||Zone.current===r.zone?r.run():r.zone.run(()=>r.run()));n=t&&!!(e[M]&8192)}}var Vw=100;function Xl(e,n=0){let r=e[dt].rendererFactory,o=!1;o||r.begin?.();try{Bw(e,n)}finally{o||r.end?.()}}function Bw(e,n){let t=Hu();try{$u(!0),vl(e,n);let r=0;for(;Co(e);){if(r===Vw)throw new D(103,!1);r++,vl(e,1)}}finally{$u(t)}}function tm(e,n){Uu(n?wo.Exhaustive:wo.OnlyDirtyViews);try{Xl(e)}finally{Uu(wo.Off)}}function Uw(e,n,t,r){if(bn(n))return;let o=n[M],i=!1,s=!1;Cs(n);let a=!0,c=null,u=null;i||(Xg(e)?(u=Ow(n),c=fn(u)):Qi()===null?(a=!1,u=Lw(n),c=fn(u)):n[Pe]&&(Ji(n[Pe]),n[Pe]=null));try{xu(n),dp(e.bindingStartIndex),t!==null&&Zg(e,n,t,2,r);let l=(o&3)===3;if(!i)if(l){let f=e.preOrderCheckHooks;f!==null&&As(n,f,null)}else{let f=e.preOrderHooks;f!==null&&Ns(n,f,0,null),Ju(n,0)}if(s||Hw(n),em(n),nm(n,0),e.contentQueries!==null&&Sg(e,n),!i)if(l){let f=e.contentCheckHooks;f!==null&&As(n,f)}else{let f=e.contentHooks;f!==null&&Ns(n,f,1),Ju(n,1)}zw(e,n);let d=e.components;d!==null&&om(n,d,0);let h=e.viewQuery;if(h!==null&&hl(2,h,r),!i)if(l){let f=e.viewCheckHooks;f!==null&&As(n,f)}else{let f=e.viewHooks;f!==null&&Ns(n,f,2),Ju(n,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),n[hs]){for(let f of n[hs])f();n[hs]=null}i||(Kg(n),n[M]&=-73)}catch(l){throw i||mr(n),l}finally{u!==null&&(or(u,c),a&&Pw(u)),ws()}}function nm(e,n){for(let t=Eg(e);t!==null;t=Cg(t))for(let r=Ee;r<t.length;r++){let o=t[r];rm(o,n)}}function Hw(e){for(let n=Eg(e);n!==null;n=Cg(n)){if(!(n[M]&2))continue;let t=n[In];for(let r=0;r<t.length;r++){let o=t[r];Ou(o)}}}function $w(e,n,t){U(18);let r=Ue(n,e);rm(r,t),U(19,r[pe])}function rm(e,n){gs(e)&&vl(e,n)}function vl(e,n){let r=e[S],o=e[M],i=e[Pe],s=!!(n===0&&o&16);if(s||=!!(o&64&&n===0),s||=!!(o&1024),s||=!!(i?.dirty&&ao(i)),s||=!1,i&&(i.dirty=!1),e[M]&=-9217,s)Uw(r,e,r.template,e[pe]);else if(o&8192){let a=x(null);try{em(e),nm(e,1);let c=r.components;c!==null&&om(e,c,1),Kg(e)}finally{x(a)}}}function om(e,n,t){for(let r=0;r<n.length;r++)$w(e,n[r],t)}function zw(e,n){let t=e.hostBindingOpCodes;if(t!==null)try{for(let r=0;r<t.length;r++){let o=t[r];if(o<0)Yt(~o);else{let i=o,s=t[++r],a=t[++r];pp(s,i);let c=n[i];U(24,c),a(2,c),U(25,c)}}}finally{Yt(-1)}}function ed(e,n){let t=Hu()?64:1088;for(e[dt].changeDetectionScheduler?.notify(n);e;){e[M]|=t;let r=Ht(e);if(gr(e)&&!r)return e;e=r}return null}function im(e,n,t,r){return[e,!0,0,n,null,r,null,t,null,null]}function sm(e,n,t,r=!0){let o=n[S];if(Gw(o,n,e,t),r){let s=gl(t,e),a=n[K],c=a.parentNode(e[Wt]);c!==null&&iw(o,e[De],a,n,c,s)}let i=n[dr];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function yl(e,n){if(e.length<=Ee)return;let t=Ee+n,r=e[t];if(r){let o=r[Gt];o!==null&&o!==e&&Gl(o,r),n>0&&(e[t-1][Ve]=r[Ve]);let i=mo(e,Ee+n);ow(r[S],r);let s=i[ft];s!==null&&s.detachView(i[S]),r[ae]=null,r[Ve]=null,r[M]&=-129}return r}function Gw(e,n,t,r){let o=Ee+r,i=t.length;r>0&&(t[o-1][Ve]=n),r<i-Ee?(n[Ve]=t[o],Eu(t,Ee+r,n)):(t.push(n),n[Ve]=null),n[ae]=t;let s=n[Gt];s!==null&&t!==s&&am(s,n);let a=n[ft];a!==null&&a.insertView(e),ms(n),n[M]|=128}function am(e,n){let t=e[In],r=n[ae];if(ht(r))e[M]|=2;else{let o=r[ae][Se];n[Se]!==o&&(e[M]|=2)}t===null?e[In]=[n]:t.push(n)}var Qt=class{_lView;_cdRefInjectingView;_appRef=null;_attachedToViewContainer=!1;exhaustive;get rootNodes(){let n=this._lView,t=n[S];return Ao(t,n,t.firstChild,[])}constructor(n,t){this._lView=n,this._cdRefInjectingView=t}get context(){return this._lView[pe]}set context(n){this._lView[pe]=n}get destroyed(){return bn(this._lView)}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let n=this._lView[ae];if(Ke(n)){let t=n[Eo],r=t?t.indexOf(this):-1;r>-1&&(yl(n,r),mo(t,r))}this._attachedToViewContainer=!1}$g(this._lView[S],this._lView)}onDestroy(n){ku(this._lView,n)}markForCheck(){ed(this._cdRefInjectingView||this._lView,4)}detach(){this._lView[M]&=-129}reattach(){ms(this._lView),this._lView[M]|=128}detectChanges(){this._lView[M]|=1024,Xl(this._lView)}checkNoChanges(){return;try{this.exhaustive??=this._lView[Dn].get(xw,Fp)}catch{this.exhaustive=Fp}}attachToViewContainerRef(){if(this._appRef)throw new D(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null;let n=gr(this._lView),t=this._lView[Gt];t!==null&&!n&&Gl(t,this._lView),Hg(this._lView[S],this._lView)}attachToAppRef(n){if(this._attachedToViewContainer)throw new D(902,!1);this._appRef=n;let t=gr(this._lView),r=this._lView[Gt];r!==null&&!t&&am(r,this._lView),ms(this._lView)}};var tt=(()=>{class e{_declarationLView;_declarationTContainer;elementRef;static __NG_ELEMENT_ID__=Ww;constructor(t,r,o){this._declarationLView=t,this._declarationTContainer=r,this.elementRef=o}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,r){return this.createEmbeddedViewImpl(t,r)}createEmbeddedViewImpl(t,r,o){let i=Yg(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:r,dehydratedView:o});return new Qt(i)}}return e})();function Ww(){return td(ce(),j())}function td(e,n){return e.type&4?new tt(n,e,br(e,n)):null}function Tr(e,n,t,r,o){let i=e.data[n];if(i===null)i=qw(e,n,t,r,o),hp()&&(i.flags|=32);else if(i.type&64){i.type=t,i.value=r,i.attrs=o;let s=up();i.injectorIndex=s===null?-1:s.injectorIndex}return vr(i,!0),i}function qw(e,n,t,r,o){let i=ju(),s=Vu(),a=s?i:i&&i.parent,c=e.data[n]=Yw(e,a,t,n,r,o);return Zw(e,c,i,s),c}function Zw(e,n,t,r){e.firstChild===null&&(e.firstChild=n),t!==null&&(r?t.child==null&&n.parent!==null&&(t.child=n):t.next===null&&(t.next=n,n.prev=t))}function Yw(e,n,t,r,o,i){let s=n?n.injectorIndex:-1,a=0;return Lu()&&(a|=128),{type:t,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:null,inputs:null,hostDirectiveInputs:null,outputs:null,hostDirectiveOutputs:null,directiveToIndex:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:n,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}var CF=new RegExp(`^(\\d+)*(${bC}|${IC})*(.*)`);var Qw=()=>null;function Dl(e,n){return Qw(e,n)}var cm=class{},sa=class{},El=class{resolveComponentFactory(n){throw new D(917,!1)}},Oo=class{static NULL=new El},Mn=class{},At=(()=>{class e{destroyNode=null;static __NG_ELEMENT_ID__=()=>Kw()}return e})();function Kw(){let e=j(),n=ce(),t=Ue(n.index,e);return(ht(t)?t:e)[K]}var um=(()=>{class e{static \u0275prov=w({token:e,providedIn:"root",factory:()=>null})}return e})();var xs={},Cl=class{injector;parentInjector;constructor(n,t){this.injector=n,this.parentInjector=t}get(n,t,r){let o=this.injector.get(n,xs,r);return o!==xs||t===xs?o:this.parentInjector.get(n,t,r)}};function Bs(e,n,t){let r=t?e.styles:null,o=t?e.classes:null,i=0;if(n!==null)for(let s=0;s<n.length;s++){let a=n[s];if(typeof a=="number")i=a;else if(i==1)o=au(o,a);else if(i==2){let c=a,u=n[++s];r=au(r,c+": "+u+";")}}t?e.styles=r:e.stylesWithoutHost=r,t?e.classes=o:e.classesWithoutHost=o}function v(e,n=0){let t=j();if(t===null)return I(e,n);let r=ce();return gg(r,t,de(e),n)}function lm(e,n,t,r,o){let i=r===null?null:{"":-1},s=o(e,t);if(s!==null){let a=s,c=null,u=null;for(let l of s)if(l.resolveHostDirectives!==null){[a,c,u]=l.resolveHostDirectives(s);break}eI(e,n,t,a,i,c,u)}i!==null&&r!==null&&Jw(t,r,i)}function Jw(e,n,t){let r=e.localNames=[];for(let o=0;o<n.length;o+=2){let i=t[n[o+1]];if(i==null)throw new D(-301,!1);r.push(n[o],i)}}function Xw(e,n,t){n.componentOffset=t,(e.components??=[]).push(n.index)}function eI(e,n,t,r,o,i,s){let a=r.length,c=!1;for(let h=0;h<a;h++){let f=r[h];!c&&pt(f)&&(c=!0,Xw(e,t,h)),ul(Fs(t,n),e,f.type)}sI(t,e.data.length,a);for(let h=0;h<a;h++){let f=r[h];f.providersResolver&&f.providersResolver(f)}let u=!1,l=!1,d=Bg(e,n,a,null);a>0&&(t.directiveToIndex=new Map);for(let h=0;h<a;h++){let f=r[h];if(t.mergedAttrs=Er(t.mergedAttrs,f.hostAttrs),nI(e,t,n,d,f),iI(d,f,o),s!==null&&s.has(f)){let[b,y]=s.get(f);t.directiveToIndex.set(f.type,[d,b+t.directiveStart,y+t.directiveStart])}else(i===null||!i.has(f))&&t.directiveToIndex.set(f.type,d);f.contentQueries!==null&&(t.flags|=4),(f.hostBindings!==null||f.hostAttrs!==null||f.hostVars!==0)&&(t.flags|=64);let m=f.type.prototype;!u&&(m.ngOnChanges||m.ngOnInit||m.ngDoCheck)&&((e.preOrderHooks??=[]).push(t.index),u=!0),!l&&(m.ngOnChanges||m.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(t.index),l=!0),d++}tI(e,t,i)}function tI(e,n,t){for(let r=n.directiveStart;r<n.directiveEnd;r++){let o=e.data[r];if(t===null||!t.has(o))Lp(0,n,o,r),Lp(1,n,o,r),Vp(n,r,!1);else{let i=t.get(o);jp(0,n,i,r),jp(1,n,i,r),Vp(n,r,!0)}}}function Lp(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s;e===0?s=n.inputs??={}:s=n.outputs??={},s[i]??=[],s[i].push(r),dm(n,i)}}function jp(e,n,t,r){let o=e===0?t.inputs:t.outputs;for(let i in o)if(o.hasOwnProperty(i)){let s=o[i],a;e===0?a=n.hostDirectiveInputs??={}:a=n.hostDirectiveOutputs??={},a[s]??=[],a[s].push(r,i),dm(n,s)}}function dm(e,n){n==="class"?e.flags|=8:n==="style"&&(e.flags|=16)}function Vp(e,n,t){let{attrs:r,inputs:o,hostDirectiveInputs:i}=e;if(r===null||!t&&o===null||t&&i===null||Bl(e)){e.initialInputs??=[],e.initialInputs.push(null);return}let s=null,a=0;for(;a<r.length;){let c=r[a];if(c===0){a+=4;continue}else if(c===5){a+=2;continue}else if(typeof c=="number")break;if(!t&&o.hasOwnProperty(c)){let u=o[c];for(let l of u)if(l===n){s??=[],s.push(c,r[a+1]);break}}else if(t&&i.hasOwnProperty(c)){let u=i[c];for(let l=0;l<u.length;l+=2)if(u[l]===n){s??=[],s.push(u[l+1],r[a+1]);break}}a+=2}e.initialInputs??=[],e.initialInputs.push(s)}function nI(e,n,t,r,o){e.data[r]=o;let i=o.factory||(o.factory=gn(o.type,!0)),s=new Sn(i,pt(o),v,null);e.blueprint[r]=s,t[r]=s,rI(e,n,r,Bg(e,t,o.hostVars,Xt),o)}function rI(e,n,t,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~n.index;oI(s)!=a&&s.push(a),s.push(t,r,i)}}function oI(e){let n=e.length;for(;n>0;){let t=e[--n];if(typeof t=="number"&&t<0)return t}return 0}function iI(e,n,t){if(t){if(n.exportAs)for(let r=0;r<n.exportAs.length;r++)t[n.exportAs[r]]=e;pt(n)&&(t[""]=e)}}function sI(e,n,t){e.flags|=1,e.directiveStart=n,e.directiveEnd=n+t,e.providerIndexes=n}function nd(e,n,t,r,o,i,s,a){let c=n[S],u=c.consts,l=Zt(u,s),d=Tr(c,e,t,r,l);return i&&lm(c,n,d,Zt(u,a),o),d.mergedAttrs=Er(d.mergedAttrs,d.attrs),d.attrs!==null&&Bs(d,d.attrs,!1),d.mergedAttrs!==null&&Bs(d,d.mergedAttrs,!0),c.queries!==null&&c.queries.elementStart(c,d),d}function rd(e,n){sg(e,n),Nu(n)&&e.queries.elementEnd(n)}function aI(e,n,t,r,o,i){let s=n.consts,a=Zt(s,o),c=Tr(n,e,t,r,a);if(c.mergedAttrs=Er(c.mergedAttrs,c.attrs),i!=null){let u=Zt(s,i);c.localNames=[];for(let l=0;l<u.length;l+=2)c.localNames.push(u[l],-1)}return c.attrs!==null&&Bs(c,c.attrs,!1),c.mergedAttrs!==null&&Bs(c,c.mergedAttrs,!0),n.queries!==null&&n.queries.elementStart(n,c),c}function od(e){return hm(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function fm(e,n){if(Array.isArray(e))for(let t=0;t<e.length;t++)n(e[t]);else{let t=e[Symbol.iterator](),r;for(;!(r=t.next()).done;)n(r.value)}}function hm(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function aa(e,n,t){if(t===Xt)return!1;let r=e[n];return Object.is(r,t)?!1:(e[n]=t,!0)}function tl(e,n,t){return function r(o){let i=qt(e)?Ue(e.index,n):n;ed(i,5);let s=n[pe],a=Bp(n,s,t,o),c=r.__ngNextListenerFn__;for(;c;)a=Bp(n,s,c,o)&&a,c=c.__ngNextListenerFn__;return a}}function Bp(e,n,t,r){let o=x(null);try{return U(6,n,t),t(r)!==!1}catch(i){return Mw(e,i),!1}finally{U(7,n,t),x(o)}}function cI(e,n,t,r,o,i,s,a){let c=pr(e),u=!1,l=null;if(!r&&c&&(l=uI(n,t,i,e.index)),l!==null){let d=l.__ngLastListenerFn__||l;d.__ngNextListenerFn__=s,l.__ngLastListenerFn__=s,u=!0}else{let d=Je(e,t),h=r?r(d):d;TC(t,h,i,a);let f=o.listen(h,i,a),m=r?b=>r(Be(b[e.index])):e.index;pm(m,n,t,i,a,f,!1)}return u}function uI(e,n,t,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===t&&o[i+1]===r){let a=n[fr],c=o[i+2];return a&&a.length>c?a[c]:null}typeof s=="string"&&(i+=2)}return null}function pm(e,n,t,r,o,i,s){let a=n.firstCreatePass?Fu(n):null,c=Pu(t),u=c.length;c.push(o,i),a&&a.push(r,e,u,(u+1)*(s?-1:1))}function Up(e,n,t,r,o,i){let s=n[t],a=n[S],u=a.data[t].outputs[r],d=s[u].subscribe(i);pm(e.index,a,n,o,i,d,!0)}var wl=Symbol("BINDING");var Us=class extends Oo{ngModule;constructor(n){super(),this.ngModule=n}resolveComponentFactory(n){let t=lt(n);return new Kt(t,this.ngModule)}};function lI(e){return Object.keys(e).map(n=>{let[t,r,o]=e[n],i={propName:t,templateName:n,isSignal:(r&ra.SignalBased)!==0};return o&&(i.transform=o),i})}function dI(e){return Object.keys(e).map(n=>({propName:e[n],templateName:n}))}function fI(e,n,t){let r=n instanceof G?n:n?.injector;return r&&e.getStandaloneInjector!==null&&(r=e.getStandaloneInjector(r)||r),r?new Cl(t,r):t}function hI(e){let n=e.get(Mn,null);if(n===null)throw new D(407,!1);let t=e.get(um,null),r=e.get(Tt,null);return{rendererFactory:n,sanitizer:t,changeDetectionScheduler:r,ngReflect:!1}}function pI(e,n){let t=(e.selectors[0][0]||"div").toLowerCase();return Fg(n,t,t==="svg"?Zh:t==="math"?Yh:null)}var Kt=class extends sa{componentDef;ngModule;selector;componentType;ngContentSelectors;isBoundToModule;cachedInputs=null;cachedOutputs=null;get inputs(){return this.cachedInputs??=lI(this.componentDef.inputs),this.cachedInputs}get outputs(){return this.cachedOutputs??=dI(this.componentDef.outputs),this.cachedOutputs}constructor(n,t){super(),this.componentDef=n,this.ngModule=t,this.componentType=n.type,this.selector=GC(n.selectors),this.ngContentSelectors=n.ngContentSelectors??[],this.isBoundToModule=!!t}create(n,t,r,o,i,s){U(22);let a=x(null);try{let c=this.componentDef,u=gI(r,c,s,i),l=fI(c,o||this.ngModule,n),d=hI(l),h=d.rendererFactory.createRenderer(null,c),f=r?gw(h,r,c.encapsulation,l):pI(c,h),m=s?.some(Hp)||i?.some(C=>typeof C!="function"&&C.bindings.some(Hp)),b=Hl(null,u,null,512|Vg(c),null,null,d,h,l,null,Tg(f,l,!0));b[re]=f,Cs(b);let y=null;try{let C=nd(re,b,2,"#host",()=>u.directiveRegistry,!0,0);f&&(jg(h,f,C),Cr(f,b)),ia(u,b,C),Ll(u,C,b),rd(u,C),t!==void 0&&vI(C,this.ngContentSelectors,t),y=Ue(C.index,b),b[pe]=y[pe],Jl(u,b,null)}catch(C){throw y!==null&&dl(y),dl(b),C}finally{U(23),ws()}return new Hs(this.componentType,b,!!m)}finally{x(a)}}};function gI(e,n,t,r){let o=e?["ng-version","20.1.3"]:WC(n.selectors[0]),i=null,s=null,a=0;if(t)for(let l of t)a+=l[wl].requiredVars,l.create&&(l.targetIdx=0,(i??=[]).push(l)),l.update&&(l.targetIdx=0,(s??=[]).push(l));if(r)for(let l=0;l<r.length;l++){let d=r[l];if(typeof d!="function")for(let h of d.bindings){a+=h[wl].requiredVars;let f=l+1;h.create&&(h.targetIdx=f,(i??=[]).push(h)),h.update&&(h.targetIdx=f,(s??=[]).push(h))}}let c=[n];if(r)for(let l of r){let d=typeof l=="function"?l:l.type,h=bu(d);c.push(h)}return Ul(0,null,mI(i,s),1,a,c,null,null,null,[o],null)}function mI(e,n){return!e&&!n?null:t=>{if(t&1&&e)for(let r of e)r.create();if(t&2&&n)for(let r of n)r.update()}}function Hp(e){let n=e[wl].kind;return n==="input"||n==="twoWay"}var Hs=class extends cm{_rootLView;_hasInputBindings;instance;hostView;changeDetectorRef;componentType;location;previousInputValues=null;_tNode;constructor(n,t,r){super(),this._rootLView=t,this._hasInputBindings=r,this._tNode=ps(t[S],re),this.location=br(this._tNode,t),this.instance=Ue(this._tNode.index,t)[pe],this.hostView=this.changeDetectorRef=new Qt(t,void 0),this.componentType=n}setInput(n,t){this._hasInputBindings;let r=this._tNode;if(this.previousInputValues??=new Map,this.previousInputValues.has(n)&&Object.is(this.previousInputValues.get(n),t))return;let o=this._rootLView,i=Kl(r,o[S],o,n,t);this.previousInputValues.set(n,t);let s=Ue(r.index,o);ed(s,1)}get injector(){return new Tn(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(n){this.hostView.onDestroy(n)}};function vI(e,n,t){let r=e.projection=[];for(let o=0;o<n.length;o++){let i=t[o];r.push(i!=null&&i.length?Array.from(i):null)}}var $e=(()=>{class e{static __NG_ELEMENT_ID__=yI}return e})();function yI(){let e=ce();return mm(e,j())}var DI=$e,gm=class extends DI{_lContainer;_hostTNode;_hostLView;constructor(n,t,r){super(),this._lContainer=n,this._hostTNode=t,this._hostLView=r}get element(){return br(this._hostTNode,this._hostLView)}get injector(){return new Tn(this._hostTNode,this._hostLView)}get parentInjector(){let n=kl(this._hostTNode,this._hostLView);if(ug(n)){let t=Ps(n,this._hostLView),r=ks(n),o=t[S].data[r+8];return new Tn(o,t)}else return new Tn(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(n){let t=$p(this._lContainer);return t!==null&&t[n]||null}get length(){return this._lContainer.length-Ee}createEmbeddedView(n,t,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=Dl(this._lContainer,n.ssrId),a=n.createEmbeddedViewImpl(t||{},i,s);return this.insertImpl(a,o,ml(this._hostTNode,s)),a}createComponent(n,t,r,o,i,s,a){let c=n&&!KE(n),u;if(c)u=t;else{let y=t||{};u=y.index,r=y.injector,o=y.projectableNodes,i=y.environmentInjector||y.ngModuleRef,s=y.directives,a=y.bindings}let l=c?n:new Kt(lt(n)),d=r||this.parentInjector;if(!i&&l.ngModule==null){let C=(c?d:this.parentInjector).get(G,null);C&&(i=C)}let h=lt(l.componentType??{}),f=Dl(this._lContainer,h?.id??null),m=f?.firstChild??null,b=l.create(d,o,m,i,s,a);return this.insertImpl(b.hostView,u,ml(this._hostTNode,f)),b}insert(n,t){return this.insertImpl(n,t,!0)}insertImpl(n,t,r){let o=n._lView;if(Jh(o)){let a=this.indexOf(n);if(a!==-1)this.detach(a);else{let c=o[ae],u=new gm(c,c[De],c[ae]);u.detach(u.indexOf(n))}}let i=this._adjustIndex(t),s=this._lContainer;return sm(s,o,i,r),n.attachToViewContainerRef(),Eu(nl(s),i,n),n}move(n,t){return this.insert(n,t)}indexOf(n){let t=$p(this._lContainer);return t!==null?t.indexOf(n):-1}remove(n){let t=this._adjustIndex(n,-1),r=yl(this._lContainer,t);r&&(mo(nl(this._lContainer),t),$g(r[S],r))}detach(n){let t=this._adjustIndex(n,-1),r=yl(this._lContainer,t);return r&&mo(nl(this._lContainer),t)!=null?new Qt(r):null}_adjustIndex(n,t=0){return n??this.length+t}};function $p(e){return e[Eo]}function nl(e){return e[Eo]||(e[Eo]=[])}function mm(e,n){let t,r=n[e.index];return Ke(r)?t=r:(t=im(r,n,null,e),n[e.index]=t,$l(n,t)),CI(t,n,e,r),new gm(t,e,n)}function EI(e,n){let t=e[K],r=t.createComment(""),o=Je(n,e),i=t.parentNode(o);return Vs(t,i,r,t.nextSibling(o),!1),r}var CI=bI,wI=()=>!1;function II(e,n,t){return wI(e,n,t)}function bI(e,n,t,r){if(e[Wt])return;let o;t.type&8?o=Be(r):o=EI(n,t),e[Wt]=o}var Il=class e{queryList;matches=null;constructor(n){this.queryList=n}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},bl=class e{queries;constructor(n=[]){this.queries=n}createEmbeddedView(n){let t=n.queries;if(t!==null){let r=n.contentQueries!==null?n.contentQueries[0]:t.length,o=[];for(let i=0;i<r;i++){let s=t.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(n){this.dirtyQueriesWithMatches(n)}detachView(n){this.dirtyQueriesWithMatches(n)}finishViewCreation(n){this.dirtyQueriesWithMatches(n)}dirtyQueriesWithMatches(n){for(let t=0;t<this.queries.length;t++)id(n,t).matches!==null&&this.queries[t].setDirty()}},$s=class{flags;read;predicate;constructor(n,t,r=null){this.flags=t,this.read=r,typeof n=="string"?this.predicate=xI(n):this.predicate=n}},_l=class e{queries;constructor(n=[]){this.queries=n}elementStart(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(n,t)}elementEnd(n){for(let t=0;t<this.queries.length;t++)this.queries[t].elementEnd(n)}embeddedTView(n){let t=null;for(let r=0;r<this.length;r++){let o=t!==null?t.length:0,i=this.getByIndex(r).embeddedTView(n,o);i&&(i.indexInDeclarationView=r,t!==null?t.push(i):t=[i])}return t!==null?new e(t):null}template(n,t){for(let r=0;r<this.queries.length;r++)this.queries[r].template(n,t)}getByIndex(n){return this.queries[n]}get length(){return this.queries.length}track(n){this.queries.push(n)}},Tl=class e{metadata;matches=null;indexInDeclarationView=-1;crossesNgTemplate=!1;_declarationNodeIndex;_appliesToNextNode=!0;constructor(n,t=-1){this.metadata=n,this._declarationNodeIndex=t}elementStart(n,t){this.isApplyingToNode(t)&&this.matchTNode(n,t)}elementEnd(n){this._declarationNodeIndex===n.index&&(this._appliesToNextNode=!1)}template(n,t){this.elementStart(n,t)}embeddedTView(n,t){return this.isApplyingToNode(n)?(this.crossesNgTemplate=!0,this.addMatch(-n.index,t),new e(this.metadata)):null}isApplyingToNode(n){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let t=this._declarationNodeIndex,r=n.parent;for(;r!==null&&r.type&8&&r.index!==t;)r=r.parent;return t===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(n,t){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(n,t,_I(t,i)),this.matchTNodeWithReadOption(n,t,Rs(t,n,i,!1,!1))}else r===tt?t.type&4&&this.matchTNodeWithReadOption(n,t,-1):this.matchTNodeWithReadOption(n,t,Rs(t,n,r,!1,!1))}matchTNodeWithReadOption(n,t,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===q||o===$e||o===tt&&t.type&4)this.addMatch(t.index,-2);else{let i=Rs(t,n,o,!1,!1);i!==null&&this.addMatch(t.index,i)}else this.addMatch(t.index,r)}}addMatch(n,t){this.matches===null?this.matches=[n,t]:this.matches.push(n,t)}};function _I(e,n){let t=e.localNames;if(t!==null){for(let r=0;r<t.length;r+=2)if(t[r]===n)return t[r+1]}return null}function TI(e,n){return e.type&11?br(e,n):e.type&4?td(e,n):null}function SI(e,n,t,r){return t===-1?TI(n,e):t===-2?MI(e,n,r):Mo(e,e[S],t,n)}function MI(e,n,t){if(t===q)return br(n,e);if(t===tt)return td(n,e);if(t===$e)return mm(n,e)}function vm(e,n,t,r){let o=n[ft].queries[r];if(o.matches===null){let i=e.data,s=t.matches,a=[];for(let c=0;s!==null&&c<s.length;c+=2){let u=s[c];if(u<0)a.push(null);else{let l=i[u];a.push(SI(n,l,s[c+1],t.metadata.read))}}o.matches=a}return o.matches}function Sl(e,n,t,r){let o=e.queries.getByIndex(t),i=o.matches;if(i!==null){let s=vm(e,n,o,t);for(let a=0;a<i.length;a+=2){let c=i[a];if(c>0)r.push(s[a/2]);else{let u=i[a+1],l=n[-c];for(let d=Ee;d<l.length;d++){let h=l[d];h[Gt]===h[ae]&&Sl(h[S],h,u,r)}if(l[In]!==null){let d=l[In];for(let h=0;h<d.length;h++){let f=d[h];Sl(f[S],f,u,r)}}}}}return r}function AI(e,n){return e[ft].queries[n].queryList}function ym(e,n,t){let r=new Ls((t&4)===4);return tp(e,n,r,r.destroy),(n[ft]??=new bl).queries.push(new Il(r))-1}function NI(e,n,t){let r=ge();return r.firstCreatePass&&(Dm(r,new $s(e,n,t),-1),(n&2)===2&&(r.staticViewQueries=!0)),ym(r,j(),n)}function RI(e,n,t,r){let o=ge();if(o.firstCreatePass){let i=ce();Dm(o,new $s(n,t,r),i.index),OI(o,e),(t&2)===2&&(o.staticContentQueries=!0)}return ym(o,j(),t)}function xI(e){return e.split(",").map(n=>n.trim())}function Dm(e,n,t){e.queries===null&&(e.queries=new _l),e.queries.track(new Tl(n,t))}function OI(e,n){let t=e.contentQueries||(e.contentQueries=[]),r=t.length?t[t.length-1]:-1;n!==r&&t.push(e.queries.length-1,n)}function id(e,n){return e.queries.getByIndex(n)}function kI(e,n){let t=e[S],r=id(t,n);return r.crossesNgTemplate?Sl(t,e,n,[]):vm(t,e,r,n)}var zp=new Set;function ca(e){zp.has(e)||(zp.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}var An=class{},ua=class{};var zs=class extends An{ngModuleType;_parent;_bootstrapComponents=[];_r3Injector;instance;destroyCbs=[];componentFactoryResolver=new Us(this);constructor(n,t,r,o=!0){super(),this.ngModuleType=n,this._parent=t;let i=Iu(n);this._bootstrapComponents=Og(i.bootstrap),this._r3Injector=Zu(n,t,[{provide:An,useValue:this},{provide:Oo,useValue:this.componentFactoryResolver},...r],_t(n),new Set(["environment"])),o&&this.resolveInjectorInitializers()}resolveInjectorInitializers(){this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(this.ngModuleType)}get injector(){return this._r3Injector}destroy(){let n=this._r3Injector;!n.destroyed&&n.destroy(),this.destroyCbs.forEach(t=>t()),this.destroyCbs=null}onDestroy(n){this.destroyCbs.push(n)}},Gs=class extends ua{moduleType;constructor(n){super(),this.moduleType=n}create(n){return new zs(this.moduleType,n,[])}};var No=class extends An{injector;componentFactoryResolver=new Us(this);instance=null;constructor(n){super();let t=new vn([...n.providers,{provide:An,useValue:this},{provide:Oo,useValue:this.componentFactoryResolver}],n.parent||lr(),n.debugName,new Set(["environment"]));this.injector=t,n.runEnvironmentInitializers&&t.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(n){this.injector.onDestroy(n)}};function Sr(e,n,t=null){return new No({providers:e,parent:n,debugName:t,runEnvironmentInitializers:!0}).injector}var PI=(()=>{class e{_injector;cachedInjectors=new Map;constructor(t){this._injector=t}getOrCreateStandaloneInjector(t){if(!t.standalone)return null;if(!this.cachedInjectors.has(t)){let r=_u(!1,t.type),o=r.length>0?Sr([r],this._injector,`Standalone[${t.type.name}]`):null;this.cachedInjectors.set(t,o)}return this.cachedInjectors.get(t)}ngOnDestroy(){try{for(let t of this.cachedInjectors.values())t!==null&&t.destroy()}finally{this.cachedInjectors.clear()}}static \u0275prov=w({token:e,providedIn:"environment",factory:()=>new e(I(G))})}return e})();function sd(e){return Ir(()=>{let n=Em(e),t=N(g({},n),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Pl.OnPush,directiveDefs:null,pipeDefs:null,dependencies:n.standalone&&e.dependencies||null,getStandaloneInjector:n.standalone?o=>o.get(PI).getOrCreateStandaloneInjector(t):null,getExternalStyles:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||Mt.Emulated,styles:e.styles||Oe,_:null,schemas:e.schemas||null,tView:null,id:""});n.standalone&&ca("NgStandalone"),Cm(t);let r=e.dependencies;return t.directiveDefs=Gp(r,FI),t.pipeDefs=Gp(r,Uh),t.id=VI(t),t})}function FI(e){return lt(e)||bu(e)}function ze(e){return Ir(()=>({type:e.type,bootstrap:e.bootstrap||Oe,declarations:e.declarations||Oe,imports:e.imports||Oe,exports:e.exports||Oe,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function LI(e,n){if(e==null)return $t;let t={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a,c;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i,c=o[3]||null):(i=o,s=o,a=ra.None,c=null),t[i]=[r,a,c],n[i]=s}return t}function jI(e){if(e==null)return $t;let n={};for(let t in e)e.hasOwnProperty(t)&&(n[e[t]]=t);return n}function F(e){return Ir(()=>{let n=Em(e);return Cm(n),n})}function Em(e){let n={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:n,inputConfig:e.inputs||$t,exportAs:e.exportAs||null,standalone:e.standalone??!0,signals:e.signals===!0,selectors:e.selectors||Oe,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,resolveHostDirectives:null,hostDirectives:null,inputs:LI(e.inputs,n),outputs:jI(e.outputs),debugInfo:null}}function Cm(e){e.features?.forEach(n=>n(e))}function Gp(e,n){return e?()=>{let t=typeof e=="function"?e():e,r=[];for(let o of t){let i=n(o);i!==null&&r.push(i)}return r}:null}function VI(e){let n=0,t=typeof e.consts=="function"?"":e.consts,r=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,t,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery];for(let i of r.join("|"))n=Math.imul(31,n)+i.charCodeAt(0)<<0;return n+=2147483648,"c"+n}function BI(e){return Object.getPrototypeOf(e.prototype).constructor}function Ce(e){let n=BI(e.type),t=!0,r=[e];for(;n;){let o;if(pt(e))o=n.\u0275cmp||n.\u0275dir;else{if(n.\u0275cmp)throw new D(903,!1);o=n.\u0275dir}if(o){if(t){r.push(o);let s=e;s.inputs=rl(e.inputs),s.declaredInputs=rl(e.declaredInputs),s.outputs=rl(e.outputs);let a=o.hostBindings;a&&GI(e,a);let c=o.viewQuery,u=o.contentQueries;if(c&&$I(e,c),u&&zI(e,u),UI(e,o),Rh(e.outputs,o.outputs),pt(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===Ce&&(t=!1)}}n=Object.getPrototypeOf(n)}HI(r)}function UI(e,n){for(let t in n.inputs){if(!n.inputs.hasOwnProperty(t)||e.inputs.hasOwnProperty(t))continue;let r=n.inputs[t];r!==void 0&&(e.inputs[t]=r,e.declaredInputs[t]=n.declaredInputs[t])}}function HI(e){let n=0,t=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=n+=o.hostVars,o.hostAttrs=Er(o.hostAttrs,t=Er(t,o.hostAttrs))}}function rl(e){return e===$t?{}:e===Oe?[]:e}function $I(e,n){let t=e.viewQuery;t?e.viewQuery=(r,o)=>{n(r,o),t(r,o)}:e.viewQuery=n}function zI(e,n){let t=e.contentQueries;t?e.contentQueries=(r,o,i)=>{n(r,o,i),t(r,o,i)}:e.contentQueries=n}function GI(e,n){let t=e.hostBindings;t?e.hostBindings=(r,o)=>{n(r,o),t(r,o)}:e.hostBindings=n}function wm(e,n,t,r,o,i,s,a){if(t.firstCreatePass){e.mergedAttrs=Er(e.mergedAttrs,e.attrs);let l=e.tView=Ul(2,e,o,i,s,t.directiveRegistry,t.pipeRegistry,null,t.schemas,t.consts,null);t.queries!==null&&(t.queries.template(t,e),l.queries=t.queries.embeddedTView(e))}a&&(e.flags|=a),vr(e,!1);let c=ZI(t,n,e,r);Is()&&Wl(t,n,c,e),Cr(c,n);let u=im(c,n,c,e);n[r+re]=u,$l(n,u),II(u,e,n)}function WI(e,n,t,r,o,i,s,a,c,u,l){let d=t+re,h;return n.firstCreatePass?(h=Tr(n,d,4,s||null,a||null),vs()&&lm(n,e,h,Zt(n.consts,u),Zl),sg(n,h)):h=n.data[d],wm(h,e,n,t,r,o,i,c),pr(h)&&ia(n,e,h),u!=null&&xo(e,h,l),h}function qI(e,n,t,r,o,i,s,a,c,u,l){let d=t+re,h;if(n.firstCreatePass){if(h=Tr(n,d,4,s||null,a||null),u!=null){let f=Zt(n.consts,u);h.localNames=[];for(let m=0;m<f.length;m+=2)h.localNames.push(f[m],-1)}}else h=n.data[d];return wm(h,e,n,t,r,o,i,c),u!=null&&xo(e,h,l),h}function Im(e,n,t,r,o,i,s,a){let c=j(),u=ge(),l=Zt(u.consts,i);return WI(c,u,e,n,t,r,o,l,void 0,s,a),Im}var ZI=YI;function YI(e,n,t,r){return Io(!0),n[K].createComment("")}var la=function(e){return e[e.CHANGE_DETECTION=0]="CHANGE_DETECTION",e[e.AFTER_NEXT_RENDER=1]="AFTER_NEXT_RENDER",e}(la||{}),Rn=new E(""),bm=!1,Ml=class extends ${__isAsync;destroyRef=void 0;pendingTasks=void 0;constructor(n=!1){super(),this.__isAsync=n,Wh()&&(this.destroyRef=p(Xe,{optional:!0})??void 0,this.pendingTasks=p(gt,{optional:!0})??void 0)}emit(n){let t=x(null);try{super.next(n)}finally{x(t)}}subscribe(n,t,r){let o=n,i=t||(()=>null),s=r;if(n&&typeof n=="object"){let c=n;o=c.next?.bind(c),i=c.error?.bind(c),s=c.complete?.bind(c)}this.__isAsync&&(i=this.wrapInTimeout(i),o&&(o=this.wrapInTimeout(o)),s&&(s=this.wrapInTimeout(s)));let a=super.subscribe({next:o,error:i,complete:s});return n instanceof X&&n.add(a),a}wrapInTimeout(n){return t=>{let r=this.pendingTasks?.add();setTimeout(()=>{try{n(t)}finally{r!==void 0&&this.pendingTasks?.remove(r)}})}}},z=Ml;function _m(e){let n,t;function r(){e=_o;try{t!==void 0&&typeof cancelAnimationFrame=="function"&&cancelAnimationFrame(t),n!==void 0&&clearTimeout(n)}catch{}}return n=setTimeout(()=>{e(),r()}),typeof requestAnimationFrame=="function"&&(t=requestAnimationFrame(()=>{e(),r()})),()=>r()}function Wp(e){return queueMicrotask(()=>e()),()=>{e=_o}}var ad="isAngularZone",Ws=ad+"_ID",QI=0,H=class e{hasPendingMacrotasks=!1;hasPendingMicrotasks=!1;isStable=!0;onUnstable=new z(!1);onMicrotaskEmpty=new z(!1);onStable=new z(!1);onError=new z(!1);constructor(n){let{enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:r=!1,shouldCoalesceRunChangeDetection:o=!1,scheduleInRootZone:i=bm}=n;if(typeof Zone>"u")throw new D(908,!1);Zone.assertZonePatched();let s=this;s._nesting=0,s._outer=s._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(s._inner=s._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(s._inner=s._inner.fork(Zone.longStackTraceZoneSpec)),s.shouldCoalesceEventChangeDetection=!o&&r,s.shouldCoalesceRunChangeDetection=o,s.callbackScheduled=!1,s.scheduleInRootZone=i,XI(s)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get(ad)===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new D(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new D(909,!1)}run(n,t,r){return this._inner.run(n,t,r)}runTask(n,t,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,n,KI,_o,_o);try{return i.runTask(s,t,r)}finally{i.cancelTask(s)}}runGuarded(n,t,r){return this._inner.runGuarded(n,t,r)}runOutsideAngular(n){return this._outer.run(n)}},KI={};function cd(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function JI(e){if(e.isCheckStableRunning||e.callbackScheduled)return;e.callbackScheduled=!0;function n(){_m(()=>{e.callbackScheduled=!1,Al(e),e.isCheckStableRunning=!0,cd(e),e.isCheckStableRunning=!1})}e.scheduleInRootZone?Zone.root.run(()=>{n()}):e._outer.run(()=>{n()}),Al(e)}function XI(e){let n=()=>{JI(e)},t=QI++;e._inner=e._inner.fork({name:"angular",properties:{[ad]:!0,[Ws]:t,[Ws+t]:!0},onInvokeTask:(r,o,i,s,a,c)=>{if(eb(c))return r.invokeTask(i,s,a,c);try{return qp(e),r.invokeTask(i,s,a,c)}finally{(e.shouldCoalesceEventChangeDetection&&s.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&n(),Zp(e)}},onInvoke:(r,o,i,s,a,c,u)=>{try{return qp(e),r.invoke(i,s,a,c,u)}finally{e.shouldCoalesceRunChangeDetection&&!e.callbackScheduled&&!tb(c)&&n(),Zp(e)}},onHasTask:(r,o,i,s)=>{r.hasTask(i,s),o===i&&(s.change=="microTask"?(e._hasPendingMicrotasks=s.microTask,Al(e),cd(e)):s.change=="macroTask"&&(e.hasPendingMacrotasks=s.macroTask))},onHandleError:(r,o,i,s)=>(r.handleError(i,s),e.runOutsideAngular(()=>e.onError.emit(s)),!1)})}function Al(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.callbackScheduled===!0?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function qp(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function Zp(e){e._nesting--,cd(e)}var qs=class{hasPendingMicrotasks=!1;hasPendingMacrotasks=!1;isStable=!0;onUnstable=new z;onMicrotaskEmpty=new z;onStable=new z;onError=new z;run(n,t,r){return n.apply(t,r)}runGuarded(n,t,r){return n.apply(t,r)}runOutsideAngular(n){return n()}runTask(n,t,r,o){return n.apply(t,r)}};function eb(e){return Tm(e,"__ignore_ng_zone__")}function tb(e){return Tm(e,"__scheduler_tick__")}function Tm(e,n){return!Array.isArray(e)||e.length!==1?!1:e[0]?.data?.[n]===!0}var ud=(()=>{class e{impl=null;execute(){this.impl?.execute()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),Sm=[0,1,2,3],Mm=(()=>{class e{ngZone=p(H);scheduler=p(Tt);errorHandler=p(Ye,{optional:!0});sequences=new Set;deferredRegistrations=new Set;executing=!1;constructor(){p(Rn,{optional:!0})}execute(){let t=this.sequences.size>0;t&&U(16),this.executing=!0;for(let r of Sm)for(let o of this.sequences)if(!(o.erroredOrDestroyed||!o.hooks[r]))try{o.pipelinedValue=this.ngZone.runOutsideAngular(()=>this.maybeTrace(()=>{let i=o.hooks[r];return i(o.pipelinedValue)},o.snapshot))}catch(i){o.erroredOrDestroyed=!0,this.errorHandler?.handleError(i)}this.executing=!1;for(let r of this.sequences)r.afterRun(),r.once&&(this.sequences.delete(r),r.destroy());for(let r of this.deferredRegistrations)this.sequences.add(r);this.deferredRegistrations.size>0&&this.scheduler.notify(7),this.deferredRegistrations.clear(),t&&U(17)}register(t){let{view:r}=t;r!==void 0?((r[wn]??=[]).push(t),mr(r),r[M]|=8192):this.executing?this.deferredRegistrations.add(t):this.addSequence(t)}addSequence(t){this.sequences.add(t),this.scheduler.notify(7)}unregister(t){this.executing&&this.sequences.has(t)?(t.erroredOrDestroyed=!0,t.pipelinedValue=void 0,t.once=!0):(this.sequences.delete(t),this.deferredRegistrations.delete(t))}maybeTrace(t,r){return r?r.run(la.AFTER_NEXT_RENDER,t):t()}static \u0275prov=w({token:e,providedIn:"root",factory:()=>new e})}return e})(),Zs=class{impl;hooks;view;once;snapshot;erroredOrDestroyed=!1;pipelinedValue=void 0;unregisterOnDestroy;constructor(n,t,r,o,i,s=null){this.impl=n,this.hooks=t,this.view=r,this.once=o,this.snapshot=s,this.unregisterOnDestroy=i?.onDestroy(()=>this.destroy())}afterRun(){this.erroredOrDestroyed=!1,this.pipelinedValue=void 0,this.snapshot?.dispose(),this.snapshot=null}destroy(){this.impl.unregister(this),this.unregisterOnDestroy?.();let n=this.view?.[wn];n&&(this.view[wn]=n.filter(t=>t!==this))}};function da(e,n){let t=n?.injector??p(fe);return ca("NgAfterNextRender"),rb(e,t,n,!0)}function nb(e){return e instanceof Function?[void 0,void 0,e,void 0]:[e.earlyRead,e.write,e.mixedReadWrite,e.read]}function rb(e,n,t,r){let o=n.get(ud);o.impl??=n.get(Mm);let i=n.get(Rn,null,{optional:!0}),s=t?.manualCleanup!==!0?n.get(Xe):null,a=n.get(_s,null,{optional:!0}),c=new Zs(o.impl,nb(e),a?.view,r,s,i?.snapshot(null));return o.impl.register(c),c}var ld=(()=>{class e{log(t){console.log(t)}warn(t){console.warn(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"platform"})}return e})();var dd=new E("");function en(e){return!!e&&typeof e.then=="function"}function fd(e){return!!e&&typeof e.subscribe=="function"}var Am=new E("");var hd=(()=>{class e{resolve;reject;initialized=!1;done=!1;donePromise=new Promise((t,r)=>{this.resolve=t,this.reject=r});appInits=p(Am,{optional:!0})??[];injector=p(fe);constructor(){}runInitializers(){if(this.initialized)return;let t=[];for(let o of this.appInits){let i=he(this.injector,o);if(en(i))t.push(i);else if(fd(i)){let s=new Promise((a,c)=>{i.subscribe({complete:a,error:c})});t.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(t).then(()=>{r()}).catch(o=>{this.reject(o)}),t.length===0&&r(),this.initialized=!0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),fa=new E("");function Nm(){Wc(()=>{let e="";throw new D(600,e)})}function Rm(e){return e.isBoundToModule}var ob=10;var nt=(()=>{class e{_runningTick=!1;_destroyed=!1;_destroyListeners=[];_views=[];internalErrorHandler=p(Me);afterRenderManager=p(ud);zonelessEnabled=p(bs);rootEffectScheduler=p(Ku);dirtyFlags=0;tracingSnapshot=null;allTestViews=new Set;autoDetectTestViews=new Set;includeAllTestViews=!1;afterTick=new $;get allViews(){return[...(this.includeAllTestViews?this.allTestViews:this.autoDetectTestViews).keys(),...this._views]}get destroyed(){return this._destroyed}componentTypes=[];components=[];internalPendingTask=p(gt);get isStable(){return this.internalPendingTask.hasPendingTasksObservable.pipe(R(t=>!t))}constructor(){p(Rn,{optional:!0})}whenStable(){let t;return new Promise(r=>{t=this.isStable.subscribe({next:o=>{o&&r()}})}).finally(()=>{t.unsubscribe()})}_injector=p(G);_rendererFactory=null;get injector(){return this._injector}bootstrap(t,r){return this.bootstrapImpl(t,r)}bootstrapImpl(t,r,o=fe.NULL){return this._injector.get(H).run(()=>{U(10);let s=t instanceof sa;if(!this._injector.get(hd).done){let m="";throw new D(405,m)}let c;s?c=t:c=this._injector.get(Oo).resolveComponentFactory(t),this.componentTypes.push(c.componentType);let u=Rm(c)?void 0:this._injector.get(An),l=r||c.selector,d=c.create(o,[],l,u),h=d.location.nativeElement,f=d.injector.get(dd,null);return f?.registerApplication(h),d.onDestroy(()=>{this.detachView(d.hostView),So(this.components,d),f?.unregisterApplication(h)}),this._loadComponent(d),U(11,d),d})}tick(){this.zonelessEnabled||(this.dirtyFlags|=1),this._tick()}_tick(){U(12),this.tracingSnapshot!==null?this.tracingSnapshot.run(la.CHANGE_DETECTION,this.tickImpl):this.tickImpl()}tickImpl=()=>{if(this._runningTick)throw new D(101,!1);let t=x(null);try{this._runningTick=!0,this.synchronize()}finally{this._runningTick=!1,this.tracingSnapshot?.dispose(),this.tracingSnapshot=null,x(t),this.afterTick.next(),U(13)}};synchronize(){this._rendererFactory===null&&!this._injector.destroyed&&(this._rendererFactory=this._injector.get(Mn,null,{optional:!0}));let t=0;for(;this.dirtyFlags!==0&&t++<ob;)U(14),this.synchronizeOnce(),U(15)}synchronizeOnce(){this.dirtyFlags&16&&(this.dirtyFlags&=-17,this.rootEffectScheduler.flush());let t=!1;if(this.dirtyFlags&7){let r=!!(this.dirtyFlags&1);this.dirtyFlags&=-8,this.dirtyFlags|=8;for(let{_lView:o}of this.allViews){if(!r&&!Co(o))continue;let i=r&&!this.zonelessEnabled?0:1;Xl(o,i),t=!0}if(this.dirtyFlags&=-5,this.syncDirtyFlagsWithViews(),this.dirtyFlags&23)return}t||(this._rendererFactory?.begin?.(),this._rendererFactory?.end?.()),this.dirtyFlags&8&&(this.dirtyFlags&=-9,this.afterRenderManager.execute()),this.syncDirtyFlagsWithViews()}syncDirtyFlagsWithViews(){if(this.allViews.some(({_lView:t})=>Co(t))){this.dirtyFlags|=2;return}else this.dirtyFlags&=-8}attachView(t){let r=t;this._views.push(r),r.attachToAppRef(this)}detachView(t){let r=t;So(this._views,r),r.detachFromAppRef()}_loadComponent(t){this.attachView(t.hostView);try{this.tick()}catch(o){this.internalErrorHandler(o)}this.components.push(t),this._injector.get(fa,[]).forEach(o=>o(t))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(t=>t()),this._views.slice().forEach(t=>t.destroy())}finally{this._destroyed=!0,this._views=[],this._destroyListeners=[]}}onDestroy(t){return this._destroyListeners.push(t),()=>So(this._destroyListeners,t)}destroy(){if(this._destroyed)throw new D(406,!1);let t=this._injector;t.destroy&&!t.destroyed&&t.destroy()}get viewCount(){return this._views.length}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function So(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function tn(e,n,t,r){let o=j(),i=ys();if(aa(o,i,n)){let s=ge(),a=qu();_w(a,o,e,n,t,r)}return tn}function xm(e,n,t){let r=j(),o=ys();if(aa(r,o,n)){let i=ge(),s=qu();Dw(s,r,e,n,r[K],t)}return xm}function Yp(e,n,t,r,o){Kl(n,e,t,o?"class":"style",r)}function pd(e,n,t,r){let o=j(),i=o[S],s=e+re,a=i.firstCreatePass?nd(s,o,2,n,Zl,vs(),t,r):i.data[s];if(Yl(a,o,e,n,ib),pr(a)){let c=o[S];ia(c,o,a),Ll(c,a,o)}return r!=null&&xo(o,a),pd}function gd(){let e=ge(),n=ce(),t=Ql(n);return e.firstCreatePass&&rd(e,t),ip(t)&&sp(),op(),t.classesWithoutHost!=null&&rC(t)&&Yp(e,t,j(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&oC(t)&&Yp(e,t,j(),t.stylesWithoutHost,!1),gd}function ha(e,n,t,r){return pd(e,n,t,r),gd(),ha}var ib=(e,n,t,r,o)=>(Io(!0),Fg(n[K],r,Cp()));function md(e,n,t){let r=j(),o=r[S],i=e+re,s=o.firstCreatePass?nd(i,r,8,"ng-container",Zl,vs(),n,t):o.data[i];if(Yl(s,r,e,"ng-container",Pm),pr(s)){let a=r[S];ia(a,r,s),Ll(a,s,r)}return t!=null&&xo(r,s),md}function pa(){let e=ge(),n=ce(),t=Ql(n);return e.firstCreatePass&&rd(e,t),pa}function Om(e,n,t){return md(e,n,t),pa(),Om}function km(e,n,t){let r=j(),o=r[S],i=e+re,s=o.firstCreatePass?aI(i,o,8,"ng-container",n,t):o.data[i];return Yl(s,r,e,"ng-container",Pm),t!=null&&xo(r,s),km}function sb(){let e=ce(),n=Ql(e);return pa}var Pm=(e,n,t,r,o)=>(Io(!0),YC(n[K],""));function ab(){return j()}var ko="en-US";var cb=ko;function Fm(e){typeof e=="string"&&(cb=e.toLowerCase().replace(/_/g,"-"))}function me(e,n,t){let r=j(),o=ge(),i=ce();return ub(o,r,r[K],i,e,n,t),me}function ub(e,n,t,r,o,i,s){let a=!0,c=null;if((r.type&3||s)&&(c??=tl(r,n,i),cI(r,e,n,s,t,o,i,c)&&(a=!1)),a){let u=r.outputs?.[o],l=r.hostDirectiveOutputs?.[o];if(l&&l.length)for(let d=0;d<l.length;d+=2){let h=l[d],f=l[d+1];c??=tl(r,n,i),Up(r,n,h,f,o,c)}if(u&&u.length)for(let d of u)c??=tl(r,n,i),Up(r,n,d,o,o,c)}}function lb(e=1){return Ep(e)}function db(e,n){let t=null,r=BC(e);for(let o=0;o<n.length;o++){let i=n[o];if(i==="*"){t=o;continue}if(r===null?Pg(e,i,!0):$C(r,i))return o}return t}function fb(e){let n=j()[Se][De];if(!n.projection){let t=e?e.length:1,r=n.projection=jh(t,null),o=r.slice(),i=n.child;for(;i!==null;){if(i.type!==128){let s=e?db(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i)}i=i.next}}}function hb(e,n=0,t,r,o,i){let s=j(),a=ge(),c=r?e+1:null;c!==null&&qI(s,a,c,r,o,i,null,t);let u=Tr(a,re+e,16,null,t||null);u.projection===null&&(u.projection=n),Bu();let d=!s[dr]||Lu();s[Se][De].projection[u.projection]===null&&c!==null?pb(s,a,c):d&&!ea(u)&&fw(a,s,u)}function pb(e,n,t){let r=re+t,o=n.data[r],i=e[r],s=Dl(i,o.tView.ssrId),a=Yg(e,o,void 0,{dehydratedView:s});sm(i,a,0,ml(o,s))}function Po(e,n,t,r){RI(e,n,t,r)}function vd(e,n,t){NI(e,n,t)}function Mr(e){let n=j(),t=ge(),r=zu();Es(r+1);let o=id(t,r);if(e.dirty&&Kh(n)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=kI(n,r);e.reset(i,vC),e.notifyOnChanges()}return!0}return!1}function Ar(){return AI(j(),zu())}function gb(e){let n=lp();return Qh(n,re+e)}function Ms(e,n){return e<<17|n<<2}function Nn(e){return e>>17&32767}function mb(e){return(e&2)==2}function vb(e,n){return e&131071|n<<17}function Nl(e){return e|2}function wr(e){return(e&131068)>>2}function ol(e,n){return e&-131069|n<<2}function yb(e){return(e&1)===1}function Rl(e){return e|1}function Db(e,n,t,r,o,i){let s=i?n.classBindings:n.styleBindings,a=Nn(s),c=wr(s);e[r]=t;let u=!1,l;if(Array.isArray(t)){let d=t;l=d[1],(l===null||cr(d,l)>0)&&(u=!0)}else l=t;if(o)if(c!==0){let h=Nn(e[a+1]);e[r+1]=Ms(h,a),h!==0&&(e[h+1]=ol(e[h+1],r)),e[a+1]=vb(e[a+1],r)}else e[r+1]=Ms(a,0),a!==0&&(e[a+1]=ol(e[a+1],r)),a=r;else e[r+1]=Ms(c,0),a===0?a=r:e[c+1]=ol(e[c+1],r),c=r;u&&(e[r+1]=Nl(e[r+1])),Qp(e,l,r,!0),Qp(e,l,r,!1),Eb(n,l,e,r,i),s=Ms(a,c),i?n.classBindings=s:n.styleBindings=s}function Eb(e,n,t,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof n=="string"&&cr(i,n)>=0&&(t[r+1]=Rl(t[r+1]))}function Qp(e,n,t,r){let o=e[t+1],i=n===null,s=r?Nn(o):wr(o),a=!1;for(;s!==0&&(a===!1||i);){let c=e[s],u=e[s+1];Cb(c,n)&&(a=!0,e[s+1]=r?Rl(u):Nl(u)),s=r?Nn(u):wr(u)}a&&(e[t+1]=r?Nl(o):Rl(o))}function Cb(e,n){return e===null||n==null||(Array.isArray(e)?e[1]:e)===n?!0:Array.isArray(e)&&typeof n=="string"?cr(e,n)>=0:!1}function Fo(e,n){return wb(e,n,null,!0),Fo}function wb(e,n,t,r){let o=j(),i=ge(),s=fp(2);if(i.firstUpdatePass&&bb(i,e,s,r),n!==Xt&&aa(o,s,n)){let a=i.data[_n()];Ab(i,a,o,o[K],e,o[s+1]=Nb(n,t),r,s)}}function Ib(e,n){return n>=e.expandoStartIndex}function bb(e,n,t,r){let o=e.data;if(o[t+1]===null){let i=o[_n()],s=Ib(e,t);Rb(i,r)&&n===null&&!s&&(n=!1),n=_b(o,i,n,r),Db(o,i,n,t,s,r)}}function _b(e,n,t,r){let o=mp(e),i=r?n.residualClasses:n.residualStyles;if(o===null)(r?n.classBindings:n.styleBindings)===0&&(t=il(null,e,n,t,r),t=Ro(t,n.attrs,r),i=null);else{let s=n.directiveStylingLast;if(s===-1||e[s]!==o)if(t=il(o,e,n,t,r),i===null){let c=Tb(e,n,r);c!==void 0&&Array.isArray(c)&&(c=il(null,e,n,c[1],r),c=Ro(c,n.attrs,r),Sb(e,n,r,c))}else i=Mb(e,n,r)}return i!==void 0&&(r?n.residualClasses=i:n.residualStyles=i),t}function Tb(e,n,t){let r=t?n.classBindings:n.styleBindings;if(wr(r)!==0)return e[Nn(r)]}function Sb(e,n,t,r){let o=t?n.classBindings:n.styleBindings;e[Nn(o)]=r}function Mb(e,n,t){let r,o=n.directiveEnd;for(let i=1+n.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Ro(r,s,t)}return Ro(r,n.attrs,t)}function il(e,n,t,r,o){let i=null,s=t.directiveEnd,a=t.directiveStylingLast;for(a===-1?a=t.directiveStart:a++;a<s&&(i=n[a],r=Ro(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(t.directiveStylingLast=a),r}function Ro(e,n,t){let r=t?1:2,o=-1;if(n!==null)for(let i=0;i<n.length;i++){let s=n[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),Bh(e,s,t?!0:n[++i]))}return e===void 0?null:e}function Ab(e,n,t,r,o,i,s,a){if(!(n.type&3))return;let c=e.data,u=c[a+1],l=yb(u)?Kp(c,n,t,o,wr(u),s):void 0;if(!Ys(l)){Ys(i)||mb(u)&&(i=Kp(c,null,t,o,a,s));let d=Ru(_n(),t);pw(r,s,d,o,i)}}function Kp(e,n,t,r,o,i){let s=n===null,a;for(;o>0;){let c=e[o],u=Array.isArray(c),l=u?c[1]:c,d=l===null,h=t[o+1];h===Xt&&(h=d?Oe:void 0);let f=d?fs(h,r):l===r?h:void 0;if(u&&!Ys(f)&&(f=fs(c,r)),Ys(f)&&(a=f,s))return a;let m=e[o+1];o=s?Nn(m):wr(m)}if(n!==null){let c=i?n.residualClasses:n.residualStyles;c!=null&&(a=fs(c,r))}return a}function Ys(e){return e!==void 0}function Nb(e,n){return e==null||e===""||(typeof n=="string"?e=e+n:typeof e=="object"&&(e=_t(ta(e)))),e}function Rb(e,n){return(e.flags&(n?8:16))!==0}function xb(e,n=""){let t=j(),r=ge(),o=e+re,i=r.firstCreatePass?Tr(r,o,1,n,null):r.data[o],s=Ob(r,t,i,n,e);t[o]=s,Is()&&Wl(r,t,s,i),vr(i,!1)}var Ob=(e,n,t,r,o)=>(Io(!0),qC(n[K],r));function kb(e,n,t,r=""){return aa(e,ys(),t)?n+go(t)+r:Xt}function Lm(e){return yd("",e),Lm}function yd(e,n,t){let r=j(),o=kb(r,e,n,t);return o!==Xt&&Pb(r,_n(),o),yd}function Pb(e,n,t){let r=Ru(n,e);ZC(e[K],r,t)}function Fb(e,n,t){let r=ge();if(r.firstCreatePass){let o=pt(e);xl(t,r.data,r.blueprint,o,!0),xl(n,r.data,r.blueprint,o,!1)}}function xl(e,n,t,r,o){if(e=de(e),Array.isArray(e))for(let i=0;i<e.length;i++)xl(e[i],n,t,r,o);else{let i=ge(),s=j(),a=ce(),c=mn(e)?e:de(e.provide),u=Su(e),l=a.providerIndexes&1048575,d=a.directiveStart,h=a.providerIndexes>>20;if(mn(e)||!e.multi){let f=new Sn(u,o,v,null),m=al(c,n,o?l:l+h,d);m===-1?(ul(Fs(a,s),i,c),sl(i,e,n.length),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(f),s.push(f)):(t[m]=f,s[m]=f)}else{let f=al(c,n,l+h,d),m=al(c,n,l,l+h),b=f>=0&&t[f],y=m>=0&&t[m];if(o&&!y||!o&&!b){ul(Fs(a,s),i,c);let C=Vb(o?jb:Lb,t.length,o,r,u,e);!o&&y&&(t[m].providerFactory=C),sl(i,e,n.length,0),n.push(c),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),t.push(C),s.push(C)}else{let C=jm(t[o?m:f],u,!o&&r);sl(i,e,f>-1?f:m,C)}!o&&r&&y&&t[m].componentProviders++}}}function sl(e,n,t,r){let o=mn(n),i=Gh(n);if(o||i){let c=(i?de(n.useClass):n).prototype.ngOnDestroy;if(c){let u=e.destroyHooks||(e.destroyHooks=[]);if(!o&&n.multi){let l=u.indexOf(t);l===-1?u.push(t,[r,c]):u[l+1].push(r,c)}else u.push(t,c)}}}function jm(e,n,t){return t&&e.componentProviders++,e.multi.push(n)-1}function al(e,n,t,r){for(let o=t;o<r;o++)if(n[o]===e)return o;return-1}function Lb(e,n,t,r,o){return Ol(this.multi,[])}function jb(e,n,t,r,o){let i=this.multi,s;if(this.providerFactory){let a=this.providerFactory.componentProviders,c=Mo(r,r[S],this.providerFactory.index,o);s=c.slice(0,a),Ol(i,s);for(let u=a;u<c.length;u++)s.push(c[u])}else s=[],Ol(i,s);return s}function Ol(e,n){for(let t=0;t<e.length;t++){let r=e[t];n.push(r())}return n}function Vb(e,n,t,r,o,i){let s=new Sn(e,t,v,null);return s.multi=[],s.index=n,s.componentProviders=0,jm(s,o,r&&!t),s}function rt(e,n=[]){return t=>{t.providersResolver=(r,o)=>Fb(r,o?o(e):e,n)}}var Qs=class{ngModuleFactory;componentFactories;constructor(n,t){this.ngModuleFactory=n,this.componentFactories=t}},Dd=(()=>{class e{compileModuleSync(t){return new Gs(t)}compileModuleAsync(t){return Promise.resolve(this.compileModuleSync(t))}compileModuleAndAllComponentsSync(t){let r=this.compileModuleSync(t),o=Iu(t),i=Og(o.declarations).reduce((s,a)=>{let c=lt(a);return c&&s.push(new Kt(c)),s},[]);return new Qs(r,i)}compileModuleAndAllComponentsAsync(t){return Promise.resolve(this.compileModuleAndAllComponentsSync(t))}clearCache(){}clearCacheFor(t){}getModuleId(t){}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Bb=(()=>{class e{zone=p(H);changeDetectionScheduler=p(Tt);applicationRef=p(nt);applicationErrorHandler=p(Me);_onMicrotaskEmptySubscription;initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.changeDetectionScheduler.runningTick||this.zone.run(()=>{try{this.applicationRef.dirtyFlags|=1,this.applicationRef._tick()}catch(t){this.applicationErrorHandler(t)}})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Vm({ngZoneFactory:e,ignoreChangesOutsideZone:n,scheduleInRootZone:t}){return e??=()=>new H(N(g({},Bm()),{scheduleInRootZone:t})),[{provide:H,useFactory:e},{provide:zt,multi:!0,useFactory:()=>{let r=p(Bb,{optional:!0});return()=>r.initialize()}},{provide:zt,multi:!0,useFactory:()=>{let r=p(Ub);return()=>{r.initialize()}}},n===!0?{provide:Yu,useValue:!0}:[],{provide:Qu,useValue:t??bm},{provide:Me,useFactory:()=>{let r=p(H),o=p(G),i;return s=>{r.runOutsideAngular(()=>{o.destroyed&&!i?setTimeout(()=>{throw s}):(i??=o.get(Ye),i.handleError(s))})}}}]}function Bm(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Ub=(()=>{class e{subscription=new X;initialized=!1;zone=p(H);pendingTasks=p(gt);initialize(){if(this.initialized)return;this.initialized=!0;let t=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(t=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{H.assertNotInAngularZone(),queueMicrotask(()=>{t!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(t),t=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{H.assertInAngularZone(),t??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var Um=(()=>{class e{applicationErrorHandler=p(Me);appRef=p(nt);taskService=p(gt);ngZone=p(H);zonelessEnabled=p(bs);tracing=p(Rn,{optional:!0});disableScheduling=p(Yu,{optional:!0})??!1;zoneIsDefined=typeof Zone<"u"&&!!Zone.root.run;schedulerTickApplyArgs=[{data:{__scheduler_tick__:!0}}];subscriptions=new X;angularZoneId=this.zoneIsDefined?this.ngZone._inner?.get(Ws):null;scheduleInRootZone=!this.zonelessEnabled&&this.zoneIsDefined&&(p(Qu,{optional:!0})??!1);cancelScheduledCallback=null;useMicrotaskScheduler=!1;runningTick=!1;pendingRenderTaskId=null;constructor(){this.subscriptions.add(this.appRef.afterTick.subscribe(()=>{this.runningTick||this.cleanup()})),this.subscriptions.add(this.ngZone.onUnstable.subscribe(()=>{this.runningTick||this.cleanup()})),this.disableScheduling||=!this.zonelessEnabled&&(this.ngZone instanceof qs||!this.zoneIsDefined)}notify(t){if(!this.zonelessEnabled&&t===5)return;let r=!1;switch(t){case 0:{this.appRef.dirtyFlags|=2;break}case 3:case 2:case 4:case 5:case 1:{this.appRef.dirtyFlags|=4;break}case 6:{this.appRef.dirtyFlags|=2,r=!0;break}case 12:{this.appRef.dirtyFlags|=16,r=!0;break}case 13:{this.appRef.dirtyFlags|=2,r=!0;break}case 11:{r=!0;break}case 9:case 8:case 7:case 10:default:this.appRef.dirtyFlags|=8}if(this.appRef.tracingSnapshot=this.tracing?.snapshot(this.appRef.tracingSnapshot)??null,!this.shouldScheduleTick(r))return;let o=this.useMicrotaskScheduler?Wp:_m;this.pendingRenderTaskId=this.taskService.add(),this.scheduleInRootZone?this.cancelScheduledCallback=Zone.root.run(()=>o(()=>this.tick())):this.cancelScheduledCallback=this.ngZone.runOutsideAngular(()=>o(()=>this.tick()))}shouldScheduleTick(t){return!(this.disableScheduling&&!t||this.appRef.destroyed||this.pendingRenderTaskId!==null||this.runningTick||this.appRef._runningTick||!this.zonelessEnabled&&this.zoneIsDefined&&Zone.current.get(Ws+this.angularZoneId))}tick(){if(this.runningTick||this.appRef.destroyed)return;if(this.appRef.dirtyFlags===0){this.cleanup();return}!this.zonelessEnabled&&this.appRef.dirtyFlags&7&&(this.appRef.dirtyFlags|=1);let t=this.taskService.add();try{this.ngZone.run(()=>{this.runningTick=!0,this.appRef._tick()},void 0,this.schedulerTickApplyArgs)}catch(r){this.taskService.remove(t),this.applicationErrorHandler(r)}finally{this.cleanup()}this.useMicrotaskScheduler=!0,Wp(()=>{this.useMicrotaskScheduler=!1,this.taskService.remove(t)})}ngOnDestroy(){this.subscriptions.unsubscribe(),this.cleanup()}cleanup(){if(this.runningTick=!1,this.cancelScheduledCallback?.(),this.cancelScheduledCallback=null,this.pendingRenderTaskId!==null){let t=this.pendingRenderTaskId;this.pendingRenderTaskId=null,this.taskService.remove(t)}}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function Hb(){return typeof $localize<"u"&&$localize.locale||ko}var ga=new E("",{providedIn:"root",factory:()=>p(ga,{optional:!0,skipSelf:!0})||Hb()});function Ge(e){return Mh(e)}function Nr(e,n){return ns(e,n?.equal)}var Hm=class{[ye];constructor(n){this[ye]=n}destroy(){this[ye].destroy()}};var Wm=Symbol("InputSignalNode#UNSET"),o_=N(g({},rs),{transformFn:void 0,applyValueToInputSignal(e,n){ir(e,n)}});function qm(e,n){let t=Object.create(o_);t.value=e,t.transformFn=n?.transform;function r(){if(rr(t),t.value===Wm){let o=null;throw new D(-950,o)}return t.value}return r[ye]=t,r}var va=class{attributeName;constructor(n){this.attributeName=n}__NG_ELEMENT_ID__=()=>Jt(this.attributeName);toString(){return`HostAttributeToken ${this.attributeName}`}},i_=new E("");i_.__NG_ELEMENT_ID__=e=>{let n=ce();if(n===null)throw new D(204,!1);if(n.type&2)return n.value;if(e&8)return null;throw new D(204,!1)};function $m(e,n){return qm(e,n)}function s_(e){return qm(Wm,e)}var Zm=($m.required=s_,$m);var Ed=new E(""),a_=new E("");function Lo(e){return!e.moduleRef}function c_(e){let n=Lo(e)?e.r3Injector:e.moduleRef.injector,t=n.get(H);return t.run(()=>{Lo(e)?e.r3Injector.resolveInjectorInitializers():e.moduleRef.resolveInjectorInitializers();let r=n.get(Me),o;if(t.runOutsideAngular(()=>{o=t.onError.subscribe({next:r})}),Lo(e)){let i=()=>n.destroy(),s=e.platformInjector.get(Ed);s.add(i),n.onDestroy(()=>{o.unsubscribe(),s.delete(i)})}else{let i=()=>e.moduleRef.destroy(),s=e.platformInjector.get(Ed);s.add(i),e.moduleRef.onDestroy(()=>{So(e.allPlatformModules,e.moduleRef),o.unsubscribe(),s.delete(i)})}return l_(r,t,()=>{let i=n.get(gt),s=i.add(),a=n.get(hd);return a.runInitializers(),a.donePromise.then(()=>{let c=n.get(ga,ko);if(Fm(c||ko),!n.get(a_,!0))return Lo(e)?n.get(nt):(e.allPlatformModules.push(e.moduleRef),e.moduleRef);if(Lo(e)){let l=n.get(nt);return e.rootComponent!==void 0&&l.bootstrap(e.rootComponent),l}else return u_?.(e.moduleRef,e.allPlatformModules),e.moduleRef}).finally(()=>void i.remove(s))})})}var u_;function l_(e,n,t){try{let r=t();return en(r)?r.catch(o=>{throw n.runOutsideAngular(()=>e(o)),o}):r}catch(r){throw n.runOutsideAngular(()=>e(r)),r}}var ma=null;function d_(e=[],n){return fe.create({name:n,providers:[{provide:vo,useValue:"platform"},{provide:Ed,useValue:new Set([()=>ma=null])},...e]})}function f_(e=[]){if(ma)return ma;let n=d_(e);return ma=n,Nm(),h_(n),n}function h_(e){let n=e.get(Js,null);he(e,()=>{n?.forEach(t=>t())})}var ot=(()=>{class e{static __NG_ELEMENT_ID__=p_}return e})();function p_(e){return g_(ce(),j(),(e&16)===16)}function g_(e,n,t){if(qt(e)&&!t){let r=Ue(e.index,n);return new Qt(r,r)}else if(e.type&175){let r=n[Se];return new Qt(r,n)}return null}var Cd=class{constructor(){}supports(n){return od(n)}create(n){return new wd(n)}},m_=(e,n)=>n,wd=class{length=0;collection;_linkedRecords=null;_unlinkedRecords=null;_previousItHead=null;_itHead=null;_itTail=null;_additionsHead=null;_additionsTail=null;_movesHead=null;_movesTail=null;_removalsHead=null;_removalsTail=null;_identityChangesHead=null;_identityChangesTail=null;_trackByFn;constructor(n){this._trackByFn=n||m_}forEachItem(n){let t;for(t=this._itHead;t!==null;t=t._next)n(t)}forEachOperation(n){let t=this._itHead,r=this._removalsHead,o=0,i=null;for(;t||r;){let s=!r||t&&t.currentIndex<zm(r,o,i)?t:r,a=zm(s,o,i),c=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(t=t._next,s.previousIndex==null)o++;else{i||(i=[]);let u=a-o,l=c-o;if(u!=l){for(let h=0;h<u;h++){let f=h<i.length?i[h]:i[h]=0,m=f+h;l<=m&&m<u&&(i[h]=f+1)}let d=s.previousIndex;i[d]=l-u}}a!==c&&n(s,a,c)}}forEachPreviousItem(n){let t;for(t=this._previousItHead;t!==null;t=t._nextPrevious)n(t)}forEachAddedItem(n){let t;for(t=this._additionsHead;t!==null;t=t._nextAdded)n(t)}forEachMovedItem(n){let t;for(t=this._movesHead;t!==null;t=t._nextMoved)n(t)}forEachRemovedItem(n){let t;for(t=this._removalsHead;t!==null;t=t._nextRemoved)n(t)}forEachIdentityChange(n){let t;for(t=this._identityChangesHead;t!==null;t=t._nextIdentityChange)n(t)}diff(n){if(n==null&&(n=[]),!od(n))throw new D(900,!1);return this.check(n)?this:null}onDestroy(){}check(n){this._reset();let t=this._itHead,r=!1,o,i,s;if(Array.isArray(n)){this.length=n.length;for(let a=0;a<this.length;a++)i=n[a],s=this._trackByFn(a,i),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,i,s,a),r=!0):(r&&(t=this._verifyReinsertion(t,i,s,a)),Object.is(t.item,i)||this._addIdentityChange(t,i)),t=t._next}else o=0,fm(n,a=>{s=this._trackByFn(o,a),t===null||!Object.is(t.trackById,s)?(t=this._mismatch(t,a,s,o),r=!0):(r&&(t=this._verifyReinsertion(t,a,s,o)),Object.is(t.item,a)||this._addIdentityChange(t,a)),t=t._next,o++}),this.length=o;return this._truncate(t),this.collection=n,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let n;for(n=this._previousItHead=this._itHead;n!==null;n=n._next)n._nextPrevious=n._next;for(n=this._additionsHead;n!==null;n=n._nextAdded)n.previousIndex=n.currentIndex;for(this._additionsHead=this._additionsTail=null,n=this._movesHead;n!==null;n=n._nextMoved)n.previousIndex=n.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(n,t,r,o){let i;return n===null?i=this._itTail:(i=n._prev,this._remove(n)),n=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._reinsertAfter(n,i,o)):(n=this._linkedRecords===null?null:this._linkedRecords.get(r,o),n!==null?(Object.is(n.item,t)||this._addIdentityChange(n,t),this._moveAfter(n,i,o)):n=this._addAfter(new Id(t,r),i,o)),n}_verifyReinsertion(n,t,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?n=this._reinsertAfter(i,n._prev,o):n.currentIndex!=o&&(n.currentIndex=o,this._addToMoves(n,o)),n}_truncate(n){for(;n!==null;){let t=n._next;this._addToRemovals(this._unlink(n)),n=t}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(n,t,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(n);let o=n._prevRemoved,i=n._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(n,t,r),this._addToMoves(n,r),n}_moveAfter(n,t,r){return this._unlink(n),this._insertAfter(n,t,r),this._addToMoves(n,r),n}_addAfter(n,t,r){return this._insertAfter(n,t,r),this._additionsTail===null?this._additionsTail=this._additionsHead=n:this._additionsTail=this._additionsTail._nextAdded=n,n}_insertAfter(n,t,r){let o=t===null?this._itHead:t._next;return n._next=o,n._prev=t,o===null?this._itTail=n:o._prev=n,t===null?this._itHead=n:t._next=n,this._linkedRecords===null&&(this._linkedRecords=new ya),this._linkedRecords.put(n),n.currentIndex=r,n}_remove(n){return this._addToRemovals(this._unlink(n))}_unlink(n){this._linkedRecords!==null&&this._linkedRecords.remove(n);let t=n._prev,r=n._next;return t===null?this._itHead=r:t._next=r,r===null?this._itTail=t:r._prev=t,n}_addToMoves(n,t){return n.previousIndex===t||(this._movesTail===null?this._movesTail=this._movesHead=n:this._movesTail=this._movesTail._nextMoved=n),n}_addToRemovals(n){return this._unlinkedRecords===null&&(this._unlinkedRecords=new ya),this._unlinkedRecords.put(n),n.currentIndex=null,n._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=n,n._prevRemoved=null):(n._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=n),n}_addIdentityChange(n,t){return n.item=t,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=n:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=n,n}},Id=class{item;trackById;currentIndex=null;previousIndex=null;_nextPrevious=null;_prev=null;_next=null;_prevDup=null;_nextDup=null;_prevRemoved=null;_nextRemoved=null;_nextAdded=null;_nextMoved=null;_nextIdentityChange=null;constructor(n,t){this.item=n,this.trackById=t}},bd=class{_head=null;_tail=null;add(n){this._head===null?(this._head=this._tail=n,n._nextDup=null,n._prevDup=null):(this._tail._nextDup=n,n._prevDup=this._tail,n._nextDup=null,this._tail=n)}get(n,t){let r;for(r=this._head;r!==null;r=r._nextDup)if((t===null||t<=r.currentIndex)&&Object.is(r.trackById,n))return r;return null}remove(n){let t=n._prevDup,r=n._nextDup;return t===null?this._head=r:t._nextDup=r,r===null?this._tail=t:r._prevDup=t,this._head===null}},ya=class{map=new Map;put(n){let t=n.trackById,r=this.map.get(t);r||(r=new bd,this.map.set(t,r)),r.add(n)}get(n,t){let r=n,o=this.map.get(r);return o?o.get(n,t):null}remove(n){let t=n.trackById;return this.map.get(t).remove(n)&&this.map.delete(t),n}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function zm(e,n,t){let r=e.previousIndex;if(r===null)return r;let o=0;return t&&r<t.length&&(o=t[r]),r+n+o}function Gm(){return new _d([new Cd])}var _d=(()=>{class e{factories;static \u0275prov=w({token:e,providedIn:"root",factory:Gm});constructor(t){this.factories=t}static create(t,r){if(r!=null){let o=r.factories.slice();t=t.concat(o)}return new e(t)}static extend(t){return{provide:e,useFactory:r=>e.create(t,r||Gm()),deps:[[e,new tg,new eg]]}}find(t){let r=this.factories.find(o=>o.supports(t));if(r!=null)return r;throw new D(901,!1)}}return e})();function Ym(e){U(8);try{let{rootComponent:n,appProviders:t,platformProviders:r}=e,o=f_(r),i=[Vm({}),{provide:Tt,useExisting:Um},Ip,...t||[]],s=new No({providers:i,parent:o,debugName:"",runEnvironmentInitializers:!1});return c_({r3Injector:s.injector,platformInjector:o,rootComponent:n})}catch(n){return Promise.reject(n)}finally{U(9)}}function Rr(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function Qm(e,n){let t=lt(e),r=n.elementInjector||lr();return new Kt(t).create(r,n.projectableNodes,n.hostElement,n.environmentInjector,n.directives,n.bindings)}function Td(e){let n=lt(e);if(!n)return null;let t=new Kt(n);return{get selector(){return t.selector},get type(){return t.componentType},get inputs(){return t.inputs},get outputs(){return t.outputs},get ngContentSelectors(){return t.ngContentSelectors},get isStandalone(){return n.standalone},get isSignal(){return n.signals}}}var Xm=null;function We(){return Xm}function Sd(e){Xm??=e}var jo=class{},Md=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(ev),providedIn:"platform"})}return e})();var ev=(()=>{class e extends Md{_location;_history;_doc=p(J);constructor(){super(),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return We().getBaseHref(this._doc)}onPopState(t){let r=We().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",t,!1),()=>r.removeEventListener("popstate",t)}onHashChange(t){let r=We().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",t,!1),()=>r.removeEventListener("hashchange",t)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(t){this._location.pathname=t}pushState(t,r,o){this._history.pushState(t,r,o)}replaceState(t,r,o){this._history.replaceState(t,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(t=0){this._history.go(t)}getState(){return this._history.state}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new e,providedIn:"platform"})}return e})();function tv(e,n){return e?n?e.endsWith("/")?n.startsWith("/")?e+n.slice(1):e+n:n.startsWith("/")?e+n:`${e}/${n}`:e:n}function Km(e){let n=e.search(/#|\?|$/);return e[n-1]==="/"?e.slice(0,n-1)+e.slice(n):e}function nn(e){return e&&e[0]!=="?"?`?${e}`:e}var Nt=(()=>{class e{historyGo(t){throw new Error("")}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(rv),providedIn:"root"})}return e})(),nv=new E(""),rv=(()=>{class e extends Nt{_platformLocation;_baseHref;_removeListenerFns=[];constructor(t,r){super(),this._platformLocation=t,this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??p(J).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(t){this._removeListenerFns.push(this._platformLocation.onPopState(t),this._platformLocation.onHashChange(t))}getBaseHref(){return this._baseHref}prepareExternalUrl(t){return tv(this._baseHref,t)}path(t=!1){let r=this._platformLocation.pathname+nn(this._platformLocation.search),o=this._platformLocation.hash;return o&&t?`${r}${o}`:r}pushState(t,r,o,i){let s=this.prepareExternalUrl(o+nn(i));this._platformLocation.pushState(t,r,s)}replaceState(t,r,o,i){let s=this.prepareExternalUrl(o+nn(i));this._platformLocation.replaceState(t,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(t=0){this._platformLocation.historyGo?.(t)}static \u0275fac=function(r){return new(r||e)(I(Md),I(nv,8))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Rt=(()=>{class e{_subject=new $;_basePath;_locationStrategy;_urlChangeListeners=[];_urlChangeSubscription=null;constructor(t){this._locationStrategy=t;let r=this._locationStrategy.getBaseHref();this._basePath=D_(Km(Jm(r))),this._locationStrategy.onPopState(o=>{this._subject.next({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(t=!1){return this.normalize(this._locationStrategy.path(t))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(t,r=""){return this.path()==this.normalize(t+nn(r))}normalize(t){return e.stripTrailingSlash(y_(this._basePath,Jm(t)))}prepareExternalUrl(t){return t&&t[0]!=="/"&&(t="/"+t),this._locationStrategy.prepareExternalUrl(t)}go(t,r="",o=null){this._locationStrategy.pushState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+nn(r)),o)}replaceState(t,r="",o=null){this._locationStrategy.replaceState(o,"",t,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(t+nn(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(t=0){this._locationStrategy.historyGo?.(t)}onUrlChange(t){return this._urlChangeListeners.push(t),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(t);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(t="",r){this._urlChangeListeners.forEach(o=>o(t,r))}subscribe(t,r,o){return this._subject.subscribe({next:t,error:r??void 0,complete:o??void 0})}static normalizeQueryParams=nn;static joinWithSlash=tv;static stripTrailingSlash=Km;static \u0275fac=function(r){return new(r||e)(I(Nt))};static \u0275prov=w({token:e,factory:()=>v_(),providedIn:"root"})}return e})();function v_(){return new Rt(I(Nt))}function y_(e,n){if(!e||!n.startsWith(e))return n;let t=n.substring(e.length);return t===""||["/",";","?","#"].includes(t[0])?t:n}function Jm(e){return e.replace(/\/index.html$/,"")}function D_(e){if(new RegExp("^(https?:)?//").test(e)){let[,t]=e.split(/\/\/[^\/]+/);return t}return e}var Da=class{$implicit;ngForOf;index;count;constructor(n,t,r,o){this.$implicit=n,this.ngForOf=t,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},sv=(()=>{class e{_viewContainer;_template;_differs;set ngForOf(t){this._ngForOf=t,this._ngForOfDirty=!0}set ngForTrackBy(t){this._trackByFn=t}get ngForTrackBy(){return this._trackByFn}_ngForOf=null;_ngForOfDirty=!0;_differ=null;_trackByFn;constructor(t,r,o){this._viewContainer=t,this._template=r,this._differs=o}set ngForTemplate(t){t&&(this._template=t)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let t=this._ngForOf;!this._differ&&t&&(this._differ=this._differs.find(t).create(this.ngForTrackBy))}if(this._differ){let t=this._differ.diff(this._ngForOf);t&&this._applyChanges(t)}}_applyChanges(t){let r=this._viewContainer;t.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Da(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),ov(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}t.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);ov(i,o)})}static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v($e),v(tt),v(_d))};static \u0275dir=F({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"}})}return e})();function ov(e,n){e.context.$implicit=n.item}var E_=(()=>{class e{_viewContainer;_context=new Ea;_thenTemplateRef=null;_elseTemplateRef=null;_thenViewRef=null;_elseViewRef=null;constructor(t,r){this._viewContainer=t,this._thenTemplateRef=r}set ngIf(t){this._context.$implicit=this._context.ngIf=t,this._updateView()}set ngIfThen(t){iv(t,!1),this._thenTemplateRef=t,this._thenViewRef=null,this._updateView()}set ngIfElse(t){iv(t,!1),this._elseTemplateRef=t,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngIfUseIfTypeGuard;static ngTemplateGuard_ngIf;static ngTemplateContextGuard(t,r){return!0}static \u0275fac=function(r){return new(r||e)(v($e),v(tt))};static \u0275dir=F({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"}})}return e})(),Ea=class{$implicit=null;ngIf=null};function iv(e,n){if(e&&!e.createEmbeddedView)throw new D(2020,!1)}var C_=(()=>{class e{_viewContainerRef;_viewRef=null;ngTemplateOutletContext=null;ngTemplateOutlet=null;ngTemplateOutletInjector=null;constructor(t){this._viewContainerRef=t}ngOnChanges(t){if(this._shouldRecreateView(t)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(t){return!!t.ngTemplateOutlet||!!t.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(t,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(t,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static \u0275fac=function(r){return new(r||e)(v($e))};static \u0275dir=F({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},features:[Ae]})}return e})();var av=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ze({type:e});static \u0275inj=ke({})}return e})();function Vo(e,n){n=encodeURIComponent(n);for(let t of e.split(";")){let r=t.indexOf("="),[o,i]=r==-1?[t,""]:[t.slice(0,r),t.slice(r+1)];if(o.trim()===n)return decodeURIComponent(i)}return null}var xn=class{};var cv="browser";var wa=new E(""),Od=(()=>{class e{_zone;_plugins;_eventNameToPlugin=new Map;constructor(t,r){this._zone=r,t.forEach(o=>{o.manager=this}),this._plugins=t.slice().reverse()}addEventListener(t,r,o,i){return this._findPluginFor(r).addEventListener(t,r,o,i)}getZone(){return this._zone}_findPluginFor(t){let r=this._eventNameToPlugin.get(t);if(r)return r;if(r=this._plugins.find(i=>i.supports(t)),!r)throw new D(5101,!1);return this._eventNameToPlugin.set(t,r),r}static \u0275fac=function(r){return new(r||e)(I(wa),I(H))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Bo=class{_doc;constructor(n){this._doc=n}manager},Ad="ng-app-id";function lv(e){for(let n of e)n.remove()}function dv(e,n){let t=n.createElement("style");return t.textContent=e,t}function w_(e,n,t,r){let o=e.head?.querySelectorAll(`style[${Ad}="${n}"],link[${Ad}="${n}"]`);if(o)for(let i of o)i.removeAttribute(Ad),i instanceof HTMLLinkElement?r.set(i.href.slice(i.href.lastIndexOf("/")+1),{usage:0,elements:[i]}):i.textContent&&t.set(i.textContent,{usage:0,elements:[i]})}function Rd(e,n){let t=n.createElement("link");return t.setAttribute("rel","stylesheet"),t.setAttribute("href",e),t}var kd=(()=>{class e{doc;appId;nonce;inline=new Map;external=new Map;hosts=new Set;constructor(t,r,o,i={}){this.doc=t,this.appId=r,this.nonce=o,w_(t,r,this.inline,this.external),this.hosts.add(t.head)}addStyles(t,r){for(let o of t)this.addUsage(o,this.inline,dv);r?.forEach(o=>this.addUsage(o,this.external,Rd))}removeStyles(t,r){for(let o of t)this.removeUsage(o,this.inline);r?.forEach(o=>this.removeUsage(o,this.external))}addUsage(t,r,o){let i=r.get(t);i?i.usage++:r.set(t,{usage:1,elements:[...this.hosts].map(s=>this.addElement(s,o(t,this.doc)))})}removeUsage(t,r){let o=r.get(t);o&&(o.usage--,o.usage<=0&&(lv(o.elements),r.delete(t)))}ngOnDestroy(){for(let[,{elements:t}]of[...this.inline,...this.external])lv(t);this.hosts.clear()}addHost(t){this.hosts.add(t);for(let[r,{elements:o}]of this.inline)o.push(this.addElement(t,dv(r,this.doc)));for(let[r,{elements:o}]of this.external)o.push(this.addElement(t,Rd(r,this.doc)))}removeHost(t){this.hosts.delete(t)}addElement(t,r){return this.nonce&&r.setAttribute("nonce",this.nonce),t.appendChild(r)}static \u0275fac=function(r){return new(r||e)(I(J),I(Ks),I(Xs,8),I(_r))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Nd={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/Math/MathML"},Pd=/%COMP%/g;var hv="%COMP%",I_=`_nghost-${hv}`,b_=`_ngcontent-${hv}`,__=!0,T_=new E("",{providedIn:"root",factory:()=>__});function S_(e){return b_.replace(Pd,e)}function M_(e){return I_.replace(Pd,e)}function pv(e,n){return n.map(t=>t.replace(Pd,e))}var Fd=(()=>{class e{eventManager;sharedStylesHost;appId;removeStylesOnCompDestroy;doc;platformId;ngZone;nonce;tracingService;rendererByCompId=new Map;defaultRenderer;platformIsServer;constructor(t,r,o,i,s,a,c,u=null,l=null){this.eventManager=t,this.sharedStylesHost=r,this.appId=o,this.removeStylesOnCompDestroy=i,this.doc=s,this.platformId=a,this.ngZone=c,this.nonce=u,this.tracingService=l,this.platformIsServer=!1,this.defaultRenderer=new Uo(t,s,c,this.platformIsServer,this.tracingService)}createRenderer(t,r){if(!t||!r)return this.defaultRenderer;let o=this.getOrCreateRenderer(t,r);return o instanceof Ca?o.applyToHost(t):o instanceof Ho&&o.applyStyles(),o}getOrCreateRenderer(t,r){let o=this.rendererByCompId,i=o.get(r.id);if(!i){let s=this.doc,a=this.ngZone,c=this.eventManager,u=this.sharedStylesHost,l=this.removeStylesOnCompDestroy,d=this.platformIsServer,h=this.tracingService;switch(r.encapsulation){case Mt.Emulated:i=new Ca(c,u,r,this.appId,l,s,a,d,h);break;case Mt.ShadowDom:return new xd(c,u,t,r,s,a,this.nonce,d,h);default:i=new Ho(c,u,r,l,s,a,d,h);break}o.set(r.id,i)}return i}ngOnDestroy(){this.rendererByCompId.clear()}componentReplaced(t){this.rendererByCompId.delete(t)}static \u0275fac=function(r){return new(r||e)(I(Od),I(kd),I(Ks),I(T_),I(J),I(_r),I(H),I(Xs),I(Rn,8))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Uo=class{eventManager;doc;ngZone;platformIsServer;tracingService;data=Object.create(null);throwOnSyntheticProps=!0;constructor(n,t,r,o,i){this.eventManager=n,this.doc=t,this.ngZone=r,this.platformIsServer=o,this.tracingService=i}destroy(){}destroyNode=null;createElement(n,t){return t?this.doc.createElementNS(Nd[t]||t,n):this.doc.createElement(n)}createComment(n){return this.doc.createComment(n)}createText(n){return this.doc.createTextNode(n)}appendChild(n,t){(fv(n)?n.content:n).appendChild(t)}insertBefore(n,t,r){n&&(fv(n)?n.content:n).insertBefore(t,r)}removeChild(n,t){t.remove()}selectRootElement(n,t){let r=typeof n=="string"?this.doc.querySelector(n):n;if(!r)throw new D(-5104,!1);return t||(r.textContent=""),r}parentNode(n){return n.parentNode}nextSibling(n){return n.nextSibling}setAttribute(n,t,r,o){if(o){t=o+":"+t;let i=Nd[o];i?n.setAttributeNS(i,t,r):n.setAttribute(t,r)}else n.setAttribute(t,r)}removeAttribute(n,t,r){if(r){let o=Nd[r];o?n.removeAttributeNS(o,t):n.removeAttribute(`${r}:${t}`)}else n.removeAttribute(t)}addClass(n,t){n.classList.add(t)}removeClass(n,t){n.classList.remove(t)}setStyle(n,t,r,o){o&(vt.DashCase|vt.Important)?n.style.setProperty(t,r,o&vt.Important?"important":""):n.style[t]=r}removeStyle(n,t,r){r&vt.DashCase?n.style.removeProperty(t):n.style[t]=""}setProperty(n,t,r){n!=null&&(n[t]=r)}setValue(n,t){n.nodeValue=t}listen(n,t,r,o){if(typeof n=="string"&&(n=We().getGlobalEventTarget(this.doc,n),!n))throw new D(5102,!1);let i=this.decoratePreventDefault(r);return this.tracingService?.wrapEventListener&&(i=this.tracingService.wrapEventListener(n,t,i)),this.eventManager.addEventListener(n,t,i,o)}decoratePreventDefault(n){return t=>{if(t==="__ngUnwrap__")return n;n(t)===!1&&t.preventDefault()}}};function fv(e){return e.tagName==="TEMPLATE"&&e.content!==void 0}var xd=class extends Uo{sharedStylesHost;hostEl;shadowRoot;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,c,u),this.sharedStylesHost=t,this.hostEl=r,this.shadowRoot=r.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let l=o.styles;l=pv(o.id,l);for(let h of l){let f=document.createElement("style");a&&f.setAttribute("nonce",a),f.textContent=h,this.shadowRoot.appendChild(f)}let d=o.getExternalStyles?.();if(d)for(let h of d){let f=Rd(h,i);a&&f.setAttribute("nonce",a),this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(n){return n===this.hostEl?this.shadowRoot:n}appendChild(n,t){return super.appendChild(this.nodeOrShadowRoot(n),t)}insertBefore(n,t,r){return super.insertBefore(this.nodeOrShadowRoot(n),t,r)}removeChild(n,t){return super.removeChild(null,t)}parentNode(n){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(n)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Ho=class extends Uo{sharedStylesHost;removeStylesOnCompDestroy;styles;styleUrls;constructor(n,t,r,o,i,s,a,c,u){super(n,i,s,a,c),this.sharedStylesHost=t,this.removeStylesOnCompDestroy=o;let l=r.styles;this.styles=u?pv(u,l):l,this.styleUrls=r.getExternalStyles?.(u)}applyStyles(){this.sharedStylesHost.addStyles(this.styles,this.styleUrls)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles,this.styleUrls)}},Ca=class extends Ho{contentAttr;hostAttr;constructor(n,t,r,o,i,s,a,c,u){let l=o+"-"+r.id;super(n,t,r,i,s,a,c,u,l),this.contentAttr=S_(l),this.hostAttr=M_(l)}applyToHost(n){this.applyStyles(),this.setAttribute(n,this.hostAttr,"")}createElement(n,t){let r=super.createElement(n,t);return super.setAttribute(r,this.contentAttr,""),r}};var Ia=class e extends jo{supportsDOMEvents=!0;static makeCurrent(){Sd(new e)}onAndCancel(n,t,r,o){return n.addEventListener(t,r,o),()=>{n.removeEventListener(t,r,o)}}dispatchEvent(n,t){n.dispatchEvent(t)}remove(n){n.remove()}createElement(n,t){return t=t||this.getDefaultDocument(),t.createElement(n)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(n){return n.nodeType===Node.ELEMENT_NODE}isShadowRoot(n){return n instanceof DocumentFragment}getGlobalEventTarget(n,t){return t==="window"?window:t==="document"?n:t==="body"?n.body:null}getBaseHref(n){let t=A_();return t==null?null:N_(t)}resetBaseElement(){$o=null}getUserAgent(){return window.navigator.userAgent}getCookie(n){return Vo(document.cookie,n)}},$o=null;function A_(){return $o=$o||document.head.querySelector("base"),$o?$o.getAttribute("href"):null}function N_(e){return new URL(e,document.baseURI).pathname}var R_=(()=>{class e{build(){return new XMLHttpRequest}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),mv=(()=>{class e extends Bo{constructor(t){super(t)}supports(t){return!0}addEventListener(t,r,o,i){return t.addEventListener(r,o,i),()=>this.removeEventListener(t,r,o,i)}removeEventListener(t,r,o,i){return t.removeEventListener(r,o,i)}static \u0275fac=function(r){return new(r||e)(I(J))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),gv=["alt","control","meta","shift"],x_={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},O_={alt:e=>e.altKey,control:e=>e.ctrlKey,meta:e=>e.metaKey,shift:e=>e.shiftKey},vv=(()=>{class e extends Bo{constructor(t){super(t)}supports(t){return e.parseEventName(t)!=null}addEventListener(t,r,o,i){let s=e.parseEventName(r),a=e.eventCallback(s.fullKey,o,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>We().onAndCancel(t,s.domEventName,a,i))}static parseEventName(t){let r=t.toLowerCase().split("."),o=r.shift();if(r.length===0||!(o==="keydown"||o==="keyup"))return null;let i=e._normalizeKey(r.pop()),s="",a=r.indexOf("code");if(a>-1&&(r.splice(a,1),s="code."),gv.forEach(u=>{let l=r.indexOf(u);l>-1&&(r.splice(l,1),s+=u+".")}),s+=i,r.length!=0||i.length===0)return null;let c={};return c.domEventName=o,c.fullKey=s,c}static matchEventFullKeyCode(t,r){let o=x_[t.key]||t.key,i="";return r.indexOf("code.")>-1&&(o=t.code,i="code."),o==null||!o?!1:(o=o.toLowerCase(),o===" "?o="space":o==="."&&(o="dot"),gv.forEach(s=>{if(s!==o){let a=O_[s];a(t)&&(i+=s+".")}}),i+=o,i===r)}static eventCallback(t,r,o){return i=>{e.matchEventFullKeyCode(i,t)&&o.runGuarded(()=>r(i))}}static _normalizeKey(t){return t==="esc"?"escape":t}static \u0275fac=function(r){return new(r||e)(I(J))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function k_(e,n){let t=g({rootComponent:e},P_(n));return Ym(t)}function P_(e){return{appProviders:[...B_,...e?.providers??[]],platformProviders:V_}}function F_(){Ia.makeCurrent()}function L_(){return new Ye}function j_(){return Fl(document),document}var V_=[{provide:_r,useValue:cv},{provide:Js,useValue:F_,multi:!0},{provide:J,useFactory:j_}];var B_=[{provide:vo,useValue:"root"},{provide:Ye,useFactory:L_},{provide:wa,useClass:mv,multi:!0,deps:[J]},{provide:wa,useClass:vv,multi:!0,deps:[J]},Fd,kd,Od,{provide:Mn,useExisting:Fd},{provide:xn,useClass:R_},[]];var kr=class{},zo=class{},rn=class e{headers;normalizedNames=new Map;lazyInit;lazyUpdate=null;constructor(n){n?typeof n=="string"?this.lazyInit=()=>{this.headers=new Map,n.split(`
`).forEach(t=>{let r=t.indexOf(":");if(r>0){let o=t.slice(0,r),i=t.slice(r+1).trim();this.addHeaderEntry(o,i)}})}:typeof Headers<"u"&&n instanceof Headers?(this.headers=new Map,n.forEach((t,r)=>{this.addHeaderEntry(r,t)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(n).forEach(([t,r])=>{this.setHeaderEntries(t,r)})}:this.headers=new Map}has(n){return this.init(),this.headers.has(n.toLowerCase())}get(n){this.init();let t=this.headers.get(n.toLowerCase());return t&&t.length>0?t[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(n){return this.init(),this.headers.get(n.toLowerCase())||null}append(n,t){return this.clone({name:n,value:t,op:"a"})}set(n,t){return this.clone({name:n,value:t,op:"s"})}delete(n,t){return this.clone({name:n,value:t,op:"d"})}maybeSetNormalizedName(n,t){this.normalizedNames.has(t)||this.normalizedNames.set(t,n)}init(){this.lazyInit&&(this.lazyInit instanceof e?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(n=>this.applyUpdate(n)),this.lazyUpdate=null))}copyFrom(n){n.init(),Array.from(n.headers.keys()).forEach(t=>{this.headers.set(t,n.headers.get(t)),this.normalizedNames.set(t,n.normalizedNames.get(t))})}clone(n){let t=new e;return t.lazyInit=this.lazyInit&&this.lazyInit instanceof e?this.lazyInit:this,t.lazyUpdate=(this.lazyUpdate||[]).concat([n]),t}applyUpdate(n){let t=n.name.toLowerCase();switch(n.op){case"a":case"s":let r=n.value;if(typeof r=="string"&&(r=[r]),r.length===0)return;this.maybeSetNormalizedName(n.name,t);let o=(n.op==="a"?this.headers.get(t):void 0)||[];o.push(...r),this.headers.set(t,o);break;case"d":let i=n.value;if(!i)this.headers.delete(t),this.normalizedNames.delete(t);else{let s=this.headers.get(t);if(!s)return;s=s.filter(a=>i.indexOf(a)===-1),s.length===0?(this.headers.delete(t),this.normalizedNames.delete(t)):this.headers.set(t,s)}break}}addHeaderEntry(n,t){let r=n.toLowerCase();this.maybeSetNormalizedName(n,r),this.headers.has(r)?this.headers.get(r).push(t):this.headers.set(r,[t])}setHeaderEntries(n,t){let r=(Array.isArray(t)?t:[t]).map(i=>i.toString()),o=n.toLowerCase();this.headers.set(o,r),this.maybeSetNormalizedName(n,o)}forEach(n){this.init(),Array.from(this.normalizedNames.keys()).forEach(t=>n(this.normalizedNames.get(t),this.headers.get(t)))}};var _a=class{encodeKey(n){return yv(n)}encodeValue(n){return yv(n)}decodeKey(n){return decodeURIComponent(n)}decodeValue(n){return decodeURIComponent(n)}};function U_(e,n){let t=new Map;return e.length>0&&e.replace(/^\?/,"").split("&").forEach(o=>{let i=o.indexOf("="),[s,a]=i==-1?[n.decodeKey(o),""]:[n.decodeKey(o.slice(0,i)),n.decodeValue(o.slice(i+1))],c=t.get(s)||[];c.push(a),t.set(s,c)}),t}var H_=/%(\d[a-f0-9])/gi,$_={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function yv(e){return encodeURIComponent(e).replace(H_,(n,t)=>$_[t]??n)}function ba(e){return`${e}`}var xt=class e{map;encoder;updates=null;cloneFrom=null;constructor(n={}){if(this.encoder=n.encoder||new _a,n.fromString){if(n.fromObject)throw new D(2805,!1);this.map=U_(n.fromString,this.encoder)}else n.fromObject?(this.map=new Map,Object.keys(n.fromObject).forEach(t=>{let r=n.fromObject[t],o=Array.isArray(r)?r.map(ba):[ba(r)];this.map.set(t,o)})):this.map=null}has(n){return this.init(),this.map.has(n)}get(n){this.init();let t=this.map.get(n);return t?t[0]:null}getAll(n){return this.init(),this.map.get(n)||null}keys(){return this.init(),Array.from(this.map.keys())}append(n,t){return this.clone({param:n,value:t,op:"a"})}appendAll(n){let t=[];return Object.keys(n).forEach(r=>{let o=n[r];Array.isArray(o)?o.forEach(i=>{t.push({param:r,value:i,op:"a"})}):t.push({param:r,value:o,op:"a"})}),this.clone(t)}set(n,t){return this.clone({param:n,value:t,op:"s"})}delete(n,t){return this.clone({param:n,value:t,op:"d"})}toString(){return this.init(),this.keys().map(n=>{let t=this.encoder.encodeKey(n);return this.map.get(n).map(r=>t+"="+this.encoder.encodeValue(r)).join("&")}).filter(n=>n!=="").join("&")}clone(n){let t=new e({encoder:this.encoder});return t.cloneFrom=this.cloneFrom||this,t.updates=(this.updates||[]).concat(n),t}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(n=>this.map.set(n,this.cloneFrom.map.get(n))),this.updates.forEach(n=>{switch(n.op){case"a":case"s":let t=(n.op==="a"?this.map.get(n.param):void 0)||[];t.push(ba(n.value)),this.map.set(n.param,t);break;case"d":if(n.value!==void 0){let r=this.map.get(n.param)||[],o=r.indexOf(ba(n.value));o!==-1&&r.splice(o,1),r.length>0?this.map.set(n.param,r):this.map.delete(n.param)}else{this.map.delete(n.param);break}}}),this.cloneFrom=this.updates=null)}};var Ta=class{map=new Map;set(n,t){return this.map.set(n,t),this}get(n){return this.map.has(n)||this.map.set(n,n.defaultValue()),this.map.get(n)}delete(n){return this.map.delete(n),this}has(n){return this.map.has(n)}keys(){return this.map.keys()}};function z_(e){switch(e){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Dv(e){return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer}function Ev(e){return typeof Blob<"u"&&e instanceof Blob}function Cv(e){return typeof FormData<"u"&&e instanceof FormData}function G_(e){return typeof URLSearchParams<"u"&&e instanceof URLSearchParams}var wv="Content-Type",Iv="Accept",_v="X-Request-URL",Tv="text/plain",Sv="application/json",W_=`${Sv}, ${Tv}, */*`,xr=class e{url;body=null;headers;context;reportProgress=!1;withCredentials=!1;credentials;keepalive=!1;cache;priority;mode;redirect;responseType="json";method;params;urlWithParams;transferCache;timeout;constructor(n,t,r,o){this.url=t,this.method=n.toUpperCase();let i;if(z_(this.method)||o?(this.body=r!==void 0?r:null,i=o):i=r,i){if(this.reportProgress=!!i.reportProgress,this.withCredentials=!!i.withCredentials,this.keepalive=!!i.keepalive,i.responseType&&(this.responseType=i.responseType),i.headers&&(this.headers=i.headers),i.context&&(this.context=i.context),i.params&&(this.params=i.params),i.priority&&(this.priority=i.priority),i.cache&&(this.cache=i.cache),i.credentials&&(this.credentials=i.credentials),typeof i.timeout=="number"){if(i.timeout<1||!Number.isInteger(i.timeout))throw new Error("");this.timeout=i.timeout}i.mode&&(this.mode=i.mode),i.redirect&&(this.redirect=i.redirect),this.transferCache=i.transferCache}if(this.headers??=new rn,this.context??=new Ta,!this.params)this.params=new xt,this.urlWithParams=t;else{let s=this.params.toString();if(s.length===0)this.urlWithParams=t;else{let a=t.indexOf("?"),c=a===-1?"?":a<t.length-1?"&":"";this.urlWithParams=t+c+s}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Dv(this.body)||Ev(this.body)||Cv(this.body)||G_(this.body)?this.body:this.body instanceof xt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Cv(this.body)?null:Ev(this.body)?this.body.type||null:Dv(this.body)?null:typeof this.body=="string"?Tv:this.body instanceof xt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?Sv:null}clone(n={}){let t=n.method||this.method,r=n.url||this.url,o=n.responseType||this.responseType,i=n.keepalive??this.keepalive,s=n.priority||this.priority,a=n.cache||this.cache,c=n.mode||this.mode,u=n.redirect||this.redirect,l=n.credentials||this.credentials,d=n.transferCache??this.transferCache,h=n.timeout??this.timeout,f=n.body!==void 0?n.body:this.body,m=n.withCredentials??this.withCredentials,b=n.reportProgress??this.reportProgress,y=n.headers||this.headers,C=n.params||this.params,le=n.context??this.context;return n.setHeaders!==void 0&&(y=Object.keys(n.setHeaders).reduce((Ie,Z)=>Ie.set(Z,n.setHeaders[Z]),y)),n.setParams&&(C=Object.keys(n.setParams).reduce((Ie,Z)=>Ie.set(Z,n.setParams[Z]),C)),new e(t,r,f,{params:C,headers:y,context:le,reportProgress:b,responseType:o,withCredentials:m,transferCache:d,keepalive:i,cache:a,priority:s,timeout:h,mode:c,redirect:u,credentials:l})}},On=function(e){return e[e.Sent=0]="Sent",e[e.UploadProgress=1]="UploadProgress",e[e.ResponseHeader=2]="ResponseHeader",e[e.DownloadProgress=3]="DownloadProgress",e[e.Response=4]="Response",e[e.User=5]="User",e}(On||{}),Pr=class{headers;status;statusText;url;ok;type;constructor(n,t=200,r="OK"){this.headers=n.headers||new rn,this.status=n.status!==void 0?n.status:t,this.statusText=n.statusText||r,this.url=n.url||null,this.ok=this.status>=200&&this.status<300}},Sa=class e extends Pr{constructor(n={}){super(n)}type=On.ResponseHeader;clone(n={}){return new e({headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Go=class e extends Pr{body;constructor(n={}){super(n),this.body=n.body!==void 0?n.body:null}type=On.Response;clone(n={}){return new e({body:n.body!==void 0?n.body:this.body,headers:n.headers||this.headers,status:n.status!==void 0?n.status:this.status,statusText:n.statusText||this.statusText,url:n.url||this.url||void 0})}},Or=class extends Pr{name="HttpErrorResponse";message;error;ok=!1;constructor(n){super(n,0,"Unknown Error"),this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${n.url||"(unknown url)"}`:this.message=`Http failure response for ${n.url||"(unknown url)"}: ${n.status} ${n.statusText}`,this.error=n.error||null}},q_=200,Z_=204;function Ld(e,n){return{body:n,headers:e.headers,context:e.context,observe:e.observe,params:e.params,reportProgress:e.reportProgress,responseType:e.responseType,withCredentials:e.withCredentials,transferCache:e.transferCache,keepalive:e.keepalive,priority:e.priority,cache:e.cache,mode:e.mode,redirect:e.redirect}}var Mv=(()=>{class e{handler;constructor(t){this.handler=t}request(t,r,o={}){let i;if(t instanceof xr)i=t;else{let c;o.headers instanceof rn?c=o.headers:c=new rn(o.headers);let u;o.params&&(o.params instanceof xt?u=o.params:u=new xt({fromObject:o.params})),i=new xr(t,r,o.body!==void 0?o.body:null,{headers:c,context:o.context,params:u,reportProgress:o.reportProgress,responseType:o.responseType||"json",withCredentials:o.withCredentials,transferCache:o.transferCache,keepalive:o.keepalive,priority:o.priority,cache:o.cache,mode:o.mode,redirect:o.redirect,credentials:o.credentials})}let s=_(i).pipe(ct(c=>this.handler.handle(c)));if(t instanceof xr||o.observe==="events")return s;let a=s.pipe(te(c=>c instanceof Go));switch(o.observe||"body"){case"body":switch(i.responseType){case"arraybuffer":return a.pipe(R(c=>{if(c.body!==null&&!(c.body instanceof ArrayBuffer))throw new D(2806,!1);return c.body}));case"blob":return a.pipe(R(c=>{if(c.body!==null&&!(c.body instanceof Blob))throw new D(2807,!1);return c.body}));case"text":return a.pipe(R(c=>{if(c.body!==null&&typeof c.body!="string")throw new D(2808,!1);return c.body}));case"json":default:return a.pipe(R(c=>c.body))}case"response":return a;default:throw new D(2809,!1)}}delete(t,r={}){return this.request("DELETE",t,r)}get(t,r={}){return this.request("GET",t,r)}head(t,r={}){return this.request("HEAD",t,r)}jsonp(t,r){return this.request("JSONP",t,{params:new xt().append(r,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(t,r={}){return this.request("OPTIONS",t,r)}patch(t,r,o={}){return this.request("PATCH",t,Ld(o,r))}post(t,r,o={}){return this.request("POST",t,Ld(o,r))}put(t,r,o={}){return this.request("PUT",t,Ld(o,r))}static \u0275fac=function(r){return new(r||e)(I(kr))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var Y_=new E("");function Av(e,n){return n(e)}function Q_(e,n){return(t,r)=>n.intercept(t,{handle:o=>e(o,r)})}function K_(e,n,t){return(r,o)=>he(t,()=>n(r,i=>e(i,o)))}var Nv=new E(""),Vd=new E(""),Rv=new E(""),Bd=new E("",{providedIn:"root",factory:()=>!0});function J_(){let e=null;return(n,t)=>{e===null&&(e=(p(Nv,{optional:!0})??[]).reduceRight(Q_,Av));let r=p(bo);if(p(Bd)){let i=r.add();return e(n,t).pipe(Ut(i))}else return e(n,t)}}var Ma=(()=>{class e extends kr{backend;injector;chain=null;pendingTasks=p(bo);contributeToStability=p(Bd);constructor(t,r){super(),this.backend=t,this.injector=r}handle(t){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(Vd),...this.injector.get(Rv,[])]));this.chain=r.reduceRight((o,i)=>K_(o,i,this.injector),Av)}if(this.contributeToStability){let r=this.pendingTasks.add();return this.chain(t,o=>this.backend.handle(o)).pipe(Ut(r))}else return this.chain(t,r=>this.backend.handle(r))}static \u0275fac=function(r){return new(r||e)(I(zo),I(G))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();var X_=/^\)\]\}',?\n/,eT=RegExp(`^${_v}:`,"m");function tT(e){return"responseURL"in e&&e.responseURL?e.responseURL:eT.test(e.getAllResponseHeaders())?e.getResponseHeader(_v):null}var jd=(()=>{class e{xhrFactory;constructor(t){this.xhrFactory=t}handle(t){if(t.method==="JSONP")throw new D(-2800,!1);let r=this.xhrFactory;return _(null).pipe(ie(()=>new O(i=>{let s=r.build();if(s.open(t.method,t.urlWithParams),t.withCredentials&&(s.withCredentials=!0),t.headers.forEach((y,C)=>s.setRequestHeader(y,C.join(","))),t.headers.has(Iv)||s.setRequestHeader(Iv,W_),!t.headers.has(wv)){let y=t.detectContentTypeHeader();y!==null&&s.setRequestHeader(wv,y)}if(t.timeout&&(s.timeout=t.timeout),t.responseType){let y=t.responseType.toLowerCase();s.responseType=y!=="json"?y:"text"}let a=t.serializeBody(),c=null,u=()=>{if(c!==null)return c;let y=s.statusText||"OK",C=new rn(s.getAllResponseHeaders()),le=tT(s)||t.url;return c=new Sa({headers:C,status:s.status,statusText:y,url:le}),c},l=()=>{let{headers:y,status:C,statusText:le,url:Ie}=u(),Z=null;C!==Z_&&(Z=typeof s.response>"u"?s.responseText:s.response),C===0&&(C=Z?q_:0);let Un=C>=200&&C<300;if(t.responseType==="json"&&typeof Z=="string"){let OD=Z;Z=Z.replace(X_,"");try{Z=Z!==""?JSON.parse(Z):null}catch(kD){Z=OD,Un&&(Un=!1,Z={error:kD,text:Z})}}Un?(i.next(new Go({body:Z,headers:y,status:C,statusText:le,url:Ie||void 0})),i.complete()):i.error(new Or({error:Z,headers:y,status:C,statusText:le,url:Ie||void 0}))},d=y=>{let{url:C}=u(),le=new Or({error:y,status:s.status||0,statusText:s.statusText||"Unknown Error",url:C||void 0});i.error(le)},h=d;t.timeout&&(h=y=>{let{url:C}=u(),le=new Or({error:new DOMException("Request timed out","TimeoutError"),status:s.status||0,statusText:s.statusText||"Request timeout",url:C||void 0});i.error(le)});let f=!1,m=y=>{f||(i.next(u()),f=!0);let C={type:On.DownloadProgress,loaded:y.loaded};y.lengthComputable&&(C.total=y.total),t.responseType==="text"&&s.responseText&&(C.partialText=s.responseText),i.next(C)},b=y=>{let C={type:On.UploadProgress,loaded:y.loaded};y.lengthComputable&&(C.total=y.total),i.next(C)};return s.addEventListener("load",l),s.addEventListener("error",d),s.addEventListener("timeout",h),s.addEventListener("abort",d),t.reportProgress&&(s.addEventListener("progress",m),a!==null&&s.upload&&s.upload.addEventListener("progress",b)),s.send(a),i.next({type:On.Sent}),()=>{s.removeEventListener("error",d),s.removeEventListener("abort",d),s.removeEventListener("load",l),s.removeEventListener("timeout",h),t.reportProgress&&(s.removeEventListener("progress",m),a!==null&&s.upload&&s.upload.removeEventListener("progress",b)),s.readyState!==s.DONE&&s.abort()}})))}static \u0275fac=function(r){return new(r||e)(I(xn))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),xv=new E(""),nT="XSRF-TOKEN",rT=new E("",{providedIn:"root",factory:()=>nT}),oT="X-XSRF-TOKEN",iT=new E("",{providedIn:"root",factory:()=>oT}),Wo=class{},sT=(()=>{class e{doc;cookieName;lastCookieString="";lastToken=null;parseCount=0;constructor(t,r){this.doc=t,this.cookieName=r}getToken(){let t=this.doc.cookie||"";return t!==this.lastCookieString&&(this.parseCount++,this.lastToken=Vo(t,this.cookieName),this.lastCookieString=t),this.lastToken}static \u0275fac=function(r){return new(r||e)(I(J),I(rT))};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})();function aT(e,n){let t=e.url.toLowerCase();if(!p(xv)||e.method==="GET"||e.method==="HEAD"||t.startsWith("http://")||t.startsWith("https://"))return n(e);let r=p(Wo).getToken(),o=p(iT);return r!=null&&!e.headers.has(o)&&(e=e.clone({headers:e.headers.set(o,r)})),n(e)}var Ud=function(e){return e[e.Interceptors=0]="Interceptors",e[e.LegacyInterceptors=1]="LegacyInterceptors",e[e.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",e[e.NoXsrfProtection=3]="NoXsrfProtection",e[e.JsonpSupport=4]="JsonpSupport",e[e.RequestsMadeViaParent=5]="RequestsMadeViaParent",e[e.Fetch=6]="Fetch",e}(Ud||{});function cT(e,n){return{\u0275kind:e,\u0275providers:n}}function Ov(...e){let n=[Mv,jd,Ma,{provide:kr,useExisting:Ma},{provide:zo,useFactory:()=>p(Y_,{optional:!0})??p(jd)},{provide:Vd,useValue:aT,multi:!0},{provide:xv,useValue:!0},{provide:Wo,useClass:sT}];for(let t of e)n.push(...t.\u0275providers);return ur(n)}var bv=new E("");function kv(){return cT(Ud.LegacyInterceptors,[{provide:bv,useFactory:J_},{provide:Vd,useExisting:bv,multi:!0}])}var uT=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ze({type:e});static \u0275inj=ke({providers:[Ov(kv())]})}return e})();var Pv=(()=>{class e{_doc;constructor(t){this._doc=t}getTitle(){return this._doc.title}setTitle(t){this._doc.title=t||""}static \u0275fac=function(r){return new(r||e)(I(J))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var A="primary",ai=Symbol("RouteTitle"),Wd=class{params;constructor(n){this.params=n||{}}has(n){return Object.prototype.hasOwnProperty.call(this.params,n)}get(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t[0]:t}return null}getAll(n){if(this.has(n)){let t=this.params[n];return Array.isArray(t)?t:[t]}return[]}get keys(){return Object.keys(this.params)}};function Fn(e){return new Wd(e)}function $v(e,n,t){let r=t.path.split("/");if(r.length>e.length||t.pathMatch==="full"&&(n.hasChildren()||r.length<e.length))return null;let o={};for(let i=0;i<r.length;i++){let s=r[i],a=e[i];if(s[0]===":")o[s.substring(1)]=a;else if(s!==a.path)return null}return{consumed:e.slice(0,r.length),posParams:o}}function dT(e,n){if(e.length!==n.length)return!1;for(let t=0;t<e.length;++t)if(!Dt(e[t],n[t]))return!1;return!0}function Dt(e,n){let t=e?qd(e):void 0,r=n?qd(n):void 0;if(!t||!r||t.length!=r.length)return!1;let o;for(let i=0;i<t.length;i++)if(o=t[i],!zv(e[o],n[o]))return!1;return!0}function qd(e){return[...Object.keys(e),...Object.getOwnPropertySymbols(e)]}function zv(e,n){if(Array.isArray(e)&&Array.isArray(n)){if(e.length!==n.length)return!1;let t=[...e].sort(),r=[...n].sort();return t.every((o,i)=>r[i]===o)}else return e===n}function Gv(e){return e.length>0?e[e.length-1]:null}function Pt(e){return Fc(e)?e:en(e)?W(Promise.resolve(e)):_(e)}var fT={exact:qv,subset:Zv},Wv={exact:hT,subset:pT,ignored:()=>!0};function Fv(e,n,t){return fT[t.paths](e.root,n.root,t.matrixParams)&&Wv[t.queryParams](e.queryParams,n.queryParams)&&!(t.fragment==="exact"&&e.fragment!==n.fragment)}function hT(e,n){return Dt(e,n)}function qv(e,n,t){if(!kn(e.segments,n.segments)||!Ra(e.segments,n.segments,t)||e.numberOfChildren!==n.numberOfChildren)return!1;for(let r in n.children)if(!e.children[r]||!qv(e.children[r],n.children[r],t))return!1;return!0}function pT(e,n){return Object.keys(n).length<=Object.keys(e).length&&Object.keys(n).every(t=>zv(e[t],n[t]))}function Zv(e,n,t){return Yv(e,n,n.segments,t)}function Yv(e,n,t,r){if(e.segments.length>t.length){let o=e.segments.slice(0,t.length);return!(!kn(o,t)||n.hasChildren()||!Ra(o,t,r))}else if(e.segments.length===t.length){if(!kn(e.segments,t)||!Ra(e.segments,t,r))return!1;for(let o in n.children)if(!e.children[o]||!Zv(e.children[o],n.children[o],r))return!1;return!0}else{let o=t.slice(0,e.segments.length),i=t.slice(e.segments.length);return!kn(e.segments,o)||!Ra(e.segments,o,r)||!e.children[A]?!1:Yv(e.children[A],n,i,r)}}function Ra(e,n,t){return n.every((r,o)=>Wv[t](e[o].parameters,r.parameters))}var Ct=class{root;queryParams;fragment;_queryParamMap;constructor(n=new V([],{}),t={},r=null){this.root=n,this.queryParams=t,this.fragment=r}get queryParamMap(){return this._queryParamMap??=Fn(this.queryParams),this._queryParamMap}toString(){return vT.serialize(this)}},V=class{segments;children;parent=null;constructor(n,t){this.segments=n,this.children=t,Object.values(t).forEach(r=>r.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return xa(this)}},on=class{path;parameters;_parameterMap;constructor(n,t){this.path=n,this.parameters=t}get parameterMap(){return this._parameterMap??=Fn(this.parameters),this._parameterMap}toString(){return Kv(this)}};function gT(e,n){return kn(e,n)&&e.every((t,r)=>Dt(t.parameters,n[r].parameters))}function kn(e,n){return e.length!==n.length?!1:e.every((t,r)=>t.path===n[r].path)}function mT(e,n){let t=[];return Object.entries(e.children).forEach(([r,o])=>{r===A&&(t=t.concat(n(o,r)))}),Object.entries(e.children).forEach(([r,o])=>{r!==A&&(t=t.concat(n(o,r)))}),t}var jn=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>new Ln,providedIn:"root"})}return e})(),Ln=class{parse(n){let t=new Yd(n);return new Ct(t.parseRootSegment(),t.parseQueryParams(),t.parseFragment())}serialize(n){let t=`/${qo(n.root,!0)}`,r=ET(n.queryParams),o=typeof n.fragment=="string"?`#${yT(n.fragment)}`:"";return`${t}${r}${o}`}},vT=new Ln;function xa(e){return e.segments.map(n=>Kv(n)).join("/")}function qo(e,n){if(!e.hasChildren())return xa(e);if(n){let t=e.children[A]?qo(e.children[A],!1):"",r=[];return Object.entries(e.children).forEach(([o,i])=>{o!==A&&r.push(`${o}:${qo(i,!1)}`)}),r.length>0?`${t}(${r.join("//")})`:t}else{let t=mT(e,(r,o)=>o===A?[qo(e.children[A],!1)]:[`${o}:${qo(r,!1)}`]);return Object.keys(e.children).length===1&&e.children[A]!=null?`${xa(e)}/${t[0]}`:`${xa(e)}/(${t.join("//")})`}}function Qv(e){return encodeURIComponent(e).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function Aa(e){return Qv(e).replace(/%3B/gi,";")}function yT(e){return encodeURI(e)}function Zd(e){return Qv(e).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function Oa(e){return decodeURIComponent(e)}function Lv(e){return Oa(e.replace(/\+/g,"%20"))}function Kv(e){return`${Zd(e.path)}${DT(e.parameters)}`}function DT(e){return Object.entries(e).map(([n,t])=>`;${Zd(n)}=${Zd(t)}`).join("")}function ET(e){let n=Object.entries(e).map(([t,r])=>Array.isArray(r)?r.map(o=>`${Aa(t)}=${Aa(o)}`).join("&"):`${Aa(t)}=${Aa(r)}`).filter(t=>t);return n.length?`?${n.join("&")}`:""}var CT=/^[^\/()?;#]+/;function Hd(e){let n=e.match(CT);return n?n[0]:""}var wT=/^[^\/()?;=#]+/;function IT(e){let n=e.match(wT);return n?n[0]:""}var bT=/^[^=?&#]+/;function _T(e){let n=e.match(bT);return n?n[0]:""}var TT=/^[^&#]+/;function ST(e){let n=e.match(TT);return n?n[0]:""}var Yd=class{url;remaining;constructor(n){this.url=n,this.remaining=n}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new V([],{}):new V([],this.parseChildren())}parseQueryParams(){let n={};if(this.consumeOptional("?"))do this.parseQueryParam(n);while(this.consumeOptional("&"));return n}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let n=[];for(this.peekStartsWith("(")||n.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),n.push(this.parseSegment());let t={};this.peekStartsWith("/(")&&(this.capture("/"),t=this.parseParens(!0));let r={};return this.peekStartsWith("(")&&(r=this.parseParens(!1)),(n.length>0||Object.keys(t).length>0)&&(r[A]=new V(n,t)),r}parseSegment(){let n=Hd(this.remaining);if(n===""&&this.peekStartsWith(";"))throw new D(4009,!1);return this.capture(n),new on(Oa(n),this.parseMatrixParams())}parseMatrixParams(){let n={};for(;this.consumeOptional(";");)this.parseParam(n);return n}parseParam(n){let t=IT(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let o=Hd(this.remaining);o&&(r=o,this.capture(r))}n[Oa(t)]=Oa(r)}parseQueryParam(n){let t=_T(this.remaining);if(!t)return;this.capture(t);let r="";if(this.consumeOptional("=")){let s=ST(this.remaining);s&&(r=s,this.capture(r))}let o=Lv(t),i=Lv(r);if(n.hasOwnProperty(o)){let s=n[o];Array.isArray(s)||(s=[s],n[o]=s),s.push(i)}else n[o]=i}parseParens(n){let t={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let r=Hd(this.remaining),o=this.remaining[r.length];if(o!=="/"&&o!==")"&&o!==";")throw new D(4010,!1);let i;r.indexOf(":")>-1?(i=r.slice(0,r.indexOf(":")),this.capture(i),this.capture(":")):n&&(i=A);let s=this.parseChildren();t[i]=Object.keys(s).length===1?s[A]:new V([],s),this.consumeOptional("//")}return t}peekStartsWith(n){return this.remaining.startsWith(n)}consumeOptional(n){return this.peekStartsWith(n)?(this.remaining=this.remaining.substring(n.length),!0):!1}capture(n){if(!this.consumeOptional(n))throw new D(4011,!1)}};function Jv(e){return e.segments.length>0?new V([],{[A]:e}):e}function Xv(e){let n={};for(let[r,o]of Object.entries(e.children)){let i=Xv(o);if(r===A&&i.segments.length===0&&i.hasChildren())for(let[s,a]of Object.entries(i.children))n[s]=a;else(i.segments.length>0||i.hasChildren())&&(n[r]=i)}let t=new V(e.segments,n);return MT(t)}function MT(e){if(e.numberOfChildren===1&&e.children[A]){let n=e.children[A];return new V(e.segments.concat(n.segments),n.children)}return e}function sn(e){return e instanceof Ct}function ey(e,n,t=null,r=null){let o=ty(e);return ny(o,n,t,r)}function ty(e){let n;function t(i){let s={};for(let c of i.children){let u=t(c);s[c.outlet]=u}let a=new V(i.url,s);return i===e&&(n=a),a}let r=t(e.root),o=Jv(r);return n??o}function ny(e,n,t,r){let o=e;for(;o.parent;)o=o.parent;if(n.length===0)return $d(o,o,o,t,r);let i=AT(n);if(i.toRoot())return $d(o,o,new V([],{}),t,r);let s=NT(i,o,e),a=s.processChildren?Yo(s.segmentGroup,s.index,i.commands):oy(s.segmentGroup,s.index,i.commands);return $d(o,s.segmentGroup,a,t,r)}function ka(e){return typeof e=="object"&&e!=null&&!e.outlets&&!e.segmentPath}function Jo(e){return typeof e=="object"&&e!=null&&e.outlets}function $d(e,n,t,r,o){let i={};r&&Object.entries(r).forEach(([c,u])=>{i[c]=Array.isArray(u)?u.map(l=>`${l}`):`${u}`});let s;e===n?s=t:s=ry(e,n,t);let a=Jv(Xv(s));return new Ct(a,i,o)}function ry(e,n,t){let r={};return Object.entries(e.children).forEach(([o,i])=>{i===n?r[o]=t:r[o]=ry(i,n,t)}),new V(e.segments,r)}var Pa=class{isAbsolute;numberOfDoubleDots;commands;constructor(n,t,r){if(this.isAbsolute=n,this.numberOfDoubleDots=t,this.commands=r,n&&r.length>0&&ka(r[0]))throw new D(4003,!1);let o=r.find(Jo);if(o&&o!==Gv(r))throw new D(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function AT(e){if(typeof e[0]=="string"&&e.length===1&&e[0]==="/")return new Pa(!0,0,e);let n=0,t=!1,r=e.reduce((o,i,s)=>{if(typeof i=="object"&&i!=null){if(i.outlets){let a={};return Object.entries(i.outlets).forEach(([c,u])=>{a[c]=typeof u=="string"?u.split("/"):u}),[...o,{outlets:a}]}if(i.segmentPath)return[...o,i.segmentPath]}return typeof i!="string"?[...o,i]:s===0?(i.split("/").forEach((a,c)=>{c==0&&a==="."||(c==0&&a===""?t=!0:a===".."?n++:a!=""&&o.push(a))}),o):[...o,i]},[]);return new Pa(t,n,r)}var jr=class{segmentGroup;processChildren;index;constructor(n,t,r){this.segmentGroup=n,this.processChildren=t,this.index=r}};function NT(e,n,t){if(e.isAbsolute)return new jr(n,!0,0);if(!t)return new jr(n,!1,NaN);if(t.parent===null)return new jr(t,!0,0);let r=ka(e.commands[0])?0:1,o=t.segments.length-1+r;return RT(t,o,e.numberOfDoubleDots)}function RT(e,n,t){let r=e,o=n,i=t;for(;i>o;){if(i-=o,r=r.parent,!r)throw new D(4005,!1);o=r.segments.length}return new jr(r,!1,o-i)}function xT(e){return Jo(e[0])?e[0].outlets:{[A]:e}}function oy(e,n,t){if(e??=new V([],{}),e.segments.length===0&&e.hasChildren())return Yo(e,n,t);let r=OT(e,n,t),o=t.slice(r.commandIndex);if(r.match&&r.pathIndex<e.segments.length){let i=new V(e.segments.slice(0,r.pathIndex),{});return i.children[A]=new V(e.segments.slice(r.pathIndex),e.children),Yo(i,0,o)}else return r.match&&o.length===0?new V(e.segments,{}):r.match&&!e.hasChildren()?Qd(e,n,t):r.match?Yo(e,0,o):Qd(e,n,t)}function Yo(e,n,t){if(t.length===0)return new V(e.segments,{});{let r=xT(t),o={};if(Object.keys(r).some(i=>i!==A)&&e.children[A]&&e.numberOfChildren===1&&e.children[A].segments.length===0){let i=Yo(e.children[A],n,t);return new V(e.segments,i.children)}return Object.entries(r).forEach(([i,s])=>{typeof s=="string"&&(s=[s]),s!==null&&(o[i]=oy(e.children[i],n,s))}),Object.entries(e.children).forEach(([i,s])=>{r[i]===void 0&&(o[i]=s)}),new V(e.segments,o)}}function OT(e,n,t){let r=0,o=n,i={match:!1,pathIndex:0,commandIndex:0};for(;o<e.segments.length;){if(r>=t.length)return i;let s=e.segments[o],a=t[r];if(Jo(a))break;let c=`${a}`,u=r<t.length-1?t[r+1]:null;if(o>0&&c===void 0)break;if(c&&u&&typeof u=="object"&&u.outlets===void 0){if(!Vv(c,u,s))return i;r+=2}else{if(!Vv(c,{},s))return i;r++}o++}return{match:!0,pathIndex:o,commandIndex:r}}function Qd(e,n,t){let r=e.segments.slice(0,n),o=0;for(;o<t.length;){let i=t[o];if(Jo(i)){let c=kT(i.outlets);return new V(r,c)}if(o===0&&ka(t[0])){let c=e.segments[n];r.push(new on(c.path,jv(t[0]))),o++;continue}let s=Jo(i)?i.outlets[A]:`${i}`,a=o<t.length-1?t[o+1]:null;s&&a&&ka(a)?(r.push(new on(s,jv(a))),o+=2):(r.push(new on(s,{})),o++)}return new V(r,{})}function kT(e){let n={};return Object.entries(e).forEach(([t,r])=>{typeof r=="string"&&(r=[r]),r!==null&&(n[t]=Qd(new V([],{}),0,r))}),n}function jv(e){let n={};return Object.entries(e).forEach(([t,r])=>n[t]=`${r}`),n}function Vv(e,n,t){return e==t.path&&Dt(n,t.parameters)}var Qo="imperative",ue=function(e){return e[e.NavigationStart=0]="NavigationStart",e[e.NavigationEnd=1]="NavigationEnd",e[e.NavigationCancel=2]="NavigationCancel",e[e.NavigationError=3]="NavigationError",e[e.RoutesRecognized=4]="RoutesRecognized",e[e.ResolveStart=5]="ResolveStart",e[e.ResolveEnd=6]="ResolveEnd",e[e.GuardsCheckStart=7]="GuardsCheckStart",e[e.GuardsCheckEnd=8]="GuardsCheckEnd",e[e.RouteConfigLoadStart=9]="RouteConfigLoadStart",e[e.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",e[e.ChildActivationStart=11]="ChildActivationStart",e[e.ChildActivationEnd=12]="ChildActivationEnd",e[e.ActivationStart=13]="ActivationStart",e[e.ActivationEnd=14]="ActivationEnd",e[e.Scroll=15]="Scroll",e[e.NavigationSkipped=16]="NavigationSkipped",e}(ue||{}),Le=class{id;url;constructor(n,t){this.id=n,this.url=t}},Ot=class extends Le{type=ue.NavigationStart;navigationTrigger;restoredState;constructor(n,t,r="imperative",o=null){super(n,t),this.navigationTrigger=r,this.restoredState=o}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},st=class extends Le{urlAfterRedirects;type=ue.NavigationEnd;constructor(n,t,r){super(n,t),this.urlAfterRedirects=r}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},we=function(e){return e[e.Redirect=0]="Redirect",e[e.SupersededByNewNavigation=1]="SupersededByNewNavigation",e[e.NoDataFromResolver=2]="NoDataFromResolver",e[e.GuardRejected=3]="GuardRejected",e[e.Aborted=4]="Aborted",e}(we||{}),Xo=function(e){return e[e.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",e[e.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",e}(Xo||{}),Et=class extends Le{reason;code;type=ue.NavigationCancel;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},kt=class extends Le{reason;code;type=ue.NavigationSkipped;constructor(n,t,r,o){super(n,t),this.reason=r,this.code=o}},Br=class extends Le{error;target;type=ue.NavigationError;constructor(n,t,r,o){super(n,t),this.error=r,this.target=o}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},ei=class extends Le{urlAfterRedirects;state;type=ue.RoutesRecognized;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Fa=class extends Le{urlAfterRedirects;state;type=ue.GuardsCheckStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},La=class extends Le{urlAfterRedirects;state;shouldActivate;type=ue.GuardsCheckEnd;constructor(n,t,r,o,i){super(n,t),this.urlAfterRedirects=r,this.state=o,this.shouldActivate=i}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},ja=class extends Le{urlAfterRedirects;state;type=ue.ResolveStart;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Va=class extends Le{urlAfterRedirects;state;type=ue.ResolveEnd;constructor(n,t,r,o){super(n,t),this.urlAfterRedirects=r,this.state=o}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Ba=class{route;type=ue.RouteConfigLoadStart;constructor(n){this.route=n}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Ua=class{route;type=ue.RouteConfigLoadEnd;constructor(n){this.route=n}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ha=class{snapshot;type=ue.ChildActivationStart;constructor(n){this.snapshot=n}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},$a=class{snapshot;type=ue.ChildActivationEnd;constructor(n){this.snapshot=n}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},za=class{snapshot;type=ue.ActivationStart;constructor(n){this.snapshot=n}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Ga=class{snapshot;type=ue.ActivationEnd;constructor(n){this.snapshot=n}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}};var ti=class{},Ur=class{url;navigationBehaviorOptions;constructor(n,t){this.url=n,this.navigationBehaviorOptions=t}};function PT(e){return!(e instanceof ti)&&!(e instanceof Ur)}function FT(e,n){return e.providers&&!e._injector&&(e._injector=Sr(e.providers,n,`Route: ${e.path}`)),e._injector??n}function it(e){return e.outlet||A}function LT(e,n){let t=e.filter(r=>it(r)===n);return t.push(...e.filter(r=>it(r)!==n)),t}function zr(e){if(!e)return null;if(e.routeConfig?._injector)return e.routeConfig._injector;for(let n=e.parent;n;n=n.parent){let t=n.routeConfig;if(t?._loadedInjector)return t._loadedInjector;if(t?._injector)return t._injector}return null}var Wa=class{rootInjector;outlet=null;route=null;children;attachRef=null;get injector(){return zr(this.route?.snapshot)??this.rootInjector}constructor(n){this.rootInjector=n,this.children=new Ft(this.rootInjector)}},Ft=(()=>{class e{rootInjector;contexts=new Map;constructor(t){this.rootInjector=t}onChildOutletCreated(t,r){let o=this.getOrCreateContext(t);o.outlet=r,this.contexts.set(t,o)}onChildOutletDestroyed(t){let r=this.getContext(t);r&&(r.outlet=null,r.attachRef=null)}onOutletDeactivated(){let t=this.contexts;return this.contexts=new Map,t}onOutletReAttached(t){this.contexts=t}getOrCreateContext(t){let r=this.getContext(t);return r||(r=new Wa(this.rootInjector),this.contexts.set(t,r)),r}getContext(t){return this.contexts.get(t)||null}static \u0275fac=function(r){return new(r||e)(I(G))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),qa=class{_root;constructor(n){this._root=n}get root(){return this._root.value}parent(n){let t=this.pathFromRoot(n);return t.length>1?t[t.length-2]:null}children(n){let t=Kd(n,this._root);return t?t.children.map(r=>r.value):[]}firstChild(n){let t=Kd(n,this._root);return t&&t.children.length>0?t.children[0].value:null}siblings(n){let t=Jd(n,this._root);return t.length<2?[]:t[t.length-2].children.map(o=>o.value).filter(o=>o!==n)}pathFromRoot(n){return Jd(n,this._root).map(t=>t.value)}};function Kd(e,n){if(e===n.value)return n;for(let t of n.children){let r=Kd(e,t);if(r)return r}return null}function Jd(e,n){if(e===n.value)return[n];for(let t of n.children){let r=Jd(e,t);if(r.length)return r.unshift(n),r}return[]}var Fe=class{value;children;constructor(n,t){this.value=n,this.children=t}toString(){return`TreeNode(${this.value})`}};function Lr(e){let n={};return e&&e.children.forEach(t=>n[t.value.outlet]=t),n}var ni=class extends qa{snapshot;constructor(n,t){super(n),this.snapshot=t,af(this,n)}toString(){return this.snapshot.toString()}};function iy(e){let n=jT(e),t=new ee([new on("",{})]),r=new ee({}),o=new ee({}),i=new ee({}),s=new ee(""),a=new Ne(t,r,i,s,o,A,e,n.root);return a.snapshot=n.root,new ni(new Fe(a,[]),n)}function jT(e){let n={},t={},r={},o="",i=new Pn([],n,r,o,t,A,e,null,{});return new ri("",new Fe(i,[]))}var Ne=class{urlSubject;paramsSubject;queryParamsSubject;fragmentSubject;dataSubject;outlet;component;snapshot;_futureSnapshot;_routerState;_paramMap;_queryParamMap;title;url;params;queryParams;fragment;data;constructor(n,t,r,o,i,s,a,c){this.urlSubject=n,this.paramsSubject=t,this.queryParamsSubject=r,this.fragmentSubject=o,this.dataSubject=i,this.outlet=s,this.component=a,this._futureSnapshot=c,this.title=this.dataSubject?.pipe(R(u=>u[ai]))??_(void 0),this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(R(n=>Fn(n))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(R(n=>Fn(n))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function Za(e,n,t="emptyOnly"){let r,{routeConfig:o}=e;return n!==null&&(t==="always"||o?.path===""||!n.component&&!n.routeConfig?.loadComponent)?r={params:g(g({},n.params),e.params),data:g(g({},n.data),e.data),resolve:g(g(g(g({},e.data),n.data),o?.data),e._resolvedData)}:r={params:g({},e.params),data:g({},e.data),resolve:g(g({},e.data),e._resolvedData??{})},o&&ay(o)&&(r.resolve[ai]=o.title),r}var Pn=class{url;params;queryParams;fragment;data;outlet;component;routeConfig;_resolve;_resolvedData;_routerState;_paramMap;_queryParamMap;get title(){return this.data?.[ai]}constructor(n,t,r,o,i,s,a,c,u){this.url=n,this.params=t,this.queryParams=r,this.fragment=o,this.data=i,this.outlet=s,this.component=a,this.routeConfig=c,this._resolve=u}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=Fn(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=Fn(this.queryParams),this._queryParamMap}toString(){let n=this.url.map(r=>r.toString()).join("/"),t=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${n}', path:'${t}')`}},ri=class extends qa{url;constructor(n,t){super(t),this.url=n,af(this,t)}toString(){return sy(this._root)}};function af(e,n){n.value._routerState=e,n.children.forEach(t=>af(e,t))}function sy(e){let n=e.children.length>0?` { ${e.children.map(sy).join(", ")} } `:"";return`${e.value}${n}`}function zd(e){if(e.snapshot){let n=e.snapshot,t=e._futureSnapshot;e.snapshot=t,Dt(n.queryParams,t.queryParams)||e.queryParamsSubject.next(t.queryParams),n.fragment!==t.fragment&&e.fragmentSubject.next(t.fragment),Dt(n.params,t.params)||e.paramsSubject.next(t.params),dT(n.url,t.url)||e.urlSubject.next(t.url),Dt(n.data,t.data)||e.dataSubject.next(t.data)}else e.snapshot=e._futureSnapshot,e.dataSubject.next(e._futureSnapshot.data)}function Xd(e,n){let t=Dt(e.params,n.params)&&gT(e.url,n.url),r=!e.parent!=!n.parent;return t&&!r&&(!e.parent||Xd(e.parent,n.parent))}function ay(e){return typeof e.title=="string"||e.title===null}var cy=new E(""),cf=(()=>{class e{activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=A;activateEvents=new z;deactivateEvents=new z;attachEvents=new z;detachEvents=new z;routerOutletData=Zm(void 0);parentContexts=p(Ft);location=p($e);changeDetector=p(ot);inputBinder=p(Ja,{optional:!0});supportsBindingToComponentInputs=!0;ngOnChanges(t){if(t.name){let{firstChange:r,previousValue:o}=t.name;if(r)return;this.isTrackedInParentContexts(o)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(o)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(t){return this.parentContexts.getContext(t)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let t=this.parentContexts.getContext(this.name);t?.route&&(t.attachRef?this.attach(t.attachRef,t.route):this.activateWith(t.route,t.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new D(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new D(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new D(4012,!1);this.location.detach();let t=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(t.instance),t}attach(t,r){this.activated=t,this._activatedRoute=r,this.location.insert(t.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(t.instance)}deactivate(){if(this.activated){let t=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new D(4013,!1);this._activatedRoute=t;let o=this.location,s=t.snapshot.component,a=this.parentContexts.getOrCreateContext(this.name).children,c=new ef(t,a,o.injector,this.routerOutletData);this.activated=o.createComponent(s,{index:o.length,injector:c,environmentInjector:r}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,selectors:[["router-outlet"]],inputs:{name:"name",routerOutletData:[1,"routerOutletData"]},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],features:[Ae]})}return e})(),ef=class{route;childContexts;parent;outletData;constructor(n,t,r,o){this.route=n,this.childContexts=t,this.parent=r,this.outletData=o}get(n,t){return n===Ne?this.route:n===Ft?this.childContexts:n===cy?this.outletData:this.parent.get(n,t)}},Ja=new E("");var uf=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275cmp=sd({type:e,selectors:[["ng-component"]],exportAs:["emptyRouterOutlet"],decls:1,vars:0,template:function(r,o){r&1&&ha(0,"router-outlet")},dependencies:[cf],encapsulation:2})}return e})();function lf(e){let n=e.children&&e.children.map(lf),t=n?N(g({},e),{children:n}):g({},e);return!t.component&&!t.loadComponent&&(n||t.loadChildren)&&t.outlet&&t.outlet!==A&&(t.component=uf),t}function VT(e,n,t){let r=oi(e,n._root,t?t._root:void 0);return new ni(r,n)}function oi(e,n,t){if(t&&e.shouldReuseRoute(n.value,t.value.snapshot)){let r=t.value;r._futureSnapshot=n.value;let o=BT(e,n,t);return new Fe(r,o)}else{if(e.shouldAttach(n.value)){let i=e.retrieve(n.value);if(i!==null){let s=i.route;return s.value._futureSnapshot=n.value,s.children=n.children.map(a=>oi(e,a)),s}}let r=UT(n.value),o=n.children.map(i=>oi(e,i));return new Fe(r,o)}}function BT(e,n,t){return n.children.map(r=>{for(let o of t.children)if(e.shouldReuseRoute(r.value,o.value.snapshot))return oi(e,r,o);return oi(e,r)})}function UT(e){return new Ne(new ee(e.url),new ee(e.params),new ee(e.queryParams),new ee(e.fragment),new ee(e.data),e.outlet,e.component,e)}var Hr=class{redirectTo;navigationBehaviorOptions;constructor(n,t){this.redirectTo=n,this.navigationBehaviorOptions=t}},uy="ngNavigationCancelingError";function Ya(e,n){let{redirectTo:t,navigationBehaviorOptions:r}=sn(n)?{redirectTo:n,navigationBehaviorOptions:void 0}:n,o=ly(!1,we.Redirect);return o.url=t,o.navigationBehaviorOptions=r,o}function ly(e,n){let t=new Error(`NavigationCancelingError: ${e||""}`);return t[uy]=!0,t.cancellationCode=n,t}function HT(e){return dy(e)&&sn(e.url)}function dy(e){return!!e&&e[uy]}var $T=(e,n,t,r)=>R(o=>(new tf(n,o.targetRouterState,o.currentRouterState,t,r).activate(e),o)),tf=class{routeReuseStrategy;futureState;currState;forwardEvent;inputBindingEnabled;constructor(n,t,r,o,i){this.routeReuseStrategy=n,this.futureState=t,this.currState=r,this.forwardEvent=o,this.inputBindingEnabled=i}activate(n){let t=this.futureState._root,r=this.currState?this.currState._root:null;this.deactivateChildRoutes(t,r,n),zd(this.futureState.root),this.activateChildRoutes(t,r,n)}deactivateChildRoutes(n,t,r){let o=Lr(t);n.children.forEach(i=>{let s=i.value.outlet;this.deactivateRoutes(i,o[s],r),delete o[s]}),Object.values(o).forEach(i=>{this.deactivateRouteAndItsChildren(i,r)})}deactivateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(o===i)if(o.component){let s=r.getContext(o.outlet);s&&this.deactivateChildRoutes(n,t,s.children)}else this.deactivateChildRoutes(n,t,r);else i&&this.deactivateRouteAndItsChildren(t,r)}deactivateRouteAndItsChildren(n,t){n.value.component&&this.routeReuseStrategy.shouldDetach(n.value.snapshot)?this.detachAndStoreRouteSubtree(n,t):this.deactivateRouteAndOutlet(n,t)}detachAndStoreRouteSubtree(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Lr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);if(r&&r.outlet){let s=r.outlet.detach(),a=r.children.onOutletDeactivated();this.routeReuseStrategy.store(n.value.snapshot,{componentRef:s,route:n,contexts:a})}}deactivateRouteAndOutlet(n,t){let r=t.getContext(n.value.outlet),o=r&&n.value.component?r.children:t,i=Lr(n);for(let s of Object.values(i))this.deactivateRouteAndItsChildren(s,o);r&&(r.outlet&&(r.outlet.deactivate(),r.children.onOutletDeactivated()),r.attachRef=null,r.route=null)}activateChildRoutes(n,t,r){let o=Lr(t);n.children.forEach(i=>{this.activateRoutes(i,o[i.value.outlet],r),this.forwardEvent(new Ga(i.value.snapshot))}),n.children.length&&this.forwardEvent(new $a(n.value.snapshot))}activateRoutes(n,t,r){let o=n.value,i=t?t.value:null;if(zd(o),o===i)if(o.component){let s=r.getOrCreateContext(o.outlet);this.activateChildRoutes(n,t,s.children)}else this.activateChildRoutes(n,t,r);else if(o.component){let s=r.getOrCreateContext(o.outlet);if(this.routeReuseStrategy.shouldAttach(o.snapshot)){let a=this.routeReuseStrategy.retrieve(o.snapshot);this.routeReuseStrategy.store(o.snapshot,null),s.children.onOutletReAttached(a.contexts),s.attachRef=a.componentRef,s.route=a.route.value,s.outlet&&s.outlet.attach(a.componentRef,a.route.value),zd(a.route.value),this.activateChildRoutes(n,null,s.children)}else s.attachRef=null,s.route=o,s.outlet&&s.outlet.activateWith(o,s.injector),this.activateChildRoutes(n,null,s.children)}else this.activateChildRoutes(n,null,r)}},Qa=class{path;route;constructor(n){this.path=n,this.route=this.path[this.path.length-1]}},Vr=class{component;route;constructor(n,t){this.component=n,this.route=t}};function zT(e,n,t){let r=e._root,o=n?n._root:null;return Zo(r,o,t,[r.value])}function GT(e){let n=e.routeConfig?e.routeConfig.canActivateChild:null;return!n||n.length===0?null:{node:e,guards:n}}function Gr(e,n){let t=Symbol(),r=n.get(e,t);return r===t?typeof e=="function"&&!lu(e)?e:n.get(e):r}function Zo(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=Lr(n);return e.children.forEach(s=>{WT(s,i[s.value.outlet],t,r.concat([s.value]),o),delete i[s.value.outlet]}),Object.entries(i).forEach(([s,a])=>Ko(a,t.getContext(s),o)),o}function WT(e,n,t,r,o={canDeactivateChecks:[],canActivateChecks:[]}){let i=e.value,s=n?n.value:null,a=t?t.getContext(e.value.outlet):null;if(s&&i.routeConfig===s.routeConfig){let c=qT(s,i,i.routeConfig.runGuardsAndResolvers);c?o.canActivateChecks.push(new Qa(r)):(i.data=s.data,i._resolvedData=s._resolvedData),i.component?Zo(e,n,a?a.children:null,r,o):Zo(e,n,t,r,o),c&&a&&a.outlet&&a.outlet.isActivated&&o.canDeactivateChecks.push(new Vr(a.outlet.component,s))}else s&&Ko(n,a,o),o.canActivateChecks.push(new Qa(r)),i.component?Zo(e,null,a?a.children:null,r,o):Zo(e,null,t,r,o);return o}function qT(e,n,t){if(typeof t=="function")return t(e,n);switch(t){case"pathParamsChange":return!kn(e.url,n.url);case"pathParamsOrQueryParamsChange":return!kn(e.url,n.url)||!Dt(e.queryParams,n.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!Xd(e,n)||!Dt(e.queryParams,n.queryParams);case"paramsChange":default:return!Xd(e,n)}}function Ko(e,n,t){let r=Lr(e),o=e.value;Object.entries(r).forEach(([i,s])=>{o.component?n?Ko(s,n.children.getContext(i),t):Ko(s,null,t):Ko(s,n,t)}),o.component?n&&n.outlet&&n.outlet.isActivated?t.canDeactivateChecks.push(new Vr(n.outlet.component,o)):t.canDeactivateChecks.push(new Vr(null,o)):t.canDeactivateChecks.push(new Vr(null,o))}function ci(e){return typeof e=="function"}function ZT(e){return typeof e=="boolean"}function YT(e){return e&&ci(e.canLoad)}function QT(e){return e&&ci(e.canActivate)}function KT(e){return e&&ci(e.canActivateChild)}function JT(e){return e&&ci(e.canDeactivate)}function XT(e){return e&&ci(e.canMatch)}function fy(e){return e instanceof qe||e?.name==="EmptyError"}var Na=Symbol("INITIAL_VALUE");function $r(){return ie(e=>Jn(e.map(n=>n.pipe(wt(1),Uc(Na)))).pipe(R(n=>{for(let t of n)if(t!==!0){if(t===Na)return Na;if(t===!1||eS(t))return t}return!0}),te(n=>n!==Na),wt(1)))}function eS(e){return sn(e)||e instanceof Hr}function tS(e,n){return Y(t=>{let{targetSnapshot:r,currentSnapshot:o,guards:{canActivateChecks:i,canDeactivateChecks:s}}=t;return s.length===0&&i.length===0?_(N(g({},t),{guardsResult:!0})):nS(s,r,o,e).pipe(Y(a=>a&&ZT(a)?rS(r,i,e,n):_(a)),R(a=>N(g({},t),{guardsResult:a})))})}function nS(e,n,t,r){return W(e).pipe(Y(o=>cS(o.component,o.route,t,n,r)),It(o=>o!==!0,!0))}function rS(e,n,t,r){return W(n).pipe(ct(o=>er(iS(o.route.parent,r),oS(o.route,r),aS(e,o.path,t),sS(e,o.route,t))),It(o=>o!==!0,!0))}function oS(e,n){return e!==null&&n&&n(new za(e)),_(!0)}function iS(e,n){return e!==null&&n&&n(new Ha(e)),_(!0)}function sS(e,n,t){let r=n.routeConfig?n.routeConfig.canActivate:null;if(!r||r.length===0)return _(!0);let o=r.map(i=>ro(()=>{let s=zr(n)??t,a=Gr(i,s),c=QT(a)?a.canActivate(n,e):he(s,()=>a(n,e));return Pt(c).pipe(It())}));return _(o).pipe($r())}function aS(e,n,t){let r=n[n.length-1],i=n.slice(0,n.length-1).reverse().map(s=>GT(s)).filter(s=>s!==null).map(s=>ro(()=>{let a=s.guards.map(c=>{let u=zr(s.node)??t,l=Gr(c,u),d=KT(l)?l.canActivateChild(r,e):he(u,()=>l(r,e));return Pt(d).pipe(It())});return _(a).pipe($r())}));return _(i).pipe($r())}function cS(e,n,t,r,o){let i=n&&n.routeConfig?n.routeConfig.canDeactivate:null;if(!i||i.length===0)return _(!0);let s=i.map(a=>{let c=zr(n)??o,u=Gr(a,c),l=JT(u)?u.canDeactivate(e,n,t,r):he(c,()=>u(e,n,t,r));return Pt(l).pipe(It())});return _(s).pipe($r())}function uS(e,n,t,r){let o=n.canLoad;if(o===void 0||o.length===0)return _(!0);let i=o.map(s=>{let a=Gr(s,e),c=YT(a)?a.canLoad(n,t):he(e,()=>a(n,t));return Pt(c)});return _(i).pipe($r(),hy(r))}function hy(e){return xc(se(n=>{if(typeof n!="boolean")throw Ya(e,n)}),R(n=>n===!0))}function lS(e,n,t,r){let o=n.canMatch;if(!o||o.length===0)return _(!0);let i=o.map(s=>{let a=Gr(s,e),c=XT(a)?a.canMatch(n,t):he(e,()=>a(n,t));return Pt(c)});return _(i).pipe($r(),hy(r))}var ii=class{segmentGroup;constructor(n){this.segmentGroup=n||null}},si=class extends Error{urlTree;constructor(n){super(),this.urlTree=n}};function Fr(e){return Qn(new ii(e))}function dS(e){return Qn(new D(4e3,!1))}function fS(e){return Qn(ly(!1,we.GuardRejected))}var nf=class{urlSerializer;urlTree;constructor(n,t){this.urlSerializer=n,this.urlTree=t}lineralizeSegments(n,t){let r=[],o=t.root;for(;;){if(r=r.concat(o.segments),o.numberOfChildren===0)return _(r);if(o.numberOfChildren>1||!o.children[A])return dS(`${n.redirectTo}`);o=o.children[A]}}applyRedirectCommands(n,t,r,o,i){return hS(t,o,i).pipe(R(s=>{if(s instanceof Ct)throw new si(s);let a=this.applyRedirectCreateUrlTree(s,this.urlSerializer.parse(s),n,r);if(s[0]==="/")throw new si(a);return a}))}applyRedirectCreateUrlTree(n,t,r,o){let i=this.createSegmentGroup(n,t.root,r,o);return new Ct(i,this.createQueryParams(t.queryParams,this.urlTree.queryParams),t.fragment)}createQueryParams(n,t){let r={};return Object.entries(n).forEach(([o,i])=>{if(typeof i=="string"&&i[0]===":"){let a=i.substring(1);r[o]=t[a]}else r[o]=i}),r}createSegmentGroup(n,t,r,o){let i=this.createSegments(n,t.segments,r,o),s={};return Object.entries(t.children).forEach(([a,c])=>{s[a]=this.createSegmentGroup(n,c,r,o)}),new V(i,s)}createSegments(n,t,r,o){return t.map(i=>i.path[0]===":"?this.findPosParam(n,i,o):this.findOrReturn(i,r))}findPosParam(n,t,r){let o=r[t.path.substring(1)];if(!o)throw new D(4001,!1);return o}findOrReturn(n,t){let r=0;for(let o of t){if(o.path===n.path)return t.splice(r),o;r++}return n}};function hS(e,n,t){if(typeof e=="string")return _(e);let r=e,{queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,params:u,data:l,title:d}=n;return Pt(he(t,()=>r({params:u,data:l,queryParams:o,fragment:i,routeConfig:s,url:a,outlet:c,title:d})))}var rf={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function pS(e,n,t,r,o){let i=py(e,n,t);return i.matched?(r=FT(n,r),lS(r,n,t,o).pipe(R(s=>s===!0?i:g({},rf)))):_(i)}function py(e,n,t){if(n.path==="**")return gS(t);if(n.path==="")return n.pathMatch==="full"&&(e.hasChildren()||t.length>0)?g({},rf):{matched:!0,consumedSegments:[],remainingSegments:t,parameters:{},positionalParamSegments:{}};let o=(n.matcher||$v)(t,e,n);if(!o)return g({},rf);let i={};Object.entries(o.posParams??{}).forEach(([a,c])=>{i[a]=c.path});let s=o.consumed.length>0?g(g({},i),o.consumed[o.consumed.length-1].parameters):i;return{matched:!0,consumedSegments:o.consumed,remainingSegments:t.slice(o.consumed.length),parameters:s,positionalParamSegments:o.posParams??{}}}function gS(e){return{matched:!0,parameters:e.length>0?Gv(e).parameters:{},consumedSegments:e,remainingSegments:[],positionalParamSegments:{}}}function Bv(e,n,t,r){return t.length>0&&yS(e,t,r)?{segmentGroup:new V(n,vS(r,new V(t,e.children))),slicedSegments:[]}:t.length===0&&DS(e,t,r)?{segmentGroup:new V(e.segments,mS(e,t,r,e.children)),slicedSegments:t}:{segmentGroup:new V(e.segments,e.children),slicedSegments:t}}function mS(e,n,t,r){let o={};for(let i of t)if(Xa(e,n,i)&&!r[it(i)]){let s=new V([],{});o[it(i)]=s}return g(g({},r),o)}function vS(e,n){let t={};t[A]=n;for(let r of e)if(r.path===""&&it(r)!==A){let o=new V([],{});t[it(r)]=o}return t}function yS(e,n,t){return t.some(r=>Xa(e,n,r)&&it(r)!==A)}function DS(e,n,t){return t.some(r=>Xa(e,n,r))}function Xa(e,n,t){return(e.hasChildren()||n.length>0)&&t.pathMatch==="full"?!1:t.path===""}function ES(e,n,t){return n.length===0&&!e.children[t]}var of=class{};function CS(e,n,t,r,o,i,s="emptyOnly"){return new sf(e,n,t,r,o,s,i).recognize()}var wS=31,sf=class{injector;configLoader;rootComponentType;config;urlTree;paramsInheritanceStrategy;urlSerializer;applyRedirects;absoluteRedirectCount=0;allowRedirects=!0;constructor(n,t,r,o,i,s,a){this.injector=n,this.configLoader=t,this.rootComponentType=r,this.config=o,this.urlTree=i,this.paramsInheritanceStrategy=s,this.urlSerializer=a,this.applyRedirects=new nf(this.urlSerializer,this.urlTree)}noMatchError(n){return new D(4002,`'${n.segmentGroup}'`)}recognize(){let n=Bv(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(n).pipe(R(({children:t,rootSnapshot:r})=>{let o=new Fe(r,t),i=new ri("",o),s=ey(r,[],this.urlTree.queryParams,this.urlTree.fragment);return s.queryParams=this.urlTree.queryParams,i.url=this.urlSerializer.serialize(s),{state:i,tree:s}}))}match(n){let t=new Pn([],Object.freeze({}),Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Object.freeze({}),A,this.rootComponentType,null,{});return this.processSegmentGroup(this.injector,this.config,n,A,t).pipe(R(r=>({children:r,rootSnapshot:t})),at(r=>{if(r instanceof si)return this.urlTree=r.urlTree,this.match(r.urlTree.root);throw r instanceof ii?this.noMatchError(r):r}))}processSegmentGroup(n,t,r,o,i){return r.segments.length===0&&r.hasChildren()?this.processChildren(n,t,r,i):this.processSegment(n,t,r,r.segments,o,!0,i).pipe(R(s=>s instanceof Fe?[s]:[]))}processChildren(n,t,r,o){let i=[];for(let s of Object.keys(r.children))s==="primary"?i.unshift(s):i.push(s);return W(i).pipe(ct(s=>{let a=r.children[s],c=LT(t,s);return this.processSegmentGroup(n,c,a,s,o)}),Bc((s,a)=>(s.push(...a),s)),Bt(null),Vc(),Y(s=>{if(s===null)return Fr(r);let a=gy(s);return IS(a),_(a)}))}processSegment(n,t,r,o,i,s,a){return W(t).pipe(ct(c=>this.processSegmentAgainstRoute(c._injector??n,t,c,r,o,i,s,a).pipe(at(u=>{if(u instanceof ii)return _(null);throw u}))),It(c=>!!c),at(c=>{if(fy(c))return ES(r,o,i)?_(new of):Fr(r);throw c}))}processSegmentAgainstRoute(n,t,r,o,i,s,a,c){return it(r)!==s&&(s===A||!Xa(o,i,r))?Fr(o):r.redirectTo===void 0?this.matchSegmentAgainstRoute(n,o,r,i,s,c):this.allowRedirects&&a?this.expandSegmentAgainstRouteUsingRedirect(n,o,t,r,i,s,c):Fr(o)}expandSegmentAgainstRouteUsingRedirect(n,t,r,o,i,s,a){let{matched:c,parameters:u,consumedSegments:l,positionalParamSegments:d,remainingSegments:h}=py(t,o,i);if(!c)return Fr(t);typeof o.redirectTo=="string"&&o.redirectTo[0]==="/"&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>wS&&(this.allowRedirects=!1));let f=new Pn(i,u,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Uv(o),it(o),o.component??o._loadedComponent??null,o,Hv(o)),m=Za(f,a,this.paramsInheritanceStrategy);return f.params=Object.freeze(m.params),f.data=Object.freeze(m.data),this.applyRedirects.applyRedirectCommands(l,o.redirectTo,d,f,n).pipe(ie(y=>this.applyRedirects.lineralizeSegments(o,y)),Y(y=>this.processSegment(n,r,t,y.concat(h),s,!1,a)))}matchSegmentAgainstRoute(n,t,r,o,i,s){let a=pS(t,r,o,n,this.urlSerializer);return r.path==="**"&&(t.children={}),a.pipe(ie(c=>c.matched?(n=r._injector??n,this.getChildConfig(n,r,o).pipe(ie(({routes:u})=>{let l=r._loadedInjector??n,{parameters:d,consumedSegments:h,remainingSegments:f}=c,m=new Pn(h,d,Object.freeze(g({},this.urlTree.queryParams)),this.urlTree.fragment,Uv(r),it(r),r.component??r._loadedComponent??null,r,Hv(r)),b=Za(m,s,this.paramsInheritanceStrategy);m.params=Object.freeze(b.params),m.data=Object.freeze(b.data);let{segmentGroup:y,slicedSegments:C}=Bv(t,h,f,u);if(C.length===0&&y.hasChildren())return this.processChildren(l,u,y,m).pipe(R(Ie=>new Fe(m,Ie)));if(u.length===0&&C.length===0)return _(new Fe(m,[]));let le=it(r)===i;return this.processSegment(l,u,y,C,le?A:i,!0,m).pipe(R(Ie=>new Fe(m,Ie instanceof Fe?[Ie]:[])))}))):Fr(t)))}getChildConfig(n,t,r){return t.children?_({routes:t.children,injector:n}):t.loadChildren?t._loadedRoutes!==void 0?_({routes:t._loadedRoutes,injector:t._loadedInjector}):uS(n,t,r,this.urlSerializer).pipe(Y(o=>o?this.configLoader.loadChildren(n,t).pipe(se(i=>{t._loadedRoutes=i.routes,t._loadedInjector=i.injector})):fS(t))):_({routes:[],injector:n})}};function IS(e){e.sort((n,t)=>n.value.outlet===A?-1:t.value.outlet===A?1:n.value.outlet.localeCompare(t.value.outlet))}function bS(e){let n=e.value.routeConfig;return n&&n.path===""}function gy(e){let n=[],t=new Set;for(let r of e){if(!bS(r)){n.push(r);continue}let o=n.find(i=>r.value.routeConfig===i.value.routeConfig);o!==void 0?(o.children.push(...r.children),t.add(o)):n.push(r)}for(let r of t){let o=gy(r.children);n.push(new Fe(r.value,o))}return n.filter(r=>!t.has(r))}function Uv(e){return e.data||{}}function Hv(e){return e.resolve||{}}function _S(e,n,t,r,o,i){return Y(s=>CS(e,n,t,r,s.extractedUrl,o,i).pipe(R(({state:a,tree:c})=>N(g({},s),{targetSnapshot:a,urlAfterRedirects:c}))))}function TS(e,n){return Y(t=>{let{targetSnapshot:r,guards:{canActivateChecks:o}}=t;if(!o.length)return _(t);let i=new Set(o.map(c=>c.route)),s=new Set;for(let c of i)if(!s.has(c))for(let u of my(c))s.add(u);let a=0;return W(s).pipe(ct(c=>i.has(c)?SS(c,r,e,n):(c.data=Za(c,c.parent,e).resolve,_(void 0))),se(()=>a++),tr(1),Y(c=>a===s.size?_(t):be))})}function my(e){let n=e.children.map(t=>my(t)).flat();return[e,...n]}function SS(e,n,t,r){let o=e.routeConfig,i=e._resolve;return o?.title!==void 0&&!ay(o)&&(i[ai]=o.title),ro(()=>(e.data=Za(e,e.parent,t).resolve,MS(i,e,n,r).pipe(R(s=>(e._resolvedData=s,e.data=g(g({},e.data),s),null)))))}function MS(e,n,t,r){let o=qd(e);if(o.length===0)return _({});let i={};return W(o).pipe(Y(s=>AS(e[s],n,t,r).pipe(It(),se(a=>{if(a instanceof Hr)throw Ya(new Ln,a);i[s]=a}))),tr(1),R(()=>i),at(s=>fy(s)?be:Qn(s)))}function AS(e,n,t,r){let o=zr(n)??r,i=Gr(e,o),s=i.resolve?i.resolve(n,t):he(o,()=>i(n,t));return Pt(s)}function Gd(e){return ie(n=>{let t=e(n);return t?W(t).pipe(R(()=>n)):_(n)})}var df=(()=>{class e{buildTitle(t){let r,o=t.root;for(;o!==void 0;)r=this.getResolvedTitleForRoute(o)??r,o=o.children.find(i=>i.outlet===A);return r}getResolvedTitleForRoute(t){return t.data[ai]}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(vy),providedIn:"root"})}return e})(),vy=(()=>{class e extends df{title;constructor(t){super(),this.title=t}updateTitle(t){let r=this.buildTitle(t);r!==void 0&&this.title.setTitle(r)}static \u0275fac=function(r){return new(r||e)(I(Pv))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Wr=new E("",{providedIn:"root",factory:()=>({})}),ui=new E(""),ff=(()=>{class e{componentLoaders=new WeakMap;childrenLoaders=new WeakMap;onLoadStartListener;onLoadEndListener;compiler=p(Dd);loadComponent(t,r){if(this.componentLoaders.get(r))return this.componentLoaders.get(r);if(r._loadedComponent)return _(r._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(r);let o=Pt(he(t,()=>r.loadComponent())).pipe(R(Dy),se(s=>{this.onLoadEndListener&&this.onLoadEndListener(r),r._loadedComponent=s}),Ut(()=>{this.componentLoaders.delete(r)})),i=new Zn(o,()=>new $).pipe(qn());return this.componentLoaders.set(r,i),i}loadChildren(t,r){if(this.childrenLoaders.get(r))return this.childrenLoaders.get(r);if(r._loadedRoutes)return _({routes:r._loadedRoutes,injector:r._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(r);let i=yy(r,this.compiler,t,this.onLoadEndListener).pipe(Ut(()=>{this.childrenLoaders.delete(r)})),s=new Zn(i,()=>new $).pipe(qn());return this.childrenLoaders.set(r,s),s}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function yy(e,n,t,r){return Pt(he(t,()=>e.loadChildren())).pipe(R(Dy),Y(o=>o instanceof ua||Array.isArray(o)?_(o):W(n.compileModuleAsync(o))),R(o=>{r&&r(e);let i,s,a=!1;return Array.isArray(o)?(s=o,a=!0):(i=o.create(t).injector,s=i.get(ui,[],{optional:!0,self:!0}).flat()),{routes:s.map(lf),injector:i}}))}function NS(e){return e&&typeof e=="object"&&"default"in e}function Dy(e){return NS(e)?e.default:e}var ec=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(RS),providedIn:"root"})}return e})(),RS=(()=>{class e{shouldProcessUrl(t){return!0}extract(t){return t}merge(t,r){return t}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),Ey=new E("");var Cy=new E(""),wy=(()=>{class e{currentNavigation=null;currentTransition=null;lastSuccessfulNavigation=null;events=new $;transitionAbortWithErrorSubject=new $;configLoader=p(ff);environmentInjector=p(G);destroyRef=p(Xe);urlSerializer=p(jn);rootContexts=p(Ft);location=p(Rt);inputBindingEnabled=p(Ja,{optional:!0})!==null;titleStrategy=p(df);options=p(Wr,{optional:!0})||{};paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly";urlHandlingStrategy=p(ec);createViewTransition=p(Ey,{optional:!0});navigationErrorHandler=p(Cy,{optional:!0});navigationId=0;get hasRequestedNavigation(){return this.navigationId!==0}transitions;afterPreactivation=()=>_(void 0);rootComponentType=null;destroyed=!1;constructor(){let t=o=>this.events.next(new Ba(o)),r=o=>this.events.next(new Ua(o));this.configLoader.onLoadEndListener=r,this.configLoader.onLoadStartListener=t,this.destroyRef.onDestroy(()=>{this.destroyed=!0})}complete(){this.transitions?.complete()}handleNavigationRequest(t){let r=++this.navigationId;this.transitions?.next(N(g({},t),{extractedUrl:this.urlHandlingStrategy.extract(t.rawUrl),targetSnapshot:null,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null,abortController:new AbortController,id:r}))}setupNavigations(t){return this.transitions=new ee(null),this.transitions.pipe(te(r=>r!==null),ie(r=>{let o=!1;return _(r).pipe(ie(i=>{if(this.navigationId>r.id)return this.cancelNavigationTransition(r,"",we.SupersededByNewNavigation),be;this.currentTransition=r,this.currentNavigation={id:i.id,initialUrl:i.rawUrl,extractedUrl:i.extractedUrl,targetBrowserUrl:typeof i.extras.browserUrl=="string"?this.urlSerializer.parse(i.extras.browserUrl):i.extras.browserUrl,trigger:i.source,extras:i.extras,previousNavigation:this.lastSuccessfulNavigation?N(g({},this.lastSuccessfulNavigation),{previousNavigation:null}):null,abort:()=>i.abortController.abort()};let s=!t.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),a=i.extras.onSameUrlNavigation??t.onSameUrlNavigation;if(!s&&a!=="reload"){let c="";return this.events.next(new kt(i.id,this.urlSerializer.serialize(i.rawUrl),c,Xo.IgnoredSameUrlNavigation)),i.resolve(!1),be}if(this.urlHandlingStrategy.shouldProcessUrl(i.rawUrl))return _(i).pipe(ie(c=>(this.events.next(new Ot(c.id,this.urlSerializer.serialize(c.extractedUrl),c.source,c.restoredState)),c.id!==this.navigationId?be:Promise.resolve(c))),_S(this.environmentInjector,this.configLoader,this.rootComponentType,t.config,this.urlSerializer,this.paramsInheritanceStrategy),se(c=>{r.targetSnapshot=c.targetSnapshot,r.urlAfterRedirects=c.urlAfterRedirects,this.currentNavigation=N(g({},this.currentNavigation),{finalUrl:c.urlAfterRedirects});let u=new ei(c.id,this.urlSerializer.serialize(c.extractedUrl),this.urlSerializer.serialize(c.urlAfterRedirects),c.targetSnapshot);this.events.next(u)}));if(s&&this.urlHandlingStrategy.shouldProcessUrl(i.currentRawUrl)){let{id:c,extractedUrl:u,source:l,restoredState:d,extras:h}=i,f=new Ot(c,this.urlSerializer.serialize(u),l,d);this.events.next(f);let m=iy(this.rootComponentType).snapshot;return this.currentTransition=r=N(g({},i),{targetSnapshot:m,urlAfterRedirects:u,extras:N(g({},h),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=u,_(r)}else{let c="";return this.events.next(new kt(i.id,this.urlSerializer.serialize(i.extractedUrl),c,Xo.IgnoredByUrlHandlingStrategy)),i.resolve(!1),be}}),se(i=>{let s=new Fa(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot);this.events.next(s)}),R(i=>(this.currentTransition=r=N(g({},i),{guards:zT(i.targetSnapshot,i.currentSnapshot,this.rootContexts)}),r)),tS(this.environmentInjector,i=>this.events.next(i)),se(i=>{if(r.guardsResult=i.guardsResult,i.guardsResult&&typeof i.guardsResult!="boolean")throw Ya(this.urlSerializer,i.guardsResult);let s=new La(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects),i.targetSnapshot,!!i.guardsResult);this.events.next(s)}),te(i=>i.guardsResult?!0:(this.cancelNavigationTransition(i,"",we.GuardRejected),!1)),Gd(i=>{if(i.guards.canActivateChecks.length!==0)return _(i).pipe(se(s=>{let a=new ja(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}),ie(s=>{let a=!1;return _(s).pipe(TS(this.paramsInheritanceStrategy,this.environmentInjector),se({next:()=>a=!0,complete:()=>{a||this.cancelNavigationTransition(s,"",we.NoDataFromResolver)}}))}),se(s=>{let a=new Va(s.id,this.urlSerializer.serialize(s.extractedUrl),this.urlSerializer.serialize(s.urlAfterRedirects),s.targetSnapshot);this.events.next(a)}))}),Gd(i=>{let s=a=>{let c=[];if(a.routeConfig?.loadComponent&&!a.routeConfig._loadedComponent){let u=zr(a)??this.environmentInjector;c.push(this.configLoader.loadComponent(u,a.routeConfig).pipe(se(l=>{a.component=l}),R(()=>{})))}for(let u of a.children)c.push(...s(u));return c};return Jn(s(i.targetSnapshot.root)).pipe(Bt(null),wt(1))}),Gd(()=>this.afterPreactivation()),ie(()=>{let{currentSnapshot:i,targetSnapshot:s}=r,a=this.createViewTransition?.(this.environmentInjector,i.root,s.root);return a?W(a).pipe(R(()=>r)):_(r)}),R(i=>{let s=VT(t.routeReuseStrategy,i.targetSnapshot,i.currentRouterState);return this.currentTransition=r=N(g({},i),{targetRouterState:s}),this.currentNavigation.targetRouterState=s,r}),se(()=>{this.events.next(new ti)}),$T(this.rootContexts,t.routeReuseStrategy,i=>this.events.next(i),this.inputBindingEnabled),wt(1),$i(new O(i=>{let s=r.abortController.signal,a=()=>i.next();return s.addEventListener("abort",a),()=>s.removeEventListener("abort",a)}).pipe(te(()=>!o&&!r.targetRouterState),se(()=>{this.cancelNavigationTransition(r,r.abortController.signal.reason+"",we.Aborted)}))),se({next:i=>{o=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new st(i.id,this.urlSerializer.serialize(i.extractedUrl),this.urlSerializer.serialize(i.urlAfterRedirects))),this.titleStrategy?.updateTitle(i.targetRouterState.snapshot),i.resolve(!0)},complete:()=>{o=!0}}),$i(this.transitionAbortWithErrorSubject.pipe(se(i=>{throw i}))),Ut(()=>{o||this.cancelNavigationTransition(r,"",we.SupersededByNewNavigation),this.currentTransition?.id===r.id&&(this.currentNavigation=null,this.currentTransition=null)}),at(i=>{if(this.destroyed)return r.resolve(!1),be;if(o=!0,dy(i))this.events.next(new Et(r.id,this.urlSerializer.serialize(r.extractedUrl),i.message,i.cancellationCode)),HT(i)?this.events.next(new Ur(i.url,i.navigationBehaviorOptions)):r.resolve(!1);else{let s=new Br(r.id,this.urlSerializer.serialize(r.extractedUrl),i,r.targetSnapshot??void 0);try{let a=he(this.environmentInjector,()=>this.navigationErrorHandler?.(s));if(a instanceof Hr){let{message:c,cancellationCode:u}=Ya(this.urlSerializer,a);this.events.next(new Et(r.id,this.urlSerializer.serialize(r.extractedUrl),c,u)),this.events.next(new Ur(a.redirectTo,a.navigationBehaviorOptions))}else throw this.events.next(s),i}catch(a){this.options.resolveNavigationPromiseOnError?r.resolve(!1):r.reject(a)}}return be}))}))}cancelNavigationTransition(t,r,o){let i=new Et(t.id,this.urlSerializer.serialize(t.extractedUrl),r,o);this.events.next(i),t.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){let t=this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))),r=this.currentNavigation?.targetBrowserUrl??this.currentNavigation?.extractedUrl;return t.toString()!==r?.toString()&&!this.currentNavigation?.extras.skipLocationChange}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function xS(e){return e!==Qo}var Iy=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(OS),providedIn:"root"})}return e})(),Ka=class{shouldDetach(n){return!1}store(n,t){}shouldAttach(n){return!1}retrieve(n){return null}shouldReuseRoute(n,t){return n.routeConfig===t.routeConfig}},OS=(()=>{class e extends Ka{static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),by=(()=>{class e{urlSerializer=p(jn);options=p(Wr,{optional:!0})||{};canceledNavigationResolution=this.options.canceledNavigationResolution||"replace";location=p(Rt);urlHandlingStrategy=p(ec);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";currentUrlTree=new Ct;getCurrentUrlTree(){return this.currentUrlTree}rawUrlTree=this.currentUrlTree;getRawUrlTree(){return this.rawUrlTree}createBrowserPath({finalUrl:t,initialUrl:r,targetBrowserUrl:o}){let i=t!==void 0?this.urlHandlingStrategy.merge(t,r):r,s=o??i;return s instanceof Ct?this.urlSerializer.serialize(s):s}commitTransition({targetRouterState:t,finalUrl:r,initialUrl:o}){r&&t?(this.currentUrlTree=r,this.rawUrlTree=this.urlHandlingStrategy.merge(r,o),this.routerState=t):this.rawUrlTree=o}routerState=iy(null);getRouterState(){return this.routerState}stateMemento=this.createStateMemento();updateStateMemento(){this.stateMemento=this.createStateMemento()}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}resetInternalState({finalUrl:t}){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,t??this.rawUrlTree)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:()=>p(kS),providedIn:"root"})}return e})(),kS=(()=>{class e extends by{currentPageId=0;lastSuccessfulId=-1;restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}registerNonRouterCurrentEntryChangeListener(t){return this.location.subscribe(r=>{r.type==="popstate"&&setTimeout(()=>{t(r.url,r.state,"popstate")})})}handleRouterEvent(t,r){t instanceof Ot?this.updateStateMemento():t instanceof kt?this.commitTransition(r):t instanceof ei?this.urlUpdateStrategy==="eager"&&(r.extras.skipLocationChange||this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof ti?(this.commitTransition(r),this.urlUpdateStrategy==="deferred"&&!r.extras.skipLocationChange&&this.setBrowserUrl(this.createBrowserPath(r),r)):t instanceof Et&&t.code!==we.SupersededByNewNavigation&&t.code!==we.Redirect?this.restoreHistory(r):t instanceof Br?this.restoreHistory(r,!0):t instanceof st&&(this.lastSuccessfulId=t.id,this.currentPageId=this.browserPageId)}setBrowserUrl(t,{extras:r,id:o}){let{replaceUrl:i,state:s}=r;if(this.location.isCurrentPathEqualTo(t)||i){let a=this.browserPageId,c=g(g({},s),this.generateNgRouterState(o,a));this.location.replaceState(t,"",c)}else{let a=g(g({},s),this.generateNgRouterState(o,this.browserPageId+1));this.location.go(t,"",a)}}restoreHistory(t,r=!1){if(this.canceledNavigationResolution==="computed"){let o=this.browserPageId,i=this.currentPageId-o;i!==0?this.location.historyGo(i):this.getCurrentUrlTree()===t.finalUrl&&i===0&&(this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(r&&this.resetInternalState(t),this.resetUrlToCurrentUrlTree())}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.getRawUrlTree()),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(t,r){return this.canceledNavigationResolution==="computed"?{navigationId:t,\u0275routerPageId:r}:{navigationId:t}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function hf(e,n){e.events.pipe(te(t=>t instanceof st||t instanceof Et||t instanceof Br||t instanceof kt),R(t=>t instanceof st||t instanceof kt?0:(t instanceof Et?t.code===we.Redirect||t.code===we.SupersededByNewNavigation:!1)?2:1),te(t=>t!==2),wt(1)).subscribe(()=>{n()})}var PS={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},FS={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},je=(()=>{class e{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}disposed=!1;nonRouterCurrentEntryChangeSubscription;console=p(ld);stateManager=p(by);options=p(Wr,{optional:!0})||{};pendingTasks=p(gt);urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred";navigationTransitions=p(wy);urlSerializer=p(jn);location=p(Rt);urlHandlingStrategy=p(ec);injector=p(G);_events=new $;get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}navigated=!1;routeReuseStrategy=p(Iy);onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore";config=p(ui,{optional:!0})?.flat()??[];componentInputBindingEnabled=!!p(Ja,{optional:!0});constructor(){this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this).subscribe({error:t=>{this.console.warn(t)}}),this.subscribeToNavigationEvents()}eventsSubscription=new X;subscribeToNavigationEvents(){let t=this.navigationTransitions.events.subscribe(r=>{try{let o=this.navigationTransitions.currentTransition,i=this.navigationTransitions.currentNavigation;if(o!==null&&i!==null){if(this.stateManager.handleRouterEvent(r,i),r instanceof Et&&r.code!==we.Redirect&&r.code!==we.SupersededByNewNavigation)this.navigated=!0;else if(r instanceof st)this.navigated=!0;else if(r instanceof Ur){let s=r.navigationBehaviorOptions,a=this.urlHandlingStrategy.merge(r.url,o.currentRawUrl),c=g({browserUrl:o.extras.browserUrl,info:o.extras.info,skipLocationChange:o.extras.skipLocationChange,replaceUrl:o.extras.replaceUrl||this.urlUpdateStrategy==="eager"||xS(o.source)},s);this.scheduleNavigation(a,Qo,null,c,{resolve:o.resolve,reject:o.reject,promise:o.promise})}}PT(r)&&this._events.next(r)}catch(o){this.navigationTransitions.transitionAbortWithErrorSubject.next(o)}});this.eventsSubscription.add(t)}resetRootComponentType(t){this.routerState.root.component=t,this.navigationTransitions.rootComponentType=t}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),Qo,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((t,r,o)=>{this.navigateToSyncWithBrowser(t,o,r)})}navigateToSyncWithBrowser(t,r,o){let i={replaceUrl:!0},s=o?.navigationId?o:null;if(o){let c=g({},o);delete c.navigationId,delete c.\u0275routerPageId,Object.keys(c).length!==0&&(i.state=c)}let a=this.parseUrl(t);this.scheduleNavigation(a,r,s,i).catch(c=>{this.disposed||this.injector.get(Me)(c)})}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(t){this.config=t.map(lf),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this._events.unsubscribe(),this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(t,r={}){let{relativeTo:o,queryParams:i,fragment:s,queryParamsHandling:a,preserveFragment:c}=r,u=c?this.currentUrlTree.fragment:s,l=null;switch(a??this.options.defaultQueryParamsHandling){case"merge":l=g(g({},this.currentUrlTree.queryParams),i);break;case"preserve":l=this.currentUrlTree.queryParams;break;default:l=i||null}l!==null&&(l=this.removeEmptyProps(l));let d;try{let h=o?o.snapshot:this.routerState.snapshot.root;d=ty(h)}catch{(typeof t[0]!="string"||t[0][0]!=="/")&&(t=[]),d=this.currentUrlTree.root}return ny(d,t,l,u??null)}navigateByUrl(t,r={skipLocationChange:!1}){let o=sn(t)?t:this.parseUrl(t),i=this.urlHandlingStrategy.merge(o,this.rawUrlTree);return this.scheduleNavigation(i,Qo,null,r)}navigate(t,r={skipLocationChange:!1}){return LS(t),this.navigateByUrl(this.createUrlTree(t,r),r)}serializeUrl(t){return this.urlSerializer.serialize(t)}parseUrl(t){try{return this.urlSerializer.parse(t)}catch{return this.urlSerializer.parse("/")}}isActive(t,r){let o;if(r===!0?o=g({},PS):r===!1?o=g({},FS):o=r,sn(t))return Fv(this.currentUrlTree,t,o);let i=this.parseUrl(t);return Fv(this.currentUrlTree,i,o)}removeEmptyProps(t){return Object.entries(t).reduce((r,[o,i])=>(i!=null&&(r[o]=i),r),{})}scheduleNavigation(t,r,o,i,s){if(this.disposed)return Promise.resolve(!1);let a,c,u;s?(a=s.resolve,c=s.reject,u=s.promise):u=new Promise((d,h)=>{a=d,c=h});let l=this.pendingTasks.add();return hf(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(l))}),this.navigationTransitions.handleNavigationRequest({source:r,restoredState:o,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:t,extras:i,resolve:a,reject:c,promise:u,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),u.catch(d=>Promise.reject(d))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();function LS(e){for(let n=0;n<e.length;n++)if(e[n]==null)throw new D(4008,!1)}var di=(()=>{class e{router;route;tabIndexAttribute;renderer;el;locationStrategy;reactiveHref=St(null);get href(){return Ge(this.reactiveHref)}set href(t){this.reactiveHref.set(t)}target;queryParams;fragment;queryParamsHandling;state;info;relativeTo;isAnchorElement;subscription;onChanges=new $;applicationErrorHandler=p(Me);options=p(Wr,{optional:!0});constructor(t,r,o,i,s,a){this.router=t,this.route=r,this.tabIndexAttribute=o,this.renderer=i,this.el=s,this.locationStrategy=a,this.reactiveHref.set(p(new va("href"),{optional:!0}));let c=s.nativeElement.tagName?.toLowerCase();this.isAnchorElement=c==="a"||c==="area"||!!(typeof customElements=="object"&&customElements.get(c)?.observedAttributes?.includes?.("href")),this.isAnchorElement?this.setTabIndexIfNotOnNativeEl("0"):this.subscribeToNavigationEventsIfNecessary()}subscribeToNavigationEventsIfNecessary(){if(this.subscription!==void 0||!this.isAnchorElement)return;let t=this.preserveFragment,r=o=>o==="merge"||o==="preserve";t||=r(this.queryParamsHandling),t||=!this.queryParamsHandling&&!r(this.options?.defaultQueryParamsHandling),t&&(this.subscription=this.router.events.subscribe(o=>{o instanceof st&&this.updateHref()}))}preserveFragment=!1;skipLocationChange=!1;replaceUrl=!1;setTabIndexIfNotOnNativeEl(t){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",t)}ngOnChanges(t){this.isAnchorElement&&(this.updateHref(),this.subscribeToNavigationEventsIfNecessary()),this.onChanges.next(this)}routerLinkInput=null;set routerLink(t){t==null?(this.routerLinkInput=null,this.setTabIndexIfNotOnNativeEl(null)):(sn(t)?this.routerLinkInput=t:this.routerLinkInput=Array.isArray(t)?t:[t],this.setTabIndexIfNotOnNativeEl("0"))}onClick(t,r,o,i,s){let a=this.urlTree;if(a===null||this.isAnchorElement&&(t!==0||r||o||i||s||typeof this.target=="string"&&this.target!="_self"))return!0;let c={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(a,c)?.catch(u=>{this.applicationErrorHandler(u)}),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let t=this.urlTree;this.reactiveHref.set(t!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(t))??"":null)}applyAttributeValue(t,r){let o=this.renderer,i=this.el.nativeElement;r!==null?o.setAttribute(i,t,r):o.removeAttribute(i,t)}get urlTree(){return this.routerLinkInput===null?null:sn(this.routerLinkInput)?this.routerLinkInput:this.router.createUrlTree(this.routerLinkInput,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static \u0275fac=function(r){return new(r||e)(v(je),v(Ne),Jt("tabindex"),v(At),v(q),v(Nt))};static \u0275dir=F({type:e,selectors:[["","routerLink",""]],hostVars:2,hostBindings:function(r,o){r&1&&me("click",function(s){return o.onClick(s.button,s.ctrlKey,s.shiftKey,s.altKey,s.metaKey)}),r&2&&tn("href",o.reactiveHref(),Vl)("target",o.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[2,"preserveFragment","preserveFragment",Rr],skipLocationChange:[2,"skipLocationChange","skipLocationChange",Rr],replaceUrl:[2,"replaceUrl","replaceUrl",Rr],routerLink:"routerLink"},features:[Ae]})}return e})();var li=class{},VS=(()=>{class e{preload(t,r){return r().pipe(at(()=>_(null)))}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var _y=(()=>{class e{router;injector;preloadingStrategy;loader;subscription;constructor(t,r,o,i){this.router=t,this.injector=r,this.preloadingStrategy=o,this.loader=i}setUpPreloading(){this.subscription=this.router.events.pipe(te(t=>t instanceof st),ct(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(t,r){let o=[];for(let i of r){i.providers&&!i._injector&&(i._injector=Sr(i.providers,t,`Route: ${i.path}`));let s=i._injector??t,a=i._loadedInjector??s;(i.loadChildren&&!i._loadedRoutes&&i.canLoad===void 0||i.loadComponent&&!i._loadedComponent)&&o.push(this.preloadConfig(s,i)),(i.children||i._loadedRoutes)&&o.push(this.processRoutes(a,i.children??i._loadedRoutes))}return W(o).pipe(Xn())}preloadConfig(t,r){return this.preloadingStrategy.preload(r,()=>{let o;r.loadChildren&&r.canLoad===void 0?o=this.loader.loadChildren(t,r):o=_(null);let i=o.pipe(Y(s=>s===null?_(void 0):(r._loadedRoutes=s.routes,r._loadedInjector=s.injector,this.processRoutes(s.injector??t,s.routes))));if(r.loadComponent&&!r._loadedComponent){let s=this.loader.loadComponent(t,r);return W([i,s]).pipe(Xn())}else return i})}static \u0275fac=function(r){return new(r||e)(I(je),I(G),I(li),I(ff))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),BS=new E("");function US(e,...n){return ur([{provide:ui,multi:!0,useValue:e},[],{provide:Ne,useFactory:HS,deps:[je]},{provide:fa,multi:!0,useFactory:zS},n.map(t=>t.\u0275providers)])}function HS(e){return e.routerState.root}function $S(e,n){return{\u0275kind:e,\u0275providers:n}}function zS(){let e=p(fe);return n=>{let t=e.get(nt);if(n!==t.components[0])return;let r=e.get(je),o=e.get(GS);e.get(WS)===1&&r.initialNavigation(),e.get(Ty,null,{optional:!0})?.setUpPreloading(),e.get(BS,null,{optional:!0})?.init(),r.resetRootComponentType(t.componentTypes[0]),o.closed||(o.next(),o.complete(),o.unsubscribe())}}var GS=new E("",{factory:()=>new $}),WS=new E("",{providedIn:"root",factory:()=>1});var Ty=new E("");function qS(e){return $S(0,[{provide:Ty,useExisting:_y},{provide:li,useExisting:e}])}var Ly=(()=>{class e{_renderer;_elementRef;onChange=t=>{};onTouched=()=>{};constructor(t,r){this._renderer=t,this._elementRef=r}setProperty(t,r){this._renderer.setProperty(this._elementRef.nativeElement,t,r)}registerOnTouched(t){this.onTouched=t}registerOnChange(t){this.onChange=t}setDisabledState(t){this.setProperty("disabled",t)}static \u0275fac=function(r){return new(r||e)(v(At),v(q))};static \u0275dir=F({type:e})}return e})(),lc=(()=>{class e extends Ly{static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275dir=F({type:e,features:[Ce]})}return e})(),mi=new E("");var YS={provide:mi,useExisting:Te(()=>jy),multi:!0};function QS(){let e=We()?We().getUserAgent():"";return/android (\d+)/.test(e.toLowerCase())}var KS=new E(""),jy=(()=>{class e extends Ly{_compositionMode;_composing=!1;constructor(t,r,o){super(t,r),this._compositionMode=o,this._compositionMode==null&&(this._compositionMode=!QS())}writeValue(t){let r=t??"";this.setProperty("value",r)}_handleInput(t){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(t)}_compositionStart(){this._composing=!0}_compositionEnd(t){this._composing=!1,this._compositionMode&&this.onChange(t)}static \u0275fac=function(r){return new(r||e)(v(At),v(q),v(KS,8))};static \u0275dir=F({type:e,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(r,o){r&1&&me("input",function(s){return o._handleInput(s.target.value)})("blur",function(){return o.onTouched()})("compositionstart",function(){return o._compositionStart()})("compositionend",function(s){return o._compositionEnd(s.target.value)})},standalone:!1,features:[rt([YS]),Ce]})}return e})();function JS(e){return e==null||XS(e)===0}function XS(e){return e==null?null:Array.isArray(e)||typeof e=="string"?e.length:e instanceof Set?e.size:null}var vi=new E(""),Vy=new E("");function eM(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t<e?{min:{min:e,actual:n.value}}:null}}function tM(e){return n=>{if(n.value==null||e==null)return null;let t=parseFloat(n.value);return!isNaN(t)&&t>e?{max:{max:e,actual:n.value}}:null}}function nM(e){return JS(e.value)?{required:!0}:null}function Sy(e){return null}function By(e){return e!=null}function Uy(e){return en(e)?W(e):e}function Hy(e){let n={};return e.forEach(t=>{n=t!=null?g(g({},n),t):n}),Object.keys(n).length===0?null:n}function $y(e,n){return n.map(t=>t(e))}function rM(e){return!e.validate}function zy(e){return e.map(n=>rM(n)?n:t=>n.validate(t))}function oM(e){if(!e)return null;let n=e.filter(By);return n.length==0?null:function(t){return Hy($y(t,n))}}function Gy(e){return e!=null?oM(zy(e)):null}function iM(e){if(!e)return null;let n=e.filter(By);return n.length==0?null:function(t){let r=$y(t,n).map(Uy);return Lc(r).pipe(R(Hy))}}function Wy(e){return e!=null?iM(zy(e)):null}function My(e,n){return e===null?[n]:Array.isArray(e)?[...e,n]:[e,n]}function qy(e){return e._rawValidators}function Zy(e){return e._rawAsyncValidators}function pf(e){return e?Array.isArray(e)?e:[e]:[]}function rc(e,n){return Array.isArray(e)?e.includes(n):e===n}function Ay(e,n){let t=pf(n);return pf(e).forEach(o=>{rc(t,o)||t.push(o)}),t}function Ny(e,n){return pf(n).filter(t=>!rc(e,t))}var oc=class{get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators=[];_rawAsyncValidators=[];_setValidators(n){this._rawValidators=n||[],this._composedValidatorFn=Gy(this._rawValidators)}_setAsyncValidators(n){this._rawAsyncValidators=n||[],this._composedAsyncValidatorFn=Wy(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_onDestroyCallbacks=[];_registerOnDestroy(n){this._onDestroyCallbacks.push(n)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(n=>n()),this._onDestroyCallbacks=[]}reset(n=void 0){this.control&&this.control.reset(n)}hasError(n,t){return this.control?this.control.hasError(n,t):!1}getError(n,t){return this.control?this.control.getError(n,t):null}},Yr=class extends oc{name;get formDirective(){return null}get path(){return null}},Vn=class extends oc{_parent=null;name=null;valueAccessor=null},ic=class{_cd;constructor(n){this._cd=n}get isTouched(){return this._cd?.control?._touched?.(),!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return this._cd?.control?._pristine?.(),!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return this._cd?.control?._status?.(),!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return this._cd?._submitted?.(),!!this._cd?.submitted}},sM={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},Pz=N(g({},sM),{"[class.ng-submitted]":"isSubmitted"}),Fz=(()=>{class e extends ic{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(Vn,2))};static \u0275dir=F({type:e,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(r,o){r&2&&Fo("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)},standalone:!1,features:[Ce]})}return e})(),Lz=(()=>{class e extends ic{constructor(t){super(t)}static \u0275fac=function(r){return new(r||e)(v(Yr,10))};static \u0275dir=F({type:e,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(r,o){r&2&&Fo("ng-untouched",o.isUntouched)("ng-touched",o.isTouched)("ng-pristine",o.isPristine)("ng-dirty",o.isDirty)("ng-valid",o.isValid)("ng-invalid",o.isInvalid)("ng-pending",o.isPending)("ng-submitted",o.isSubmitted)},standalone:!1,features:[Ce]})}return e})();var fi="VALID",tc="INVALID",qr="PENDING",hi="DISABLED",an=class{},sc=class extends an{value;source;constructor(n,t){super(),this.value=n,this.source=t}},pi=class extends an{pristine;source;constructor(n,t){super(),this.pristine=n,this.source=t}},gi=class extends an{touched;source;constructor(n,t){super(),this.touched=n,this.source=t}},Zr=class extends an{status;source;constructor(n,t){super(),this.status=n,this.source=t}},gf=class extends an{source;constructor(n){super(),this.source=n}},mf=class extends an{source;constructor(n){super(),this.source=n}};function Df(e){return(dc(e)?e.validators:e)||null}function aM(e){return Array.isArray(e)?Gy(e):e||null}function Ef(e,n){return(dc(n)?n.asyncValidators:e)||null}function cM(e){return Array.isArray(e)?Wy(e):e||null}function dc(e){return e!=null&&!Array.isArray(e)&&typeof e=="object"}function Yy(e,n,t){let r=e.controls;if(!(n?Object.keys(r):r).length)throw new D(1e3,"");if(!r[t])throw new D(1001,"")}function Qy(e,n,t){e._forEachChild((r,o)=>{if(t[o]===void 0)throw new D(1002,"")})}var Qr=class{_pendingDirty=!1;_hasOwnPendingAsyncValidator=null;_pendingTouched=!1;_onCollectionChange=()=>{};_updateOn;_parent=null;_asyncValidationSubscription;_composedValidatorFn;_composedAsyncValidatorFn;_rawValidators;_rawAsyncValidators;value;constructor(n,t){this._assignValidators(n),this._assignAsyncValidators(t)}get validator(){return this._composedValidatorFn}set validator(n){this._rawValidators=this._composedValidatorFn=n}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(n){this._rawAsyncValidators=this._composedAsyncValidatorFn=n}get parent(){return this._parent}get status(){return Ge(this.statusReactive)}set status(n){Ge(()=>this.statusReactive.set(n))}_status=Nr(()=>this.statusReactive());statusReactive=St(void 0);get valid(){return this.status===fi}get invalid(){return this.status===tc}get pending(){return this.status==qr}get disabled(){return this.status===hi}get enabled(){return this.status!==hi}errors;get pristine(){return Ge(this.pristineReactive)}set pristine(n){Ge(()=>this.pristineReactive.set(n))}_pristine=Nr(()=>this.pristineReactive());pristineReactive=St(!0);get dirty(){return!this.pristine}get touched(){return Ge(this.touchedReactive)}set touched(n){Ge(()=>this.touchedReactive.set(n))}_touched=Nr(()=>this.touchedReactive());touchedReactive=St(!1);get untouched(){return!this.touched}_events=new $;events=this._events.asObservable();valueChanges;statusChanges;get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(n){this._assignValidators(n)}setAsyncValidators(n){this._assignAsyncValidators(n)}addValidators(n){this.setValidators(Ay(n,this._rawValidators))}addAsyncValidators(n){this.setAsyncValidators(Ay(n,this._rawAsyncValidators))}removeValidators(n){this.setValidators(Ny(n,this._rawValidators))}removeAsyncValidators(n){this.setAsyncValidators(Ny(n,this._rawAsyncValidators))}hasValidator(n){return rc(this._rawValidators,n)}hasAsyncValidator(n){return rc(this._rawAsyncValidators,n)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(n={}){let t=this.touched===!1;this.touched=!0;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsTouched(N(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new gi(!0,r))}markAllAsDirty(n={}){this.markAsDirty({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsDirty(n))}markAllAsTouched(n={}){this.markAsTouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:this}),this._forEachChild(t=>t.markAllAsTouched(n))}markAsUntouched(n={}){let t=this.touched===!0;this.touched=!1,this._pendingTouched=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsUntouched({onlySelf:!0,emitEvent:n.emitEvent,sourceControl:r})}),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,r),t&&n.emitEvent!==!1&&this._events.next(new gi(!1,r))}markAsDirty(n={}){let t=this.pristine===!0;this.pristine=!1;let r=n.sourceControl??this;this._parent&&!n.onlySelf&&this._parent.markAsDirty(N(g({},n),{sourceControl:r})),t&&n.emitEvent!==!1&&this._events.next(new pi(!1,r))}markAsPristine(n={}){let t=this.pristine===!1;this.pristine=!0,this._pendingDirty=!1;let r=n.sourceControl??this;this._forEachChild(o=>{o.markAsPristine({onlySelf:!0,emitEvent:n.emitEvent})}),this._parent&&!n.onlySelf&&this._parent._updatePristine(n,r),t&&n.emitEvent!==!1&&this._events.next(new pi(!0,r))}markAsPending(n={}){this.status=qr;let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new Zr(this.status,t)),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.markAsPending(N(g({},n),{sourceControl:t}))}disable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=hi,this.errors=null,this._forEachChild(o=>{o.disable(N(g({},n),{onlySelf:!0}))}),this._updateValue();let r=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new sc(this.value,r)),this._events.next(new Zr(this.status,r)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(N(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(o=>o(!0))}enable(n={}){let t=this._parentMarkedDirty(n.onlySelf);this.status=fi,this._forEachChild(r=>{r.enable(N(g({},n),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent}),this._updateAncestors(N(g({},n),{skipPristineCheck:t}),this),this._onDisabledChange.forEach(r=>r(!1))}_updateAncestors(n,t){this._parent&&!n.onlySelf&&(this._parent.updateValueAndValidity(n),n.skipPristineCheck||this._parent._updatePristine({},t),this._parent._updateTouched({},t))}setParent(n){this._parent=n}getRawValue(){return this.value}updateValueAndValidity(n={}){if(this._setInitialStatus(),this._updateValue(),this.enabled){let r=this._cancelExistingSubscription();this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===fi||this.status===qr)&&this._runAsyncValidator(r,n.emitEvent)}let t=n.sourceControl??this;n.emitEvent!==!1&&(this._events.next(new sc(this.value,t)),this._events.next(new Zr(this.status,t)),this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!n.onlySelf&&this._parent.updateValueAndValidity(N(g({},n),{sourceControl:t}))}_updateTreeValidity(n={emitEvent:!0}){this._forEachChild(t=>t._updateTreeValidity(n)),this.updateValueAndValidity({onlySelf:!0,emitEvent:n.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?hi:fi}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(n,t){if(this.asyncValidator){this.status=qr,this._hasOwnPendingAsyncValidator={emitEvent:t!==!1,shouldHaveEmitted:n!==!1};let r=Uy(this.asyncValidator(this));this._asyncValidationSubscription=r.subscribe(o=>{this._hasOwnPendingAsyncValidator=null,this.setErrors(o,{emitEvent:t,shouldHaveEmitted:n})})}}_cancelExistingSubscription(){if(this._asyncValidationSubscription){this._asyncValidationSubscription.unsubscribe();let n=(this._hasOwnPendingAsyncValidator?.emitEvent||this._hasOwnPendingAsyncValidator?.shouldHaveEmitted)??!1;return this._hasOwnPendingAsyncValidator=null,n}return!1}setErrors(n,t={}){this.errors=n,this._updateControlsErrors(t.emitEvent!==!1,this,t.shouldHaveEmitted)}get(n){let t=n;return t==null||(Array.isArray(t)||(t=t.split(".")),t.length===0)?null:t.reduce((r,o)=>r&&r._find(o),this)}getError(n,t){let r=t?this.get(t):this;return r&&r.errors?r.errors[n]:null}hasError(n,t){return!!this.getError(n,t)}get root(){let n=this;for(;n._parent;)n=n._parent;return n}_updateControlsErrors(n,t,r){this.status=this._calculateStatus(),n&&this.statusChanges.emit(this.status),(n||r)&&this._events.next(new Zr(this.status,t)),this._parent&&this._parent._updateControlsErrors(n,t,r)}_initObservables(){this.valueChanges=new z,this.statusChanges=new z}_calculateStatus(){return this._allControlsDisabled()?hi:this.errors?tc:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(qr)?qr:this._anyControlsHaveStatus(tc)?tc:fi}_anyControlsHaveStatus(n){return this._anyControls(t=>t.status===n)}_anyControlsDirty(){return this._anyControls(n=>n.dirty)}_anyControlsTouched(){return this._anyControls(n=>n.touched)}_updatePristine(n,t){let r=!this._anyControlsDirty(),o=this.pristine!==r;this.pristine=r,this._parent&&!n.onlySelf&&this._parent._updatePristine(n,t),o&&this._events.next(new pi(this.pristine,t))}_updateTouched(n={},t){this.touched=this._anyControlsTouched(),this._events.next(new gi(this.touched,t)),this._parent&&!n.onlySelf&&this._parent._updateTouched(n,t)}_onDisabledChange=[];_registerOnCollectionChange(n){this._onCollectionChange=n}_setUpdateStrategy(n){dc(n)&&n.updateOn!=null&&(this._updateOn=n.updateOn)}_parentMarkedDirty(n){let t=this._parent&&this._parent.dirty;return!n&&!!t&&!this._parent._anyControlsDirty()}_find(n){return null}_assignValidators(n){this._rawValidators=Array.isArray(n)?n.slice():n,this._composedValidatorFn=aM(this._rawValidators)}_assignAsyncValidators(n){this._rawAsyncValidators=Array.isArray(n)?n.slice():n,this._composedAsyncValidatorFn=cM(this._rawAsyncValidators)}},ac=class extends Qr{constructor(n,t,r){super(Df(t),Ef(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;registerControl(n,t){return this.controls[n]?this.controls[n]:(this.controls[n]=t,t.setParent(this),t._registerOnCollectionChange(this._onCollectionChange),t)}addControl(n,t,r={}){this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}removeControl(n,t={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}setControl(n,t,r={}){this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),delete this.controls[n],t&&this.registerControl(n,t),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}contains(n){return this.controls.hasOwnProperty(n)&&this.controls[n].enabled}setValue(n,t={}){Qy(this,!0,n),Object.keys(n).forEach(r=>{Yy(this,!0,r),this.controls[r].setValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(Object.keys(n).forEach(r=>{let o=this.controls[r];o&&o.patchValue(n[r],{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n={},t={}){this._forEachChild((r,o)=>{r.reset(n?n[o]:null,{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this._reduceChildren({},(n,t,r)=>(n[r]=t.getRawValue(),n))}_syncPendingControls(){let n=this._reduceChildren(!1,(t,r)=>r._syncPendingControls()?!0:t);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){Object.keys(this.controls).forEach(t=>{let r=this.controls[t];r&&n(r,t)})}_setUpControls(){this._forEachChild(n=>{n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(n){for(let[t,r]of Object.entries(this.controls))if(this.contains(t)&&n(r))return!0;return!1}_reduceValue(){let n={};return this._reduceChildren(n,(t,r,o)=>((r.enabled||this.disabled)&&(t[o]=r.value),t))}_reduceChildren(n,t){let r=n;return this._forEachChild((o,i)=>{r=t(r,o,i)}),r}_allControlsDisabled(){for(let n of Object.keys(this.controls))if(this.controls[n].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(n){return this.controls.hasOwnProperty(n)?this.controls[n]:null}};var vf=class extends ac{};var Cf=new E("",{providedIn:"root",factory:()=>fc}),fc="always";function uM(e,n){return[...n.path,e]}function Ry(e,n,t=fc){wf(e,n),n.valueAccessor.writeValue(e.value),(e.disabled||t==="always")&&n.valueAccessor.setDisabledState?.(e.disabled),dM(e,n),hM(e,n),fM(e,n),lM(e,n)}function xy(e,n,t=!0){let r=()=>{};n.valueAccessor&&(n.valueAccessor.registerOnChange(r),n.valueAccessor.registerOnTouched(r)),uc(e,n),e&&(n._invokeOnDestroyCallbacks(),e._registerOnCollectionChange(()=>{}))}function cc(e,n){e.forEach(t=>{t.registerOnValidatorChange&&t.registerOnValidatorChange(n)})}function lM(e,n){if(n.valueAccessor.setDisabledState){let t=r=>{n.valueAccessor.setDisabledState(r)};e.registerOnDisabledChange(t),n._registerOnDestroy(()=>{e._unregisterOnDisabledChange(t)})}}function wf(e,n){let t=qy(e);n.validator!==null?e.setValidators(My(t,n.validator)):typeof t=="function"&&e.setValidators([t]);let r=Zy(e);n.asyncValidator!==null?e.setAsyncValidators(My(r,n.asyncValidator)):typeof r=="function"&&e.setAsyncValidators([r]);let o=()=>e.updateValueAndValidity();cc(n._rawValidators,o),cc(n._rawAsyncValidators,o)}function uc(e,n){let t=!1;if(e!==null){if(n.validator!==null){let o=qy(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.validator);i.length!==o.length&&(t=!0,e.setValidators(i))}}if(n.asyncValidator!==null){let o=Zy(e);if(Array.isArray(o)&&o.length>0){let i=o.filter(s=>s!==n.asyncValidator);i.length!==o.length&&(t=!0,e.setAsyncValidators(i))}}}let r=()=>{};return cc(n._rawValidators,r),cc(n._rawAsyncValidators,r),t}function dM(e,n){n.valueAccessor.registerOnChange(t=>{e._pendingValue=t,e._pendingChange=!0,e._pendingDirty=!0,e.updateOn==="change"&&Ky(e,n)})}function fM(e,n){n.valueAccessor.registerOnTouched(()=>{e._pendingTouched=!0,e.updateOn==="blur"&&e._pendingChange&&Ky(e,n),e.updateOn!=="submit"&&e.markAsTouched()})}function Ky(e,n){e._pendingDirty&&e.markAsDirty(),e.setValue(e._pendingValue,{emitModelToViewChange:!1}),n.viewToModelUpdate(e._pendingValue),e._pendingChange=!1}function hM(e,n){let t=(r,o)=>{n.valueAccessor.writeValue(r),o&&n.viewToModelUpdate(r)};e.registerOnChange(t),n._registerOnDestroy(()=>{e._unregisterOnChange(t)})}function pM(e,n){e==null,wf(e,n)}function gM(e,n){return uc(e,n)}function mM(e,n){if(!e.hasOwnProperty("model"))return!1;let t=e.model;return t.isFirstChange()?!0:!Object.is(n,t.currentValue)}function vM(e){return Object.getPrototypeOf(e.constructor)===lc}function yM(e,n){e._syncPendingControls(),n.forEach(t=>{let r=t.control;r.updateOn==="submit"&&r._pendingChange&&(t.viewToModelUpdate(r._pendingValue),r._pendingChange=!1)})}function DM(e,n){if(!n)return null;Array.isArray(n);let t,r,o;return n.forEach(i=>{i.constructor===jy?t=i:vM(i)?r=i:o=i}),o||r||t||null}function EM(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function Oy(e,n){let t=e.indexOf(n);t>-1&&e.splice(t,1)}function ky(e){return typeof e=="object"&&e!==null&&Object.keys(e).length===2&&"value"in e&&"disabled"in e}var nc=class extends Qr{defaultValue=null;_onChange=[];_pendingValue;_pendingChange=!1;constructor(n=null,t,r){super(Df(t),Ef(r,t)),this._applyFormState(n),this._setUpdateStrategy(t),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),dc(t)&&(t.nonNullable||t.initialValueIsDefault)&&(ky(n)?this.defaultValue=n.value:this.defaultValue=n)}setValue(n,t={}){this.value=this._pendingValue=n,this._onChange.length&&t.emitModelToViewChange!==!1&&this._onChange.forEach(r=>r(this.value,t.emitViewToModelChange!==!1)),this.updateValueAndValidity(t)}patchValue(n,t={}){this.setValue(n,t)}reset(n=this.defaultValue,t={}){this._applyFormState(n),this.markAsPristine(t),this.markAsUntouched(t),this.setValue(this.value,t),this._pendingChange=!1}_updateValue(){}_anyControls(n){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(n){this._onChange.push(n)}_unregisterOnChange(n){Oy(this._onChange,n)}registerOnDisabledChange(n){this._onDisabledChange.push(n)}_unregisterOnDisabledChange(n){Oy(this._onDisabledChange,n)}_forEachChild(n){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(n){ky(n)?(this.value=this._pendingValue=n.value,n.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=n}};var CM=e=>e instanceof nc;var Vz=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""],standalone:!1})}return e})(),wM={provide:mi,useExisting:Te(()=>IM),multi:!0},IM=(()=>{class e extends lc{writeValue(t){let r=t??"";this.setProperty("value",r)}registerOnChange(t){this.onChange=r=>{t(r==""?null:parseFloat(r))}}static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(r,o){r&1&&me("input",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},standalone:!1,features:[rt([wM]),Ce]})}return e})();var Jy=new E("");var bM={provide:Yr,useExisting:Te(()=>_M)},_M=(()=>{class e extends Yr{callSetDisabledState;get submitted(){return Ge(this._submittedReactive)}set submitted(t){this._submittedReactive.set(t)}_submitted=Nr(()=>this._submittedReactive());_submittedReactive=St(!1);_oldForm;_onCollectionChange=()=>this._updateDomValue();directives=[];form=null;ngSubmit=new z;constructor(t,r,o){super(),this.callSetDisabledState=o,this._setValidators(t),this._setAsyncValidators(r)}ngOnChanges(t){t.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(uc(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(t){let r=this.form.get(t.path);return Ry(r,t,this.callSetDisabledState),r.updateValueAndValidity({emitEvent:!1}),this.directives.push(t),r}getControl(t){return this.form.get(t.path)}removeControl(t){xy(t.control||null,t,!1),EM(this.directives,t)}addFormGroup(t){this._setUpFormContainer(t)}removeFormGroup(t){this._cleanUpFormContainer(t)}getFormGroup(t){return this.form.get(t.path)}addFormArray(t){this._setUpFormContainer(t)}removeFormArray(t){this._cleanUpFormContainer(t)}getFormArray(t){return this.form.get(t.path)}updateModel(t,r){this.form.get(t.path).setValue(r)}onSubmit(t){return this._submittedReactive.set(!0),yM(this.form,this.directives),this.ngSubmit.emit(t),this.form._events.next(new gf(this.control)),t?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(t=void 0,r={}){this.form.reset(t,r),this._submittedReactive.set(!1),r?.emitEvent!==!1&&this.form._events.next(new mf(this.form))}_updateDomValue(){this.directives.forEach(t=>{let r=t.control,o=this.form.get(t.path);r!==o&&(xy(r||null,t),CM(o)&&(Ry(o,t,this.callSetDisabledState),t.control=o))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(t){let r=this.form.get(t.path);pM(r,t),r.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(t){if(this.form){let r=this.form.get(t.path);r&&gM(r,t)&&r.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){wf(this.form,this),this._oldForm&&uc(this._oldForm,this)}static \u0275fac=function(r){return new(r||e)(v(vi,10),v(Vy,10),v(Cf,8))};static \u0275dir=F({type:e,selectors:[["","formGroup",""]],hostBindings:function(r,o){r&1&&me("submit",function(s){return o.onSubmit(s)})("reset",function(){return o.onReset()})},inputs:{form:[0,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],standalone:!1,features:[rt([bM]),Ce,Ae]})}return e})();var TM={provide:Vn,useExisting:Te(()=>SM)},SM=(()=>{class e extends Vn{_ngModelWarningConfig;_added=!1;viewModel;control;name=null;set isDisabled(t){}model;update=new z;static _ngModelWarningSentOnce=!1;_ngModelWarningSent=!1;constructor(t,r,o,i,s){super(),this._ngModelWarningConfig=s,this._parent=t,this._setValidators(r),this._setAsyncValidators(o),this.valueAccessor=DM(this,i)}ngOnChanges(t){this._added||this._setUpControl(),mM(t,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(t){this.viewModel=t,this.update.emit(t)}get path(){return uM(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_setUpControl(){this.control=this.formDirective.addControl(this),this._added=!0}static \u0275fac=function(r){return new(r||e)(v(Yr,13),v(vi,10),v(Vy,10),v(mi,10),v(Jy,8))};static \u0275dir=F({type:e,selectors:[["","formControlName",""]],inputs:{name:[0,"formControlName","name"],isDisabled:[0,"disabled","isDisabled"],model:[0,"ngModel","model"]},outputs:{update:"ngModelChange"},standalone:!1,features:[rt([TM]),Ce,Ae]})}return e})();var MM={provide:mi,useExisting:Te(()=>eD),multi:!0};function Xy(e,n){return e==null?`${n}`:(n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function AM(e){return e.split(":")[0]}var eD=(()=>{class e extends lc{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;appRefInjector=p(nt).injector;appRefDestroyRef=this.appRefInjector.get(Xe);destroyRef=p(Xe);cdr=p(ot);_queuedWrite=!1;_writeValueAfterRender(){this._queuedWrite||this.appRefDestroyRef.destroyed||(this._queuedWrite=!0,da({write:()=>{this.destroyRef.destroyed||(this._queuedWrite=!1,this.writeValue(this.value))}},{injector:this.appRefInjector}))}writeValue(t){this.cdr.markForCheck(),this.value=t;let r=this._getOptionId(t),o=Xy(r,t);this.setProperty("value",o)}registerOnChange(t){this.onChange=r=>{this.value=this._getOptionValue(r),t(this.value)}}_registerOption(){return(this._idCounter++).toString()}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r),t))return r;return null}_getOptionValue(t){let r=AM(t);return this._optionMap.has(r)?this._optionMap.get(r):t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["select","formControlName","",3,"multiple",""],["select","formControl","",3,"multiple",""],["select","ngModel","",3,"multiple",""]],hostBindings:function(r,o){r&1&&me("change",function(s){return o.onChange(s.target.value)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[rt([MM]),Ce]})}return e})(),Bz=(()=>{class e{_element;_renderer;_select;id;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption())}set ngValue(t){this._select!=null&&(this._select._optionMap.set(this.id,t),this._setElementValue(Xy(this.id,t)),this._select._writeValueAfterRender())}set value(t){this._setElementValue(t),this._select&&this._select._writeValueAfterRender()}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select._writeValueAfterRender())}static \u0275fac=function(r){return new(r||e)(v(q),v(At),v(eD,9))};static \u0275dir=F({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})(),NM={provide:mi,useExisting:Te(()=>tD),multi:!0};function Py(e,n){return e==null?`${n}`:(typeof n=="string"&&(n=`'${n}'`),n&&typeof n=="object"&&(n="Object"),`${e}: ${n}`.slice(0,50))}function RM(e){return e.split(":")[0]}var tD=(()=>{class e extends lc{value;_optionMap=new Map;_idCounter=0;set compareWith(t){this._compareWith=t}_compareWith=Object.is;writeValue(t){this.value=t;let r;if(Array.isArray(t)){let o=t.map(i=>this._getOptionId(i));r=(i,s)=>{i._setSelected(o.indexOf(s.toString())>-1)}}else r=(o,i)=>{o._setSelected(!1)};this._optionMap.forEach(r)}registerOnChange(t){this.onChange=r=>{let o=[],i=r.selectedOptions;if(i!==void 0){let s=i;for(let a=0;a<s.length;a++){let c=s[a],u=this._getOptionValue(c.value);o.push(u)}}else{let s=r.options;for(let a=0;a<s.length;a++){let c=s[a];if(c.selected){let u=this._getOptionValue(c.value);o.push(u)}}}this.value=o,t(o)}}_registerOption(t){let r=(this._idCounter++).toString();return this._optionMap.set(r,t),r}_getOptionId(t){for(let r of this._optionMap.keys())if(this._compareWith(this._optionMap.get(r)._value,t))return r;return null}_getOptionValue(t){let r=RM(t);return this._optionMap.has(r)?this._optionMap.get(r)._value:t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["select","multiple","","formControlName",""],["select","multiple","","formControl",""],["select","multiple","","ngModel",""]],hostBindings:function(r,o){r&1&&me("change",function(s){return o.onChange(s.target)})("blur",function(){return o.onTouched()})},inputs:{compareWith:"compareWith"},standalone:!1,features:[rt([NM]),Ce]})}return e})(),Uz=(()=>{class e{_element;_renderer;_select;id;_value;constructor(t,r,o){this._element=t,this._renderer=r,this._select=o,this._select&&(this.id=this._select._registerOption(this))}set ngValue(t){this._select!=null&&(this._value=t,this._setElementValue(Py(this.id,t)),this._select.writeValue(this._select.value))}set value(t){this._select?(this._value=t,this._setElementValue(Py(this.id,t)),this._select.writeValue(this._select.value)):this._setElementValue(t)}_setElementValue(t){this._renderer.setProperty(this._element.nativeElement,"value",t)}_setSelected(t){this._renderer.setProperty(this._element.nativeElement,"selected",t)}ngOnDestroy(){this._select&&(this._select._optionMap.delete(this.id),this._select.writeValue(this._select.value))}static \u0275fac=function(r){return new(r||e)(v(q),v(At),v(tD,9))};static \u0275dir=F({type:e,selectors:[["option"]],inputs:{ngValue:"ngValue",value:"value"},standalone:!1})}return e})();function nD(e){return typeof e=="number"?e:parseFloat(e)}var If=(()=>{class e{_validator=Sy;_onChange;_enabled;ngOnChanges(t){if(this.inputName in t){let r=this.normalizeInput(t[this.inputName].currentValue);this._enabled=this.enabled(r),this._validator=this._enabled?this.createValidator(r):Sy,this._onChange&&this._onChange()}}validate(t){return this._validator(t)}registerOnValidatorChange(t){this._onChange=t}enabled(t){return t!=null}static \u0275fac=function(r){return new(r||e)};static \u0275dir=F({type:e,features:[Ae]})}return e})(),xM={provide:vi,useExisting:Te(()=>OM),multi:!0},OM=(()=>{class e extends If{max;inputName="max";normalizeInput=t=>nD(t);createValidator=t=>tM(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","max","","formControlName",""],["input","type","number","max","","formControl",""],["input","type","number","max","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&tn("max",o._enabled?o.max:null)},inputs:{max:"max"},standalone:!1,features:[rt([xM]),Ce]})}return e})(),kM={provide:vi,useExisting:Te(()=>PM),multi:!0},PM=(()=>{class e extends If{min;inputName="min";normalizeInput=t=>nD(t);createValidator=t=>eM(t);static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(r,o){r&2&&tn("min",o._enabled?o.min:null)},inputs:{min:"min"},standalone:!1,features:[rt([kM]),Ce]})}return e})(),FM={provide:vi,useExisting:Te(()=>LM),multi:!0};var LM=(()=>{class e extends If{required;inputName="required";normalizeInput=Rr;createValidator=t=>nM;enabled(t){return t}static \u0275fac=(()=>{let t;return function(o){return(t||(t=He(e)))(o||e)}})();static \u0275dir=F({type:e,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(r,o){r&2&&tn("required",o._enabled?"":null)},inputs:{required:"required"},standalone:!1,features:[rt([FM]),Ce]})}return e})();var rD=(()=>{class e{static \u0275fac=function(r){return new(r||e)};static \u0275mod=ze({type:e});static \u0275inj=ke({})}return e})(),yf=class extends Qr{constructor(n,t,r){super(Df(t),Ef(r,t)),this.controls=n,this._initObservables(),this._setUpdateStrategy(t),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}controls;at(n){return this.controls[this._adjustIndex(n)]}push(n,t={}){this.controls.push(n),this._registerControl(n),this.updateValueAndValidity({emitEvent:t.emitEvent}),this._onCollectionChange()}insert(n,t,r={}){this.controls.splice(n,0,t),this._registerControl(t),this.updateValueAndValidity({emitEvent:r.emitEvent})}removeAt(n,t={}){let r=this._adjustIndex(n);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),this.updateValueAndValidity({emitEvent:t.emitEvent})}setControl(n,t,r={}){let o=this._adjustIndex(n);o<0&&(o=0),this.controls[o]&&this.controls[o]._registerOnCollectionChange(()=>{}),this.controls.splice(o,1),t&&(this.controls.splice(o,0,t),this._registerControl(t)),this.updateValueAndValidity({emitEvent:r.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(n,t={}){Qy(this,!1,n),n.forEach((r,o)=>{Yy(this,!1,o),this.at(o).setValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t)}patchValue(n,t={}){n!=null&&(n.forEach((r,o)=>{this.at(o)&&this.at(o).patchValue(r,{onlySelf:!0,emitEvent:t.emitEvent})}),this.updateValueAndValidity(t))}reset(n=[],t={}){this._forEachChild((r,o)=>{r.reset(n[o],{onlySelf:!0,emitEvent:t.emitEvent})}),this._updatePristine(t,this),this._updateTouched(t,this),this.updateValueAndValidity(t)}getRawValue(){return this.controls.map(n=>n.getRawValue())}clear(n={}){this.controls.length<1||(this._forEachChild(t=>t._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:n.emitEvent}))}_adjustIndex(n){return n<0?n+this.length:n}_syncPendingControls(){let n=this.controls.reduce((t,r)=>r._syncPendingControls()?!0:t,!1);return n&&this.updateValueAndValidity({onlySelf:!0}),n}_forEachChild(n){this.controls.forEach((t,r)=>{n(t,r)})}_updateValue(){this.value=this.controls.filter(n=>n.enabled||this.disabled).map(n=>n.value)}_anyControls(n){return this.controls.some(t=>t.enabled&&n(t))}_setUpControls(){this._forEachChild(n=>this._registerControl(n))}_allControlsDisabled(){for(let n of this.controls)if(n.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(n){n.setParent(this),n._registerOnCollectionChange(this._onCollectionChange)}_find(n){return this.at(n)??null}};function Fy(e){return!!e&&(e.asyncValidators!==void 0||e.validators!==void 0||e.updateOn!==void 0)}var Hz=(()=>{class e{useNonNullable=!1;get nonNullable(){let t=new e;return t.useNonNullable=!0,t}group(t,r=null){let o=this._reduceControls(t),i={};return Fy(r)?i=r:r!==null&&(i.validators=r.validator,i.asyncValidators=r.asyncValidator),new ac(o,i)}record(t,r=null){let o=this._reduceControls(t);return new vf(o,r)}control(t,r,o){let i={};return this.useNonNullable?(Fy(r)?i=r:(i.validators=r,i.asyncValidators=o),new nc(t,N(g({},i),{nonNullable:!0}))):new nc(t,r,o)}array(t,r,o){let i=t.map(s=>this._createControl(s));return new yf(i,r,o)}_reduceControls(t){let r={};return Object.keys(t).forEach(o=>{r[o]=this._createControl(t[o])}),r}_createControl(t){if(t instanceof nc)return t;if(t instanceof Qr)return t;if(Array.isArray(t)){let r=t[0],o=t.length>1?t[1]:null,i=t.length>2?t[2]:null;return this.control(r,o,i)}else return this.control(t)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})();var $z=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:Cf,useValue:t.callSetDisabledState??fc}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ze({type:e});static \u0275inj=ke({imports:[rD]})}return e})(),zz=(()=>{class e{static withConfig(t){return{ngModule:e,providers:[{provide:Jy,useValue:t.warnOnNgModelWithFormControl??"always"},{provide:Cf,useValue:t.callSetDisabledState??fc}]}}static \u0275fac=function(r){return new(r||e)};static \u0275mod=ze({type:e});static \u0275inj=ke({imports:[rD]})}return e})();var qz=(e,n,t,r,o)=>VM(e[1],n[1],t[1],r[1],o).map(i=>jM(e[0],n[0],t[0],r[0],i)),jM=(e,n,t,r,o)=>{let i=3*n*Math.pow(o-1,2),s=-3*t*o+3*t+r*o,a=e*Math.pow(o-1,3);return o*(i+o*s)-a},VM=(e,n,t,r,o)=>(e-=o,n-=o,t-=o,r-=o,UM(r-3*t+3*n-e,3*t-6*n+3*e,3*n-3*e,e).filter(s=>s>=0&&s<=1)),BM=(e,n,t)=>{let r=n*n-4*e*t;return r<0?[]:[(-n+Math.sqrt(r))/(2*e),(-n-Math.sqrt(r))/(2*e)]},UM=(e,n,t,r)=>{if(e===0)return BM(n,t,r);n/=e,t/=e,r/=e;let o=(3*t-n*n)/3,i=(2*n*n*n-9*n*t+27*r)/27;if(o===0)return[Math.pow(-i,.3333333333333333)];if(i===0)return[Math.sqrt(-o),-Math.sqrt(-o)];let s=Math.pow(i/2,2)+Math.pow(o/3,3);if(s===0)return[Math.pow(i/2,.5)-n/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),.3333333333333333)-Math.pow(i/2+Math.sqrt(s),.3333333333333333)-n/3];let a=Math.sqrt(Math.pow(-(o/3),3)),c=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(o/3),3))))),u=2*Math.pow(a,1/3);return[u*Math.cos(c/3)-n/3,u*Math.cos((c+2*Math.PI)/3)-n/3,u*Math.cos((c+4*Math.PI)/3)-n/3]};var hc=e=>iD(e),Jr=(e,n)=>(typeof e=="string"&&(n=e,e=void 0),hc(e).includes(n)),iD=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let n=e.Ionic.platforms;return n==null&&(n=e.Ionic.platforms=HM(e),n.forEach(t=>e.document.documentElement.classList.add(`plt-${t}`))),n},HM=e=>{let n=Re.get("platform");return Object.keys(oD).filter(t=>{let r=n?.[t];return typeof r=="function"?r(e):oD[t](e)})},$M=e=>pc(e)&&!aD(e),bf=e=>!!(Bn(e,/iPad/i)||Bn(e,/Macintosh/i)&&pc(e)),zM=e=>Bn(e,/iPhone/i),GM=e=>Bn(e,/iPhone|iPod/i)||bf(e),sD=e=>Bn(e,/android|sink/i),WM=e=>sD(e)&&!Bn(e,/mobile/i),qM=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return r>390&&r<520&&o>620&&o<800},ZM=e=>{let n=e.innerWidth,t=e.innerHeight,r=Math.min(n,t),o=Math.max(n,t);return bf(e)||WM(e)||r>460&&r<820&&o>780&&o<1400},pc=e=>JM(e,"(any-pointer:coarse)"),YM=e=>!pc(e),aD=e=>cD(e)||uD(e),cD=e=>!!(e.cordova||e.phonegap||e.PhoneGap),uD=e=>{let n=e.Capacitor;return!!(n?.isNative||n?.isNativePlatform&&n.isNativePlatform())},QM=e=>Bn(e,/electron/i),KM=e=>{var n;return!!(!((n=e.matchMedia)===null||n===void 0)&&n.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},Bn=(e,n)=>n.test(e.navigator.userAgent),JM=(e,n)=>{var t;return(t=e.matchMedia)===null||t===void 0?void 0:t.call(e,n).matches},oD={ipad:bf,iphone:zM,ios:GM,android:sD,phablet:qM,tablet:ZM,cordova:cD,capacitor:uD,electron:QM,pwa:KM,mobile:pc,mobileweb:$M,desktop:YM,hybrid:aD},Kr,_f=e=>e&&Vf(e)||Kr,XM=(e={})=>{if(typeof window>"u")return;let n=window.document,t=window,r=t.Ionic=t.Ionic||{},o=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},Bf(t)),{persistConfig:!1}),r.config),Hf(t)),e);Re.reset(o),Re.getBoolean("persistConfig")&&Uf(t,o),iD(t),r.config=Re,r.mode=Kr=Re.get("mode",n.documentElement.getAttribute("mode")||(Jr(t,"ios")?"ios":"md")),Re.set("mode",Kr),n.documentElement.setAttribute("mode",Kr),n.documentElement.classList.add(Kr),Re.getBoolean("_testing")&&Re.set("animated",!1);let i=a=>{var c;return(c=a.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},s=a=>["ios","md"].includes(a);jf(a=>{for(;a;){let c=a.mode||a.getAttribute("mode");if(c){if(s(c))return c;i(a)&&wi('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}a=a.parentElement}return Kr})};var e0=e=>e!==void 0?(Array.isArray(e)?e:e.split(" ")).filter(t=>t!=null).map(t=>t.trim()).filter(t=>t!==""):[],Jz=e=>{let n={};return e0(e).forEach(t=>n[t]=!0),n};var nG=(e,n,t,r,o,i)=>oe(null,null,function*(){var s;if(e)return e.attachViewToDom(n,t,o,r);if(!i&&typeof t!="string"&&!(t instanceof HTMLElement))throw new Error("framework delegate is missing");let a=typeof t=="string"?(s=n.ownerDocument)===null||s===void 0?void 0:s.createElement(t):t;return r&&r.forEach(c=>a.classList.add(c)),o&&Object.assign(a,o),n.appendChild(a),yield new Promise(c=>jt(a,c)),a}),rG=(e,n)=>{if(n){if(e){let t=n.parentElement;return e.removeViewFromDom(t,n)}n.remove()}return Promise.resolve()},oG=()=>{let e,n;return{attachViewToDom:(c,u,...l)=>oe(null,[c,u,...l],function*(o,i,s={},a=[]){var d,h;e=o;let f;if(i){let b=typeof i=="string"?(d=e.ownerDocument)===null||d===void 0?void 0:d.createElement(i):i;a.forEach(y=>b.classList.add(y)),Object.assign(b,s),e.appendChild(b),f=b,yield new Promise(y=>jt(b,y))}else if(e.children.length>0&&(e.tagName==="ION-MODAL"||e.tagName==="ION-POPOVER")&&!(f=e.children[0]).classList.contains("ion-delegate-host")){let y=(h=e.ownerDocument)===null||h===void 0?void 0:h.createElement("div");y.classList.add("ion-delegate-host"),a.forEach(C=>y.classList.add(C)),y.append(...e.children),e.appendChild(y),f=y}let m=document.querySelector("ion-app")||document.body;return n=document.createComment("ionic teleport"),e.parentNode.insertBefore(n,e),m.appendChild(e),f??e}),removeViewFromDom:()=>(e&&n&&(n.parentNode.insertBefore(e,n),n.remove()),Promise.resolve())}};var Di='[tabindex]:not([tabindex^="-"]):not([hidden]):not([disabled]), input:not([type=hidden]):not([tabindex^="-"]):not([hidden]):not([disabled]), textarea:not([tabindex^="-"]):not([hidden]):not([disabled]), button:not([tabindex^="-"]):not([hidden]):not([disabled]), select:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-checkbox:not([tabindex^="-"]):not([hidden]):not([disabled]), ion-radio:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable:not([tabindex^="-"]):not([hidden]):not([disabled]), .ion-focusable[disabled="false"]:not([tabindex^="-"]):not([hidden])',lD=(e,n)=>{let t=e.querySelector(Di);pD(t,n??e)},dD=(e,n)=>{let t=Array.from(e.querySelectorAll(Di)),r=t.length>0?t[t.length-1]:null;pD(r,n??e)},pD=(e,n)=>{let t=e,r=e?.shadowRoot;if(r&&(t=r.querySelector(Di)||e),t){let o=t.closest("ion-radio-group");o?o.setFocus():Ec(t)}else n.focus()},Tf=0,t0=0,gc=new WeakMap,gD=e=>({create(t){return o0(e,t)},dismiss(t,r,o){return c0(document,t,r,e,o)},getTop(){return oe(this,null,function*(){return yi(document,e)})}});var n0=gD("ion-modal");var r0=gD("ion-popover");var hG=e=>{typeof document<"u"&&a0(document);let n=Tf++;e.overlayIndex=n},pG=e=>(e.hasAttribute("id")||(e.id=`ion-overlay-${++t0}`),e.id),o0=(e,n)=>typeof window<"u"&&typeof window.customElements<"u"?window.customElements.whenDefined(e).then(()=>{let t=document.createElement(e);return t.classList.add("overlay-hidden"),Object.assign(t,Object.assign(Object.assign({},n),{hasController:!0})),vD(document).appendChild(t),new Promise(r=>jt(t,r))}):Promise.resolve(),i0=e=>e.classList.contains("overlay-hidden"),fD=(e,n)=>{let t=e,r=e?.shadowRoot;r&&(t=r.querySelector(Di)||e),t?Ec(t):n.focus()},s0=(e,n)=>{let t=yi(n,"ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover"),r=e.target;if(!t||!r||t.classList.contains(g0))return;let o=()=>{if(t===r)t.lastFocus=void 0;else if(r.tagName==="ION-TOAST")fD(t.lastFocus,t);else{let s=Wf(t);if(!s.contains(r))return;let a=s.querySelector(".ion-overlay-wrapper");if(!a)return;if(a.contains(r)||r===s.querySelector("ion-backdrop"))t.lastFocus=r;else{let c=t.lastFocus;lD(a,t),c===n.activeElement&&dD(a,t),t.lastFocus=n.activeElement}}},i=()=>{if(t.contains(r))t.lastFocus=r;else if(r.tagName==="ION-TOAST")fD(t.lastFocus,t);else{let s=t.lastFocus;lD(t),s===n.activeElement&&dD(t),t.lastFocus=n.activeElement}};t.shadowRoot?i():o()},a0=e=>{Tf===0&&(Tf=1,e.addEventListener("focus",n=>{s0(n,e)},!0),e.addEventListener("ionBackButton",n=>{let t=yi(e);t?.backdropDismiss&&n.detail.register(Zf,()=>{t.dismiss(void 0,hD)})}),qf()||e.addEventListener("keydown",n=>{if(n.key==="Escape"){let t=yi(e);t?.backdropDismiss&&t.dismiss(void 0,hD)}}))},c0=(e,n,t,r,o)=>{let i=yi(e,r,o);return i?i.dismiss(n,t):Promise.reject("overlay does not exist")},u0=(e,n)=>(n===void 0&&(n="ion-alert,ion-action-sheet,ion-loading,ion-modal,ion-picker-legacy,ion-popover,ion-toast"),Array.from(e.querySelectorAll(n)).filter(t=>t.overlayIndex>0)),mc=(e,n)=>u0(e,n).filter(t=>!i0(t)),yi=(e,n,t)=>{let r=mc(e,n);return t===void 0?r[r.length-1]:r.find(o=>o.id===t)},mD=(e=!1)=>{let t=vD(document).querySelector("ion-router-outlet, ion-nav, #ion-view-container-root");t&&(e?t.setAttribute("aria-hidden","true"):t.removeAttribute("aria-hidden"))},gG=(e,n,t,r,o)=>oe(null,null,function*(){var i,s;if(e.presented)return;e.el.tagName!=="ION-TOAST"&&(mD(!0),document.body.classList.add(Tc)),h0(e.el),DD(e.el),e.presented=!0,e.willPresent.emit(),(i=e.willPresentShorthand)===null||i===void 0||i.emit();let a=_f(e),c=e.enterAnimation?e.enterAnimation:Re.get(n,a==="ios"?t:r);(yield yD(e,c,e.el,o))&&(e.didPresent.emit(),(s=e.didPresentShorthand)===null||s===void 0||s.emit()),e.el.tagName!=="ION-TOAST"&&l0(e.el),e.keyboardClose&&(document.activeElement===null||!e.el.contains(document.activeElement))&&e.el.focus(),e.el.removeAttribute("aria-hidden")}),l0=e=>oe(null,null,function*(){let n=document.activeElement;if(!n)return;let t=n?.shadowRoot;t&&(n=t.querySelector(Di)||n),yield e.onDidDismiss(),(document.activeElement===null||document.activeElement===document.body)&&n.focus()}),mG=(e,n,t,r,o,i,s)=>oe(null,null,function*(){var a,c;if(!e.presented)return!1;let l=(Lt!==void 0?mc(Lt):[]).filter(h=>h.tagName!=="ION-TOAST");l.length===1&&l[0].id===e.el.id&&(mD(!1),document.body.classList.remove(Tc)),e.presented=!1;try{DD(e.el),e.el.style.setProperty("pointer-events","none"),e.willDismiss.emit({data:n,role:t}),(a=e.willDismissShorthand)===null||a===void 0||a.emit({data:n,role:t});let h=_f(e),f=e.leaveAnimation?e.leaveAnimation:Re.get(r,h==="ios"?o:i);t!==f0&&(yield yD(e,f,e.el,s)),e.didDismiss.emit({data:n,role:t}),(c=e.didDismissShorthand)===null||c===void 0||c.emit({data:n,role:t}),(gc.get(e)||[]).forEach(b=>b.destroy()),gc.delete(e),e.el.classList.add("overlay-hidden"),e.el.style.removeProperty("pointer-events"),e.el.lastFocus!==void 0&&(e.el.lastFocus=void 0)}catch(h){$f(`[${e.el.tagName.toLowerCase()}] - `,h)}return e.el.remove(),p0(),!0}),vD=e=>e.querySelector("ion-app")||e.body,yD=(e,n,t,r)=>oe(null,null,function*(){t.classList.remove("overlay-hidden");let o=e.el,i=n(o,r);(!e.animated||!Re.getBoolean("animated",!0))&&i.duration(0),e.keyboardClose&&i.beforeAddWrite(()=>{let a=t.ownerDocument.activeElement;a?.matches("input,ion-input, ion-textarea")&&a.blur()});let s=gc.get(e)||[];return gc.set(e,[...s,i]),yield i.play(),!0}),vG=(e,n)=>{let t,r=new Promise(o=>t=o);return d0(e,n,o=>{t(o.detail)}),r},d0=(e,n,t)=>{let r=o=>{Gf(e,n,r),t(o)};zf(e,n,r)};var hD="backdrop",f0="gesture",yG=39;var DG=()=>{let e,n=()=>{e&&(e(),e=void 0)};return{addClickListener:(r,o)=>{n();let i=o!==void 0?document.getElementById(o):null;if(!i){wi(`[${r.tagName.toLowerCase()}] - A trigger element with the ID "${o}" was not found in the DOM. The trigger element must be in the DOM when the "trigger" property is set on an overlay component.`,r);return}e=((a,c)=>{let u=()=>{c.present()};return a.addEventListener("click",u),()=>{a.removeEventListener("click",u)}})(i,r)},removeClickListener:n}},DD=e=>{Lt!==void 0&&Jr("android")&&e.setAttribute("aria-hidden","true")},h0=e=>{var n;if(Lt===void 0)return;let t=mc(Lt);for(let r=t.length-1;r>=0;r--){let o=t[r],i=(n=t[r+1])!==null&&n!==void 0?n:e;(i.hasAttribute("aria-hidden")||i.tagName!=="ION-TOAST")&&o.setAttribute("aria-hidden","true")}},p0=()=>{if(Lt===void 0)return;let e=mc(Lt);for(let n=e.length-1;n>=0;n--){let t=e[n];if(t.removeAttribute("aria-hidden"),t.tagName!=="ION-TOAST")break}},g0="ion-disable-focus-trap";var m0=["tabsInner"];var v0=(()=>{class e{doc;_readyPromise;win;backButton=new $;keyboardDidShow=new $;keyboardDidHide=new $;pause=new $;resume=new $;resize=new $;constructor(t,r){this.doc=t,r.run(()=>{this.win=t.defaultView,this.backButton.subscribeWithPriority=function(i,s){return this.subscribe(a=>a.register(i,c=>r.run(()=>s(c))))},Xr(this.pause,t,"pause",r),Xr(this.resume,t,"resume",r),Xr(this.backButton,t,"ionBackButton",r),Xr(this.resize,this.win,"resize",r),Xr(this.keyboardDidShow,this.win,"ionKeyboardDidShow",r),Xr(this.keyboardDidHide,this.win,"ionKeyboardDidHide",r);let o;this._readyPromise=new Promise(i=>{o=i}),this.win?.cordova?t.addEventListener("deviceready",()=>{o("cordova")},{once:!0}):o("dom")})}is(t){return Jr(this.win,t)}platforms(){return hc(this.win)}ready(){return this._readyPromise}get isRTL(){return this.doc.dir==="rtl"}getQueryParam(t){return y0(this.win.location.href,t)}isLandscape(){return!this.isPortrait()}isPortrait(){return this.win.matchMedia?.("(orientation: portrait)").matches}testUserAgent(t){let r=this.win.navigator;return!!(r?.userAgent&&r.userAgent.indexOf(t)>=0)}url(){return this.win.location.href}width(){return this.win.innerWidth}height(){return this.win.innerHeight}static \u0275fac=function(r){return new(r||e)(I(J),I(H))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),y0=(e,n)=>{n=n.replace(/[[\]\\]/g,"\\$&");let r=new RegExp("[\\?&]"+n+"=([^&#]*)").exec(e);return r?decodeURIComponent(r[1].replace(/\+/g," ")):null},Xr=(e,n,t,r)=>{n&&n.addEventListener(t,o=>{r.run(()=>{let i=o?.detail;e.next(i)})})},Ci=(()=>{class e{location;serializer;router;topOutlet;direction=ED;animated=CD;animationBuilder;guessDirection="forward";guessAnimation;lastNavId=-1;constructor(t,r,o,i){this.location=r,this.serializer=o,this.router=i,i&&i.events.subscribe(s=>{if(s instanceof Ot){let a=s.restoredState?s.restoredState.navigationId:s.id;this.guessDirection=this.guessAnimation=a<this.lastNavId?"back":"forward",this.lastNavId=this.guessDirection==="forward"?s.id:a}}),t.backButton.subscribeWithPriority(0,s=>{this.pop(),s()})}navigateForward(t,r={}){return this.setDirection("forward",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateBack(t,r={}){return this.setDirection("back",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}navigateRoot(t,r={}){return this.setDirection("root",r.animated,r.animationDirection,r.animation),this.navigate(t,r)}back(t={animated:!0,animationDirection:"back"}){return this.setDirection("back",t.animated,t.animationDirection,t.animation),this.location.back()}pop(){return oe(this,null,function*(){let t=this.topOutlet;for(;t;){if(yield t.pop())return!0;t=t.parentOutlet}return!1})}setDirection(t,r,o,i){this.direction=t,this.animated=D0(t,r,o),this.animationBuilder=i}setTopOutlet(t){this.topOutlet=t}consumeTransition(){let t="root",r,o=this.animationBuilder;return this.direction==="auto"?(t=this.guessDirection,r=this.guessAnimation):(r=this.animated,t=this.direction),this.direction=ED,this.animated=CD,this.animationBuilder=void 0,{direction:t,animation:r,animationBuilder:o}}navigate(t,r){if(Array.isArray(t))return this.router.navigate(t,r);{let o=this.serializer.parse(t.toString());return r.queryParams!==void 0&&(o.queryParams=g({},r.queryParams)),r.fragment!==void 0&&(o.fragment=r.fragment),this.router.navigateByUrl(o,r)}}static \u0275fac=function(r){return new(r||e)(I(v0),I(Rt),I(jn),I(je,8))};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),D0=(e,n,t)=>{if(n!==!1){if(t!==void 0)return t;if(e==="forward"||e==="back")return e;if(e==="root"&&n===!0)return"forward"}},ED="auto",CD=void 0,_D=(()=>{class e{get(t,r){let o=Sf();return o?o.get(t,r):null}getBoolean(t,r){let o=Sf();return o?o.getBoolean(t,r):!1}getNumber(t,r){let o=Sf();return o?o.getNumber(t,r):0}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac,providedIn:"root"})}return e})(),E0=new E("USERCONFIG"),Sf=()=>{if(typeof window<"u"){let e=window.Ionic;if(e?.config)return e.config}return null},vc=class{data;constructor(n={}){this.data=n,console.warn("[Ionic Warning]: NavParams has been deprecated in favor of using Angular's input API. Developers should migrate to either the @Input decorator or the Signals-based input API.")}get(n){return this.data[n]}},C0=(()=>{class e{zone=p(H);applicationRef=p(nt);config=p(E0);create(t,r,o){return new Af(t,r,this.applicationRef,this.zone,o,this.config.useSetInputAPI??!1)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),Af=class{environmentInjector;injector;applicationRef;zone;elementReferenceKey;enableSignalsSupport;elRefMap=new WeakMap;elEventsMap=new WeakMap;constructor(n,t,r,o,i,s){this.environmentInjector=n,this.injector=t,this.applicationRef=r,this.zone=o,this.elementReferenceKey=i,this.enableSignalsSupport=s}attachViewToDom(n,t,r,o){return this.zone.run(()=>new Promise(i=>{let s=g({},r);this.elementReferenceKey!==void 0&&(s[this.elementReferenceKey]=n);let a=w0(this.zone,this.environmentInjector,this.injector,this.applicationRef,this.elRefMap,this.elEventsMap,n,t,s,o,this.elementReferenceKey,this.enableSignalsSupport);i(a)}))}removeViewFromDom(n,t){return this.zone.run(()=>new Promise(r=>{let o=this.elRefMap.get(t);if(o){o.destroy(),this.elRefMap.delete(t);let i=this.elEventsMap.get(t);i&&(i(),this.elEventsMap.delete(t))}r()}))}},w0=(e,n,t,r,o,i,s,a,c,u,l,d)=>{let h=fe.create({providers:b0(c),parent:t}),f=Qm(a,{environmentInjector:n,elementInjector:h}),m=f.instance,b=f.location.nativeElement;if(c)if(l&&m[l]!==void 0&&console.error(`[Ionic Error]: ${l} is a reserved property when using ${s.tagName.toLowerCase()}. Rename or remove the "${l}" property from ${a.name}.`),d===!0&&f.setInput!==void 0){let C=c,{modal:le,popover:Ie}=C,Z=Dc(C,["modal","popover"]);for(let Un in Z)f.setInput(Un,Z[Un]);le!==void 0&&Object.assign(m,{modal:le}),Ie!==void 0&&Object.assign(m,{popover:Ie})}else Object.assign(m,c);if(u)for(let le of u)b.classList.add(le);let y=TD(e,m,b);return s.appendChild(b),r.attachView(f.hostView),o.set(b,f),i.set(b,y),b},I0=[Cc,wc,Ic,bc,_c],TD=(e,n,t)=>e.run(()=>{let r=I0.filter(o=>typeof n[o]=="function").map(o=>{let i=s=>n[o](s.detail);return t.addEventListener(o,i),()=>t.removeEventListener(o,i)});return()=>r.forEach(o=>o())}),wD=new E("NavParamsToken"),b0=e=>[{provide:wD,useValue:e},{provide:vc,useFactory:_0,deps:[wD]}],_0=e=>new vc(e),T0=(e,n)=>{let t=e.prototype;n.forEach(r=>{Object.defineProperty(t,r,{get(){return this.el[r]},set(o){this.z.runOutsideAngular(()=>this.el[r]=o)}})})},S0=(e,n)=>{let t=e.prototype;n.forEach(r=>{t[r]=function(){let o=arguments;return this.z.runOutsideAngular(()=>this.el[r].apply(this.el,o))}})},Lf=(e,n,t)=>{t.forEach(r=>e[r]=oo(n,r))};function yc(e){return function(t){let{defineCustomElementFn:r,inputs:o,methods:i}=e;return r!==void 0&&r(),o&&T0(t,o),i&&S0(t,i),t}}var M0=["alignment","animated","arrow","keepContentsMounted","backdropDismiss","cssClass","dismissOnSelect","enterAnimation","event","focusTrap","isOpen","keyboardClose","leaveAnimation","mode","showBackdrop","translucent","trigger","triggerAction","reference","size","side"],A0=["present","dismiss","onDidDismiss","onWillDismiss"],JG=(()=>{let e=class Nf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Lf(this,this.el,["ionPopoverDidPresent","ionPopoverWillPresent","ionPopoverWillDismiss","ionPopoverDidDismiss","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Nf)(v(ot),v(q),v(H))};static \u0275dir=F({type:Nf,selectors:[["ion-popover"]],contentQueries:function(r,o,i){if(r&1&&Po(i,tt,5),r&2){let s;Mr(s=Ar())&&(o.template=s.first)}},inputs:{alignment:"alignment",animated:"animated",arrow:"arrow",keepContentsMounted:"keepContentsMounted",backdropDismiss:"backdropDismiss",cssClass:"cssClass",dismissOnSelect:"dismissOnSelect",enterAnimation:"enterAnimation",event:"event",focusTrap:"focusTrap",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger",triggerAction:"triggerAction",reference:"reference",size:"size",side:"side"},standalone:!1})};return e=no([yc({inputs:M0,methods:A0})],e),e})(),N0=["animated","keepContentsMounted","backdropBreakpoint","backdropDismiss","breakpoints","canDismiss","cssClass","enterAnimation","expandToScroll","event","focusTrap","handle","handleBehavior","initialBreakpoint","isOpen","keyboardClose","leaveAnimation","mode","presentingElement","showBackdrop","translucent","trigger"],R0=["present","dismiss","onDidDismiss","onWillDismiss","setCurrentBreakpoint","getCurrentBreakpoint"],XG=(()=>{let e=class Rf{z;template;isCmpOpen=!1;el;constructor(t,r,o){this.z=o,this.el=r.nativeElement,this.el.addEventListener("ionMount",()=>{this.isCmpOpen=!0,t.detectChanges()}),this.el.addEventListener("didDismiss",()=>{this.isCmpOpen=!1,t.detectChanges()}),Lf(this,this.el,["ionModalDidPresent","ionModalWillPresent","ionModalWillDismiss","ionModalDidDismiss","ionBreakpointDidChange","didPresent","willPresent","willDismiss","didDismiss"])}static \u0275fac=function(r){return new(r||Rf)(v(ot),v(q),v(H))};static \u0275dir=F({type:Rf,selectors:[["ion-modal"]],contentQueries:function(r,o,i){if(r&1&&Po(i,tt,5),r&2){let s;Mr(s=Ar())&&(o.template=s.first)}},inputs:{animated:"animated",keepContentsMounted:"keepContentsMounted",backdropBreakpoint:"backdropBreakpoint",backdropDismiss:"backdropDismiss",breakpoints:"breakpoints",canDismiss:"canDismiss",cssClass:"cssClass",enterAnimation:"enterAnimation",expandToScroll:"expandToScroll",event:"event",focusTrap:"focusTrap",handle:"handle",handleBehavior:"handleBehavior",initialBreakpoint:"initialBreakpoint",isOpen:"isOpen",keyboardClose:"keyboardClose",leaveAnimation:"leaveAnimation",mode:"mode",presentingElement:"presentingElement",showBackdrop:"showBackdrop",translucent:"translucent",trigger:"trigger"},standalone:!1})};return e=no([yc({inputs:N0,methods:R0})],e),e})(),x0=(e,n,t)=>t==="root"?SD(e,n):t==="forward"?O0(e,n):k0(e,n),SD=(e,n)=>(e=e.filter(t=>t.stackId!==n.stackId),e.push(n),e),O0=(e,n)=>(e.indexOf(n)>=0?e=e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):e.push(n),e),k0=(e,n)=>e.indexOf(n)>=0?e.filter(r=>r.stackId!==n.stackId||r.id<=n.id):SD(e,n),xf=(e,n)=>{let t=e.createUrlTree(["."],{relativeTo:n});return e.serializeUrl(t)},MD=(e,n)=>n?e.stackId!==n.stackId:!0,P0=(e,n)=>{if(!e)return;let t=AD(n);for(let r=0;r<t.length;r++){if(r>=e.length)return t[r];if(t[r]!==e[r])return}},AD=e=>e.split("/").map(n=>n.trim()).filter(n=>n!==""),ND=e=>{e&&(e.ref.destroy(),e.unlistenEvents())},Of=class{containerEl;router;navCtrl;zone;location;views=[];runningTask;skipTransition=!1;tabsPrefix;activeView;nextId=0;constructor(n,t,r,o,i,s){this.containerEl=t,this.router=r,this.navCtrl=o,this.zone=i,this.location=s,this.tabsPrefix=n!==void 0?AD(n):void 0}createView(n,t){let r=xf(this.router,t),o=n?.location?.nativeElement,i=TD(this.zone,n.instance,o);return{id:this.nextId++,stackId:P0(this.tabsPrefix,r),unlistenEvents:i,element:o,ref:n,url:r}}getExistingView(n){let t=xf(this.router,n),r=this.views.find(o=>o.url===t);return r&&r.ref.changeDetectorRef.reattach(),r}setActive(n){let t=this.navCtrl.consumeTransition(),{direction:r,animation:o,animationBuilder:i}=t,s=this.activeView,a=MD(n,s);a&&(r="back",o=void 0);let c=this.views.slice(),u,l=this.router;l.getCurrentNavigation?u=l.getCurrentNavigation():l.navigations?.value&&(u=l.navigations.value),u?.extras?.replaceUrl&&this.views.length>0&&this.views.splice(-1,1);let d=this.views.includes(n),h=this.insertView(n,r);d||n.ref.changeDetectorRef.detectChanges();let f=n.animationBuilder;return i===void 0&&r==="back"&&!a&&f!==void 0&&(i=f),s&&(s.animationBuilder=i),this.zone.runOutsideAngular(()=>this.wait(()=>(s&&s.ref.changeDetectorRef.detach(),n.ref.changeDetectorRef.reattach(),this.transition(n,s,o,this.canGoBack(1),!1,i).then(()=>F0(n,h,c,this.location,this.zone)).then(()=>({enteringView:n,direction:r,animation:o,tabSwitch:a})))))}canGoBack(n,t=this.getActiveStackId()){return this.getStack(t).length>n}pop(n,t=this.getActiveStackId()){return this.zone.run(()=>{let r=this.getStack(t);if(r.length<=n)return Promise.resolve(!1);let o=r[r.length-n-1],i=o.url,s=o.savedData;if(s){let c=s.get("primary");c?.route?._routerState?.snapshot.url&&(i=c.route._routerState.snapshot.url)}let{animationBuilder:a}=this.navCtrl.consumeTransition();return this.navCtrl.navigateBack(i,N(g({},o.savedExtras),{animation:a})).then(()=>!0)})}startBackTransition(){let n=this.activeView;if(n){let t=this.getStack(n.stackId),r=t[t.length-2],o=r.animationBuilder;return this.wait(()=>this.transition(r,n,"back",this.canGoBack(2),!0,o))}return Promise.resolve()}endBackTransition(n){n?(this.skipTransition=!0,this.pop(1)):this.activeView&&RD(this.activeView,this.views,this.views,this.location,this.zone)}getLastUrl(n){let t=this.getStack(n);return t.length>0?t[t.length-1]:void 0}getRootUrl(n){let t=this.getStack(n);return t.length>0?t[0]:void 0}getActiveStackId(){return this.activeView?this.activeView.stackId:void 0}getActiveView(){return this.activeView}hasRunningTask(){return this.runningTask!==void 0}destroy(){this.containerEl=void 0,this.views.forEach(ND),this.activeView=void 0,this.views=[]}getStack(n){return this.views.filter(t=>t.stackId===n)}insertView(n,t){return this.activeView=n,this.views=x0(this.views,n,t),this.views.slice()}transition(n,t,r,o,i,s){if(this.skipTransition)return this.skipTransition=!1,Promise.resolve(!1);if(t===n)return Promise.resolve(!1);let a=n?n.element:void 0,c=t?t.element:void 0,u=this.containerEl;return a&&a!==c&&(a.classList.add("ion-page"),a.classList.add("ion-page-invisible"),u.commit)?u.commit(a,c,{duration:r===void 0?0:void 0,direction:r,showGoBack:o,progressAnimation:i,animationBuilder:s}):Promise.resolve(!1)}wait(n){return oe(this,null,function*(){this.runningTask!==void 0&&(yield this.runningTask,this.runningTask=void 0);let t=this.runningTask=n();return t.finally(()=>this.runningTask=void 0),t})}},F0=(e,n,t,r,o)=>typeof requestAnimationFrame=="function"?new Promise(i=>{requestAnimationFrame(()=>{RD(e,n,t,r,o),i()})}):Promise.resolve(),RD=(e,n,t,r,o)=>{o.run(()=>t.filter(i=>!n.includes(i)).forEach(ND)),n.forEach(i=>{let a=r.path().split("?")[0].split("#")[0];if(i!==e&&i.url!==a){let c=i.element;c.setAttribute("aria-hidden","true"),c.classList.add("ion-page-hidden"),i.ref.changeDetectorRef.detach()}})},L0=(()=>{class e{parentOutlet;nativeEl;activatedView=null;tabsPrefix;_swipeGesture;stackCtrl;proxyMap=new WeakMap;currentActivatedRoute$=new ee(null);activated=null;get activatedComponentRef(){return this.activated}_activatedRoute=null;name=A;stackWillChange=new z;stackDidChange=new z;activateEvents=new z;deactivateEvents=new z;parentContexts=p(Ft);location=p($e);environmentInjector=p(G);inputBinder=p(xD,{optional:!0});supportsBindingToComponentInputs=!0;config=p(_D);navCtrl=p(Ci);set animation(t){this.nativeEl.animation=t}set animated(t){this.nativeEl.animated=t}set swipeGesture(t){this._swipeGesture=t,this.nativeEl.swipeHandler=t?{canStart:()=>this.stackCtrl.canGoBack(1)&&!this.stackCtrl.hasRunningTask(),onStart:()=>this.stackCtrl.startBackTransition(),onEnd:r=>this.stackCtrl.endBackTransition(r)}:void 0}constructor(t,r,o,i,s,a,c,u){this.parentOutlet=u,this.nativeEl=i.nativeElement,this.name=t||A,this.tabsPrefix=r==="true"?xf(s,c):void 0,this.stackCtrl=new Of(this.tabsPrefix,this.nativeEl,s,this.navCtrl,a,o),this.parentContexts.onChildOutletCreated(this.name,this)}ngOnDestroy(){this.stackCtrl.destroy(),this.inputBinder?.unsubscribeFromRouteData(this)}getContext(){return this.parentContexts.getContext(this.name)}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(!this.activated){let t=this.getContext();t?.route&&this.activateWith(t.route,t.injector)}new Promise(t=>jt(this.nativeEl,t)).then(()=>{this._swipeGesture===void 0&&(this.swipeGesture=this.config.getBoolean("swipeBackEnabled",this.nativeEl.mode==="ios"))})}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new Error("Outlet is not activated");return this.activated.instance}get activatedRoute(){if(!this.activated)throw new Error("Outlet is not activated");return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){throw new Error("incompatible reuse strategy")}attach(t,r){throw new Error("incompatible reuse strategy")}deactivate(){if(this.activated){if(this.activatedView){let r=this.getContext();this.activatedView.savedData=new Map(r.children.contexts);let o=this.activatedView.savedData.get("primary");if(o&&r.route&&(o.route=g({},r.route)),this.activatedView.savedExtras={},r.route){let i=r.route.snapshot;this.activatedView.savedExtras.queryParams=i.queryParams,this.activatedView.savedExtras.fragment=i.fragment}}let t=this.component;this.activatedView=null,this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(t)}}activateWith(t,r){if(this.isActivated)throw new Error("Cannot activate an already activated outlet");this._activatedRoute=t;let o,i=this.stackCtrl.getExistingView(t);if(i){o=this.activated=i.ref;let a=i.savedData;if(a){let c=this.getContext();c.children.contexts=a}this.updateActivatedRouteProxy(o.instance,t)}else{let a=t._futureSnapshot,c=this.parentContexts.getOrCreateContext(this.name).children,u=new ee(null),l=this.createActivatedRouteProxy(u,t),d=new kf(l,c,this.location.injector),h=a.routeConfig.component??a.component;o=this.activated=this.outletContent.createComponent(h,{index:this.outletContent.length,injector:d,environmentInjector:r??this.environmentInjector}),u.next(o.instance),i=this.stackCtrl.createView(this.activated,t),this.proxyMap.set(o.instance,l),this.currentActivatedRoute$.next({component:o.instance,activatedRoute:t})}this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activatedView=i,this.navCtrl.setTopOutlet(this);let s=this.stackCtrl.getActiveView();this.stackWillChange.emit({enteringView:i,tabSwitch:MD(i,s)}),this.stackCtrl.setActive(i).then(a=>{this.activateEvents.emit(o.instance),this.stackDidChange.emit(a)})}canGoBack(t=1,r){return this.stackCtrl.canGoBack(t,r)}pop(t=1,r){return this.stackCtrl.pop(t,r)}getLastUrl(t){let r=this.stackCtrl.getLastUrl(t);return r?r.url:void 0}getLastRouteView(t){return this.stackCtrl.getLastUrl(t)}getRootView(t){return this.stackCtrl.getRootUrl(t)}getActiveStackId(){return this.stackCtrl.getActiveStackId()}createActivatedRouteProxy(t,r){let o=new Ne;return o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,o._paramMap=this.proxyObservable(t,"paramMap"),o._queryParamMap=this.proxyObservable(t,"queryParamMap"),o.url=this.proxyObservable(t,"url"),o.params=this.proxyObservable(t,"params"),o.queryParams=this.proxyObservable(t,"queryParams"),o.fragment=this.proxyObservable(t,"fragment"),o.data=this.proxyObservable(t,"data"),o}proxyObservable(t,r){return t.pipe(te(o=>!!o),ie(o=>this.currentActivatedRoute$.pipe(te(i=>i!==null&&i.component===o),ie(i=>i&&i.activatedRoute[r]),jc())))}updateActivatedRouteProxy(t,r){let o=this.proxyMap.get(t);if(!o)throw new Error("Could not find activated route proxy for view");o._futureSnapshot=r._futureSnapshot,o._routerState=r._routerState,o.snapshot=r.snapshot,o.outlet=r.outlet,o.component=r.component,this.currentActivatedRoute$.next({component:t,activatedRoute:r})}static \u0275fac=function(r){return new(r||e)(Jt("name"),Jt("tabs"),v(Rt),v(q),v(je),v(H),v(Ne),v(e,12))};static \u0275dir=F({type:e,selectors:[["ion-router-outlet"]],inputs:{animated:"animated",animation:"animation",mode:"mode",swipeGesture:"swipeGesture",name:"name"},outputs:{stackWillChange:"stackWillChange",stackDidChange:"stackDidChange",activateEvents:"activate",deactivateEvents:"deactivate"},exportAs:["outlet"],standalone:!1})}return e})(),kf=class{route;childContexts;parent;constructor(n,t,r){this.route=n,this.childContexts=t,this.parent=r}get(n,t){return n===Ne?this.route:n===Ft?this.childContexts:this.parent.get(n,t)}},xD=new E(""),j0=(()=>{class e{outletDataSubscriptions=new Map;bindActivatedRouteToOutletComponent(t){this.unsubscribeFromRouteData(t),this.subscribeToRouteData(t)}unsubscribeFromRouteData(t){this.outletDataSubscriptions.get(t)?.unsubscribe(),this.outletDataSubscriptions.delete(t)}subscribeToRouteData(t){let{activatedRoute:r}=t,o=Jn([r.queryParams,r.params,r.data]).pipe(ie(([i,s,a],c)=>(a=g(g(g({},i),s),a),c===0?_(a):Promise.resolve(a)))).subscribe(i=>{if(!t.isActivated||!t.activatedComponentRef||t.activatedRoute!==r||r.component===null){this.unsubscribeFromRouteData(t);return}let s=Td(r.component);if(!s){this.unsubscribeFromRouteData(t);return}for(let{templateName:a}of s.inputs)t.activatedComponentRef.setInput(a,i[a])});this.outletDataSubscriptions.set(t,o)}static \u0275fac=function(r){return new(r||e)};static \u0275prov=w({token:e,factory:e.\u0275fac})}return e})(),e8=()=>({provide:xD,useFactory:V0,deps:[je]});function V0(e){return e?.componentInputBindingEnabled?new j0:null}var B0=["color","defaultHref","disabled","icon","mode","routerAnimation","text","type"],t8=(()=>{let e=class Pf{routerOutlet;navCtrl;config;r;z;el;constructor(t,r,o,i,s,a){this.routerOutlet=t,this.navCtrl=r,this.config=o,this.r=i,this.z=s,a.detach(),this.el=this.r.nativeElement}onClick(t){let r=this.defaultHref||this.config.get("backButtonDefaultHref");this.routerOutlet?.canGoBack()?(this.navCtrl.setDirection("back",void 0,void 0,this.routerAnimation),this.routerOutlet.pop(),t.preventDefault()):r!=null&&(this.navCtrl.navigateBack(r,{animation:this.routerAnimation}),t.preventDefault())}static \u0275fac=function(r){return new(r||Pf)(v(L0,8),v(Ci),v(_D),v(q),v(H),v(ot))};static \u0275dir=F({type:Pf,hostBindings:function(r,o){r&1&&me("click",function(s){return o.onClick(s)})},inputs:{color:"color",defaultHref:"defaultHref",disabled:"disabled",icon:"icon",mode:"mode",routerAnimation:"routerAnimation",text:"text",type:"type"},standalone:!1})};return e=no([yc({inputs:B0})],e),e})(),n8=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref(),this.updateTabindex()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTabindex(){let t=["ION-BACK-BUTTON","ION-BREADCRUMB","ION-BUTTON","ION-CARD","ION-FAB-BUTTON","ION-ITEM","ION-ITEM-OPTION","ION-MENU-BUTTON","ION-SEGMENT-BUTTON","ION-TAB-BUTTON"],r=this.elementRef.nativeElement;t.includes(r.tagName)&&r.getAttribute("tabindex")==="0"&&r.removeAttribute("tabindex")}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(t){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation),t.preventDefault()}static \u0275fac=function(r){return new(r||e)(v(Nt),v(Ci),v(q),v(je),v(di,8))};static \u0275dir=F({type:e,selectors:[["","routerLink","",5,"a",5,"area"]],hostBindings:function(r,o){r&1&&me("click",function(s){return o.onClick(s)})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Ae]})}return e})(),r8=(()=>{class e{locationStrategy;navCtrl;elementRef;router;routerLink;routerDirection="forward";routerAnimation;constructor(t,r,o,i,s){this.locationStrategy=t,this.navCtrl=r,this.elementRef=o,this.router=i,this.routerLink=s}ngOnInit(){this.updateTargetUrlAndHref()}ngOnChanges(){this.updateTargetUrlAndHref()}updateTargetUrlAndHref(){if(this.routerLink?.urlTree){let t=this.locationStrategy.prepareExternalUrl(this.router.serializeUrl(this.routerLink.urlTree));this.elementRef.nativeElement.href=t}}onClick(){this.navCtrl.setDirection(this.routerDirection,void 0,void 0,this.routerAnimation)}static \u0275fac=function(r){return new(r||e)(v(Nt),v(Ci),v(q),v(je),v(di,8))};static \u0275dir=F({type:e,selectors:[["a","routerLink",""],["area","routerLink",""]],hostBindings:function(r,o){r&1&&me("click",function(){return o.onClick()})},inputs:{routerDirection:"routerDirection",routerAnimation:"routerAnimation"},standalone:!1,features:[Ae]})}return e})(),U0=["animated","animation","root","rootParams","swipeGesture"],H0=["push","insert","insertPages","pop","popTo","popToRoot","removeIndex","setRoot","setPages","getActive","getByIndex","canGoBack","getPrevious"],o8=(()=>{let e=class Ff{z;el;constructor(t,r,o,i,s,a){this.z=s,a.detach(),this.el=t.nativeElement,t.nativeElement.delegate=i.create(r,o),Lf(this,this.el,["ionNavDidChange","ionNavWillChange"])}static \u0275fac=function(r){return new(r||Ff)(v(q),v(G),v(fe),v(C0),v(H),v(ot))};static \u0275dir=F({type:Ff,inputs:{animated:"animated",animation:"animation",root:"root",rootParams:"rootParams",swipeGesture:"swipeGesture"},standalone:!1})};return e=no([yc({inputs:U0,methods:H0})],e),e})(),i8=(()=>{class e{navCtrl;tabsInner;ionTabsWillChange=new z;ionTabsDidChange=new z;tabBarSlot="bottom";hasTab=!1;selectedTab;leavingTab;constructor(t){this.navCtrl=t}ngAfterViewInit(){let t=this.tabs.length>0?this.tabs.first:void 0;t&&(this.hasTab=!0,this.setActiveTab(t.tab),this.tabSwitch())}ngAfterContentInit(){this.detectSlotChanges()}ngAfterContentChecked(){this.detectSlotChanges()}onStackWillChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&this.ionTabsWillChange.emit({tab:o})}onStackDidChange({enteringView:t,tabSwitch:r}){let o=t.stackId;r&&o!==void 0&&(this.tabBar&&(this.tabBar.selectedTab=o),this.ionTabsDidChange.emit({tab:o}))}select(t){let r=typeof t=="string",o=r?t:t.detail.tab;if(this.hasTab){this.setActiveTab(o),this.tabSwitch();return}let i=this.outlet.getActiveStackId()===o,s=`${this.outlet.tabsPrefix}/${o}`;if(r||t.stopPropagation(),i){let a=this.outlet.getActiveStackId();if(this.outlet.getLastRouteView(a)?.url===s)return;let u=this.outlet.getRootView(o),l=u&&s===u.url&&u.savedExtras;return this.navCtrl.navigateRoot(s,N(g({},l),{animated:!0,animationDirection:"back"}))}else{let a=this.outlet.getLastRouteView(o),c=a?.url||s,u=a?.savedExtras;return this.navCtrl.navigateRoot(c,N(g({},u),{animated:!0,animationDirection:"back"}))}}setActiveTab(t){let o=this.tabs.find(i=>i.tab===t);if(!o){console.error(`[Ionic Error]: Tab with id: "${t}" does not exist`);return}this.leavingTab=this.selectedTab,this.selectedTab=o,this.ionTabsWillChange.emit({tab:t}),o.el.active=!0}tabSwitch(){let{selectedTab:t,leavingTab:r}=this;this.tabBar&&t&&(this.tabBar.selectedTab=t.tab),r?.tab!==t?.tab&&r?.el&&(r.el.active=!1),t&&this.ionTabsDidChange.emit({tab:t.tab})}getSelected(){return this.hasTab?this.selectedTab?.tab:this.outlet.getActiveStackId()}detectSlotChanges(){this.tabBars.forEach(t=>{let r=t.el.getAttribute("slot");r!==this.tabBarSlot&&(this.tabBarSlot=r,this.relocateTabBar())})}relocateTabBar(){let t=this.tabBar.el;this.tabBarSlot==="top"?this.tabsInner.nativeElement.before(t):this.tabsInner.nativeElement.after(t)}static \u0275fac=function(r){return new(r||e)(v(Ci))};static \u0275dir=F({type:e,selectors:[["ion-tabs"]],viewQuery:function(r,o){if(r&1&&vd(m0,7,q),r&2){let i;Mr(i=Ar())&&(o.tabsInner=i.first)}},hostBindings:function(r,o){r&1&&me("ionTabButtonClick",function(s){return o.select(s)})},outputs:{ionTabsWillChange:"ionTabsWillChange",ionTabsDidChange:"ionTabsDidChange"},standalone:!1})}return e})(),$0=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),s8=(()=>{class e{injector;elementRef;onChange=()=>{};onTouched=()=>{};lastValue;statusChanges;constructor(t,r){this.injector=t,this.elementRef=r}writeValue(t){this.elementRef.nativeElement.value=this.lastValue=t,Ei(this.elementRef)}handleValueChange(t,r){t===this.elementRef.nativeElement&&(r!==this.lastValue&&(this.lastValue=r,this.onChange(r)),Ei(this.elementRef))}_handleBlurEvent(t){t===this.elementRef.nativeElement?(this.onTouched(),Ei(this.elementRef)):t.closest("ion-radio-group")===this.elementRef.nativeElement&&this.onTouched()}registerOnChange(t){this.onChange=t}registerOnTouched(t){this.onTouched=t}setDisabledState(t){this.elementRef.nativeElement.disabled=t}ngOnDestroy(){this.statusChanges&&this.statusChanges.unsubscribe()}ngAfterViewInit(){let t;try{t=this.injector.get(Vn)}catch{}if(!t)return;t.statusChanges&&(this.statusChanges=t.statusChanges.subscribe(()=>Ei(this.elementRef)));let r=t.control;r&&["markAsTouched","markAllAsTouched","markAsUntouched","markAsDirty","markAsPristine"].forEach(i=>{if(typeof r[i]<"u"){let s=r[i].bind(r);r[i]=(...a)=>{s(...a),Ei(this.elementRef)}}})}static \u0275fac=function(r){return new(r||e)(v(fe),v(q))};static \u0275dir=F({type:e,hostBindings:function(r,o){r&1&&me("ionBlur",function(s){return o._handleBlurEvent(s.target)})},standalone:!1})}return e})(),Ei=e=>{$0(()=>{let n=e.nativeElement,t=n.value!=null&&n.value.toString().length>0,r=z0(n);Mf(n,r);let o=n.closest("ion-item");o&&(t?Mf(o,[...r,"item-has-value"]):Mf(o,r))})},z0=e=>{let n=e.classList,t=[];for(let r=0;r<n.length;r++){let o=n.item(r);o!==null&&G0(o,"ng-")&&t.push(`ion-${o.substring(3)}`)}return t},Mf=(e,n)=>{let t=e.classList;t.remove("ion-valid","ion-invalid","ion-touched","ion-untouched","ion-dirty","ion-pristine"),t.add(...n)},G0=(e,n)=>e.substring(0,n.length)===n,ID=class{shouldDetach(n){return!1}shouldAttach(n){return!1}store(n,t){}retrieve(n){return null}shouldReuseRoute(n,t){if(n.routeConfig!==t.routeConfig)return!1;let r=n.params,o=t.params,i=Object.keys(r),s=Object.keys(o);if(i.length!==s.length)return!1;for(let a of i)if(o[a]!==r[a])return!1;return!0}},bD=class{ctrl;constructor(n){this.ctrl=n}create(n){return this.ctrl.create(n||{})}dismiss(n,t,r){return this.ctrl.dismiss(n,t,r)}getTop(){return this.ctrl.getTop()}};export{O as a,no as b,W as c,Qn as d,PD as e,R as f,oo as g,at as h,Te as i,w as j,ke as k,p as l,ur as m,G as n,ap as o,cp as p,fe as q,J as r,He as s,Jt as t,q as u,Ng as v,nw as w,v as x,$e as y,sd as z,ze as A,F as B,Ce as C,Im as D,H as E,Am as F,tn as G,xm as H,pd as I,gd as J,ha as K,md as L,pa as M,Om as N,km as O,sb as P,ab as Q,me as R,lb as S,fb as T,hb as U,Po as V,vd as W,Mr as X,Ar as Y,gb as Z,Fo as _,xb as $,Lm as aa,rt as ba,ot as ca,Rt as da,sv as ea,E_ as fa,C_ as ga,av as ha,k_ as ia,Mv as ja,uT as ka,Ne as la,Iy as ma,je as na,VS as oa,US as pa,qS as qa,qz as ra,Jr as sa,_f as ta,XM as ua,Jz as va,nG as wa,rG as xa,oG as ya,lD as za,n0 as Aa,r0 as Ba,hG as Ca,pG as Da,gG as Ea,mG as Fa,vG as Ga,hD as Ha,f0 as Ia,yG as Ja,DG as Ka,g0 as La,mi as Ma,jy as Na,vi as Oa,Fz as Pa,Lz as Qa,Vz as Ra,IM as Sa,_M as Ta,SM as Ua,eD as Va,Bz as Wa,Uz as Xa,OM as Ya,PM as Za,LM as _a,Hz as $a,$z as ab,zz as bb,Ci as cb,_D as db,E0 as eb,C0 as fb,yc as gb,JG as hb,XG as ib,L0 as jb,e8 as kb,t8 as lb,n8 as mb,r8 as nb,o8 as ob,i8 as pb,$0 as qb,s8 as rb,Ei as sb,ID as tb,bD as ub};
